import React, { useEffect, useState, useContext } from "react";
import { useMediaQuery, useTheme } from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
  TableContainer,
  IconButton,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import DocumentUploadDialog from "src/@core/components/custom-components/DocumentDialogUpload";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";

const AwardEdit = (props) => {
  const {
    getAllDocuments,
    uploadDocuments,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    shortFormData,
    patchMicrosite,
  } = useContext(AuthContext);
  const [documents, setDocuments] = useState({ data: [] });
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [selectedAward, setSelectedAward] = useState(null);
  const [fieldChanged, setFieldChanged] = useState(false);

  const { onCancel, data, userData } = props;

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    unregister,
    getValues,
    formState: { errors },
  } = useForm();

  const [entries, setEntries] = useState([]);

  useEffect(() => {
    if (data && data?.awardList) {
      setEntries(data?.awardList);
    }
  }, [data]);

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`awards[${index}].name`, entry.name);
      setValue(`awards[${index}].description`, entry.description);
    });
  }, [entries, setValue]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.awards || [];
    setEntries([...currentEntries, { name: "", description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);
    unregister(`awards[${index}].name`);
    unregister(`awards[${index}].description`);
    reset({
      ...getValues(),
      awards: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    const awardsData =
      data?.awards?.length > 0 ? data.awards : [{ name: "", description: "" }];

    const userUniqueId =
      userData && userData.id !== undefined ? userData.id : user.id;

    const response = await patchMicrosite(
      { awardList: data.awards },
      userUniqueId,
      () => {
        console.log("Success Awards.");
      },
      () => {
        console.error("serviceDetails failed");
      }
    );

    onCancel();
    reset();
  };

  const [modalPopup, setModalPopup] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [disableButton, setDisableButton] = useState(false);
  const [awardToDelete, setAwardToDelete] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleViewIconClick = (award) => {
    setSelectedAward(award);
  };

  const handleDialogClose = () => {
    setSelectedAward(null);
  };

  async function handleDelete() {
    await documentDelete(awardToDelete, userUniqueId);
    setSelectedAward(null);
    setConfirmDeleteDialogOpen(false);
    fetchAllDocuments();
  }

  const fetchAllDocuments = async () => {
    if (allCategories.length > 0 && allSubCategories.length > 0) {
      const allDocs = await getAllDocuments(documentJson);
      setDocuments(allDocs);
    }
  };
  
  useEffect(() => {
    fetchAllDocuments();
  }, []);
  
  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const userUniqueId =
    userData && userData.id !== undefined ? userData.id : user.id;

  const documentDetails = {
    userId: userUniqueId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("awards"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const documentJson = {
    userId: userUniqueId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("awards"),
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });
    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append("documentSectionsEnum", "AWARDS");

    // API call
    await uploadDocuments(
      formData,
      userUniqueId,
      () => {
        setModalPopup(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },
      () => {
        console.log("Failure");
      }
    );

    fetchAllDocuments();
    setLoading(false);
    setDisableButton(false);
  };

  const theme = useTheme(); // Access current theme
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isExtraSmallScreen = useMediaQuery("(max-width:360px)");

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container direction="column" spacing={2}>
          <Grid
            container
            justifyContent={
              entries.length === 0 ? { lg: "space-between", md: "space-between", sm: "space-between" } : "flex-end"
            }
            flexDirection={
              entries.length === 0 ? { xs: "column", lg: "row", md: "row", sm: "row" } : ""
            }
            alignItems="center"
            sx={{
              mt: { xs: 4, lg: 2 },
              mb: { xs: 1, lg: 0 },
            }}
          >
            {entries.length === 0 && (
              <Typography
                style={{
                  paddingLeft: "20px",
                  textAlign: "center",
                  flex: 1,
                  marginLeft: isSmallScreen ? "" : "4rem",
                }}
              >
                Click on ADD to add Awards
              </Typography>
            )}
            <Button
              onClick={addEntry}
              color="primary"
              variant="contained"
              
              sx={{
                mb: { xs: 2, lg: 1 },
                mt: { xs: 2, lg: 4 },
                alignSelf: isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",

              }}
            >
              Add
            </Button>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ borderBottom: "1px solid rgb(230, 230, 231)", my: 2 }} />
          </Grid>
          <TableContainer component={Paper}>
            <Grid container direction="column" spacing={2}>
              <Grid item xs={12}>
                <Table sx={{ width: "100%" }}>
                  <TableHead sx={{ padding: "13px !important" }}>
                    {entries.length > 0 && (
                      <TableRow
                        style={{ backgroundColor: "#f2f7f2" }}
                      >
                        <TableCell sx={{ padding: "5px" }}>Name</TableCell>
                        <TableCell sx={{ padding: "5px" }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ padding: "5px" }}>Delete</TableCell>
                      </TableRow>
                    )}
                  </TableHead>
                  <TableBody>
                    {entries?.map((entry, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Controller
                            name={`awards[${index}].name`}
                            control={control}
                            defaultValue={entry.name}
                            rules={{ required: "This field is required" }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Name"
                                variant="outlined"
                                size="small"
                                fullWidth
                                error={Boolean(errors?.awards?.[index]?.name)}
                                helperText={
                                  errors?.awards?.[index]?.name?.message || ""
                                }
                                onChange={(e) => {
                                  field.onChange(e);
                                  setFieldChanged(true);
                                }}
                                sx={{ width: isSmallScreen ? "100px" : "100%" }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <Controller
                            name={`awards[${index}].description`}
                            control={control}
                            defaultValue={entry.description}
                            rules={{ required: "Description is required" }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Description"
                                variant="outlined"
                                size="small"
                                fullWidth
                                error={Boolean(
                                  errors?.awards?.[index]?.description
                                )}
                                helperText={
                                  errors?.awards?.[index]?.description
                                    ?.message || ""
                                }
                                sx={{ width: isSmallScreen ? "200px" : "100%" }}
                                onChange={(e) => {
                                  field.onChange(e);
                                  setFieldChanged(true);
                                }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => removeEntry(index)}
                            color="error"
                          >
                            <Icon icon="iconamoon:trash" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Grid>
              <Grid
                container
                justifyContent={
                  documents?.data?.length === 0
                    ? { lg: "space-between", md: "space-between", sm: "space-between" } : "flex-end"
                }
                flexDirection={
                  documents?.data?.length === 0
                    ?  { xs: "column", lg: "row", md: "row", sm: "row" } 
                    : ""
                }
                alignItems="center"
                sx={{
                  mt: { xs: 2, lg: 0 },
                  mb: { xs: 2, lg: 0 },
                }}
              >
                {documents?.data?.length === 0 && (
                  <Typography
                    style={{
                      paddingLeft: "20px",
                      textAlign: "center",
                      flex: 1,
                      marginLeft: isSmallScreen ? "1rem" : "10rem",
                    }}
                  >
                    Click on UPLOAD IMAGES to upload images
                  </Typography>
                )}
                <Button
                  aria-controls="simple-menu"
                  aria-haspopup="true"
                  onClick={() => {
                    setModalPopup(true);
                  }}
                  variant="contained"
                  sx={{
                    px: 4,
                    mt: 4,
                    mb: 2,
                    ml: 4,
                    display: "flex",
                    justifyContent: "end",
                    alignSelf: isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",

                  }}
                >
                  Upload Images
                </Button>
              </Grid>
              <Grid item xs={12}>
                <Table sx={{ width: "100%" }}>
                  <TableHead>
                    {documents?.data?.length > 0 && (
                      <TableRow
                        style={{ backgroundColor: "#f2f7f2" }}
                      >
                        <TableCell sx={{ padding: "5px" }}>
                          File name
                        </TableCell>
                        <TableCell sx={{ padding: "5px" }}>Action</TableCell>
                      </TableRow>
                    )}
                  </TableHead>
                  <TableBody>
                    {documents?.data?.map((award, index) => (
                      <TableRow key={index} >
                        <TableCell >
                          <Typography className="data-field">
                            {award && award.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => {
                              setConfirmDeleteDialogOpen(true);
                              setAwardToDelete(award);
                            }}
                            color="error"
                          >
                            <Icon icon="iconamoon:trash" />
                          </IconButton>
                          <IconButton
                            onClick={() => handleViewIconClick(award)}
                            color="error"
                            disabled={selectedAward}
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Grid>
            </Grid>
            <Grid
              container
              justifyContent="center"
              padding="20px 0"
              sx={{ mt: 1, mb: 1 }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: isExtraSmallScreen ? "row" : "row",
                  gap: isExtraSmallScreen ? "1rem" : 0,
                }}
              >
                <Button
                  size="medium"
                  sx={{ mr: isExtraSmallScreen ? 0 : 3 }}
                  variant="outlined"
                  color="primary"
                  onClick={() => onCancel()}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  variant="contained"
                  disabled={!fieldChanged}
                >
                  Submit
                </Button>
              </Box>
            </Grid>
            <ViewDialogByLocation
              location={selectedAward}
              setSelectedLocation={setSelectedAward}
              onClose={handleDialogClose}
            />
            <Grid>
              <Dialog
                open={dialogSuccess}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                PaperProps={{
                  sx: {
                    p: (theme) => `${theme.spacing(2.5)} !important`,
                    backgroundColor: (theme) => theme.palette.primary.background,
                  },
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    borderRadius: 1,
                    textAlign: "center",
                    border: (theme) => `1px solid ${theme.palette.divider}`,
                    borderColor: "primary.main",
                  }}
                >
                  <DialogContent>
                    <DialogContentText
                      id="alert-dialog-description"
                      color="primary.main"
                    >
                      Successfully uploaded
                    </DialogContentText>
                  </DialogContent>
                  <DialogActions>
                    <Button
                      variant="contained"
                      onClick={handleClose}
                      sx={{ margin: "auto", width: 100 }}
                    >
                      Ok
                    </Button>
                  </DialogActions>
                </Box>
              </Dialog>

              <DeleteConfirmationDialog
                open={confirmDeleteDialogOpen}
                onClose={() => setConfirmDeleteDialogOpen(false)}
                onConfirm={handleDelete}
              />
            </Grid>
          </TableContainer>

          <DocumentUploadDialog
            open={modalPopup}
            onClose={() => setModalPopup(false)}
            onSave={() => handleSave()}
            selectedFiles={selectedFiles}
            setSelectedFiles={setSelectedFiles}
            loading={loading}
            disableButton={disableButton}
          />
        </Grid>
      </form>
    </>
  );
};

export default AwardEdit;
