
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import { useTheme } from "@emotion/react";
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";
import Section7 from "./Section7";
import MUITableCell from "src/pages/SP/MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const AssignmentAndStatus = ({ data, expanded, employeesData }) => {
  const { can } = useRBAC();

  // ** Hook
  const theme = useTheme();

  const [state6, setState6] = useState(true);
  const { listValues } = useContext(AuthContext);
  const handleState6 = () => {
    setState6(!state6);
  };

  const [assignedTo, setAssignedTo] = useState(data?.assignedTo);
  const [createdBy, setCreatedBy] = useState(data?.createdBy);

  const handleAssignedToChange = (event) => {
    const selectedId = event.target.value;
    setAssignedTo(selectedId);
  };

  useEffect(() => {
    if (!!data && !!data?.assignedTo) {
      setAssignedTo(data.assignedTo);
    }
    if (!!data && !!data?.createdBy) {
      setCreatedBy(data.createdBy);
    }
  }, [data]);

  const [assignedToName, setAssignedToName] = useState("");
  const [createdByName, setCreatedByName] = useState("");

  useEffect(() => {
    if (!!assignedTo && employeesData && employeesData.length > 0) {
      setAssignedToName(
        employeesData?.find((item) => item.id == assignedTo)?.name
      );
    }
    if (!!createdBy && employeesData && employeesData.length > 0) {
      setCreatedByName(
        employeesData?.find((item) => item.id == createdBy)?.name
      );
    }
  }, [assignedTo, createdBy, employeesData]);

  const leadStatus = data?.leadStatus
    ? listValues.find((item) => item?.id === data?.leadStatus)?.name
    : null;

    const leadPriority = data?.leadPriority
    ? listValues.find((item) => item?.id === data?.leadPriority)?.name
    : null;

  const sourceGroup = data?.sourceGroup
    ? listValues.find((item) => item?.id === data?.sourceGroup)?.name
    : null;

  const subSourceGroup = data?.subSourceGroup
    ? listValues.find((item) => item?.id === data?.subSourceGroup)?.name
    : null;

  return (
    <>
      {/* {can('society_otherDetails_READ') && */}
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Assignment and Status"}
        body={
          <>
            {state6 && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                //onClick={can('society_otherDetails_UPDATE') ? handleState6 : null}>
                onClick={handleState6}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Assigned To:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {assignedToName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Lead Status:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {leadStatus}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Lead Priority:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {leadPriority}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Source Group:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {sourceGroup}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Sub Source Group:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {subSourceGroup}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Created On:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.createdOn
                            ? new Date(data?.createdOn).toLocaleDateString(
                                "en-GB"
                              )
                            : ""}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Created By:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {createdByName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Remarks:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.remarks}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {!state6 && (
              <Section7
                formData={data}
                onCancel={handleState6}
                employeesData={employeesData}
                assignedTo={assignedTo}
                createdByName={createdByName}
                handleAssignedToChange={handleAssignedToChange}
              />
            )}
          </>
        }
        expanded={expanded}
      />
      {/* } */}
    </>
  );
};
export default AssignmentAndStatus;
