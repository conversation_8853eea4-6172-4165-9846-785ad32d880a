// ** MUI Imports
import Grid from '@mui/material/Grid'
import Table from '@mui/material/Table'
import Switch from '@mui/material/Switch'
import TableRow from '@mui/material/TableRow'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import Typography from '@mui/material/Typography'
import TableContainer from '@mui/material/TableContainer'
import FormControlLabel from '@mui/material/FormControlLabel'

// ** Custom Components Imports
import CustomChip from 'src/@core/components/mui/chip'

import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  InputLabel,
  TextField,
} from "@mui/material";

// ** Third Party Imports
import { useForm, Controller } from "react-hook-form";

const OtherDetails = () => {

  const {
    register,
    setError,
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
  } = useForm();

  return (
    <Grid container spacing={6}>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>
          Encroachment :
        </Grid>
        <Grid item flexGrow={1} xs={7} sm={7} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="encroachment"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.encroachment}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="text"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.encroachment)}
                  aria-describedby="validation-encroachment"
                />
              )}
            />
            {errors.encroachment?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-encroachment"
              >
                Encroachment is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>
          Litigation :
        </Grid>
        <Grid item flexGrow={1} xs={7} sm={7} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="litigation"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.litigation}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="text"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.litigation)}
                  aria-describedby="validation-litigation"
                />
              )}
            />
            {errors.litigation?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-litigation"
              >
                litigation is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>         
            Society Funds For Project :
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="societyFunds"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.litigation}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="text"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.societyFunds)}
                  aria-describedby="validation-societyFunds"
                />
              )}
            />
            {errors.societyFunds?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-societyFunds"
              >
                societyFunds is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>
            Market Area Price(Residential) :
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="marketAreaPriceResidential"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.marketAreaPriceResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.marketAreaPriceResidential)}
                  aria-describedby="validation-marketAreaPriceResidential"
                />
              )}
            />
            {errors.marketAreaPriceResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-marketAreaPriceResidential"
              >
                Market Area Price(Residential) is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>         
            Market Area Price(Commercial) :
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="marketAreaPriceCommercial"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.marketAreaPriceCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.marketAreaPriceCommercial)}
                  aria-describedby="validation-marketAreaPriceCommercial"
                />
              )}
            />
            {errors.marketAreaPriceCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-marketAreaPriceCommercial"
              >
                Market Area Price(Commercial) is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>


      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>        
            Parking Spaces:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="parkingSpaces"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.parkingSpaces}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.parkingSpaces)}
                  aria-describedby="validation-parkingSpaces"
                />
              )}
            />
            {errors.parkingSpaces?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-parkingSpaces"
              >
                Parking Spaces are required
              </FormHelperText>
            )}

          </FormControl>
        </Grid>

      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={5} sm={5} sx={{ marginTop: 5 }}>
          Notes :
        </Grid>
        <Grid item flexGrow={1} xs={7} sm={7} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="notes"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.encroachment}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="text"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.notes)}
                  placeholder=""
                  aria-describedby="validation-notes"
                />
              )}
            />
            {errors.notes?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-notes"
              >
                notes is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

      </Grid>


    </Grid>
  )
}

export default OtherDetails
