import {
  Box,
  FormControlLabel,
  FormHelper<PERSON>ext,
  <PERSON>rid,
  Switch,
  <PERSON><PERSON>ield,
  Typography,
} from "@mui/material";
import { useState, useEffect } from "react";
import Divider from "@mui/material/Divider";
import { useTheme } from "@mui/material/styles";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
const Consents = ({
  setNextActive,
  posts,
  handleTotalNumberOfMembersChange,
  handleNoOfConsentsChange,
  handlePercentageOfMembersConsentChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  const [totalNumberOfMembers, setTotalNumberOfMembers] = useState(
    defaultData?.totalNoOfMembers
  );
  const [noOfConsents, setNoOfConsents] = useState(defaultData?.noOfConsents);
  const [percentage, setPercentage] = useState(0);
  const [errors, setErrors] = useState({
    totalNumberOfMembers: "",
    noOfConsents: "",
  });
  const [consents, setConsents] = useState([]);

  const {
    settings: { direction },
  } = useSettings();

  const handleTotalNumberChange = (event) => {
    const totalNumberOfMembers = event.target.value.trim();


    if (/^\d*$/.test(totalNumberOfMembers) && !/^0\d+$/.test(totalNumberOfMembers)) {
      setTotalNumberOfMembers(totalNumberOfMembers);
      handleTotalNumberOfMembersChange(totalNumberOfMembers);
      setErrors((prevErrors) => ({
        ...prevErrors,
        totalNumberOfMembers: "",
      }));
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        totalNumberOfMembers: {
          type: 'invalid',
          message: 'Enter a valid Total No. of Members',
        },
      }));
    }
  };

  const handleNumberOfConsentsChange = (event) => {
    const noOfConsents = event.target.value.trim();

  
    if (/^\d*$/.test(noOfConsents) && !/^0\d+$/.test(noOfConsents)) {
      if (parseInt(noOfConsents) <= parseInt(totalNumberOfMembers)) {
        setNoOfConsents(noOfConsents);
        handleNoOfConsentsChange(noOfConsents);
        setErrors((prevErrors) => ({
          ...prevErrors,
          noOfConsents: "",
        }));
      } else {
        setNoOfConsents(noOfConsents);
        handleNoOfConsentsChange(noOfConsents);
        setErrors((prevErrors) => ({
          ...prevErrors,
          noOfConsents: {
            type: 'warning',
            message: 'Please provide a valid no. of consents.\n The number of consents cannot exceed the total number of members',
          },
        }));
      }
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        noOfConsents: {
          type: 'invalid',
          message: 'Enter a valid Number of Consents.',
        },
      }));
    }
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setConsents(data);
    }
  }, [posts]);

  useEffect(() => {
    const noOfConsentsLength = String(noOfConsents).length;
    const totalNumberOfMembersLength = String(totalNumberOfMembers).length;

    if (noOfConsentsLength > 0 && totalNumberOfMembersLength > 0 && email && contactNumber && name&& societyName) {
      if (totalNumberOfMembers !== 0 && noOfConsents !== 0) {
        console.log("total", totalNumberOfMembers, "conxsents", noOfConsents);
        const calculatedPercentage =
          (noOfConsents / totalNumberOfMembers) * 100;
        const percentage = Number.isNaN(calculatedPercentage)
          ? 0
          : calculatedPercentage.toFixed(0);
        if (percentage > 100) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            noOfConsents: {
              type: "invalid",
              message:
                "provide a valid no. of consents.\n The number of consents cannot exceed the total number of members",
            },
          }));
          setNextActive(false);
          setPercentage(0);
        } else {
          setErrors((prevErrors) => ({
            ...prevErrors,
            noOfConsents: "",
          }));
          setPercentage(percentage);
          handlePercentageOfMembersConsentChange(percentage);
          setNextActive(true);
        }
      } else if (totalNumberOfMembers === 0 && noOfConsents === 0) {
        const percentage = 0;
        setPercentage(percentage);
        console.log("percentage", percentage);
        handlePercentageOfMembersConsentChange(percentage);
        setNextActive(true);
      }
    } else {
      setPercentage(0);
      setNextActive(false);
    }
  }, [totalNumberOfMembers, noOfConsents,name,email,contactNumber,societyName]);

  const theme = useTheme();

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 1,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box >
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}>
                How many members have given the Consent for Redevelopment?
              </Typography>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12} sm={3} >
          <TextField
            type="number"
            value={totalNumberOfMembers}
            label="Total No. of Members"
            InputLabelProps={{
              sx: {
                fontSize: '0.9rem',
                 // Adjust the font size as needed
              },
            }}
            onChange={handleTotalNumberChange}
            helperText={errors.totalNumberOfMembers?.message}
            error={Boolean(errors.totalNumberOfMembers)}
            placeholder="Enter Total No. of Members"
            size='small'

            inputProps={{ min: 1, maxLength: 5 }}
          />
          {errors.totalNumberOfMembers?.type === "required" && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-totalNumberOfMembers"
            >
              Total No. of Members is required
            </FormHelperText>
          )}
        </Grid>

        <Grid item xs={12} sm={3} >
          <TextField
            type="number"
            value={noOfConsents}
            label="No. of Consents Given"
            InputLabelProps={{
              sx: {
                fontSize: '0.9rem', // Adjust the font size as needed
              },
            }}
            onChange={handleNumberOfConsentsChange}
            helperText={errors.noOfConsents?.message}
            error={Boolean(errors.noOfConsents)}
            placeholder="Enter No. of Consents Given"
            size='small'

            inputProps={{ min: 0, maxLength: 5 }}
          />
          {errors.noOfConsents?.type === "required" && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-noOfConsents"
            >
              No of Consents is required
            </FormHelperText>
          )}
        </Grid>
      </Grid>
      <div style={{ marginTop: "1rem" }}></div>

      <Grid item xs={12}>
        <Typography variant="body1" fontStyle={"normal"} fontWeight={"bold"}  fontSize='0.9rem !important' sx={{marginBottom:{sm:'10px'}}}>
          Percentage of Members Consent = {percentage}%
        </Typography>
        {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
        {consents.length > 0 && (
          <Typography
            variant="body1"
            sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
          >
            <Divider
              sx={{
                mt: `${theme.spacing(5)} !important`,
                mb: `${theme.spacing(3)} !important`,
              }}
            />
            Review below articles for more info
          </Typography>
        )}
      </Grid>

      {consents.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={consents} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default Consents;
