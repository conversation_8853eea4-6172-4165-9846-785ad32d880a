// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

import { useTheme } from "@emotion/react";

// ** Demo
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import Section2 from "./Section2";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const TeamArchitect = ({ data,expanded }) => {
  // ** Hook
  const { can } = useRBAC();
  const theme = useTheme();

  const [state2, setState2] = useState("view");

  const viewClick2 = () => {
    setState2("edit");
  };

  const editClick2 = () => {
    setState2("view");
  };

  return (
    <>
     {/* {can('architect_teamDetails_READ') &&  */}
     <AccordionBasic
     id={"basic-schema-1"}
     ariaControls={"basic-schema-1"}
     heading={"Team"}
     body={
       <>
         {state2 === "view" && (
           <TableContainer
           sx={{ padding:'4px 6px' }}
             className="tableBody"
            // onClick={can('architect_teamDetails_UPDATE') ? viewClick2 : null}
             onClick={ viewClick2}
           >
             <Table>
             <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                     
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>Area of operation:</Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.areaofOperation === "OTHER" ? data?.otherArea : data?.areaofOperation}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>Design:</Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {" "}
                       {data?.design === "OTHER" ? data?.otherDesign : data?.design}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>Liasoning:</Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.liasoning === "OTHER" ? data?.otherLiasoning : data?.liasoning}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>
                       Years Of Experience:
                     </Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.yearsOfExperience}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>
                       Team size:
                     </Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.teamSize}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>Awards:</Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.awards}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 <TableRow>
                   <MUITableCell>
                     <Typography style={field}>Brief Profile:</Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography className="data-field" >
                       {data?.briefProfile}
                     </Typography>
                   </MUITableCell>
                 </TableRow>
                 
               </TableBody>
             </Table>
           </TableContainer>
         )}
         {state2 === "edit" && (
           <Section2 formData={data} onCancel={editClick2} />
         )}
       </>
     }
     expanded={expanded}
   />
     {/* } */}
      
    </>
  );
};
export default TeamArchitect;
