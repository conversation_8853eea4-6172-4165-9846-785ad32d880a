import React from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Button, DialogContentText } from "@mui/material";
import Box from "@mui/material/Box";

import { getAuthorizationHeaders } from "src/helpers/utils";

const ActivateDialog = ({ open, onClose, data }) => {

  async function onActivate(data) {
    try {
      await axios.patch(
        authConfig.baseURL + authConfig.contactGroupsActivate + "/" + data.id,
        {
          headers: getAuthorizationHeaders(),
        }
      );
    } catch (error) {
      console.error("Activate operation failed", error);
    }
    onClose();
    
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.text.primary,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.dark",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.dark"
            >
              Are you sure, you want to Activate row?
            </DialogContentText>
          </DialogContent>
          <DialogActions> 
            <Button
              variant="contained"
              onClick={() => onActivate(data)}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};


export default ActivateDialog;