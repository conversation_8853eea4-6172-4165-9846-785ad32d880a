// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState, useEffect } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ProjectEdit from "./ProjectEdit";
import ServicesTabs from "./ServiceTabs";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ProjectView = ({ data, expanded, serviceId, userData }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  const [projectsFinalData, setProjectsFinalData] = useState(null);
  useEffect(() => {
    const getMetadataByServiceId = (data, serviceId) => {
      const project = data?.find((item) => item.serviceNameId === serviceId);
      return project ? project : null;
    };

    const project = getMetadataByServiceId(data, serviceId);
    setProjectsFinalData(project);
  }, [serviceId, data]);

  const hasProjects = projectsFinalData && projectsFinalData.metadata && projectsFinalData.metadata.length > 0;

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Project"}
        body={
          <>
            {state3 === "view" && (
              <>
                {hasProjects ? (
                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                    onClick={viewClick3}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ paddingLeft: "20px" }}>Name</TableCell>
                          <TableCell>Image</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        {projectsFinalData.metadata.map((project, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography
                                className="data-field"
                                marginLeft={"12px"}
                              >
                                {project?.projectName}
                              </Typography>
                            </TableCell>

                            <TableCell>
                              <Typography className="data-field">
                                {project?.documentDTO?.location &&
                                  project.documentDTO.location.split("/").pop()}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography
                    variant="body1"
                    sx={{ textAlign: "left", mt: 3, cursor: "pointer" }}
                    onClick={viewClick3}
                  >
                    Click here to add Projects
                  </Typography>
                )}
              </>
            )}
            {state3 === "edit" && (
              <ProjectEdit
                data={projectsFinalData}
                onCancel={editClick3}
                serviceId={serviceId}
                userData={userData}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default ProjectView;
