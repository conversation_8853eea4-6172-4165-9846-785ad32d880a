// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { DialogActions, FormControlLabel, Switch } from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";




const LookupValuesEdit = ({ onCancel, formData,fetchUsers }) => {


  const [isActive, setIsActive] = useState(formData?.isActive); 

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  //Hooks
  const auth = useAuth();

  const [listNameId, setListNameId] = useState(formData?.listNamesId);
  const [listOfListNames, setListOfListNames] = useState([]);
  const [listNamesOptions, setListNamesOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=LIST_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfListNames(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!listOfListNames) {
      let data = [];
      listOfListNames.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListNamesOptions(data);
    }
  }, [listOfListNames]);
  
  
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  async function submit(data) {   

    const fields = {
      id:formData.id,
      name: data.name.trim(),
      listNamesId:listNameId,
      isActive:isActive, 
      
    };


    const response = await auth.patchServicesData(fields, () => {
      console.error(" Master Data Details failed");
    });

    const currentPage = 1;  
    const currentPageSize = 10;

    fetchUsers(currentPage, currentPageSize);

    onCancel();
  }

  


  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        
      <Grid item xs={12} sm={6}>
                   <SelectAutoComplete
                        id={"listNamesId"}
                        label={"Select lookup name"}
                        nameArray={listNamesOptions}
                        register={register}
                        value={listNameId}
                        defaultValue={listNameId}
                        onChange={(e) => {
                          setListNameId(e.target.value);
                        }}
                        error={Boolean(errors.listNamesId)}
                      />
                </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: 'Service name is required' }} 
              defaultValue={formData?.name}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="Section1-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={3} sm={6} sx={{  mb: 1.5 }}>
      <Controller
        name="isActive" 
        control={control}
        render={() => (
          <FormControlLabel
            control={
              <Switch
                checked={isActive} 
                onChange={handleOnChange}
                name="isActive" 
              />
            }
            label="Is Active"
          />
        )}
      />
    </Grid>


        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit" 
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LookupValuesEdit;
