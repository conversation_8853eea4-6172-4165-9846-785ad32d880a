import * as yup from "yup";

export const EmployeeValidations = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  return yup.object().shape({
    firstName: yup.string().when("$fields", {
      is: () => fieldsArray.includes("firstName"),
      then: yup
        .string()
        .required("First Name is required")
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "First Name must not exceed 30 characters")
        .min(3, "First Name must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    lastName: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("lastName"),
        then: yup
          .string()
          .required("LastName is required")
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "LastName must not exceed 30 characters")
          .min(3, "LastName must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    designation: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("designation"),
        then: yup
          .string()
          .required("Designation is required")
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "Designation name must not exceed 30 characters")
          .min(3, "Designation name must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
      reportingTo: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("reportingTo"),
        then: yup
          .string()
          .required("Reporting To is required")
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "Reporting to name must not exceed 30 characters")
          .min(3, "Reporting to name must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    workLocation: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("workLocation"),
        then: yup
          .string()
          .required("Work Location is required")
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "Work location name must not exceed 30 characters")
          .min(3, "Work location name must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    street1: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("street1"),
        then: yup
          .string()
          .nullable()
          .max(200, "Street1 must not exceed 200 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    pinCode: yup.string().when("$fields", {
      is: () => fieldsArray.includes("pinCode"),
      then: yup.string().nullable(),
      otherwise: yup.string().notRequired().nullable(),
    }),
    email: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("email"),
        then: yup
          .string()
          .nullable()
          .required("Email address is required")
          .email("Please enter a valid email address")
          .max(50, "Email must not exceed 50 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    contactNumber: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("contactNumber"),
        then: yup
          .string()
          .required("Mobile number is required")
          .nullable()
          .matches(
            /^(?:\+91\s?)?[6-9]\d{9}$/,
            "Please enter a valid contact number"
          )
          .max(13, "Contact number must not exceed 13 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    city: yup.string().when("$fields", {
      is: () => fieldsArray.includes("city"),
      then: yup
        .string()
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "City must not exceed 30 characters")
        .min(3, "City must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    state: yup.string().when("$fields", {
      is: () => fieldsArray.includes("state"),
      then: yup
        .string()
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "State must not exceed 30 characters")
        .min(3, "State must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    country: yup.string().when("$fields", {
      is: () => fieldsArray.includes("country"),
      then: yup
        .string()
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "Country must not exceed 30 characters")
        .min(3, "Country must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
  });
};

export default EmployeeValidations;

export { yup };
