// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

//import MUITableCell from "../../MUITableCell";

import MUITableCell from "src/pages/SP/MUITableCell";

import AreaOfExpertiseEdit from "./AreaOfExpertiseEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const AreaOfExperties = ({ data, expanded, userData }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Area Of Expertise"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Sectors:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Weightage:
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>HealthCare</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.healthCare}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>TownShip</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.townShip}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Commercial</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.commercial}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Hospitality</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.hospitality}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Education</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.education}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Leisure</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.leisure}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Other</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data && data?.areaOfExpertiseDTO?.other}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state3 === "edit" && (
              <AreaOfExpertiseEdit
                userData={userData}
                formData={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};

export default AreaOfExperties;
