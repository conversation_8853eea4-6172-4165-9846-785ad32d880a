import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";

const LegalServicesEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [legalConsultation, setLegalConsultation] = useState(
    formData?.legalConsultation
  );
  const [bylawsAndRulesDrafting, setBylawsAndRulesDrafting] = useState(
    formData?.bylawsAndRulesDrafting
  );
  const [propertyDocumentationReview, setPropertyDocumentationReview] =
    useState(formData?.propertyDocumentationReview);
  const [propertyDisputesResolution, setPropertyDisputesResolution] = useState(
    formData?.propertyDisputesResolution
  );
  const [
    legalGuidanceDuringRedevelopment,
    setLegalGuidanceDuringRedevelopment,
  ] = useState(formData?.legalGuidanceDuringRedevelopment);
  const [
    cooperativeHousingSocietyRegistration,
    setCooperativeHousingSocietyRegistration,
  ] = useState(formData?.cooperativeHousingSocietyRegistration);
  const [advisingOnGeneralBodyMeetings, setAdvisingOnGeneralBodyMeetings] =
    useState(formData?.advisingOnGeneralBodyMeetings);
  const [memberDisputes, setMemberDisputes] = useState(
    formData?.memberDisputes
  );
  const [tenantAndLandlordMatters, setTenantAndLandlordMatters] = useState(
    formData?.tenantAndLandlordMatters
  );
  const [complianceAndRegulatoryMatters, setComplianceAndRegulatoryMatters] =
    useState(formData?.complianceAndRegulatoryMatters);
  const [governmentLiaison, setGovernmentLiaison] = useState(
    formData?.governmentLiaison
  );
  const [legalNoticesAndPetitions, setLegalNoticesAndPetitions] = useState(
    formData?.legalNoticesAndPetitions
  );
  const [consumerComplaints, setConsumerComplaints] = useState(
    formData?.consumerComplaints
  );
  const [arbitrationAndMediation, setArbitrationAndMediation] = useState(
    formData?.arbitrationAndMediation
  );
  const [propertyTaxMatters, setPropertyTaxMatters] = useState(
    formData?.propertyTaxMatters
  );
  const [documentationAndLegalRecords, setDocumentationAndLegalRecords] =
    useState(formData?.documentationAndLegalRecords);
  const [legalDueDiligence, setLegalDueDiligence] = useState(
    formData?.legalDueDiligence
  );
  const [insuranceClaims, setInsuranceClaims] = useState(
    formData?.insuranceClaims
  );

  const [allServices, setAllServices] = useState(
    formData?.allServices
  );

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  useEffect(() => {
    if (legalConsultation && bylawsAndRulesDrafting && propertyDocumentationReview && propertyDisputesResolution &&
        legalGuidanceDuringRedevelopment && cooperativeHousingSocietyRegistration && advisingOnGeneralBodyMeetings &&
        memberDisputes && tenantAndLandlordMatters && complianceAndRegulatoryMatters && governmentLiaison &&
        legalNoticesAndPetitions && consumerComplaints && arbitrationAndMediation && propertyTaxMatters &&
        documentationAndLegalRecords && legalDueDiligence && insuranceClaims) {    
            setValue("allServices", true);
            setAllServices(true);


    }
    else{
        setValue("allServices", false);
        setAllServices(false);
    }
    
  },[legalConsultation, bylawsAndRulesDrafting, propertyDocumentationReview, propertyDisputesResolution,
    legalGuidanceDuringRedevelopment, cooperativeHousingSocietyRegistration, advisingOnGeneralBodyMeetings,
    memberDisputes, tenantAndLandlordMatters, complianceAndRegulatoryMatters, governmentLiaison,
    legalNoticesAndPetitions, consumerComplaints, arbitrationAndMediation, propertyTaxMatters,
    documentationAndLegalRecords, legalDueDiligence, insuranceClaims, allServices])
    
  return (
    <>
      <Box sx={{ pt: 1.5 }}>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="legalConsultation"
                control={control}
                defaultValue={
                  formData?.legalConsultation ? legalConsultation : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("legalConsultation", true);
                            setLegalConsultation(true);
                          } else {
                            setValue("legalConsultation", false);
                            setLegalConsultation(false);
                          }
                        }}
                      />
                    }
                    label={<span>Legal Consultation</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="bylawsAndRulesDrafting"
                control={control}
                defaultValue={
                  formData?.bylawsAndRulesDrafting
                    ? bylawsAndRulesDrafting
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("bylawsAndRulesDrafting", true);
                            setBylawsAndRulesDrafting(true);
                          } else {
                            setValue("bylawsAndRulesDrafting", false);
                            setBylawsAndRulesDrafting(false);
                          }
                        }}
                      />
                    }
                    label={<span>Bye laws and Rules Drafting</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="propertyDocumentationReview"
                control={control}
                defaultValue={
                  formData?.propertyDocumentationReview
                    ? propertyDocumentationReview
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("propertyDocumentationReview", true);
                            setPropertyDocumentationReview(true);
                          } else {
                            setValue("propertyDocumentationReview", false);
                            setPropertyDocumentationReview(false);
                          }
                        }}
                      />
                    }
                    label={<span>Property Documentation Review</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="propertyDisputesResolution"
                control={control}
                defaultValue={
                  formData?.propertyDisputesResolution
                    ? propertyDisputesResolution
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("propertyDisputesResolution", true);
                            setPropertyDisputesResolution(true);
                          } else {
                            setValue("propertyDisputesResolution", false);
                            setPropertyDisputesResolution(false);
                          }
                        }}
                      />
                    }
                    label={<span>Property Disputes Resolution</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="legalGuidanceDuringRedevelopment"
                control={control}
                defaultValue={
                  formData?.legalGuidanceDuringRedevelopment
                    ? legalGuidanceDuringRedevelopment
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("legalGuidanceDuringRedevelopment", true);
                            setLegalGuidanceDuringRedevelopment(true);
                          } else {
                            setValue("legalGuidanceDuringRedevelopment", false);
                            setLegalGuidanceDuringRedevelopment(false);
                          }
                        }}
                      />
                    }
                    label={<span>Legal Guidance During Redevelopment and Construction</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="cooperativeHousingSocietyRegistration"
                control={control}
                defaultValue={
                  formData?.cooperativeHousingSocietyRegistration
                    ? cooperativeHousingSocietyRegistration
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue(
                              "cooperativeHousingSocietyRegistration",
                              true
                            );
                            setCooperativeHousingSocietyRegistration(true);
                          } else {
                            setValue(
                              "cooperativeHousingSocietyRegistration",
                              false
                            );
                            setCooperativeHousingSocietyRegistration(false);
                          }
                        }}
                      />
                    }
                    label={
                      <span>Cooperative Housing Society Registration</span>
                    }
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="advisingOnGeneralBodyMeetings"
                control={control}
                defaultValue={
                  formData?.advisingOnGeneralBodyMeetings
                    ? advisingOnGeneralBodyMeetings
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("advisingOnGeneralBodyMeetings", true);
                            setAdvisingOnGeneralBodyMeetings(true);
                          } else {
                            setValue("advisingOnGeneralBodyMeetings", false);
                            setAdvisingOnGeneralBodyMeetings(false);
                          }
                        }}
                      />
                    }
                    label={<span>Advising on General Body Meetings</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="memberDisputes"
                control={control}
                defaultValue={formData?.memberDisputes ? memberDisputes : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("memberDisputes", true);
                            setMemberDisputes(true);
                          } else {
                            setValue("memberDisputes", false);
                            setMemberDisputes(false);
                          }
                        }}
                      />
                    }
                    label={<span>Member Disputes</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="tenantAndLandlordMatters"
                control={control}
                defaultValue={
                  formData?.tenantAndLandlordMatters
                    ? tenantAndLandlordMatters
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("tenantAndLandlordMatters", true);
                            setTenantAndLandlordMatters(true);
                          } else {
                            setValue("tenantAndLandlordMatters", false);
                            setTenantAndLandlordMatters(false);
                          }
                        }}
                      />
                    }
                    label={<span>Tenant and Landlord Matters</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="complianceAndRegulatoryMatters"
                control={control}
                defaultValue={
                  formData?.complianceAndRegulatoryMatters
                    ? complianceAndRegulatoryMatters
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("complianceAndRegulatoryMatters", true);
                            setComplianceAndRegulatoryMatters(true);
                          } else {
                            setValue("complianceAndRegulatoryMatters", false);
                            setComplianceAndRegulatoryMatters(false);
                          }
                        }}
                      />
                    }
                    label={<span>Compliance and Regulatory Matters</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="governmentLiaison"
                control={control}
                defaultValue={
                  formData?.governmentLiaison ? governmentLiaison : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("governmentLiaison", true);
                            setGovernmentLiaison(true);
                          } else {
                            setValue("governmentLiaison", false);
                            setGovernmentLiaison(false);
                          }
                        }}
                      />
                    }
                    label={<span>Government Liaison</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="legalNoticesAndPetitions"
                control={control}
                defaultValue={
                  formData?.legalNoticesAndPetitions
                    ? legalNoticesAndPetitions
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("legalNoticesAndPetitions", true);
                            setLegalNoticesAndPetitions(true);
                          } else {
                            setValue("legalNoticesAndPetitions", false);
                            setLegalNoticesAndPetitions(false);
                          }
                        }}
                      />
                    }
                    label={<span>Legal Notices and Petitions</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="consumerComplaints"
                control={control}
                defaultValue={
                  formData?.consumerComplaints ? consumerComplaints : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("consumerComplaints", true);
                            setConsumerComplaints(true);
                          } else {
                            setValue("consumerComplaints", false);
                            setConsumerComplaints(false);
                          }
                        }}
                      />
                    }
                    label={<span>Consumer Complaints</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="arbitrationAndMediation"
                control={control}
                defaultValue={
                  formData?.arbitrationAndMediation
                    ? arbitrationAndMediation
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("arbitrationAndMediation", true);
                            setArbitrationAndMediation(true);
                          } else {
                            setValue("arbitrationAndMediation", false);
                            setArbitrationAndMediation(false);
                          }
                        }}
                      />
                    }
                    label={<span>Arbitration and Mediation</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="propertyTaxMatters"
                control={control}
                defaultValue={
                  formData?.propertyTaxMatters ? propertyTaxMatters : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("propertyTaxMatters", true);
                            setPropertyTaxMatters(true);
                          } else {
                            setValue("propertyTaxMatters", false);
                            setPropertyTaxMatters(false);
                          }
                        }}
                      />
                    }
                    label={<span>Property Tax Matters</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="documentationAndLegalRecords"
                control={control}
                defaultValue={
                  formData?.documentationAndLegalRecords
                    ? documentationAndLegalRecords
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("documentationAndLegalRecords", true);
                            setDocumentationAndLegalRecords(true);
                          } else {
                            setValue("documentationAndLegalRecords", false);
                            setDocumentationAndLegalRecords(false);
                          }
                        }}
                      />
                    }
                    label={<span>Documentation and Legal Records Maintenance</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="legalDueDiligence"
                control={control}
                defaultValue={
                  formData?.legalDueDiligence ? legalDueDiligence : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("legalDueDiligence", true);
                            setLegalDueDiligence(true);
                          } else {
                            setValue("legalDueDiligence", false);
                            setLegalDueDiligence(false);
                          }
                        }}
                      />
                    }
                    label={<span>Legal Due Diligence</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="insuranceClaims"
                control={control}
                defaultValue={
                  formData?.insuranceClaims ? insuranceClaims : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("insuranceClaims", true);
                            setInsuranceClaims(true);
                          } else {
                            setValue("insuranceClaims", false);
                            setInsuranceClaims(false);
                          }
                        }}
                      />
                    }
                    label={<span>Insurance Claims</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="allServices"
                control={control}
                defaultValue={
                  formData?.allServices
                    ? formData.allServices
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("allServices", true);
                            setAllServices(true);

                            setValue("legalConsultation", true);
                            setLegalConsultation(true);

                            setValue("bylawsAndRulesDrafting", true);
                            setBylawsAndRulesDrafting(true);

                            setValue("propertyDocumentationReview", true);
                            setPropertyDocumentationReview(true);

                            setValue("propertyDisputesResolution", true);
                            setPropertyDisputesResolution(true);

                            setValue("legalGuidanceDuringRedevelopment", true);
                            setLegalGuidanceDuringRedevelopment(true);

                            setValue(
                              "cooperativeHousingSocietyRegistration",
                              true
                            );
                            setCooperativeHousingSocietyRegistration(true);

                            setValue("advisingOnGeneralBodyMeetings", true);
                            setAdvisingOnGeneralBodyMeetings(true);

                            setValue("memberDisputes", true);
                            setMemberDisputes(true);

                            setValue("tenantAndLandlordMatters", true);
                            setTenantAndLandlordMatters(true);

                            setValue("complianceAndRegulatoryMatters", true);
                            setComplianceAndRegulatoryMatters(true);

                            setValue("governmentLiaison", true);
                            setGovernmentLiaison(true);

                            setValue("legalNoticesAndPetitions", true);
                            setLegalNoticesAndPetitions(true);

                            setValue("consumerComplaints", true);
                            setConsumerComplaints(true);

                            setValue("arbitrationAndMediation", true);
                            setArbitrationAndMediation(true);

                            setValue("propertyTaxMatters", true);
                            setPropertyTaxMatters(true);

                            setValue("documentationAndLegalRecords", true);
                            setDocumentationAndLegalRecords(true);

                            setValue("legalDueDiligence", true);
                            setLegalDueDiligence(true);

                            setValue("insuranceClaims", true);
                            setInsuranceClaims(true);
                          } else {
                            setValue("allServices", false);
                            setAllServices(false);

                            setValue("legalConsultation", false);
                            setLegalConsultation(false);

                            setValue("bylawsAndRulesDrafting", false);
                            setBylawsAndRulesDrafting(false);

                            setValue("propertyDocumentationReview", false);
                            setPropertyDocumentationReview(false);

                            setValue("propertyDisputesResolution", false);
                            setPropertyDisputesResolution(false);

                            setValue("legalGuidanceDuringRedevelopment", false);
                            setLegalGuidanceDuringRedevelopment(false);

                            setValue(
                              "cooperativeHousingSocietyRegistration",
                              false
                            );
                            setCooperativeHousingSocietyRegistration(false);

                            setValue("advisingOnGeneralBodyMeetings", false);
                            setAdvisingOnGeneralBodyMeetings(false);

                            setValue("memberDisputes", false);
                            setMemberDisputes(false);

                            setValue("tenantAndLandlordMatters", false);
                            setTenantAndLandlordMatters(false);

                            setValue("complianceAndRegulatoryMatters", false);
                            setComplianceAndRegulatoryMatters(false);

                            setValue("governmentLiaison", false);
                            setGovernmentLiaison(false);

                            setValue("legalNoticesAndPetitions", false);
                            setLegalNoticesAndPetitions(false);

                            setValue("consumerComplaints", false);
                            setConsumerComplaints(false);

                            setValue("arbitrationAndMediation", false);
                            setArbitrationAndMediation(false);

                            setValue("propertyTaxMatters", false);
                            setPropertyTaxMatters(false);

                            setValue("documentationAndLegalRecords", false);
                            setDocumentationAndLegalRecords(false);

                            setValue("legalDueDiligence", false);
                            setLegalDueDiligence(false);

                            setValue("insuranceClaims", false);
                            setInsuranceClaims(false);
                          }
                        }}
                      />
                    }
                    label={<span>ALL</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default LegalServicesEdit;
