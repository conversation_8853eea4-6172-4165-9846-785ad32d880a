// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import MenuItem from '@mui/material/MenuItem'
import TextField from '@mui/material/TextField'
import InputLabel from '@mui/material/InputLabel'
import FormControl from '@mui/material/FormControl'
import OutlinedInput from '@mui/material/OutlinedInput'
import { styled, useTheme } from '@mui/material/styles'
import FormHelperText from '@mui/material/FormHelperText'
import Select from '@mui/material/Select'
import { useForm, Controller } from 'react-hook-form'

import { useEffect } from 'react'

// ** Custom Components Imports
import CustomChip from 'src/@core/components/mui/chip'
import CustomRadioIcons from 'src/@core/components/custom-radio/icons'
import { IconButton, Typography } from '@mui/material'
import AddIcon from '@mui/icons-material/Add';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SelectCategory from 'src/@core/components/custom-components/SelectCategory'

const data = [
  {
    isSelected: true,
    value: 'percentage',
    title: 'Percentage',
    content: 'Create a deal which offer uses some % off (i.e 5% OFF) on total.'
  },
  {
    value: 'flat-amount',
    title: 'Flat Amount',
    content: 'Create a deal which offer uses flat $ off (i.e $5 OFF) on the total.'
  },
  {
    value: 'prime-member',
    title: 'Prime Member',
    content: 'Create prime member only deal to encourage the prime members.'
  }
]
const regionArray = ['Asia', 'Europe', 'Africa', 'Australia', 'North America', 'South America']

const ImgWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-end',
  justifyContent: 'center',
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(4, 4, 0, 4)
  },
  [theme.breakpoints.up('sm')]: {
    height: 250,
    padding: theme.spacing(5, 5, 0, 5)
  },
  '& img': {
    height: 'auto',
    maxWidth: '100%'
  }
}))

const land = [
  {
    value: "FREEHOLD",
    key: "Free Hold",
  },
  {
    value: "COLLECTOR",
    key: "Collector",
  },
  {
    value: "MHADA",
    key: "MHADA",
  }
];


const landStatus = [
  {
    value: "LANDLORD",
    key: "Land lord",
  },
  {
    value: "PAGRI",
    key: "Pagri",
  },
  {
    value: "CESS",
    key: "Cess",
  }
];

const BasicDetails = () => {
  const initialIconSelected = data.filter(item => item.isSelected)[data.filter(item => item.isSelected).length - 1]
    .value

  // ** States
  const [region, setRegion] = useState([])
  const [selectedRadio, setSelectedRadio] = useState(initialIconSelected)
  const [landStatus1, setLandStatus1] = useState();
  const [landStatus2, setLandStatus2] = useState();

  const [userLocation, setUserLocation] = useState(null);

  // ** Hook
  const theme = useTheme()


  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          setUserLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });

          // Reverse geocoding using Nominatim API
          const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}`);
          const data = await response.json();

          // Extract the area name or address from the response and update the TextField
          const areaName = data.display_name;
          setValue('location', areaName);
        },
        (error) => {
          console.error('Error getting user location:', error);
        }
      );
    } else {
      console.error('Geolocation is not supported by this browser.');
    }
  };

  useEffect(() => {
    getUserLocation();
  }, []); 


  const { control, setValue, handleSubmit, formState: { errors },clearErrors,register } = useForm({
    defaultValues: {
      nearestLandmarks: [''], // Initial value for the nearest landmarks
    },
  });

  const [additionalFields, setAdditionalFields] = useState(1);
  const [additional, setAdditional] = useState(1);

  const handleAddField = () => {
    setAdditionalFields(prev => prev + 1);
  };

  const handleAdd = () => {
    setAdditional(prev => prev + 1);
  };

  return (
    <Grid container spacing={5}>

      <Grid item xs={12} sm={6} style={{ marginTop: '20px' }}>
        <FormControl fullWidth>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Controller
              name='location'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='text'
                  label='Location'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.location)}
                  helperText={errors.location?.message}
                />
              )}
            />
            {/* Add a button/icon to trigger fetching user location */}
            <IconButton>
              <LocationOnIcon />
            </IconButton>
          </div>
        </FormControl>


      </Grid>
      <Grid item xs={12} sm={12} style={{ marginTop: '30px' }}>
        <Typography fontSize={20}>Society Address</Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <Controller
            name='houseNo'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <TextField
                variant='standard'
                {...field}
                type='text'
                label='Flat No/House-No/Apartment'
                InputLabelProps={{ shrink: true }}
                error={Boolean(errors.houseNo)}
                helperText={errors.houseNo?.message}
              />
            )}
          />
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <Controller
            name='area'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <TextField
                variant='standard'
                {...field}
                type='text'
                label='Area/Street/Sector'
                InputLabelProps={{ shrink: true }}
                error={Boolean(errors.area)}
                helperText={errors.area?.message}
              />
            )}
          />
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6}>
        {[...Array(additionalFields)].map((_, index) => (
          <Grid item xs={12} sm={12} key={index}>
            <FormControl fullWidth>
              <Controller
                name={`nearestLandmarks[${index}]`}
                control={control}
                defaultValue=''
                render={({ field }) => (
                  <TextField
                    variant='standard'
                    {...field}
                    type='text'
                    label={`Nearest Landmarks`}
                    InputLabelProps={{ shrink: true, style: { marginTop: '8px' } }}
                    error={Boolean(errors.nearestLandmarks?.[index])}
                    helperText={errors.nearestLandmarks?.[index]?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>
        ))}
      </Grid>
      <Grid item xs={12} sm={2}>
        <IconButton onClick={handleAddField}>
          <AddIcon />
        </IconButton>

      </Grid>
      <Grid item xs={12} sm={6}>
      {[...Array(additional)].map((_, index) => (
          <Grid item xs={12} sm={12} key={index}>
            <FormControl fullWidth>
              <Controller
                name={`newProjectsInVicinity[${index}]`}
                control={control}
                defaultValue=''
                render={({ field }) => (
                  <TextField
                    variant='standard'
                    {...field}
                    type='text'
                    label={`New Projects In Vicinity`}
                    InputLabelProps={{ shrink: true, style: { marginTop: '8px' } }}
                    error={Boolean(errors.newProjectsInVicinity?.[index])}
                    helperText={errors.newProjectsInVicinity?.[index]?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>
        ))}
      </Grid>
      <Grid item xs={12} sm={2}>
        <IconButton onClick={handleAdd}>
          <AddIcon />
        </IconButton>

      </Grid>

          <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={1.5} sm={1.5} sx={{ marginTop: 5 }}>
            Land Status        
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 1 }}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"landStatus1"}
              name="landStatus1"
              nameArray={land}
              defaultValue={""}
              value={landStatus1}
              onChange={(e) => {
                setLandStatus1(e.target.value);
              }}
              error={Boolean(errors.landStatus1)}
              aria-describedby="validation-landStatus1"
            />
            {errors.landStatus1 && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-landStatus1"
              >
                Please select Land Status
              </FormHelperText>
            )}
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"landStatus2"}
              name="landStatus2"
              nameArray={landStatus}
              defaultValue={""}
              value={landStatus2}
              onChange={(e) => {
                setLandStatus2(e.target.value);
              }}
              error={Boolean(errors.landStatus2)}
              aria-describedby="validation-landStatus2"
            />
            {errors.landStatus2 && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-landStatus2"
              >
                Please select Land Status
              </FormHelperText>
            )}
        </Grid>
      </Grid>

    </Grid >
  )
}

export default BasicDetails
