import React from 'react';
import { TextField } from '@mui/material';

const MobileNumberValidation = ({ onChange, ...props }) => {
  const handleChange = (event) => {
    const newValue = event.target.value.replace(/[^\d\+91]/g, "");
    if (newValue.startsWith("+91")) {
      onChange(newValue.slice(0, 13));
    } else {
      onChange(newValue.slice(0, 10));
    }
  };


  return (
    <TextField
      type="text"
      {...props}
      onChange={handleChange}
    />
  );
};

export default MobileNumberValidation;
