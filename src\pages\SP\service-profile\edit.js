import { Fragment, useContext, useState } from "react";
import Card from "@mui/material/Card";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableHead from "@mui/material/TableHead";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import TableContainer from "@mui/material/TableContainer";
import {
  Button,
  CardActions,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Grid,
  Divider,
  FormControlLabel,
  Switch,
  TextField,
  Box,
} from "@mui/material";

import { Radio, RadioGroup } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";

const EditMode = ({
  data,
  formData,
  selectedOptions: initialSelectedOptions, // Pass initial selectedOptions down
  handleChange,
  handleChangeSelect,
  handleSubmit,
  isEditMode,
  setIsEditMode,
  tabContent,
}) => {
  const [selectedOptions, setSelectedOptions] = useState(
    initialSelectedOptions
  );

  const {
    listValues
  } = useContext(AuthContext);

  const { control } = useForm();

  const title = tabContent.name + " Services";
  return (
    <Card>
       <Grid
        sx={{
          position: 'sticky',
          top: 0, // Stick to the top
          zIndex: 2, // Higher than the content to stay on top
          backgroundColor: '#ffffff', // Match the background with your theme
        }}
      >
        <CardHeader
           title={<span style={{ fontWeight: "bold" }}>{title}</span>}
          sx={{
            color: "black",
            lineHeight: 1.8,
            mt:2,
            fontSize: "1.7rem",
            fontWeight:"bold !important",
            paddingTop: "5px",
            paddingBottom: "1px",
            borderRadius: "5px",
            display: "flex", // Use flex to center content
            justifyContent: "center", // Center horizontally
            textAlign: "center", // Use block to allow textAlign to work
          }}
        />
         <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 16px' }}>
          <CardHeader title="Select the options for the service you provide." sx={{ flex: 1 }} />
          <CardActions 
            sx={{
              flexDirection: { xs: 'column', md: 'row' }, // Column layout in mobile, Row layout in desktop
              justifyContent: { xs: 'center', md: 'flex-end' }, // Centered in mobile, Right-aligned in desktop
              padding: '8px 16px', // Adjust padding
            }}
          >
            <Button
              variant="contained"
              onClick={() => setIsEditMode(!isEditMode)}
              sx={{ marginTop: '15px' }} 
            >
              {isEditMode ? "Cancel" : "Edit"}
            </Button>
            {isEditMode && <Button onClick={handleSubmit}  sx={{ marginTop: '15px' }} >Save</Button>}
          </CardActions>
        </Box>
        <Divider />
      </Grid>

      
        <CardContent
          sx={{
            maxHeight: 'calc(100vh - 200px)', // Adjust height accordingly
            overflowY: 'auto', // Enable vertical scrolling
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Category</TableCell>
                  <TableCell>Select Options</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
  {Object.values(
    // Group categories by sectionId
    data.reduce((sections, category) => {
      if (!sections[category.sectionId]) {
        sections[category.sectionId] = [];
      }
      sections[category.sectionId].push(category);
      return sections;
    }, {})
  ).map((sectionCategories) => (
    <Fragment key={sectionCategories[0].sectionId}>
      <TableRow>
        <TableCell colSpan={2} style={{ fontSize: "18px", fontWeight: "bold" }}>
          {/* section header */}
          {sectionCategories[0].sectionId ? listValues.find(item => item.id === sectionCategories[0].sectionId)?.name : null}
        </TableCell>
      </TableRow>
      {/* Render categories within the section . the sectionCategories always contains data from the database. If there is no data, it would show an error here itself due to the map((sectionCategories)) operation. That's why we didn't check for null.*/}
      {sectionCategories.map((category) => (
        <TableRow key={category.id}>
          <TableCell>{category.name}</TableCell>
          
            {/* Render form elements based on component type */}
            <TableCell>
                        {(() => {
                          const formDataItem = formData.find(
                            (item) => item.id === category.id
                          );
                          switch (category.component) {
                            case "Radio buttons":
                              return (
                                <FormControl component="fieldset">
                                  <Controller
                                    name={`selectedOptions.${category.name}`}
                                    control={control}
                                    defaultValue={
                                      formDataItem
                                        ? formDataItem.values[0]?.id || ""
                                        : ""
                                    } // Default value for the radio group (none selected)
                                    render={({ field }) => (
                                      <RadioGroup
                                        {...field}
                                        aria-label="option"
                                        size="small"
                                        value={field.value}
                                        onChange={(event) => {
                                          handleChange(category.id, event);
                                          field.onChange(event); // This updates the value in react-hook-form
                                        }}
                                        row
                                      >
                                        {category.values.map((value) => (
                                          <FormControlLabel
                                            key={value.id} // Using value.id as the key
                                            value={value.id} // Using value.id as the value
                                            control={<Radio />} // Radio button control
                                            label={value.name} // Displaying the name property as the label
                                          />
                                        ))}
                                      </RadioGroup>
                                    )}
                                  />
                                </FormControl>
                              );
                            case "Multi Select Dropdown":
                              return (
                                <FormControl style={{ width: "500px" }}>
                                  <InputLabel
                                    id={category.name}
                                    style={{ zIndex: 0 }}
                                  >
                                    Select From {category.name}
                                  </InputLabel>
                                  <Controller
                                    name={`selectedOptions.${category.name}`}
                                    control={control}
                                    defaultValue={
                                      formDataItem
                                        ? formDataItem.values.map(
                                            (val) => val.id
                                          )
                                        : []
                                    }
                                    render={({ field }) => (
                                      <Select
                                        multiple
                                        labelId={category.name}
                                        size="small"
                                        label={`Select from ${category.name}`}
                                        value={field.value || []}
                                        onChange={(event) => {
                                          handleChange(category.id, event);
                                          field.onChange(event.target.value); // This updates the value in react-hook-form
                                        }}
                                        renderValue={(selected) => (
                                          <span>
                                            {selected
                                              .map(
                                                (selectedValue) =>
                                                  category.values.find(
                                                    (value) =>
                                                      value.id === selectedValue
                                                  )?.name
                                              )
                                              .join(", ")}
                                          </span>
                                        )}
                                      >
                                        {category.values.map((value) => (
                                          <MenuItem
                                            key={value.id}
                                            value={value.id}
                                          >
                                            {value.name}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    )}
                                  />
                                </FormControl>
                              );

                            case "Single Select Dropdown":
                              return (
                                <FormControl style={{ width: "500px" }}>
                                  <InputLabel
                                    id={category.name}
                                    style={{ zIndex: 0 }}
                                  >
                                    Select From {category.name}
                                  </InputLabel>
                                  <Controller
                                    name={`selectedOptions.${category.name}`}
                                    control={control}
                                    defaultValue={
                                      formDataItem
                                        ? formDataItem.values[0]?.id || ""
                                        : ""
                                    }
                                    render={({ field }) => (
                                      <Select
                                        labelId={category.name}
                                        label={`Select from ${category.name}`}
                                        size="small"
                                        value={field.value || ""}
                                        onChange={(event) => {
                                          handleChangeSelect(category.id, event);
                                          field.onChange(event); // This updates the value in react-hook-form
                                        }}
                                      >
                                        {category.values.map((value) => (
                                          <MenuItem
                                            key={value.id}
                                            value={value.id}
                                          >
                                            {value.name}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    )}
                                  />
                                </FormControl>
                              );
                            case "Switch":
                              return (
                                <FormControl
                                  key={category.name}
                                  component="fieldset"
                                >
                                  <Controller
                                    name={`selectedOptions.${category.name}`}
                                    control={control}
                                    defaultValue={
                                      formDataItem
                                        ? formDataItem.values[0]
                                        : false
                                    } // Default value for the switch
                                    render={({ field }) => (
                                      <FormControlLabel
                                        control={
                                          <Switch
                                            {...field}
                                            checked={field.value}
                                            size="small"
                                            onChange={(event) => {
                                              handleChange(category.id, event);
                                              field.onChange(event); // This updates the value in react-hook-form
                                            }}
                                            name={category.name}
                                            inputProps={{
                                              "aria-label": category.name,
                                            }}
                                          />
                                        }
                                        label="Yes"
                                      />
                                    )}
                                  />
                                </FormControl>
                              );
                            // Add cases for other conditions as needed
                            default:
                              return (
                                <FormControl key={category.name} component="fieldset">
                                  <Controller
                                    name={`selectedOptions.${category.name}`}
                                    control={control}
                                    defaultValue={formDataItem ? formDataItem.otherValue : ""}
                                    render={({ field }) => (
                                      <TextField
                                        {...field}
                                        label={category.name}
                                        size="small"
                                        variant="outlined"
                                        onChange={(event) => {
                                          handleChange(category.id, event);
                                          field.onChange(event);
                                        }}
                                        fullWidth
                                        inputProps={{
                                          "aria-label": category.name,
                                        }}
                                      />
                                    )}
                                  />
                                </FormControl>
                              ); // for handling default behavior
                          }
                        })()}
                      </TableCell>
        </TableRow>
      ))}
    </Fragment>
  ))}
</TableBody>

            </Table>
          </TableContainer>
        </CardContent>
     
    </Card>
  );
};

export default EditMode;
