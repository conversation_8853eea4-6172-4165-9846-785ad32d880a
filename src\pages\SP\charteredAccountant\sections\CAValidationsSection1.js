import * as yup from "yup";


const CAValidationsSection1 = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  return yup.object().shape({
    name: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("name"),
      then: yup
        .string()
    .nullable()
    .required("Name is required")
    .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
    .max(30, "Name must not exceed 30 characters")
    .min(3, "Name must have at least 3 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  companyName: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("companyName"),
      then: yup
        .string()
    .nullable()
    .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
    .max(30, "Company name must not exceed 30 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),
    



 

  mobileNumber: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("mobileNumber"),
      then: yup
        .string()
    .nullable()
    .required("Mobile number is required")
   
    .max(13, "Contact number must not exceed 13 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),
    


    email: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("email"),
      then: yup
        .string()
    .nullable()
    .required("Email address is required")
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      "Please enter a valid email address")
   
    .max(50, "Email must not exceed 50 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  address: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("address"),
      then: yup
        .string()
    .nullable()
    .required("Address is required")
    .max(500, "Address must not exceed 500 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),
    

    websiteUrl: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("websiteUrl"),
      then: yup
        .string()
    .nullable()
    .url('Please enter a valid Website URL: https://www.example.com')
    .max(50, "Website URL must not exceed 50 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),


  teamSize: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("address"),
      then: yup
        .string()
    .nullable()
    .required("Team size is required")
    .matches(
      /^(?:[1-9]|[1-9][0-9]|100)$/,
      "Please enter a valid team size (1 to 100)",
      ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    
 

    awards: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("address"),
      then: yup
        .string()
    .nullable()
    .max(1000, "Awards must have a maximum length of 1000 characters."),
    otherwise: yup.string().notRequired().nullable(),
  }),
   

    briefProfile: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("address"),
      then: yup
        .string()
    .nullable()
    .max(1000, "brief profile must have a maximum length of 1000 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

    yearsOfExperience: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("address"),
      then: yup
        .string()
    .required("Please select years of Experience"),
    otherwise: yup.string().notRequired().nullable(),
  }),

   
  });
};

export default CAValidationsSection1;

