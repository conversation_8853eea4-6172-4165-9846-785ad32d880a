import dynamic from 'next/dynamic';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const PieChart = () => {
  const state = {
    series: [65, 30, 5], // Example data values for Service Providers, Societies, and Employees
    options: {
      chart: {
        width: '100%',  
        type: 'pie',
      },
      labels: ['Service Providers', 'Societies', 'Employees'], // Updated labels
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: '110%', 
          },
          legend: {
            position: 'bottom',
            fontSize: '10px', 
        }
      }}],
      legend: {
        position: 'right',
        offsetY: 0,
        itemMargin: {
          horizontal: 5,
          vertical: 5
        }
      },
      title: {
        text: 'Distribution of Actors in the Application',
        align: 'center',
      },
      colors: ['#FF4560', '#00E396', '#008FFB'],
      plotOptions: {
        pie: {
          expandOnClick: true
        }
      }
    },
  };

  return (
    <div style={{ overflow: 'hidden' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="pie" width="100%" />
      </div>
    </div>
  );
};

export default PieChart;
