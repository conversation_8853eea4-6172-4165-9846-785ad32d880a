// ** React Imports
import { forwardRef } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import { useAuth } from 'src/hooks/useAuth'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'

// ** Icon Imports

import SelectBasic from 'src/@core/components/custom-components/SelectBasic'

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false,
  Textarea: ''
}

const names = [
  {
    value:'LESS_THAN_5_YEARS', 
    key:'Less than 5 years'
  },
  {
    value:'_5_TO_10_YEARS',
    key:'5-10 years'
  },
  {
    value:'MORE_THAN_10_YEARS',
    key:'More than 10 years'
  }
];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const areaofOperation = ['Island', 'Western Suburb', 'Central Suburb', 'Thane', 'All', 'Other']

const areasOfExpertise = [
  'Residential',
  'Commercial',
  'Retail',
  'Industrial',
  'Religious places',
  'Financial COPMOF, Cash flows',
  'Technical Feasibility  '
]

const Section2 = ({ onCancel,formData }) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();


  async function submit(data) {

    const response = await auth.updateEntity(data,() => {
      console.error("Structural Details failed");
    });
    onCancel();
  }
  

  return (
    
      <Grid container spacing={5}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='teamSize'
              control={control}
              rules={{ required: true, pattern: /^(?:[1-9]|[1-9][0-9]|100)$/ }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type='number'
                  value={value}
                  label='Team Size'
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder='Enter team size(1-100)'
                  aria-describedby='validation-teamSize'
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.teamSize?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                This field is required
              </FormHelperText>
            )}
            {errors.teamSize?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                Please enter a valid Team Size (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
            <SelectBasic register={register} id={'yearsOfExperience'} label={'Year of Experience'} name="yearsOfExperience" nameArray={names} />
          </Grid>
       

        <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='awards'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.awards}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Awards'
                  error={Boolean(errors.awards)}
                  aria-describedby='broker-validation-basic-awards'
                />
              )}
            />
            {errors.awards && (
              <FormHelperText sx={{ color: 'error.main' }} id='broker-validation-basic-awards'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='briefProfile'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.briefProfile}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Brief Profile in your words'
                  error={Boolean(errors.briefProfile)}
                  aria-describedby='advocates-validation-basic-briefProfile'
                />
              )}
            />
            {errors.briefProfile && (
              <FormHelperText sx={{ color: 'error.main' }} id='advocates-validation-basic-briefProfile'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='houzerTeamReference'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.houzerTeamReference}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Houzer Team Reference'
                  error={Boolean(errors.houzerTeamReference)}
                  aria-describedby='advocates-validation-basic-houzerTeamRefernce'
                />
              )}
            />
            {errors.awards && (
              <FormHelperText sx={{ color: 'error.main' }} id='advocates-validation-basic-houzerTeamRefernce'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button variant='outlined' color='primary' size='medium' sx={{ mr:3 }} onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
  
  )
}

export default Section2
