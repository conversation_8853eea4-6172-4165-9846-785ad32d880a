import { useState, useEffect, useContext, useRef } from "react";
import {
  Box,
  Grid,
  Button,
  Typography,
  Divider,
  IconButton,
  Card,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  FormHelperText,
  DialogContentText,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  Tooltip,
  ListItemText,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";

import Icon from "src/@core/components/icon";

import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import "react-datepicker/dist/react-datepicker.css";
import authConfig from "src/configs/auth";
import axios from "axios";
import { DataGrid } from "@mui/x-data-grid";
import toast, { Toaster } from "react-hot-toast";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ContactGroupDetails = ({
  open,
  onClose,
  categoriesData,
  employeesData,
  fetchContactGroups,
}) => {
  const auth = useAuth();
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();

    const currentToast = useRef(null); // To keep track of the current toast


  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [selectedRows, setSelectedRows] = useState([]);
  const [allServicesList, setAllServicesList] = useState([]);
  const [locationsList, setLocationsList] = useState([]);
  const [leadStatusList, setLeadStatusList] = useState([]);
  const [leadPriorityList, setLeadPriorityList] = useState([]);

  const [selectedUsers, setSelectedUsers] = useState([]);

  const [selectedCategory, setSelectedCategory] = useState("");
  const [designationsData, setDesignationsData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);

  const [isStrategicPartner, setIsStrategicPartner] = useState(false);
  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicroSiteEmpanelled, setIsMicroSiteEmpanelled] = useState(false);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [societyKeyword, setSocietyKeyword] = useState("");
  const [societySearch, setSocietySearch] = useState("");
  const [selectedServices, setSelectedServices] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [spSelectedLeadStatus, setSpSelectedLeadStatus] = useState([]);
  const [spSelectedLeadPriority, setSpSelectedLeadPriority] = useState([]);
  const [chsSelectedLeadStatus, setChsSelectedLeadStatus] = useState([]);
  const [chsSelectedLeadPriority, setChsSelectedLeadPriority] = useState([]);
  const [selectedAssignments, setSelectedAssignments] = useState([]);
  const [selectedDesignations, setSelectedDesignations] = useState([]);
  const [categoryId, setCategoryId] = useState("");
  const [chsLocations, setChsLocations] = useState([]);
  const [chsAssignments, setChsAssignments] = useState([]);
  const [employeeLocations, setEmployeeLocations] = useState([]);
  const [employeeAssignments, setEmployeeAssignments] = useState([]);
  const [finalList, setFinalList] = useState(false);
  


  const [usersData, setUserData] = useState([]);

  const onDelete = (id) => {
    setSelectedUsers((prevUsers) => prevUsers.filter((user) => user.id !== id));
   
  };
 

  const columns = [
    {
      field: "category",
      minWidth: 100,
      headerName: "Contact Type",
      flex: 0.2,
      renderCell: (params) => {
        const category = categoriesData?.find(
          (item) => item?.id === params?.row?.contactType
        );
        return <span>{category ? category?.name : ""}</span>;
      },
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
      valueGetter: (params) => {
        const { firstName, lastName } = params?.row;
        return lastName ? `${firstName} ${lastName}` : firstName;
      },
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.4,
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
    },
  ];

  const cols = [
    {
      field: "category",
      minWidth: 100,
      headerName: "Contact Type",
      flex: 0.2,
      renderCell: (params) => {
        const category = categoriesData?.find(
          (item) => item?.id === params?.row?.contactType
        );
        return <span>{category ? category?.name : ""}</span>;
      },
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
      valueGetter: (params) => {
        const { firstName, lastName } = params?.row;
        return lastName ? `${firstName} ${lastName}` : firstName;
      },
    },
    {
      field: "email",
      minWidth: 110,
      headerName: "Email",
      flex: 0.3,
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.09,
      minWidth: 100,
      renderCell: (params) => {
        const onClickDelete = () => {
          const id = params.row.id;
          onDelete(id);
        };
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Delete">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 34, cursor: "pointer" }}
                onClick={onClickDelete}
              >
                <Icon icon="iconamoon:trash" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleStrategicPartner = (event) => {
    setIsStrategicPartner(event.target.checked);
  };

  const handleListingEmpanelled = (event) => {
    setIsListingEmpanelled(event.target.checked);
  };

  const handleMicrositeEmpanelled = (event) => {
    setIsMicroSiteEmpanelled(event.target.checked);
  };

  const handleCancel = () => {
    onClose(); 
    setFinalList(false);
    setSelectedCategory("");
    setCategoryId("");
    setSearchKeyword("");
    setSocietyKeyword("");
    setKeyword("");
    setIsListingEmpanelled(false);
    setIsMicroSiteEmpanelled(false);
    setIsStrategicPartner(false);
    setSelectedUsers([]);
    reset({
      contactGroupName: "",
      categoryId: "",
      services: "",
      locations: "",
      assignedTo: "",
      leadStatus: "",
      leadPriority: "",
      empLocations: "",
      empAssignedTo: "",
      designation: "",
      chsLocations: "",
      chsAssignedTo: "",
      chsLeadStatus: "",
      chsLeadPriority: "",
    });
  
  };
  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleLocationsSuccess = (data) => {
    setLocationsList(data?.listValues);
  };

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusList(data?.listValues);
  };

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityList(data?.listValues);
  };

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };

  const handleWorkLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        handleLocationsSuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        handleLeadStatusSuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handleLeadPrioritySuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        handleWorkLocationSuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        handleDesignationSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const fetchUsers = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    categoryId,
    selectedServices,
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    selectedLocations,
    selectedAssignments,
    spSelectedLeadStatus,
    spSelectedLeadPriority,
    selectedDesignations,
    employeeLocations,
    employeeAssignments,
    chsAssignments,
    chsLocations,
    chsSelectedLeadStatus,
    chsSelectedLeadPriority,
    societySearch
  ) => {
    const url = getUrl(authConfig.allUsersForContacts);

    const headers = getAuthorizationHeaders();

    let data;
    if (categoryId == "b4ae14a7-9c73-41a4-bf7c-22d628c2e3e8") {
      data = {
        page: currentPage,
        pageSize: currentPageSize,
        searchKeyword: searchKeyword,
        categoryId: categoryId,
        serviceTypeIds: selectedServices,
        isStrategicPartner: isStrategicPartner,
        isListingEmpanelled: isListingEmpanelled,
        isMicroSiteEmpanelled: isMicroSiteEmpanelled,
        location: selectedLocations,
        assignedTo: selectedAssignments,
        leadStatus: spSelectedLeadStatus,
        leadPriority: spSelectedLeadPriority,
      };
    } else if (categoryId == "1b3b44e7-7e0b-4c97-8c3a-90e773d2925d") {
      data = {
        page: currentPage,
        pageSize: currentPageSize,
        searchKeyword: searchKeyword,
        categoryId: categoryId,
        location: employeeLocations,
        assignedTo: employeeAssignments,
        designation: selectedDesignations,
      };
    } else if (categoryId == "9fb6e12c-8c82-4d03-a675-5d6a4e78a9c7") {
      data = {
        page: currentPage,
        pageSize: currentPageSize,
        searchKeyword: societySearch,
        categoryId: categoryId,
        location: chsLocations,
        assignedTo: chsAssignments,
        leadStatus: chsSelectedLeadStatus,
        leadPriority: chsSelectedLeadPriority,
      };
    }

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserData(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
        handleFailure();
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchUsers(
      page,
      pageSize,
      searchKeyword,
      categoryId,
      selectedServices,
      isStrategicPartner,
      isListingEmpanelled,
      isMicroSiteEmpanelled,
      selectedLocations,
      selectedAssignments,
      spSelectedLeadStatus,
      spSelectedLeadPriority,
      selectedDesignations,
      employeeLocations,
      employeeAssignments,
      chsAssignments,
      chsLocations,
      chsSelectedLeadStatus,
      chsSelectedLeadPriority,
      societySearch
    );
  }, [
    page,
    pageSize,
    searchKeyword,
    categoryId,
    selectedServices,
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    selectedLocations,
    selectedAssignments,
    selectedDesignations,
    employeeLocations,
    employeeAssignments,
    chsAssignments,
    chsLocations,
    chsSelectedLeadStatus,
    chsSelectedLeadPriority,
    societySearch,
    spSelectedLeadStatus,
    spSelectedLeadPriority,
  ]);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Contact Group Created Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Create Group. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const [currentToastId, setCurrentToastId] = useState(null);

  const onFormInvalid = (toastError) => {
    if (currentToastId !== null) {
      toast.dismiss(currentToastId);
    }

    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        const toastId = toast.error(toastError[error].message, {
          duration: 4000, // Set the duration you prefer
          onClose: () => {
            setCurrentToastId(null); // Reset currentToastId when the toast closes
          },
        });
        setCurrentToastId(toastId);
      }
    });
  };


  const isApiCalling = useRef(false);

  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    if (selectedUsers.length < 1) {
      toast.error("At least one contact should be in final list to create a group", {
        style: { zIndex: 9999 }, // Adjust the zIndex to ensure it appears above the dialog
      });
      return;
    }

    isApiCalling.current = true;
    const fields = {
      name: data?.contactGroupName,
      contactList: selectedUsers,
      searchFilter: {
        sp: {
          service: selectedServices,
          listing: isListingEmpanelled,
          microsite: isMicroSiteEmpanelled,
          houzerStrategic: isStrategicPartner,
          search: searchKeyword,
          location: selectedLocations,
          assignedTo: selectedAssignments,
          leadStatus: spSelectedLeadStatus,
          leadPriority: spSelectedLeadPriority,
        },
        chs: {
          search: societyKeyword,
          location: chsLocations,
          assignedTo: chsAssignments,
          leadStatus: chsSelectedLeadStatus,
          leadPriority: chsSelectedLeadPriority,
        },
        employee: {
          location: employeeLocations,
          assignedTo: employeeAssignments,
          designation: selectedDesignations,
        },
      },
    };

    try {
      const response = await auth.postContactGroup(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
    }
    fetchContactGroups();
    handleCancel();
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCategoryChange = (event) => {
    const selectedId = event.target.value;
    setCategoryId(event.target.value);
    const selectedCat = categoriesData.find((cat) => cat.id === selectedId);
    if (selectedCat) {
      setSelectedCategory(selectedCat.name);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleServiceChange = (event) => {
    const value = event.target.value;
    setSelectedServices(value);
  };

  const handleLocationChange = (event) => {
    const value = event.target.value;
    setSelectedLocations(value);
  };

  const handleSPLeadStatusChange = (event) => {
    const value = event.target.value;
    setSpSelectedLeadStatus(value);
  };

  const handleSPLeadPriorityChange = (event) => {
    const value = event.target.value;
    setSpSelectedLeadPriority(value);
  };

  const handleCHSLeadStatusChange = (event) => {
    const value = event.target.value;
    setChsSelectedLeadStatus(value);
  };

  const handleCHSLeadPriorityChange = (event) => {
    const value = event.target.value;
    setChsSelectedLeadPriority(value);
  };

  const handleEmployeeLocationChange = (event) => {
    const value = event.target.value;
    setEmployeeLocations(value);
  };

  const handleEmployeeAssignmentsChange = (event) => {
    setEmployeeAssignments(event.target.value);
  };

  const handleAssignedToChange = (event) => {
    const value = event.target.value;
    setSelectedAssignments(value);
  };

  const handleDesignationChange = (event) => {
    const value = event.target.value;
    setSelectedDesignations(value);
  };

  const handleCHSLocationChange = (event) => {
    const value = event.target.value;
    setChsLocations(value);
  };

  const handleCHSAssignmentsChange = (event) => {
    const value = event.target.value;
    setChsAssignments(value);
  };
  const handleServiceChangeForSelect = (event, field, allServicesList) => {
    const { target: { value } } = event;
    const isSelectAll = value.includes('selectAll');
    const isDeselectAll = value.includes('deselectAll');
  
    if (isSelectAll) {
      field.onChange(allServicesList.map(service => service.id));
    } else if (isDeselectAll) {
      field.onChange([]);
    } else {
      field.onChange(value);
    }
  };
  

  useEffect(()=>{
    if(selectedRows.length>0){
      setFinalList(true)
    }else{
      setFinalList(false)
    }
  },[selectedRows])

  const handleFinalListClick = () => {

    const selectedData = usersData.filter((row) =>
      selectedRows.includes(row.id)
    );

    setSelectedUsers((prevSelectedUsers) => {
      const newSelectedData = selectedData.filter(
        (row) => !prevSelectedUsers.some((selected) => selected.id === row.id)
      );
      return [...prevSelectedUsers, ...newSelectedData];
    });
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Create a Contact Group
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "text.primary",
                backgroundColor: "action.selected",
                "&:hover": {
                  backgroundColor: (theme) =>
                    `rgba(${theme.palette.customColors.main}, 0.16)`,
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Card>
            <Grid
              sx={{
                backgroundColor: "#e6ecff",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contact Group name
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="contactGroupName"
                    control={control}
                    rules={{ required: "Contact Group Name is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Contact Group Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your Contact Group Name "
                        error={Boolean(errors.contactGroupName)}
                        helperText={errors.contactGroupName?.message}
                        aria-describedby="validation-contactGroupName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#e6ecff",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contacts filter criteria
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel id="categoryId"> Category Type</InputLabel>
                  <Controller
                    name="categoryId"
                    control={control}
                    rules={{ required: false }} // Validation rules if any
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="categoryId-label"
                        label="Category Type"
                        id="categoryId"
                        size="small"
                        onChange={(e) => {
                          field.onChange(e);
                          handleCategoryChange(e);
                        }}
                      >
                        {categoriesData
                          ?.filter(
                            (data) =>
                              data.name !== "SuperAdmin" && data.isActive
                          )
                          .map((data) => ({
                            id: data.id,
                            label: data.name,
                          }))
                          .map((cat) => (
                            <MenuItem key={cat.id} value={cat.id}>
                              {cat.label}
                            </MenuItem>
                          ))}
                      </Select>
                    )}
                  />
                  {errors.categoryId && (
                    <FormHelperText
                      id="validation-categoryId"
                      sx={{ color: "error.main" }}
                    >
                      {errors.categoryId.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              {selectedCategory == "Service Provider" && (
                <>
                <Grid item xs={12} sm={3}>
  <FormControl fullWidth error={Boolean(errors.services)}>
    <InputLabel style={{ zIndex: 0 }}>
      Select Services
    </InputLabel>
    <Controller
      name="services"
      control={control}
      rules={{ required: false }}
      render={({ field }) => (
        <Select
          multiple
          size="small"
          labelId="services"
          label="Select Services"
          {...field}
          value={field.value || []}
          onChange={(event) => handleServiceChangeForSelect(event, field, allServicesList)}
          renderValue={(selected) => (
            <span>
              {selected
                .map(
                  (selectedValue) =>
                    allServicesList.find(
                      (service) => service.id === selectedValue
                    )?.listValue
                )
                .join(", ")}
            </span>
          )}
        >
          {/* Add "Select All" and "Deselect All" menu items */}
          <MenuItem value="selectAll">
            <Checkbox
              checked={(field.value || []).length === allServicesList.length}
            />
            <ListItemText primary="Select All" />
          </MenuItem>
          <MenuItem value="deselectAll">
            <Checkbox checked={(field.value || []).length === 0} />
            <ListItemText primary="Deselect All" />
          </MenuItem>
          {/* Map through allServicesList to create menu items for each service */}
          {allServicesList.map((service) => (
            <MenuItem key={service.id} value={service.id}>
              <Checkbox checked={(field.value || []).indexOf(service.id) > -1} />
              <ListItemText primary={service.listValue} />
            </MenuItem>
          ))}
        </Select>
      )}
    />
  </FormControl>
</Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            size="small"
                            id="mainSearch"
                            placeholder="Search by company name"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                fontSize: "0.9rem",
                                borderRadius: "5px",
                                backgroundColor: "white",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                    }}
                                  />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={3} md={2}></Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.services)}>
                      <InputLabel style={{ zIndex: 0 }}>
                        Select Locations
                      </InputLabel>
                      <Controller
                        name="locations"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="locations"
                            label="Select Locations"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleLocationChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      locationsList.find(
                                        (location) =>
                                          location.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {locationsList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isListingEmpanelled}
                          onChange={handleListingEmpanelled}
                          name="listingCheckbox"
                          color="primary"
                        />
                      }
                      label="Is Listing Empanelled"
                    />
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isMicroSiteEmpanelled}
                          onChange={handleMicrositeEmpanelled}
                          name="micrositeCheckbox"
                          color="primary"
                        />
                      }
                      label="Is Microsite Empanelled"
                    />
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isStrategicPartner}
                          onChange={handleStrategicPartner}
                          name="strategicPartnerCheckbox"
                          color="primary"
                        />
                      }
                      label="Houzer Strategic Partner"
                    />
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>Lead Status</InputLabel>
                      <Controller
                        name="leadStatus"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="leadStatus"
                            label="Lead Status"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleSPLeadStatusChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      leadStatusList?.find(
                                        (leadStatus) =>
                                          leadStatus.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {leadStatusList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>
                        Lead Priority
                      </InputLabel>
                      <Controller
                        name="leadPriority"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="leadPriority"
                            label="Lead Priority"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleSPLeadPriorityChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      leadPriorityList.find(
                                        (leadPriority) =>
                                          leadPriority.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {leadPriorityList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel id="assignedTo"> Assigned To</InputLabel>
                      <Controller
                        name="assignedTo"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <Select
                            multiple
                            {...field}
                            labelId="assignedTo-label"
                            label="Assigned To"
                            id="assignedTo"
                            value={value || []}
                            // onChange={onChange}
                            onChange={(event) => {
                              onChange(event); // Call the field's onChange to update the form state
                              handleAssignedToChange(event); // Call the custom function to update the component state
                            }}
                            size="small"
                          >
                            {employeesData?.map((data) => (
                              <MenuItem key={data.id} value={data.id}>
                                {data.name}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                </>
              )}

              {selectedCategory == "Employee" && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>
                        Select Locations
                      </InputLabel>
                      <Controller
                        name="empLocations"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="emp-locations"
                            label="Select Locations"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleEmployeeLocationChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      locationsData.find(
                                        (location) =>
                                          location.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {locationsData.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>
                        Select Designation
                      </InputLabel>
                      <Controller
                        name="designation"
                        control={control}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="designation"
                            label="Select Designation"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleDesignationChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      designationsData.find(
                                        (service) =>
                                          service.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {designationsData.map((service) => (
                              <MenuItem key={service.id} value={service.id}>
                                {service.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel id="assignedTo"> Assigned To</InputLabel>
                      <Controller
                        name="empAssignedTo"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <Select
                            multiple
                            {...field}
                            labelId="emp-assignedTo-label"
                            label="Assigned To"
                            id="assignedTo"
                            value={value || []}
                            // onChange={onChange}
                            onChange={(event) => {
                              onChange(event); // Call the field's onChange to update the form state
                              handleEmployeeAssignmentsChange(event); // Call the custom function to update the component state
                            }}
                            size="small"
                          >
                            {employeesData?.map((data) => (
                              <MenuItem key={data.id} value={data.id}>
                                {data.name}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                </>
              )}
              {selectedCategory.includes("Society") && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="search"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            size="small"
                            id="search"
                            placeholder="Search by Society name"
                            value={societyKeyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setSocietyKeyword(e.target.value);
                              setSocietySearch(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSocietySearch(keyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                fontSize: "0.9rem",
                                borderRadius: "5px",
                                backgroundColor: "white",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setSocietySearch(keyword);
                                    }}
                                  />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.services)}>
                      <InputLabel style={{ zIndex: 0 }}>
                        Select Locations
                      </InputLabel>
                      <Controller
                        name="chsLocations"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="chsLocations"
                            label="Select Locations"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLocationChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      locationsList.find(
                                        (location) =>
                                          location.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {locationsList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid itm xs={12} sm={3}></Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>Lead Status</InputLabel>
                      <Controller
                        name="chsLeadStatus"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="leadStatus"
                            label="Lead Status"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLeadStatusChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      leadStatusList?.find(
                                        (leadStatus) =>
                                          leadStatus.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {leadStatusList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel style={{ zIndex: 0 }}>
                        Lead Priority
                      </InputLabel>
                      <Controller
                        name="chsLeadPriority"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <Select
                            multiple
                            size="small"
                            labelId="leadPriority"
                            label="Lead Priority"
                            {...field}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLeadPriorityChange(event);
                            }}
                            renderValue={(selected) => (
                              <span>
                                {selected
                                  .map(
                                    (selectedValue) =>
                                      leadPriorityList.find(
                                        (leadPriority) =>
                                          leadPriority.id === selectedValue
                                      )?.listValue
                                  )
                                  .join(", ")}
                              </span>
                            )}
                          >
                            {leadPriorityList.map((location) => (
                              <MenuItem key={location.id} value={location.id}>
                                {location.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel id="assignedTo"> Assigned To</InputLabel>
                      <Controller
                        name="chsAssignedTo"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <Select
                            multiple
                            {...field}
                            labelId="assignedTo-label"
                            label="Assigned To"
                            id="assignedTo"
                            value={value || []}
                            onChange={(event) => {
                              onChange(event);
                              handleCHSAssignmentsChange(event);
                            }}
                            size="small"
                          >
                            {employeesData?.map((data) => (
                              <MenuItem key={data.id} value={data.id}>
                                {data.name}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                </>
              )}
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#e6ecff",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contacts
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={usersData}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                //onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38} 
                onSelectionModelChange={(newSelection) => {
                  setSelectedRows(newSelection);
                }}
              />
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                marginTop: "20px",
                marginBottom: "20px",
              }}
            >
              <Tooltip
                title={!finalList ? "Select at least one row to enable" : ""}
              >
                <span>
                  <Button
                    disabled={!finalList}
                    variant="contained"
                    onClick={handleFinalListClick}
                  >
                    Add to Final List
                  </Button>
                </span>
              </Tooltip>
            </div>
          </Card>
          <div style={{ height: 380, width: "100%" }}>
            <DataGrid
              rows={selectedUsers}
              columns={cols}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={selectedUsers?.length}
              rowHeight={38}
              headerHeight={38} 
            />
          </div>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit, onFormInvalid)}
          >
            Create Group
          </Button>
        </DialogActions>
        <Toaster position="top-right" />
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: 'white',
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ContactGroupDetails;
