import React, { useEffect, useState, useContext } from "react";
import { useForm, Controller } from "react-hook-form";
import { useMediaQuery, useTheme } from "@mui/material";
import {
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const EducationalInsightsForm = ({ data, onCancel, userData }) => {
  const { user, entityData, setEntityData, patchMicrosite } =
    useContext(AuthContext);

  const { control, handleSubmit, reset, setValue, unregister, getValues } =
    useForm();
  const [entries, setEntries] = useState([]);
  const [fieldChanged, setFieldChanged] = useState(false);

  const entityId = user?.entityId;

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`educationalInsights[${index}].url`, entry.url);
    });
  }, [entries, setValue]);

  useEffect(() => {
    if (data && data?.educationalInsightsList) {
      setEntries(data && data?.educationalInsightsList);
    }
  }, [data]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.educationalInsights || [];
    setEntries([...currentEntries, { name: "", description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);
    unregister(`educationalInsights[${index}].url`);
    reset({
      ...getValues(),
      educationalInsights: newEntries,
    });
    setFieldChanged(true);

  };

  const onSubmit = async (data) => {
    console.log(data)
    const insightsData =
      data.educationalInsights.length > 0
        ? data.educationalInsights
        : [];
    const userUniqueId =
      userData && userData.id !== undefined ? userData.id : user.id;
    const response = await patchMicrosite(
      { educationalInsightsList: insightsData },
      userUniqueId,
      () => {
        console.log("Success educationalInsights.");
      },
      () => {
        console.error("educationalInsights failed");
      }
    );
    onCancel();
    reset();
  };

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isExtraSmallScreen = useMediaQuery("(max-width:360px)");


  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid
            container
            justifyContent={
              entries.length === 0 ? { lg: "space-between", md: "space-between", sm: "space-between" } : "flex-end"
            }
            flexDirection={
              entries.length === 0 ? { xs: "column", lg: "row", md: "row", sm: "row" } : ""
            }               alignItems="center"
            sx={{
              mt: { xs: 2, lg: 0 },
              mb: { xs: 2, lg: 2 },
            }}
          >
            {entries.length === 0 && (
              <Typography
                style={{
                  textAlign: "center",
                  flex: 1,
                  marginLeft: isSmallScreen ? "" : "4rem",
                }}
              >
                Click on ADD to add Educational Insights
              </Typography>
            )}
            <Button
              onClick={addEntry}
              color="primary"
              variant="contained"
              sx={{
                mb: { xs: 2, lg: 2 },
                mt: { xs: 2, lg: 4 },
                alignSelf: isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",

              }}
            >
              Add
            </Button>
          </Grid>
          <TableContainer component={Paper}>
          <Table>
              <TableHead>
                {entries.length > 0 && (
                  <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                    <TableCell style={{ padding: "5px 5px 5px 1.5rem" }}>
                      YouTube Url
                    </TableCell>
                    <TableCell sx={{ padding: "5px" }}>Delete</TableCell>
                  </TableRow>
                )}
              </TableHead>
              <TableBody>
                {entries.map((entry, index) => (
                  <TableRow key={index}>
                    <TableCell style={{ borderBottom: "none" }}>
                      <Controller
                        name={`educationalInsights[${index}].url`}
                        control={control}
                        defaultValue={entry.url}
                        rules={{
                          required: "YouTube URL/Link is required",
                          pattern: {
                            value:
                              /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)\S*$/,
                            message:
                              "Please enter a valid YouTube URL (e.g., https://www.youtube.com/watch?v=VIDEO_ID)",
                          },
                        }}
                        render={({ field, fieldState }) => (
                          <TextField
                            {...field}
                            label="YouTube Url"
                            variant="outlined"
                            size="small"
                            fullWidth
                            placeholder="Copy & Paste valid URL (https://www.youtube.com/watch?v=VIDEO_ID)"
                            error={Boolean(fieldState?.error?.message)}
                            helperText={fieldState?.error?.message || " "}
                            sx={{ width: isSmallScreen ? "300px" : "100%" }}
                            onChange={(e) => {
                              field.onChange(e);
                              setFieldChanged(true);
                            }}
                          />
                        )}
                      />
                    </TableCell>
                    <TableCell style={{ borderBottom: "none" }}>
                      <IconButton onClick={() => removeEntry(index)} color="error">
                        <Icon
                          style={{ marginBottom: "23px" }}
                          icon="iconamoon:trash"
                        />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Grid
            container
            justifyContent="center"
            sx={{
              mt: { xs: 5, lg: 4 },
              mb: { xs: 2, lg: 4 },
            }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit"
              variant="contained"
              color="primary"
              disabled={!fieldChanged}

            >
              Submit
            </Button>
          </Grid>
        </form>
      </Grid>
    </Grid>
  );
};

export default EducationalInsightsForm;
