import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON>Header,
    Divider,
    Grid} from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";

import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import ProfileView from "./profileView";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;




function BasicProfile() {

  const [expanded, setExpanded] = useState(true);
  
  const { can } = useRBAC();

  const {
    basicProfileGetData,
    getBasicProfileData,
  } = useContext(AuthContext);



  useEffect(() => {
    getBasicProfileData();
  }, []);

 
if(can('spBasicProfile_READ')){
  return (
    <>
      <style>
        {`
         .tableBody:hover {
             background-color: #f6f6f7;
             cursor: pointer
         }
     `}
      </style>

      <DatePickerWrapper>
        <Card>
        <CardHeader title='Basic Profile' />
        <Divider/>
        <CardContent>
          <Grid item xs={12}>
            <ProfileView
              data={basicProfileGetData}
              expanded={expanded}
            ></ProfileView>
          </Grid>
        </CardContent>
        </Card>
      </DatePickerWrapper>
    </>
  );}
  else{
    return null;
  }
}

export default BasicProfile;
