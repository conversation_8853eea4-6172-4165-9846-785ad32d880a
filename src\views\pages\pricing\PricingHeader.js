// ** MUI Imports
import Box from "@mui/material/Box";
import Switch from "@mui/material/Switch";
import Typography from "@mui/material/Typography";
import InputLabel from "@mui/material/InputLabel";
import useMediaQuery from "@mui/material/useMediaQuery";

// ** Icon Import
import Icon from "src/@core/components/icon";

// ** Custom Component Import
import CustomChip from "src/@core/components/mui/chip";

const PricingHeader = (props) => {
  // ** Props
  const {handleChange } = props;

  // ** Hook
  const hidden = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  return (
    <Box sx={{ mb: [5, 3], textAlign: "center" }}>
      <Typography
        variant="h4"
        sx={{ fontSize: { sm: "1.7rem !important", xs: "1.5rem !important" } }}
      >
        Pricing Plans
      </Typography>
      <Box sx={{ mt: 2.5, mb: { xs: 1.75, sm: "1.5rem" } }}>
        <Typography
          fontWeight={600}
          sx={{
            color: "text.secondary",
            fontSize: { sm: "1rem !important", xs: "0.8rem !important" },
            textTransform: "uppercase",
          }}
        >
          what you get?
        </Typography>
      </Box>
   
    </Box>
  );
};

export default PricingHeader;
