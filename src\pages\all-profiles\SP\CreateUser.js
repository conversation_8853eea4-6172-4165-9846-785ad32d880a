// ** React Imports
import { useContext, useEffect, useState } from "react";
import { useRef } from "react";
import { useWatch } from "react-hook-form";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import MenuItem from "@mui/material/MenuItem";
import FormHelperText from "@mui/material/FormHelperText"

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

import axios from "axios";
import { getUrl } from "src/helpers/utils";

// ** Hooks
// ** Icon Imports
import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  InputLabel,
  Select,
  Divider,
} from "@mui/material";
import { Box } from "@mui/system";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

const CreateUser = ({
  openDialog,
  searchData,
  handleDialogClose,
  page,
  pageSize,
  searchKeyword,
  fetchServiceProviders,
}) => {
  const { getAllListValuesByListNameId, userSaveSpPost } = useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");

  const [submitSuccess, setSubmitSuccess] = useState(false);

  //Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    reset,
    formData,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
    },
  });
  const [allServicesList, setAllServicesList] = useState([]);

  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.locationId
  );
  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);

  const [email, setEmail] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [otp, setOTP] = useState("");
  const [countdown, setCountdown] = useState(0);

  const [loading, setLoading] = useState(false);
  const [resend, setResend] = useState(false);
  const [loadingEmail, setLoadingEmail] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);

  //const [isSubmitting, setIsSubmitting] = useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);
  const [okayButtonClose, setOkayButtonClose] = useState(false);

  const handleOTPDialogClose = () => {
    setSubmitSuccess(false);
  };

  const handleCancelClick = () => {
    setIsEmailVerified(false);
    setDisableVerifyEmailButton(false);
    reset({ firstName: "", lastName: "", email: ""});
    setSelectedLocationId(null);
    setSelectedDesignationId(null);
    handleDialogClose();
  };

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };
  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };
  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const [locationsData, setLocationsData] = useState(null);

  const [selectedLocationName, setSelectedLocationName] = useState(null);
  const watchServicesProvided = useWatch({ control, name: "servicesProvided" });

  const isApiCalling = useRef(false);

  const designationName = designationsData?.find(
    (designation) => designation.id === selectedDesignationId
  )?.listValue;
  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    console.log("Data to be sent:", data);
    isApiCalling.current = true;
    setLoadingSave(true);
    const shortFormData = {
      firstName: data?.firstName,
      companyName: data?.companyName,
      location: selectedLocationName,
      locationId: data?.selectedLocationId,
      servicesProvided: data?.servicesProvided,
      email: data?.email,
      mobileNumber: data?.mobileNumber,
      designation: data?.selectedDesignationId,
      otherDesignation: data?.otherDesignation,
    };
    try { 
      console.log("Calling saveUserSp with data:", shortFormData);
      const response = await userSaveSpPost(shortFormData);
      console.log("Response from server:", response);
  
      if (response) {
        const message = `
          <div>
            <h3>User Created Successfully!</h3>
          </div>
        `;
        setLoadingSave(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
        fetchServiceProviders(page, pageSize, searchKeyword, searchData);
        reset({ firstName: "", lastName: "", email: "", });
        setIsEmailVerified(false);
        handleCancelClick();
      }
    } catch (error) {
      console.error("Error creating user:", error);
      const message = `
        <div>
          <h3>Submit failed, try again later.</h3>
        </div>
      `;
  
      setDialogMessage(message);
      setSubmitSuccess(true);
    } finally {
      setLoadingSave(false);
      isApiCalling.current = false;
    }
    reset({ firstName: "", lastName: "", email: "" });
    setIsEmailVerified(false);
    handleDialogClose();
    isApiCalling.current = false;
  };
  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
    const selectedLocation = locationsData.find(
      (location) => location.id === selectedId
    );
    const locationName = selectedLocation ? selectedLocation.listValue : "";
    setSelectedLocationName(locationName);
  };

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const servicesProvidedValues = watchServicesProvided?.map(
    (id) => allServicesList.find((service) => service.id === id)?.listValue
  );

  async function sendOTP(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    isApiCalling.current = true;
    setResend(true);

    const ipAddress = await fetchIpAddress();
    setPleaseVerifyEmailMessage(false);
    setLoadingEmail(true);
    await axios({
      method: "POST",
      url: getUrl(authConfig.registerEndpointNewV3),
      data: {
        firstName: data?.firstName,
        email: data?.email,
        ipAddress: ipAddress,
        role: "SERVICE_PROVIDER",
        lastName: data?.lastName,
      },
    })
      .then((res) => {
        if (res.data.isVerified) {
          const message = `
          <div>
          <h3>
          Email already exists!!!
          </h3>
          </div>
          `;
          setLoadingEmail(false);
          setOkayButtonClose(true);
          setDisableVerifyEmailButton(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
        } else {
          setEmail(data?.email);
          const message = `
          <div>
          <h3>
          OTP has been sent to your Email for verification. Please check.
          </h3>
          </div>
        `;
          setLoadingEmail(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
          setCountdown(30);
          setShowOTPOptions(true);
          setOkayButtonClose(false);
        }
      })
      .catch((err) => {
        console.log("Error sendOTP: ", err);
        const message = `
        <div>
        <h3>
        Error sending OTP to your Email. <br> Please Enter Valid Email (or) Please try again.
        </h3>
        </div>
      `;
        setLoadingEmail(false);
        setDialogMessage(message);
        setDisableVerifyEmailButton(false);
        setOkayButtonClose(true);
        setSubmitSuccess(true);
      });

    isApiCalling.current = false; // Reset the isSendingOTP ref
  }

  async function verifyOtp(data) {
    setLoading(true);
    data.location = selectedLocationName;
    data.locationId = selectedLocationId;

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint3) + "?isMember=false",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        setIsEmailVerified(true);
        setOTP("");
        setShowOTPOptions(false);

        const message = `
        <div>
        <h3>
        Email has been verified successfully.
        </h3>
        </div>
      `;
        setLoading(false);
        setDialogMessage(message);
        setOkayButtonClose(true);
        setSubmitSuccess(true);
        window.localStorage.setItem("verifiedEmail", data?.email);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
        <div>
        <h3>
        OTP doesn't match. Please try again.
        </h3>
        </div>

      `;
        setLoading(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
      });
  }
  const handleLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationSPId,
        handleDesignationSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        handleLocationSuccess,
        handleError
      );
     
    }
  }, [authConfig,setValue]);  

  return (
    <Box sx={{ pt: 3 }}>
      <Dialog
        open={submitSuccess}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          {showOTPOptions && (
            <Grid container spacing={5}>
              <Grid container justifyContent="center">
                <TextField
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  placeholder="OTP"
                  value={otp}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value) && value.length <= 6) {
                      setOTP(value);
                    }
                  }}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors?.otp)}
                  helperText={errors?.otp?.message}
                  sx={{
                    borderRadius: "5px",
                    background: "white",
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={12}>
                <Grid container justifyContent="center">
                  <Button
                    variant="contained"
                    disabled={!otp || Boolean(errors?.otp)}
                    onClick={verifyOtp}
                    sx={{
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loading ? (
                      <CircularProgress color="inherit" size={22} />
                    ) : (
                      "VALIDATE OTP"
                    )}
                  </Button>
                  <Button
                    variant={countdown > 0 ? "outlined" : "contained"}
                    disabled={countdown > 0}
                    onClick={handleSubmit(sendOTP)}
                    sx={{
                      marginLeft: "7px",
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loading ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "RESEND OTP"
                    )}
                  </Button>
                </Grid>
                {countdown > 0 && (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "primary.main",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}

          {okayButtonClose && (
            <DialogActions>
              <Button
                onClick={handleOTPDialogClose}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          )}
        </Box>
      </Dialog>

      <Dialog
        fullWidth
        maxWidth="md"
        scroll="paper"
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleCancelClick();
          }
        }}
        //   onClose={handleDialogClose}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start"},
            fontSize: { xs: 19, md: 20  },
          }}
          textAlign={"center"}
        >
          {"Creating User as Service Provider"}
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleCancelClick}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(10, 8)} !important`,
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} >
              <FormControl fullWidth>
                <Controller
                  name="companyName"
                  control={control}
                  rules={{ required: true}}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Name of the company"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter your company name"
                      error={Boolean(errors.companyName)}
                      helperText={errors.companyName?.message}
                      aria-describedby="validation-basic-companyName"
                      inputProps={ {maxLength:50 }}

                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
      <FormControl fullWidth error={Boolean(errors.selectedLocationId)}>
        <InputLabel id="location-select-label">Location</InputLabel>
        <Controller
          name="selectedLocationId"
          control={control}
          rules={{ required: "Location is required" }}
          render={({ field }) => (
            <Select
              {...field}
              labelId="location-select-label"
              id="location-select"
              label="Location"
              size="small"
            >
              {locationsData?.map((location) => (
                <MenuItem key={location.id} value={location.id}>
                  {location.listValue}
                </MenuItem>
              ))}
            </Select>
          )}
        />
       
      </FormControl>
    </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.servicesProvided)}>
                <InputLabel style={{ zIndex: 0 }}>Type of vendor</InputLabel>
                <Controller
                  name="servicesProvided"
                  control={control}
                  defaultValue={formData?.servicesProvided || []}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Select
                      multiple
                      size="small"
                      labelId="service-providing"
                      label="Select Services Providing"
                      {...field}
                      value={field.value || []}
                      onChange={(event) => {
                        field.onChange(event.target.value);
                      }}
                      renderValue={(selected) => (
                        <span>
                          {selected
                            .map(
                              (selectedValue) =>
                                allServicesList.find(
                                  (service) => service.id === selectedValue
                                )?.listValue
                            )
                            .join(", ")}
                        </span>
                      )}
                    >
                      {allServicesList.map((service) => (
                        <MenuItem key={service.id} value={service.id}>
                          {service.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
                {/* {errors.servicesProvided && (
      <FormHelperText>{errors.servicesProvided.message}</FormHelperText>
    )} */}
              </FormControl>
            </Grid>
            {servicesProvidedValues &&
              servicesProvidedValues.includes("Any other") && (
                <Grid item xs={12} sm={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="anyOtherServiceProvided"
                      control={control}
                      defaultValue={formData?.anyOtherServiceProvided || ""}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Specify Profession Type"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter your profession type"
                          error={Boolean(errors.anyOtherServiceProvided)}
                          helperText={errors.anyOtherServiceProvided?.message}
                          aria-describedby="validation-basic-other-service"
                          value={field.value}
                        />
                      )}
                    />
                    {/* {errors.anyOtherServiceProvided && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherServiceProvided.message}
        </FormHelperText>
      )} */}
                  </FormControl>
                </Grid>
              )}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="mobileNumber"
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.mobileNumber}
                  render={({ field }) => (
                    <MobileNumberValidation
                      {...field}
                      type="tel"
                      label="Mobile Number"
                      size="small"
                      error={Boolean(errors.mobileNumber)}
                      helperText={errors.mobileNumber?.message}
                      InputLabelProps={{ shrink: true }}
                      placeholder="+91 1234567890"
                      inputProps={{
                        maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                      }}
                    />
                  )}
                />
                {/* {errors.mobileNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
              </FormControl>
            </Grid>
             <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="firstName"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      label="Name of the person"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter your name"
                      error={Boolean(errors.firstName)}
                      helperText={errors.firstName?.message}
                      aria-describedby="validation-basic-firstName"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
        <FormControl fullWidth error={Boolean(errors.selectedDesignationId)}>
          <InputLabel id="designation-select-label">Designation</InputLabel>
          <Controller
            name="selectedDesignationId"
            control={control}
            rules={{ required: "Designation is required" }}
            render={({ field }) => (
              <Select
                {...field}
                labelId="designation-select-label"
                id="designation-select"
                label="Designation"
                size="small"
              >
                {designationsData?.map((designation) => (
                  <MenuItem key={designation.id} value={designation.id}>
                    {designation.listValue}
                  </MenuItem>
                ))}
              </Select>
            )}
          />
          {errors.selectedDesignationId && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-selectedDesignationId"
            >
            </FormHelperText>
          )}
        </FormControl>
      </Grid>
           
            {designationName === "Any Other" && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="otherDesignation"
                    control={control}
                    defaultValue={formData?.otherDesignation || ""}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Specify Designation"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your designation"
                        error={Boolean(errors.otherDesignation)}
                        helperText={errors.otherDesignation?.message}
                        aria-describedby="validation-basic-other-designation"
                      />
                    )}
                  />
                  {errors.otherDesignation && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.otherDesignation.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}
          
        
          
            <Grid item xs={12} sm={6}>
              {!isEmailVerified && (
                <>
                  <FormControl fullWidth>
                    <Controller
                      name="email"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <EmailTextField
                          {...field}
                          type="email"
                          label="Email address"
                          size="small"
                          error={Boolean(errors.email)}
                          inputProps={{ maxLength: 50 }}
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter email address"
                          helperText={errors.email?.message}
                        />
                      )}
                    />
                  </FormControl>
                </>
              )}
        
              {isEmailVerified && (
                <>
                  <FormControl fullWidth>
                    <div style={{ display: "flex", alignItems: "baseline" }}>
                      <Typography sx={{ mt: 4, fontWeight: "bold" }}>
                        Email:
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          marginLeft: "12px",
                        }}
                      >
                        {email}
                      </Typography>
                    </div>
                  </FormControl>
                </>
              )}
            </Grid>
            <Grid item xs={12} sm={2}>
              {!isEmailVerified ? (
                <>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={handleSubmit(sendOTP)}
                    disabled={disableVerifyEmailButton}
                    sx={{fontSize:'0.76rem !important', padding:'6px 16px !important'}}
                    //disabled={disableVerifyEmailButton || isSubmitting}
                  >
                    {loadingEmail ? (
                      <CircularProgress color="inherit" size={22} />
                    ) : (
                      "Verify"
                    )}
                  </Button>
                </>
              ) : (
                <Box sx={{ display: "flex", alignItems: "center", mt: 4 }}>
                  <CheckCircleOutlineIcon
                    sx={{
                      color: "green",
                    }}
                  />
                  <Typography
                    sx={{
                      marginLeft: "6px",
                      marginBottom: "-10px",
                      paddingBottom: 2,
                    }}
                  >
                    Verified
                  </Typography>
                </Box>
              )}
            </Grid>
        </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => handleCancelClick()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={!isEmailVerified}
                //disabled={isSubmitting || !isEmailVerified}
                onClick={handleSubmit(submit)}
              >
                {loadingSave ? (
                  <CircularProgress color="inherit" size={22} />
                ) : (
                  "Save"
                )}
              </Button>
            </center>
          </Grid>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateUser;
