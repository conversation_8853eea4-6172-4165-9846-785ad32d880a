import { Card } from "@mui/material";
import { useRBAC } from "src/pages/permission/RBACContext";
import ServiceRequisitions from "src/pages/service-requisitions/ServiceRequisitions";


const SocietyRequisitions = () => {
  const { can } = useRBAC();
  if(can('serviceRequests_READ')){
  return (
    <Card>
      <ServiceRequisitions
        role="SOCIETY"
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default SocietyRequisitions;
