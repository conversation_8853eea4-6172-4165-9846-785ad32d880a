// ** React Imports
import { forwardRef, useState } from 'react'

// ** MUI Imports

import Grid from '@mui/material/Grid'

import Button from '@mui/material/Button'

import TextField from '@mui/material/TextField'

import FormControl from '@mui/material/FormControl'

import { Box } from '@mui/system'


// ** Third Party Imports
import toast from 'react-hot-toast'
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'

// ** Icon Imports

import SelectMultipleBasic from 'src/@core/components/custom-components/SelectMultipleBasic'
import { yupResolver } from '@hookform/resolvers/yup'
import { StructuralValidation } from './StructuralValidation'

const expertise = [
  {
    value:'RESIDENTIAL',
    name:'Residential'
  },
  {
    value:'COMMERCIAL',
    name:'Commercial'
  },
  {
    value:'RETAIL',
    name:'Retail'
  },
  {
    value:'INDUSTRIAL',
    name:'Industrial'
  },
  {
    value:'RELIGIOUS_PLACES',
    name:'Religious places'
  },
  {
    value:'FINANCIAL_COPMOF',
    name:'Financial COPMOF'
  },
  {
    value:'CASH_FLOWS',
    name:'Cash flows'
  },
  {
    value:'TECHNICAL_FEASIBILITY',
    name:'Technical feasibility'
  }
];





const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const Section4 = ({ onCancel,formData } ) => {
  const auth = useAuth();

  const fields = ["otherServices"]

  const [areaOfExpertise,setAreaOfExpertise] = useState(formData?.areaOfExpertise)
  
  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(StructuralValidation(fields)),
    mode: "onChange",
  });


  async function submit(data) {

    if (Array.isArray(data?.areaOfExpertise)) {
      data.areaOfExpertise = data.areaOfExpertise.join(",");
    }

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value])
    );
    const hasWhiteSpace = Object.values(trimmedData).some((value) => typeof value === 'string' && value === '');
    if (hasWhiteSpace) {
      toast.error('Fields cannot contain only white spaces');
      return;
    }

    const response = await auth.updateEntity(trimmedData, () => {
      console.error("Structural Company Details failed");
    });

    onCancel();
  }


  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>

        <Grid item xs={12} sm={4}>
        <SelectMultipleBasic
              id={"areaOfExpertise"}
              label={"Area of Expertise"}
              nameArray={expertise}
              register={register}
              defaultValue={formData?.areaOfExpertise}
            />
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name='otherServices'
              control={control}
             
              defaultValue={formData?.otherServices}
              render={({ field}) => (
                <TextField
            
                  rows={4}
                  multiline
                  {...field}
                  label='Other Services'
                  InputLabelProps={{ shrink: true }}
               
                  error={Boolean(errors.otherServices)}
                  aria-describedby='broker-validation-basic-otherServices'
                  helperText={errors.otherServices?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        

        <Grid item xs={12}>
          <center>
            <Button variant='outlined' color='primary' size='medium' sx={{ mr:3 }} onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Section4
