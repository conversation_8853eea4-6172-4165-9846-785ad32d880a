import React, { useEffect, useState } from "react";

import { useF<PERSON>, Controller } from "react-hook-form";
import {
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useContext } from "react";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";

const MembershipEdit = (props) => {
  const { onCancel, data } = props;
  const { user, patchArchitectAdditionalData } = useContext(AuthContext);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    unregister,
    getValues,
    formState: { errors },
  } = useForm();

  const [entries, setEntries] = useState([]);

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`awards[${index}].description`, entry.description);
    });
  }, [entries, setValue]);

  useEffect(() => {
    if (data && data.memberShipList) {
      setEntries(data.memberShipList);
    }
  }, [data]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.awards || [];

    setEntries([...currentEntries, { name: "", description: "" }]);
  };
  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);

    unregister(`awards[${index}].description`);

    reset({
      ...getValues(),
      awards: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    console.log("submitted Data -Initial", data);

    const awardsData = data?.awards?.filter(
      (entry) => entry.description.trim() !== ""
    ) || [{ description: "" }];
    const response = await patchArchitectAdditionalData(
      { memberShipList: awardsData },
      () => {
        console.log("Success memberships.");
      },
      () => {
        console.error("memberships failed");
      }
    );

    onCancel();
    reset();
  };
  const [fieldChanged, setFieldChanged] = useState(false);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Paper>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>description</TableCell>
                <TableCell>Delete</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {entries?.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Controller
                      name={`awards[${index}].description`}
                      control={control}
                      defaultValue={entry.description}
                      rules={{ required: "Description is required" }}
                      render={({ field }) => (
                        <>
                          <TextField
                            {...field}
                            label="Description"
                            variant="outlined"
                            fullWidth
                            error={Boolean(
                              errors?.awards?.[index]?.description
                            )}
                            helperText={
                              errors?.awards?.[index]?.description?.message ||
                              ""
                            }
                            onChange={(e) => {
                              field.onChange(e);
                              setFieldChanged(true);
                            }}
                          />
                        </>
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => {
                        removeEntry(index);
                      }}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              <Grid style={{ display: "flex", justifyContent: "flex-start" }}>
                <Button
                  onClick={addEntry}
                  color="primary"
                  variant="contained"
                  sx={{
                    mb: { xs: 2, lg: 4 },
                    mt: 2,
                  }}
                >
                  Add
                </Button>
              </Grid>

              <TableRow>
                <TableCell colSpan={4}>
                  <Grid
                    container
                    justifyContent="center"
                    sx={{
                      mt: { xs: 2, lg: 4 },
                      mb: { xs: 2, lg: 4 },
                    }}
                  >
                    <Button
                      size="medium"
                      sx={{ mr: 3 }}
                      variant="outlined"
                      color="primary"
                      onClick={() => onCancel()}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      color="primary"
                      variant="contained"
                      disabled={!fieldChanged}
                    >
                      Submit
                    </Button>
                  </Grid>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Paper>
      </form>
    </>
  );
};

export default MembershipEdit;
