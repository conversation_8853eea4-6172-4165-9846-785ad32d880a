// ** React Imports
import { forwardRef } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";

// ** Third Party Imports

import { useForm, Controller } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { Typography } from "@mui/material";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import { yupResolver } from "@hookform/resolvers/yup";
import BrokerValidationSection1 from "./BrokerValidation";

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const names = ["Less than 5 years", "5-10 years", "More than 10 years"];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section1 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();
  const fields = ["name", "companyName", "email", "mobileNumber", "address", "teamSize", "reraNumber", "websiteUrl"];
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(BrokerValidationSection1(fields)),
    mode: "onChange",
  
  });

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
   
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Broker Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              defaultValue={formData?.name}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="Section1-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="companyName"
              control={control}
              defaultValue={formData?.companyName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Company Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter company name"
                  error={Boolean(errors.companyName)}
                  helperText={errors.companyName?.message}
                  aria-describedby="Section1-company"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">System Code</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.systemCode}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Address"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.address)}
                  helperText={errors.address?.message}
                  aria-describedby="Section1-address"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Contact Number"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.mobileNumber)}
                  placeholder="+91 1234567890"
                  helperText={errors.mobileNumber?.message}
                  aria-describedby="Section1-mobileNumber"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
              defaultValue={formData?.email}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="Email"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.email)}
                  helperText={errors.email?.message}
                  placeholder="Enter email address"
                  aria-describedby="validation-email"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              defaultValue={formData?.websiteUrl}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Website URL"
                  InputLabelProps={{ shrink: true }}
                  helperText={errors.websiteUrl?.message}
                  error={Boolean(errors.websiteUrl)}
                  placeholder="https://www.example.com"
                  aria-describedby="validation-websiteUrl"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="teamSize"
              control={control}
              defaultValue={formData?.teamSize}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="number"
                  label="Team Size "
                  InputLabelProps={{ shrink: true }}
                  helperText={errors.teamSize?.message}
                  placeholder="Enter Team size"
                  error={Boolean(errors.teamSize)}
                  aria-describedby="ca-validation-basic-teamSize"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="reraNumber"
              control={control}
              defaultValue={formData?.reraNumber}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Rera Number"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter RERA number"
                  error={Boolean(errors.reraNumber)}
                  helperText={errors.reraNumber?.message}
                  aria-describedby="Section1-reraNumber"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section1;
