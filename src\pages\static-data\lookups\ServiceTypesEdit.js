import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Grid,
  FormControl,
  FormHelperText,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import { Box } from "@mui/system";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import authConfig from "src/configs/auth";
import { Controller, useForm } from "react-hook-form";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { useAuth } from "src/hooks/useAuth";

const ServiceProfilesDialog = ({ open, onClose }) => {
  const [listNameId, setListNameId] = useState("");

  const auth = useAuth();

  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm();

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  // State for dropdown options
  const [allServicesList, setAllServicesList] = useState([]);
  const [allUiComponentsId, setAllUiComponentsId] = useState([]);
  const [allSectionsId, setAllSectionsId] = useState([]);
  const [listNamesOptions, setListNamesOptions] = useState([]);
  const [listOfListNames, setListOfListNames] = useState([]);

  // Fetch list names
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=LIST_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfListNames(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  // Convert list names to options
  useEffect(() => {
    if (listOfListNames) {
      const data = listOfListNames.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setListNamesOptions(data);
    }
  }, [listOfListNames]);

  // Fetch other list values
  useEffect(() => {
    if (authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues.map((service) => ({
              value: service.id,
              key: service.listValue,
            }))
          ),
        (error) => console.error("Error fetching services:", error)
      );

      getAllListValuesByListNameId(
        authConfig.uiComponentsId,
        (data) =>
          setAllUiComponentsId(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        (error) => console.error("Error fetching UI components:", error)
      );

      getAllListValuesByListNameId(
        authConfig.allSectionsId,
        (data) =>
          setAllSectionsId(
            data?.listValues.map((section) => ({
              value: section.id,
              key: section.listValue,
            }))
          ),
        (error) => console.error("Error fetching sections:", error)
      );
    }
  }, [authConfig]);

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Specification added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (response) => {
    let message;

    if (response.response.status === 400) {
      message = `
        <div> 
          <h3>${response.response?.data?.message || "An error occurred"}</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3>Failed to Add Specification. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const isApiCalling = useRef(false);

  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    const fields = {
      listNamesId: listNameId,
      listValuesId: data?.listValuesId,
      uiComponentsId: data?.uiComponentsId,
      sectionId: data?.sectionId,
      requisition: true,
    };

    isApiCalling.current = true;

    try {
      const response = await auth.postSpecification(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure(response);
    } finally {
      isApiCalling.current = false;
    }

    handleCancel();
    isApiCalling.current = false;
  }

  const handleCancel = () => {
    onClose();
    setListNameId("")
    setValue("listValuesId","")
    setValue("sectionId","")
    setValue("uiComponentsId","")
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleCancel}
        fullWidth
        maxWidth="md"
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Add Specification
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(3)} !important`,
          }}
        >
          <Grid container spacing={2}>
            {/* Service Type field */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.serviceType)}>
                <Controller
                  name="listValuesId"
                  control={control}
                  rules={{ required: "Service Type is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="serviceType"
                      label="Select Service Type"
                      nameArray={allServicesList}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.serviceType && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.serviceType.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* List Name field */}
            <Grid item xs={12} sm={6}>
              <SelectAutoComplete
                id="listNamesId"
                label="Select Lable Name"
                nameArray={listNamesOptions}
                value={listNameId}
                onChange={(e) => setListNameId(e.target.value)}
                error={Boolean(errors.listNamesId)}
              />
            </Grid>

            {/* UI Component field */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.uiComponents)}>
                <Controller
                  name="uiComponentsId"
                  control={control}
                  rules={{ required: "UI Component is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="uiComponents"
                      label="UI Component"
                      nameArray={allUiComponentsId}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.uiComponents && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.uiComponents.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Sections field */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.sections)}>
                <Controller
                  name="sectionId"
                  control={control}
                  rules={{ required: "Sections is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="sections"
                      label="Sections"
                      nameArray={allSectionsId}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.sections && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.sections.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions
          sx={{
            justifyContent: "center",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            justifyContent: "end",
          }}
        >
          <Button variant="outlined" color="primary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            size="medium"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ServiceProfilesDialog;
