// ** React Imports
import { useState } from 'react'
import { useEffect } from 'react'

// ** Next Imports
import Link from 'next/link'

// ** MUI Components
import Alert from '@mui/material/Alert'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Checkbox from '@mui/material/Checkbox'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import InputLabel from '@mui/material/InputLabel'
import IconButton from '@mui/material/IconButton'
import Box from '@mui/material/Box'
import { Dialog,DialogActions,DialogContent,DialogContentText,CircularProgress, Menu, MenuItem} from '@mui/material'
import FormControl from '@mui/material/FormControl'
import useMediaQuery from '@mui/material/useMediaQuery'
import OutlinedInput from '@mui/material/OutlinedInput'
import { styled, useTheme } from '@mui/material/styles'
import FormHelperText from '@mui/material/FormHelperText'
import InputAdornment from '@mui/material/InputAdornment'
import MuiFormControlLabel from '@mui/material/FormControlLabel'


// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Third Party Imports
import * as yup from 'yup'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'

// ** Hooks
import { useAuth } from 'src/hooks/useAuth'
import useBgColor from 'src/@core/hooks/useBgColor'
import { useSettings } from 'src/@core/hooks/useSettings'

// ** Configs
import themeConfig from 'src/configs/themeConfig'

// ** Config
import authConfig from 'src/configs/auth'

// ** Layout Import
import BlankLayout from 'src/@core/layouts/BlankLayout'

// ** Demo Imports
import FooterIllustrationsV2 from 'src/views/pages/auth/FooterIllustrationsV2'

// ** Styled Components
const LoginIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 650,
  margin: theme.spacing(5),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 500,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 450,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "100%",
  },
}));



const schema = yup.object().shape({
  email: yup.string().email().required("Email is Mandatory"),
  password: yup.string().required("Password is Mandatory").min(8)
})

const defaultValues = {
  password: '',
  email: ''
}

const LogoutPage = () => {

  useEffect(() => {
    localStorage.removeItem(authConfig.storageUserKeyName) //userData
    localStorage.removeItem(authConfig.storageTokenKeyName) //accessToken
    localStorage.removeItem('refreshToken')
  }, [])

  
  // ** Hooks
  const auth = useAuth()
  const theme = useTheme()
  const bgColors = useBgColor()
  const { settings } = useSettings()
  const hidden = useMediaQuery(theme.breakpoints.down('md'))

  // ** Vars
  const { skin } = settings

  const {
    control,
    setError,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues,
    resolver: yupResolver(schema)
  })

  const [anchorEl, setAnchorEl] = useState(null);

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (role) => {
    localStorage.setItem("role", role);
    setAnchorEl(null);
    window.location.href = "/register?role=" + role;
    handleCloseMenu();
    
  };
 

  return (
    <Box className="content-right" sx={{ backgroundColor: "background.paper" }}>
      <RightWrapper>
        <Box
          sx={{
            p: [8, 14],
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Box sx={{ width: "100%", maxWidth: 400 }}>
          <a href={authConfig.guestURL + 'home'}>
            <img
              width={50}
              height={46}
              alt=""
              src="/images/logo.webp"
              style={{ marginLeft: '-10px' }}
              className=""
            ></img>
            </a>
            <Box sx={{ mt: 1, mb: 8 }}>
              <Typography
                sx={{
                  mb: 10,
                  fontWeight: 500,
                  fontSize: "1.625rem",
                  lineHeight: 1.385,
                }}
              >
                {` Successfully logged out 👋🏻`}
              </Typography>
              <Box sx={{ mb: 6 }}>
                <Button href='/login' fullWidth variant="contained" color="primary">
                  Login
                </Button>
              </Box>
              <Box>
                <Button onClick={handleOpenMenu} variant="outlined" color="primary" fullWidth>
                  Create New Account
                </Button>
                <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleCloseMenu}
                        anchorReference="anchorPosition"
                        anchorPosition={{
                          top: anchorEl
                            ? anchorEl.getBoundingClientRect().bottom
                            : 0,
                          left: anchorEl
                            ? anchorEl.getBoundingClientRect().left
                            : 0,
                        }}
                       
                      >
                        <MenuItem
                          onClick={() =>
                            handleMenuItemClick("SERVICE_PROVIDER")

                          }
                          style={{ padding: '3px 6px' }}
                        >
                          Service Provider
                        </MenuItem>
                        <MenuItem
                          onClick={() => handleMenuItemClick("SOCIETY")}
                          style={{ padding: '3px 6px' }}
                        >
                          Society Member
                        </MenuItem>
                      </Menu>
              </Box>
            </Box> 
          </Box>
        </Box>
      </RightWrapper>
      {!hidden ? (
        <Box
          sx={{
            flex: 1,
            display: "flex",
            position: "relative",
            alignItems: "center",
            borderRadius: "20px",
            justifyContent: "center",
            backgroundColor: "customColors.bodyBg",
            margin: (theme) => theme.spacing(10),
          }}
        >
          <LoginIllustration
            alt="login-illustration"
            src={`/images/pages/login.webp`}
          />
          <FooterIllustrationsV2 />
        </Box>
      ) : null}
    </Box>
  );
}
LogoutPage.getLayout = page => <BlankLayout>{page}</BlankLayout>
LogoutPage.guestGuard = true

export default LogoutPage
