import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from "next/head";
import { useAuth } from '../src/context/AuthContext';
import LoadingSpinner from '../src/components/common/LoadingSpinner';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (isAuthenticated) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    }
  }, [isAuthenticated, loading, router]);

  return (
    <>
      <Head>
        <title>Sample App - Authentication</title>
        <meta name="description" content="Sample App with Authentication" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <LoadingSpinner loading={true} message="Redirecting..." overlay />
    </>
  );
}
