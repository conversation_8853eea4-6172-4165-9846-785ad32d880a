// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section1 from 'src/pages/SP/broker/sections/Section1'
import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section2 from './Section2'
import Section4 from './Section4'
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const Requirements = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
    const theme = useTheme()

    const [state4, setState4] = useState(true)

  const handleState4 = () => {
    setState4(!state4)
  }
    
    // Pre-Populating code Start
    // const [requirements, setRequirements] = useState({
    //     notes:"",
    //     requirements_ExtraArea:"",
    //     requirements_Rent:"",
    //     requirements_Corpus:"",
    //     leadGivenTo:""
    
    //   });

    return (
        <>
         {/* {can('society_requirements_READ') && */}
      <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Requirements'}
                body={
                  <>
                    {state4 && (
                      
                          <TableContainer sx={{ padding:'4px 6px' }}
                            className='tableBody'
                            //onClick={can('society_requirements_UPDATE') ? handleState4 : null}>
                            onClick={handleState4}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Extra Area:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >
                                    {data?.requirements_ExtraArea}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Rent:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.requirements_Rent}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Corpus:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.requirements_Corpus}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Notes:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.notes}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Lead Given To:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.leadGivenTo}</Typography>
                                  </MUITableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {!state4 && <Section4 formData={data} onCancel={handleState4} />}
                  </>
                }
                expanded={expanded}
              />  
              {/* }   */}
        </>
    );

}
export default Requirements;