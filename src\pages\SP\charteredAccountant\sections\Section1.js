// ** React Imports
import { forwardRef, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputAdornment from "@mui/material/InputAdornment";
import { Box } from "@mui/system";

// ** Third Party Imports

import { useForm, Controller } from "react-hook-form";

// ** Icon Imports
import Icon from "src/@core/components/icon";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import { useAuth } from "src/hooks/useAuth";
import { Typography } from "@mui/material";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import CAValidationSection1 from "./CAValidationsSection1";
import { yupResolver } from "@hookform/resolvers/yup";


const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const names = [
  {
    value: "LESS_THAN_5_YEARS",
    key: "Less than 5 years",
  },
  {
    value: "_5_TO_10_YEARS",
    key: "5-10 years",
  },
  {
    value: "MORE_THAN_10_YEARS",
    key: "More than 10 years",
  },
];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section1 = ({ onCancel, formData }) => {
  
  //Hooks
  const auth = useAuth();

  const fields = ["name","companyName","mobileNumber","email","address","websiteUrl","teamSize","awards","briefProfile","yearsOfExperience"]

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } =useForm({
    resolver: yupResolver(CAValidationSection1(fields)),
    mode: "onChange",
  });



  const [yearsOfExperience, setYearsOfExperience] = useState(
    formData?.yearsOfExperience
  );

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
 
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" CA Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field }) => (
                <NameTextField
                    {...field}
                  label="Name"
                  placeholder="Enter your name"
                  InputLabelProps={{ shrink: true }}
                 
                  error={Boolean(errors.name)}
                  aria-describedby="ca-validation-basic-name"
                  helperText={errors.name?.message}
                       InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:user" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
           
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="companyName"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.companyName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Company Name"
                  placeholder="Enter Company Name"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.companyName)}
                  aria-describedby="ca-validation-basic-company"
                  helperText={errors.companyName?.message}
            
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:building-shop" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">System Code</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.systemCode}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
             
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Contact Number"             
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter Contact Number"
                  error={Boolean(errors.mobileNumber)}
                  helperText={errors.mobileNumber?.message}
             
                  aria-describedby="validation-mobileNumber"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          icon="tabler:phone"
                          style={{ fontSize: "16px" }}
                         
                        />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
            
              defaultValue={formData?.email}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="email"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.email)}
                  placeholder="Enter Email Address"
                 
                  aria-describedby="validation-email"
                  helperText={errors.email?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:mail" style={{ fontSize: "16px" }} />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
             
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Address"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.address)}
                  aria-describedby="ca-validation-basic-address"
                  helperText={errors.address?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:message" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              defaultValue={formData?.websiteUrl}
              render={({ field }) => (
                <TextField
                {...field}
                  type="Website Url"
           
                  label="Website Url"
                 
                  InputLabelProps={{ shrink: true }}
                
                  error={Boolean(errors.websiteUrl)}
                  placeholder="https://www.example.com"
                  aria-describedby="ca-validation-basic-websiteUrl"
                  helperText={errors.websiteUrl?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          icon="tabler:LinkIcon"
                          style={{ fontSize: "16px" }}
                        />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
           
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="teamSize"
              control={control}
            
              defaultValue={formData?.teamSize}
              render={({ field }) => (
                <TextField
                  type="number"
                 {...field}
                  label="Team Size "
                  InputLabelProps={{ shrink: true }}
                 
                  placeholder="Enter Team Size"
                  error={Boolean(errors.teamSize)}
                  aria-describedby="ca-validation-basic-teamSize"
                  helperText={errors.teamSize?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:user" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="awards"
              control={control}
              defaultValue={formData?.awards}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Awards"
                  InputLabelProps={{ shrink: true }}
                  
                  error={Boolean(errors.awards)}
                  aria-describedby="ca-validation-basic-awards"
                  helperText={errors.awards?.message}
                />
              )}
            />
           
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="briefProfile"
              control={control}
              defaultValue={formData?.briefProfile}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Brief Profile in your words"
                  InputLabelProps={{ shrink: true }}
                  
                  error={Boolean(errors.briefProfile)}
                  aria-describedby="ca-validation-basic-briefProfile"
                  helperText={errors.briefProfile?.message}
                />
              )}
            />
           
          </FormControl>
        </Grid>

        <Grid item xs={4}>
          <SelectProject
            register={register}
            id={"yearsOfExperience"}
            label={"Years Of Experience"}
            nameArray={names}
            defaultValue={formData?.yearsOfExperience}
            value={yearsOfExperience}
            onChange={(e) => setYearsOfExperience(e.target.value)}
            error={Boolean(errors.yearsOfExperience)}
            aria-describedby="validation-yearsOfExperience"
            
          />
          {errors.yearsOfExperience && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-yearsOfExperience"
            >
           {errors.yearsOfExperience.message}
            </FormHelperText>
            )}
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section1;
