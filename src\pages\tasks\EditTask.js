import React, { useContext, useEffect, useState } from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  Chip,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import Icon from "src/@core/components/icon";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "src/hooks/useAuth";
import RemarksTable from "./RemarksTable";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const EditTask = ({ open, onCancel, formData, fetchUsers,employeesData }) => {
  const auth = useAuth();
  const { getAllListValuesByListNameId, user } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = (value) => {
    setExpanded(value);
  };

  const {
    register,
    handleSubmit,
    setValue,
    control,
    reset,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    setValue("title",formData?.additionalDetails?.title);
    setValue("description",formData?.additionalDetails?.description);
    setValue("statusId",formData?.additionalDetails?.statusId);
    setValue("priorityId",formData?.additionalDetails?.priorityId);
    setValue("assignedTo",formData?.assignedTo);
    setValue("targetStartDate",formData?.additionalDetails?.targetStartDate);
    setValue("targetEndDate",formData?.additionalDetails?.targetEndDate);
    setValue("actualStartDate",formData?.additionalDetails?.actualStartDate);
    setValue("actualEndDate",formData?.additionalDetails?.actualEndDate);
    setValue("estimatedTime",formData?.additionalDetails?.estimatedTime);
    setValue("actualEndTime",formData?.additionalDetails?.actualEndTime);
  }, [formData, setValue]);

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [targetStartDate, setTargetStartDate] = useState(null);
  const [actualStartDate, setActualStartDate] = useState(null);
  const [priorityData, setPriorityData] = useState(null);

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {
    if(!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.priorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
    }
  }, [authConfig]);




  useEffect(() => {
    setTags((formData?.additionalDetails?.tags) ? (formData?.additionalDetails?.tags).split(',') : [])
  },[formData])

  const [tags, setTags] = useState([]);

  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && inputValue) {
      event.preventDefault();
      if (!tags.includes(inputValue)) {
        setTags([...tags, inputValue]);
        setInputValue("");
      }
    }
  };

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleDelete = (tagToDelete) => () => {
    setTags((tags) => tags.filter((tag) => tag !== tagToDelete));
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3>Task Updated Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
        <div> 
          <h3> Failed to Update task. Please try again later.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    let tasksTags;
    if (Array.isArray(tags)) {
      tasksTags = tags.join(",");
    }

    const remarks = formData?.additionalDetails?.remarksList;
    if (data?.remarks) {
      const newRemarksObject = {
          userId: user?.id,
          remarks: data?.remarks,
          createdOn: user?.createdOn,
      };
      remarks.push(newRemarksObject);
  }

    const fields = {
      id:formData?.id,
      assignedTo: data?.assignedTo,
      additionalDetails: {
        title: data?.title,
        description: data?.description,
        statusId: data?.statusId,
        priorityId: data?.priorityId,
        targetStartDate: data?.targetStartDate,
        targetEndDate: data?.targetEndDate,
        actualStartDate: data?.actualStartDate,
        actualEndDate: data?.actualEndDate,
        estimatedTime: data?.estimatedTime,
        actualEndTime: data?.actualEndTime,
        tags: tasksTags,
        remarksList: remarks,
      },
      statusId: data?.statusId,
    };

    try {
      const response = await auth.taskPatch(fields, handleFailure, handleSuccess);
      fetchUsers();
    } catch (error) {
      console.error("Master Data Creation failed:", error);
      handleFailure();
    }

    onCancel();
  }

  const handleStartDateChange = (date) => {
    setTargetStartDate(date);
  };

  const handleActualStartDateChange = (date) => {
    setActualStartDate(date);
  };

  const currentDate = new Date();
  const formattedDate = `${currentDate.getDate()}-${
    currentDate.getMonth() + 1
  }-${currentDate.getFullYear()}`;

  return (
    <>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog fullScreen open={open} onClose={onCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20  },
          }}
          textAlign={"center"}
        >
          Edit a Task
          <Box sx={{ position: "absolute", top: "0px", right: "36px" }}>
          <CloseExpandIcons
                        expanded={expanded}
                        onToggle={handleToggle}
                      />
              </Box>
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={onCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Task Details"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="title"
                        control={control}
                        defaultValue={formData?.additionalDetails?.title}
                        rules={{ required: "Title is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Title"
                            placeholder="Title"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.title)}
                            helperText={errors.title?.message}
                            aria-describedby="validation-basic-title"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <FormControl fullWidth>
                      <Controller
                        name="description"
                        control={control}
                        defaultValue={formData?.additionalDetails?.description}
                        rules={{ required: "Description is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Description"
                            placeholder="Description"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.description)}
                            helperText={errors.description?.message}
                            aria-describedby="validation-basic-description"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.statusId)}>
                      <InputLabel id="statusId-label">Status</InputLabel>
                      <Controller
                        name="statusId"
                        control={control}
                        defaultValue={formData?.additionalDetails?.statusId}
                        rules={{ required: "Status is required" }}
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="statusId-label"
                            label="Status"
                            id="statusId"
                            size="small"
                          >
                            {statusData?.map((status) => (
                              <MenuItem key={status.id} value={status.id}>
                                {status.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      {errors.statusId && (
                        <FormHelperText
                          id="validation-statusId"
                          sx={{ color: "error.main" }}
                        >
                          {errors.statusId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.priorityId)}>
                      <InputLabel id="priorityId"> Priority</InputLabel>
                      <Controller
                        name="priorityId"
                        control={control}
                        defaultValue={formData?.additionalDetails?.priorityId}
                        rules={{ required: "Priority is required" }} // Validation rules if any
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="priorityId-label"
                            label="Priority"
                            id="priorityId"
                            size="small"
                          >
                            {priorityData?.map((status) => (
                              <MenuItem key={status.id} value={status.id}>
                                {status.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      {errors.priorityId && (
                        <FormHelperText
                          id="validation-priorityId"
                          sx={{ color: "error.main" }}
                        >
                          {errors.priorityId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                      <InputLabel id="assignedTo"> Assigned To</InputLabel>
                      <Controller
                        name="assignedTo"
                        control={control}
                        defaultValue={formData?.assignedTo}
                        rules={{ required: "assigned To is required" }} // Validation rules if any
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="assignedTo-label"
                            label="assignedTo"
                            id="assignedTo"
                            size="small"
                          >
                            {employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .map((emp) => (
                                <MenuItem key={emp.id} value={emp.id}>
                                  {emp.label}
                                </MenuItem>
                              ))}
                          </Select>
                        )}
                      />
                      {errors.assignedTo && (
                        <FormHelperText
                          id="validation-assignedTo"
                          sx={{ color: "error.main" }}
                        >
                          {errors.assignedTo.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid container item xs={12} sm={3} alignItems="center">
                    <Grid item xs={4}>
                      <Typography style={field}>Created By :</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography className="data-field">
                        {user?.firstName} {user?.lastName}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid container item xs={12} sm={3} alignItems="center">
                    <Grid item xs={4}>
                      <Typography style={field}>Created Date:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography className="data-field">
                      {formData?.createdOn ? formData.createdOn.split('T')[0] : ''}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Estimated Details"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="targetStartDate"
                        control={control}
                        defaultValue={formData?.additionalDetails?.targetStartDate}
                        rules={{ required: "target start date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Target start date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.targetStartDate)}
                            helperText={errors.targetStartDate?.message}
                            aria-describedby="targetStartDate"
                            value={targetStartDate || field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleStartDateChange(e.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="targetEndDate"
                        control={control}
                        defaultValue={formData?.additionalDetails?.targetEndDate}
                        rules={{ required: "target End date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Target end date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.targetEndDate)}
                            helperText={errors.targetEndDate?.message}
                            aria-describedby="targetEndDate"
                            value={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                            }}
                            inputProps={{
                              min: targetStartDate // Minimum selectable date
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualStartDate"
                        control={control}
                        defaultValue={formData?.additionalDetails?.actualStartDate}
                        rules={{ required: "Actual start date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual start date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualStartDate)}
                            helperText={errors.actualStartDate?.message}
                            aria-describedby="actualStartDate"
                            value={actualStartDate || field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleActualStartDateChange(e.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualEndDate"
                        control={control}
                        defaultValue={formData?.additionalDetails?.actualEndDate}
                        rules={{ required: "Actual End date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual end date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualEndDate)}
                            helperText={errors.actualEndDate?.message}
                            aria-describedby="actualEndDate"
                            value={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                            }}
                            inputProps={{
                              min: actualStartDate // Minimum selectable date
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>  
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="estimatedTime"
                        control={control}
                        defaultValue={formData?.additionalDetails?.estimatedTime}
                        rules={{ required: "Estimated Time is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Estimated Time"
                            type="time"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.estimatedTime)}
                            helperText={errors.estimatedTime?.message}
                            aria-describedby="estimatedTime"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualEndTime"
                        control={control}
                        defaultValue={formData?.additionalDetails?.actualEndTime}
                        rules={{ required: "Actual End Time is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual End Time"
                            type="time"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualEndTime)}
                            helperText={errors.actualEndTime?.message}
                            aria-describedby="actualEndTime"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Tags and attachments"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth variant="outlined" margin="normal">
                      <Controller
                        name="tags"
                        control={control}
                        // defaultValue={formData?.additionalDetails?.tags}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            size="small"
                            variant="outlined"
                            label={"Tags"}
                            placeholder="Enter text then Hit Enter to add tags..."
                            value={inputValue}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            InputProps={{
                              startAdornment: (
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    gap: "5px",
                                    marginTop:"10px",
                                    maxWidth: "calc(100% - 100px)",
                                  }}
                                >
                                  {tags?.map((tag, index) => (
                                    <Chip
                                      key={index}
                                      label={tag}
                                      onDelete={handleDelete(tag)}
                                    />
                                  ))}
                                </Box>
                              ),
                              style: { flexWrap: "wrap", paddingRight: "50px" },
                            }}
                            error={Boolean(errors.tags)}
                            helperText={errors.tags?.message || " "}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Remarks"}
            body={
              <>
                <Grid item xs={12} style={{ marginTop: "20px" }}>
                  <FormControl fullWidth>
                    <Controller
                      name="remarks"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          rows={4}
                          multiline
                          {...field}
                          label="Remarks"
                          InputLabelProps={{ shrink: true }}
                          inputProps={{ maxLength: 1000 }}
                          error={Boolean(errors.remarks)}
                          aria-describedby="Section2_remarks"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} >
                <RemarksTable remarksList={formData?.additionalDetails?.remarksList} employeesData={employeesData}/>  
                </Grid>
              </>
            }
            expanded={expanded}
          />
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default EditTask;
