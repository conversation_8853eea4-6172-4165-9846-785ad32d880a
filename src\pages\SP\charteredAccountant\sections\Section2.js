// ** React Imports
import { useState, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Divider } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";

// ** Icon Imports
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  email: "",
  lastName: "",
  password: "",
  firstName: "",
};

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const schema = yup.object().shape({
  email: yup.string().email().required(),
  lastName: yup
    .string()
    .min(3, (obj) => showErrors("lastName", obj.value.length, obj.min))
    .required(),
  password: yup
    .string()
    .min(8, (obj) => showErrors("password", obj.value.length, obj.min))
    .required(),
  firstName: yup
    .string()
    .min(3, (obj) => showErrors("firstName", obj.value.length, obj.min))
    .required(),
});

const Section2 = ({ onCancel, setter, defaultData,setUnsavedChanges }) => {
  const { updateEntityServices } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  const [formData, setFormData] = useState(defaultData);

  const [projectRegistrationSupported, setProjectRegistrationSupported] =
    useState(defaultData.projectRegistration.isSupported);
  
  const [quarterlyComplianceSupported, setQuarterlyComplianceSupported] =
    useState(defaultData.quarterlyCompliance.isSupported);
  
  const [changeOfDeveloperSupported, setChangeOfDeveloperSupported] = useState(
    defaultData.changeOfDeveloper.isSupported
  );
  
  const [litigationSupported, setLitigationSupported] = useState(
    defaultData.litigation.isSupported
  );
  
  const [titleReportSupported, setTitleReportSupported] = useState(
    defaultData.titleReport.isSupported
  );
  
  const [caCertificateSupported, setCaCertificateSupported] = useState(
    defaultData.caCertificate.isSupported
  );
  
  const [
    draftAllotmentLetterAndAgreementForSaleSupported,
    setDraftAllotmentLetterAndAgreementForSaleSupported,
  ] = useState(defaultData.draftAllotmentLetterAndAgreementForSale.isSupported);
  
  const [
    staffTrainingRelatedToRERASupported,
    setStaffTrainingRelatedToRERASupported,
  ] = useState(defaultData.staffTrainingRelatedToRERA.isSupported);
  
  const [engineerCertificateSupported, setEngineerCertificateSupported] =
    useState(defaultData.engineerCertificate.isSupported);

    const [selectAll, setSelectAll] = useState(false);

    const handleSelectAll = (event) => {
      const { checked } = event.target;
  
      setProjectRegistrationSupported(checked);
      setQuarterlyComplianceSupported(checked);
      setChangeOfDeveloperSupported(checked);
      setLitigationSupported(checked);
      setTitleReportSupported(checked);
      setCaCertificateSupported(checked);
      setDraftAllotmentLetterAndAgreementForSaleSupported(checked);
      setStaffTrainingRelatedToRERASupported(checked);
      setEngineerCertificateSupported(checked);
  
      setFormData(prev => ({
          ...prev,
          projectRegistration: { ...prev.projectRegistration, isSupported: checked },
          quarterlyCompliance: { ...prev.quarterlyCompliance, isSupported: checked },
          changeOfDeveloper: { ...prev.changeOfDeveloper, isSupported: checked },
          litigation: { ...prev.litigation, isSupported: checked },
          titleReport: { ...prev.titleReport, isSupported: checked },
          caCertificate: { ...prev.caCertificate, isSupported: checked },
          draftAllotmentLetterAndAgreementForSale: { ...prev.draftAllotmentLetterAndAgreementForSale, isSupported: checked },
          staffTrainingRelatedToRERA: { ...prev.staffTrainingRelatedToRERA, isSupported: checked },
          engineerCertificate: { ...prev.engineerCertificate, isSupported: checked }
      }));
  };
  
   

    useEffect(() => {
      if (
        projectRegistrationSupported &&
        quarterlyComplianceSupported &&
        changeOfDeveloperSupported &&
        litigationSupported &&
        titleReportSupported &&
        caCertificateSupported &&
        draftAllotmentLetterAndAgreementForSaleSupported &&
        staffTrainingRelatedToRERASupported &&
        engineerCertificateSupported
      ) {
        setSelectAll(true);
      } else {
        setSelectAll(false);
      }
  }, [
      projectRegistrationSupported,
      quarterlyComplianceSupported,
      changeOfDeveloperSupported,
      litigationSupported,
      titleReportSupported,
      caCertificateSupported,
      draftAllotmentLetterAndAgreementForSaleSupported,
      staffTrainingRelatedToRERASupported,
      engineerCertificateSupported
  ]);
  
  
  const handleOnChange = (event) => {
    const { name, checked } = event.target;

    switch (name) {
      case "projectRegistration":
        setFormData({
          ...formData,
          projectRegistration: {
            ...formData.projectRegistration,
            isSupported: checked,
          },
        });
        break;
      case "quarterlyCompliance":
        setFormData({
          ...formData,
          quarterlyCompliance: {
            ...formData.quarterlyCompliance,
            isSupported: checked,
          },
        });
        break;
      case "changeOfDeveloper":
        setFormData({
          ...formData,
          changeOfDeveloper: {
            ...formData.changeOfDeveloper,
            isSupported: checked,
          },
        });
        break;
      case "litigation":
        setFormData({
          ...formData,
          litigation: { ...formData.litigation, isSupported: checked },
        });
        break;
      case "titleReport":
        setFormData({
          ...formData,
          titleReport: { ...formData.titleReport, isSupported: checked },
        });
        break;
      case "caCertificate":
        setFormData({
          ...formData,
          caCertificate: { ...formData.caCertificate, isSupported: checked },
        });
        break;
      case "draftAllotmentLetterAndAgreementForSale":
        setFormData({
          ...formData,
          draftAllotmentLetterAndAgreementForSale: {
            ...formData.draftAllotmentLetterAndAgreementForSale,
            isSupported: checked,
          },
        });
        break;
      case "staffTrainingRelatedToRERA":
        setFormData({
          ...formData,
          staffTrainingRelatedToRERA: {
            ...formData.staffTrainingRelatedToRERA,
            isSupported: checked,
          },
        });
        break;
      case "engineerCertificate":
        setFormData({
          ...formData,
          engineerCertificate: {
            ...formData.engineerCertificate,
            isSupported: checked,
          },
        });
        break;
    }
    setUnsavedChanges(true)
  };

  async function onSubmit() {
    setUnsavedChanges(false)
    const response = await updateEntityServices(
      { developerSocietyServices: formData },
      () => {
        console.error("CA service Details failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(formData);
    }

    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Project Registration:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="projectRegistration"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={projectRegistrationSupported}
                    onChange={(event) => {
                      setProjectRegistrationSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="projectRegistration"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Quarterly Compliance:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="quarterlyCompliance"
            control={control}
            
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={quarterlyComplianceSupported}
                    onChange={(event) => {
                      setQuarterlyComplianceSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="quarterlyCompliance"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Change of Developer:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="changeOfDeveloper"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={changeOfDeveloperSupported}
                    onChange={(event) => {
                      setChangeOfDeveloperSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="changeOfDeveloper"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Litigation:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="litigation"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={litigationSupported}
                    onChange={(event) => {
                      setLitigationSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="litigation"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Title Report:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="titleReport"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={titleReportSupported}
                    onChange={(event) => {
                      setTitleReportSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="titleReport"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>CA Certificate:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="caCertificate"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={caCertificateSupported}
                    onChange={(event) => {
                      setCaCertificateSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="caCertificate"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>
              Draft Allotment Letter And Agreement for Sale:
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="draftAllotmentLetterAndAgreementForSale"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={draftAllotmentLetterAndAgreementForSaleSupported}
                    onChange={(event) => {
                      setDraftAllotmentLetterAndAgreementForSaleSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="draftAllotmentLetterAndAgreementForSale"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Staff Training Related to RERA:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="staffTrainingRelatedToRERA"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={staffTrainingRelatedToRERASupported}
                    onChange={(event) => {
                      setStaffTrainingRelatedToRERASupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="staffTrainingRelatedToRERA"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Engineer Certificate:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="engineerCertificate"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={engineerCertificateSupported}
                    onChange={(event) => {
                      setEngineerCertificateSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="engineerCertificate"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={onSubmit}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default Section2;
