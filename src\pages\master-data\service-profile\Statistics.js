import {
  Box,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  InputAdornment,
  Tooltip,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
  Typography,
  Menu,
  MenuItem,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from "src/@core/components/mui/chip";
import StatisticsView from "./StatisticsView";
import SearchIcon from "@mui/icons-material/Search";
import DeleteSubServiceDialog from "./DeleteSubServiceDialog";

const Statistics = () => {
  const [servicesStatisticsList, setServicesStatisticsList] = useState([]);
  const [userStatisticsList, setUserStatisticsList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();
  const { servicesData, setServicesData, servicesDataDetails } =
    useContext(AuthContext);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState(null); // Set initial value to null
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClose = () => setOpenDialogContent(false);

  const handleMenuOpen = (event, row) => {
    setAnchorEl(event.currentTarget);
    setCurrentRow(row); // Set the current row when the menu is opened
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> Sub Service Values added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Add Sub Service Values. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.servicesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      getAllServices: true,
      getAllZones: false,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setServicesStatisticsList(response.data?.listValuesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const fetchAll = async (serviceId) => {
    const url = `${getUrl(
      authConfig.getAllServiceProfiles
    )}/${serviceId}/statistics/questions`;
    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "get",
        url: url,
        headers: headers,
      });

      if (response.data) {
        setUserStatisticsList(response.data);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    setPage(direction);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const columns = [
    { field: "name", headerName: "Service Type", flex: 0.8, minWidth: 120 },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.09,
      minWidth: 100,
      renderCell: (params) => {
        const onClickViewProfile = () => {
          fetchAll(currentRow?.id); // Use the current row ID to fetch details
          setOpenDialog(true);
          handleMenuClose(); // Close the menu after action
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              aria-controls="simple-menu"
              aria-haspopup="true"
              onClick={(event) => handleMenuOpen(event, params.row)} // Pass the current row to handleMenuOpen
            >
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              id="simple-menu"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Grid>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={12}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid item xs={12} sm={6} sx={{ textAlign: "flex-start" }}>
                    <Typography variant="h6">
                      Services with a List of Statistical Questions
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Grid
                      container
                      spacing={2}
                      alignItems="center"
                      justifyContent="flex-end"
                    >
                      <Grid item xs={12} sm={9} md={7} lg={6}>
                        <FormControl fullWidth>
                          <Controller
                            name="mainSearch"
                            control={control}
                            render={({ field: { onChange } }) => (
                              <TextField
                                id="mainSearch"
                                placeholder="Search by service name"
                                value={keyword}
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  setKeyword(e.target.value);
                                  setSearchKeyword(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    setSearchKeyword(keyword);
                                    fetchUsers(page, pageSize, searchKeyword);
                                  }
                                }}
                                sx={{
                                  "& .MuiInputBase-root": {
                                    height: "40px",
                                  },
                                }}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="start">
                                      <SearchIcon
                                        sx={{
                                          cursor: "pointer",
                                          marginRight: "-15px",
                                        }}
                                        onClick={() => {
                                          setSearchKeyword(keyword);
                                          fetchUsers(
                                            page,
                                            pageSize,
                                            searchKeyword
                                          );
                                        }}
                                      />{" "}
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <Dialog
            open={openDialog}
            onClose={handleCloseDialog}
            fullWidth
            maxWidth="md"
            scroll="paper"
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 15, md: 21 },
              }}
              textAlign={"center"}
            >
              Edit Statistics Questions
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseDialog}
                  sx={{
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition:
                        "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
              {currentRow ? ( // Ensure currentRow is defined before rendering
                <StatisticsView
                  data={currentRow}
                  names={userStatisticsList}
                  expanded={expanded}
                  onCancel={handleCloseDialog}
                  fetchUsers={fetchUsers}
                  fetchAll={fetchAll}
                />
              ) : (
                <Typography variant="h6" color="error">
                  Error loading data. Please try again.
                </Typography>
              )}
            </DialogContent>
          </Dialog>

          <Divider />
          <CardContent>
            <div style={{ height: 380, width: "100%", overflow: "auto" }}>
              <DataGrid
                rows={servicesStatisticsList}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            </div>
          </CardContent>
        </Card>
      </Grid>
      <Divider />
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default Statistics;
