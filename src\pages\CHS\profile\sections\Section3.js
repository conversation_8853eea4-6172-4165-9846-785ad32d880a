// ** React Imports
import { forwardRef, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import { Box } from "@mui/system";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { Button } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import { yupResolver } from "@hookform/resolvers/yup";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";



// ** Icon Imports

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section3 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();

  const fields = ["fsiConsumedFsi","fsi_AvailableFsi","fsi_PermissibleFsi","dpRestrictions","litigationsOrEncroachment","buildingAge","heightRestriction"]

  const [scheme, setScheme] = useState(formData?.scheme);

  const {
    register,
    handleSubmit,
    setError,
    control,clearErrors,
    formState: { errors },
  } = useForm();

  const schemeOptions = [
    {
      value: "_33_7B",
      key: "33(7B)",
    },
  ];

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }

    const userUniqueId =
      formData && formData.userId !== undefined ? formData?.userId : user?.id;
    const response = await auth.updateEntity(trimmedData, userUniqueId)
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
        <Grid item xs={12} sm={3}>
  <FormControl fullWidth>
    <Controller
      name="fsiConsumedFsi"
      control={control}
      rules={{
        required: false,
        maxLength: { value: 100, message: "Must not exceed 100 characters" },
      }}
      defaultValue={formData?.fsiConsumedFsi}
      render={({ field }) => (
        <TextField
          {...field}
          type="text"
          label="Consumed FSI"
          placeholder="Consumed FSI in sq. ft."
          InputLabelProps={{ shrink: true }}
          size='small'
          error={Boolean(errors.fsiConsumedFsi)}
          helperText={errors.fsiConsumedFsi?.message}
          aria-describedby="validation-basic-fsiConsumedFsi"
          onKeyPress={(event) => {
            if (!/\d/.test(event.key)) {
              event.preventDefault();
            }
          }}
          inputProps={{
            pattern: '[0-9]*',
            maxLength: 6, // allow up to 10 digits
          }}
        />
      )}
    />
  </FormControl>
</Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="fsi_AvailableFsi"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 100, message: "Must not exceed 100 characters" },
                }}
                defaultValue={formData?.fsi_AvailableFsi}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Available FSI"
                    size='small'
                    placeholder="Available FSI in sq. ft."
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.fsi_AvailableFsi)}
                    helperText={errors.fsi_AvailableFsi?.message}
                    aria-describedby="validation-basic-fsi_AvailableFsi"
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 10 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="fsi_PermissibleFsi"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 100, message: "Must not exceed 100 characters" },
                }}
                defaultValue={formData?.fsi_PermissibleFsi}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Permissible FSI"
                    placeholder="permissible FSI in sq. ft."
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.fsi_PermissibleFsi)}
                    helperText={errors.fsi_PermissibleFsi?.message}
                    aria-describedby="validation-basic-fsi_PermissibleFsi"
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 10 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        
          <Grid item xs={12} sm={3}>
            <SelectCategory
             clearErrors={clearErrors}
              register={register}
              id={"scheme"}
              label={"*Scheme"}
              name="scheme"
              nameArray={schemeOptions}
              defaultValue={formData?.scheme}
              value={scheme}
              onChange={(e) => setScheme(e.target.value)}
              error={Boolean(errors.scheme)}
              aria-describedby="validation-scheme"
            />
            {errors.scheme && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-scheme'>
              Please select Scheme
            </FormHelperText>
            )}
          </Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="dpRestrictions"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 200, message: "Must not exceed 200 characters" },
                }}
                defaultValue={formData?.dpRestrictions}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Dp Restrictions"
                    placeholder="Dp Restrictions"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.dpRestrictions)}
                    helperText={errors.dpRestrictions?.message}
                    aria-describedby="validation-basic-dpRestrictions"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="litigationsOrEncroachment"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 200, message: "Must not exceed 200 characters" },
                }}
                defaultValue={formData?.litigationsOrEncroachment}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Litigations /
                        Encroachment"
                    InputLabelProps={{ shrink: true }}
                    size='small'
                    error={Boolean(errors.litigationsOrEncroachment)}
                    helperText={errors.litigationsOrEncroachment?.message}
                    aria-describedby="validation-basic-litigationsOrEncroachment"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="buildingAge"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 100, message: "Must not exceed 100 characters" },
                }}
                defaultValue={formData?.buildingAge}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Building Age"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.buildingAge)}
                    size='small'
                    helperText={errors.buildingAge?.message}
                    aria-describedby="validation-basic-building-age"
                  />
                )}
              />
            </FormControl>
          </Grid>

         
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <Controller
                name="heightRestriction"
                control={control}
                rules={{
                  required: false,
                  maxLength: { value: 100, message: "Must not exceed 100 characters" },
                }}
                defaultValue={formData?.heightRestriction}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Height Restriction"
                    placeholder="Height restriction in feet"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.heightRestriction)}
                    size='small'
                    helperText={errors.heightRestriction?.message}
                    aria-describedby="validation-basic-height-restriction"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Section3;
