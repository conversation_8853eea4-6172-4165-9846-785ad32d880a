
// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";

import { useAuth } from 'src/hooks/useAuth';

// ** Third Party Imports
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  FormHelperText,
  Switch,
  Typography,
} from "@mui/material";
import { Box } from "@mui/system";
import { useState, useEffect, useContext } from "react";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
const EditDialog = ({ open, onClose, formData,page,pageSize,searchKeyword, fetchAllUsers }) => {


  const { listValues } = useContext(AuthContext);

  const [data,setData] = useState(formData)

  const [calculateLoad, setCalculateLoad] = useState(false);
  const [saveLoad, setSaveLoad] = useState(false);
  const [calculatedData,setCalculatedData] = useState("")

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [calculationDialog,setCalculationDialog] = useState(false);
  const [openUpdateDialog,setOpenUpdateDialog] = useState(false);
  
  const [isActive, setIsActive] = useState(data?.isActive);

  const [authority,setAuthority] = useState("")
  const [roadWidth,setRoadWidth] = useState("")
  const [grossPlotArea,setGrossPlotArea] = useState("")

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  useEffect(() => {
    setData(formData)
    
    const loc = formData?.location
        ? listValues.find((item) => item.name === formData?.location)?.id
        : null;
    setLocate(loc)

    setZone(formData?.zone)
  },[formData])
  useEffect(() => {
    setIsActive(data?.isActive);
}, [data?.isActive]);

const handleUpdateDialog = () => {
  setOpenUpdateDialog(false);
};

const handleCloseDialog= () =>{
  setCalculationDialog(false)
  onClose() 
}

   //Hooks
   const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm();


  const [locate,setLocate] = useState(null)
  const [locationId, setLocationId] = useState({
    name: data?.location || "",
    id: data?.id || "",
  });
  const [zone, setZone] = useState(data?.zone);

  const [listOfLocations, setListOfLocations] = useState([]);
  const [locationsOptions, setLocationsOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=LOCATIONS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfLocations(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);
  
  useEffect(() => {
    if (!!listOfLocations) {
      let data = [];
      listOfLocations.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setLocationsOptions(data);
    }
  }, [listOfLocations]);


  const [selectedWard, setSelectedWard] = useState({
    name: data?.ward || "",
    id: data?.id || "",
  });
  const [listOfWards, setListOfWards] = useState([]);

  const wardOptions = listOfWards.map((ward) => ({
    value: ward,
    key: ward?.name,
  }));

  const [selectedType, setSelectedType] = useState({
    name: data?.type || "",
    id: data?.id || "",
  });
  const [listOfTypes, setListOfTypes] = useState([]);

  
  const typeOptions = listOfTypes.map((type) => ({
    value: type,
    key: type?.name,
  }));


  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data?.masterDataResponse);

          const matchingWard = res.data?.masterDataResponse.find(ward => ward?.name === formData?.ward);

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingWard) {
            setSelectedWard(matchingWard);
          } else {
            setSelectedWard(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();

    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);

          const matchingType = res.data?.masterDataResponse.find(type => type?.name === formData?.type);

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingType) {
            setSelectedType(matchingType);
          } else {
            setSelectedType(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();



  }, [formData]);


  const [selectedSociety,setSelectedSociety] = useState({})
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [mainObjectsOfSocieties, setMainObjectsOfSocieties] = useState([]);

  const societyOptions = listOfSocieties.map((society) => ({
    value: society,
    key: society?.name,
  }));

  useEffect(() => {
      const fetchSocieties = async () => {     
        axios({
          method: "get",
          url: getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
          headers: getAuthorizationHeaders(),
        })
          .then((res) => {

            const metadataArray = res.data?.data?.map(item => item?.metaData);
            setMainObjectsOfSocieties(res.data?.data)
            setListOfSocieties(metadataArray);
            const matchingSociety = metadataArray.find(society => society?.name === formData?.societyName);

            // Set the matching location as the defaultValue for the SelectCategory
            if (matchingSociety) {
              setSelectedSociety(matchingSociety);
            }else {
              setSelectedSociety(null);
            }
          })
          .catch((err) => console.log("error", err));
      };
      fetchSocieties(); 
  }, [formData]);

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    setOpenUpdateDialog(false);
  };
  const handleSocietyChange = (newValue) => {
    setSelectedSociety(newValue);
    setData({
      ...data, // This keeps the existing properties
      ...newValue, // This adds/updates properties from newValue
    });

    setZone(newValue?.zone)
    const matchingLocation = listValues?.find(location => location?.name === newValue?.location);

    // Set the matching location as the defaultValue for the SelectCategory
    if (matchingLocation) {
      setLocate(matchingLocation?.id)
      setLocationId(matchingLocation);
    }

    const matchingWard = listOfWards?.find(ward => ward?.name === newValue?.ward);

    // Set the matching ward as the defaultValue for the SelectCategory
    if (matchingWard) {
      setSelectedWard(matchingWard);
    } else {
      setSelectedWard(null);
    }

    const matchingType = listOfTypes?.find(type => type?.name === newValue?.type);

    // Set the matching type as the defaultValue for the SelectCategory
    if (matchingType) {
      setSelectedType(matchingType);
    } else {
      setSelectedType(null);
    }
  };


  const handleLocationChange = (newValue) => {
    const matchedLocation = listValues.find((item) => item.id === newValue);

    // If a matching object is found, setLocationId with the entire object
    if (matchedLocation) {
      setLocationId(matchedLocation);
    } else {
      console.error("No matching location found in listValues");
    }
    setLocate(newValue)
    const selectedLocation = listOfLocations.find(
      (location) => location.id === newValue
    );
    if (selectedLocation) {
      const zone = selectedLocation.zoneId
        ? listValues.find((item) => item.id === selectedLocation.zoneId)?.name
        : null;
      setZone(zone);
    }
  };

  const handleUpdateSuccess = () => {
    const message = `
      <div> 
        <h3> Calculated FSI updated Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleUpdateFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Update Calculated FSI. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function Submit(data) {
    setCalculationDialog(true);
    setCalculateLoad(true);
    let fields;
    const matchedLoc = listValues.find((item) => item.id === locate);

      fields = {
        id: formData?.id,
        userId: formData?.userId,
        isActive: isActive,
        societyName: selectedSociety?.name,
        location: matchedLoc?.name,
        ward: selectedWard?.name,
        type:selectedType?.name,
        zone:zone,
        saveInTable:false,
        updateInProfile:false,
        authority: data?.authority,
        roadWidth: data?.roadWidth,
        grossPlotArea: data?.grossPlotArea,
      };
    
    const response = await auth.patchCalculatedFsi(fields, () => {
      console.error(" Calculated FSI Details failed");
    });

    setCalculatedData(response)
    

    fetchAllUsers(page,pageSize,searchKeyword);
    
    
    setCalculateLoad(false);
    
    reset();
   
  }


  async function SaveCalculation(data) {
    setOpenUpdateDialog(false)
    setSaveLoad(true);
    let fields;


      fields = {
        id: formData?.id,
        userId: formData?.userId,
        isActive: isActive,
        societyName: selectedSociety?.name,
        location: locationId?.name,
        ward: selectedWard?.name,
        zone:zone,
        type:selectedType?.name,
        saveInTable:true,
        updateInProfile:true,
        authority: authority ? authority : formData?.authority,
        roadWidth: roadWidth ? roadWidth : formData?.roadWidth,
        grossPlotArea: grossPlotArea ? grossPlotArea : formData?.grossPlotArea,
      };
    
    const response = await auth.patchCalculatedFsi(fields, () => {
      console.error(" Calculated FSI Details failed");
    });

    if(response){
      handleUpdateSuccess();
    }else{
      handleUpdateFailure();
    }


    fetchAllUsers(page,pageSize,searchKeyword);
    
    setCalculationDialog(false)
    setSaveLoad(false);
    onClose();
    reset();
  }

  async function SaveCalculate(data) {
    setOpenUpdateDialog(false)
    setSaveLoad(true);
    let fields;


      fields = {
        id: formData?.id,
        userId: formData?.userId,
        isActive: isActive,
        societyName: selectedSociety?.name,
        location: locationId?.name,
        ward: selectedWard?.name,
        type:selectedType?.name,
        zone:zone,
        saveInTable:true,
        updateInProfile:false,
        authority: authority ? authority : formData?.authority,
        roadWidth: roadWidth ? roadWidth : formData?.roadWidth,
        grossPlotArea: grossPlotArea ? grossPlotArea : formData?.grossPlotArea,
      };
    
    const response = await auth.patchCalculatedFsi(fields, () => {
      console.error(" Calculated FSI Details failed");
    });

    if(response){
      handleUpdateSuccess();
    }else{
      handleUpdateFailure();
    }

    fetchAllUsers(page,pageSize,searchKeyword);
    
    setCalculationDialog(false)
    setSaveLoad(false);
    
    reset();
    onClose();
  }

  const handleUpdateProfile = () => {
    const message = `
      <div> 
        <h3> Do you want to update the profile data with this.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenUpdateDialog(true);
  };

  

  return (
    <>
      <Dialog
        open={open}
        onClose={handleCloseDialog}
        fullWidth
        maxWidth="md"
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent:  "start" ,
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Edit Calculated FSI
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            ":playing": (theme) => `${theme.spacing(4)} !important`,
            px: (theme) => [
              `${theme.spacing(6)} !important`,
              `${theme.spacing(10)} !important`,
            ],
          }}
        >
          <Grid container spacing={3}>
            
              <Grid item xs={12} sm={6}>
              <SelectCategory
                register={register}
                id="society"
                label="Select Society"
                name="society"
                nameArray={societyOptions}
                value={selectedSociety}
                defaultValue={selectedSociety}
                onChange={(e) => handleSocietyChange(e.target.value)}
                clearErrors={clearErrors}
              />
              {errors.society && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-society"
                >
                  {errors.society?.message}
                </FormHelperText>
              )}
            </Grid>
           
             
              <Grid item xs={12} sm={6}>

            <FormControl fullWidth>
                    <Controller
                      name="authority"
                      control={control}
                      rules={{ required: { value: true, message: "Authority is required" },                       
                      }}
                      defaultValue={data?.authority}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("authority", data?.authority);
                        }, [data?.authority, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value.replace(/\s/g, ''); 
                          if (/^[a-zA-Z]*$/.test(inputValue)) { 
                            field.onChange(inputValue); // Update the field value
                            setAuthority(inputValue)
                          }
                        };

                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Authority"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your authority"
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="validation-basic-authority"
                            inputProps={{ maxLength: 50 }}
                            onChange={handleInputChange}
                          />
                        );
                      }}
                    />
                  </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <SelectCategory
              register={register}
              clearErrors={clearErrors}
              id={"location"}
              label={" Location "}
              name="location-select"
              nameArray={locationsOptions}
              defaultValue={locate}
              value={locate}
              onChange={(event) => handleLocationChange(event.target.value)}
              aria-describedby="location"
            />
          </Grid>
          <Grid container item xs={12} sm={6} spacing={2}>
            <Grid item>
              <Typography className="data-field">Zone:</Typography>
            </Grid>
            <Grid item>
              <Typography style={{ fontWeight: "bold" }}>{zone}</Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6}>
                  <SelectCategory
                    register={register}
                    id="type"
                    label="Select Type"
                    name="type"
                    nameArray={typeOptions}
                    value={selectedType}
                    defaultValue={selectedType}
                    onChange={(e) => {
                      setSelectedType(e.target.value);
                    }}
                    clearErrors={clearErrors}
                  />
                  {errors.type && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-type"
                    >
                      {errors.type?.message}
                    </FormHelperText>
                  )}

                </Grid>
                <Grid item xs={12} sm={6}>
                  <SelectCategory
                    register={register}
                    id="ward"
                    label="Choose Your Ward"
                    name="ward"
                    nameArray={wardOptions}
                    value={selectedWard}
                    defaultValue={selectedWard}
                    onChange={(e) => {
                      setSelectedWard(e.target.value);
                    }}
                    clearErrors={clearErrors}
                  />
                  {errors.ward && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-ward"
                    >
                      {errors.ward?.message}
                    </FormHelperText>
                  )}
                </Grid>
              <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="roadWidth"
                      control={control}
                      rules={{
                        required: { value: true, message: "Road width is required" },
                        validate: value => parseFloat(value) > 0 || "Road width should be greater than 0"
                      }}
                      defaultValue={data?.roadWidth}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("roadWidth", data?.roadWidth);
                        }, [data?.roadWidth, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value;
                          if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) { // Check if input matches the pattern
                            field.onChange(inputValue); // Update the field value
                            setRoadWidth(inputValue)
                          }
                        };


                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Road Width"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter road width"
                            inputProps={{ maxLength: 10 }}
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="validation-basic-roadWidth"
                            onChange={handleInputChange}
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>

                {/* Plot Area Field */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="grossPlotArea"
                      control={control}
                      rules={{ 
                        required: { value: true, message: "plot area is required" },
                        validate: value => parseFloat(value) > 0 || "plot area should be greater than 0"
                      }}
                      defaultValue={data?.grossPlotArea}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("grossPlotArea", data?.grossPlotArea);
                        }, [data?.grossPlotArea, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value;
                          if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) { // Check if input matches the pattern
                            field.onChange(inputValue); // Update the field value
                            setGrossPlotArea(inputValue)
                          }
                        };

                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Net Plot Area in Sq mts"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter Net Plot Area"
                            inputProps={{ maxLength: 10 }}
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="validation-basic-grossPlotArea"
                            onChange={handleInputChange}
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} >
                <Controller
                  name="isActive"
                  control={control}
                  render={() => (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={isActive}
                          onChange={handleOnChange}
                          name="isActive"
                        />
                      }
                      label="Is Active"
                    />
                  )}
                />
              </Grid>
          </Grid> 
          {calculationDialog && (
              <Grid  container spacing={2}>
                <Grid item xs={12} sm={12}  style={{fontWeight: 'bold', marginTop: '15px', marginBottom: '2px'}}>Calculated FSI Details: </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography>The maximum FSI under 33(7) / 33(7)B for your Society/ Plot is :
                       </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{fontWeight: 'bold'}}>{calculatedData?.fungibleFSI}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography> Built Up Area available on your Plot as per FSI (sq mts) :  </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{fontWeight: 'bold'}}>{calculatedData?.buildupArea}</Typography>
                  </Grid>
              </Grid>
            )}      
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.85)} !important`,
          }}
        >
          <center>
          <Button
                display="flex"
                justifyContent="center"
                sx={{ mr: 3 }}
                variant="contained"
                color="primary"
                onClick={handleUpdateProfile}
              >
                {saveLoad ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Save"
              )}
              </Button>
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(Submit)}
            >
               {calculateLoad ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Calculate FSI"
              )}
            </Button>
          </center>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openUpdateDialog}
        onClose={handleUpdateDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
          <Button
              variant="contained"
              onClick={SaveCalculation}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={SaveCalculate}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default EditDialog;
