// ** MUI Components
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import ApexChartWrapper from 'src/@core/styles/libs/react-apexcharts'

// ** Demo Imports
import CardStatisticsTransactions from 'src/views/ui/cards/statistics/CardStatisticsTransactions'
import ProfessionalsSlide from 'src/views/pages/professionals/ProfessionalsSlide'
import WhyEmpel from 'src/views/pages/professionals/WhyEmpel'
import WhatYouGet from 'src/views/pages/professionals/WhatYouGet'

// ** Styled Components

const BoxWrapper = styled(Box)(({ theme }) => ({
  minHeight: '100vh',

  // For V1 Blank layout pages
  '& .content-center': {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(5),
    minHeight: `calc(100vh - ${theme.spacing(theme.mixins.toolbar.minHeight / 4)})`
  },

  // For V2 Blank layout pages
  '& .content-right': {
    display: 'flex',
    overflowX: 'hidden',
    position: 'relative',
    minHeight: `calc(100vh - ${theme.spacing(theme.mixins.toolbar.minHeight / 4)})`
  }
}))

const Img = styled('img')(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    height: 300,
    marginTop: theme.spacing(2)
  },
  [theme.breakpoints.up('lg')]: {
    height: 400,
    marginTop: theme.spacing(5)
  }
}))

const Professionals = () => {
  return (
    <>
      <BoxWrapper>
        <Box className='content-center' sx={{ px: '0 !important', minHeight: '68vh !important' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              width: { xs: '100vw', sm: '85vw' }
            }}
          >
            <Box
              sx={{
                p: { xs: '1.5rem', sm: '3rem' },
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                justifyContent: 'center',
                width: { xs: '100vw', sm: '65%' }
              }}
            >
              <Typography variant='h2' sx={{ mb: 6, fontWeight: '700' }}>
                Make your business visible to a 1,00,000 crore market
              </Typography>
              <Typography variant='h6' sx={{ mb: 4, color: '#777' }}>
                Enroll with Houzer, your entry ticket to the Housing Societies Market.
              </Typography>
            </Box>
            <Box sx={{ width: { xs: '95vw', sm: '35%' }, display: 'flex', justifyContent: 'center' }}>
              <Img alt='banner-image' src='/images/pages/create-deal-review-complete.png' />
            </Box>
            {/* <Img height='500' alt='error-illustration' src='/images/pages/404.png' /> */}
          </Box>
          {/* <FooterIllustrations /> */}
        </Box>
        <Box
          className='content-center'
          sx={{
            padding: '0 !important',
            minHeight: { xs: '30vh', sm: '22vh !important' },
            backgroundColor: 'rgb(51 48 60 / 4%)'
          }}
        >
          <Box sx={{ width: '80vw', textAlign: 'center' }}>
            <ApexChartWrapper>
              <Grid container spacing={6}>
                <Grid item xs={12}>
                  <CardStatisticsTransactions />
                </Grid>
              </Grid>
            </ApexChartWrapper>

            {/* <FooterIllustrations /> */}
          </Box>
        </Box>
        {/* Second Slide */}
        <Box className='content-center' sx={{ px: '0 !important', minHeight: '100vh !important' }}>
          <ProfessionalsSlide />
        </Box>
        {/* End */}
        {/* Fourth slide */}
        <Box className='content-center' sx={{ px: '0 !important', minHeight: '100vh !important' }}>
          <WhyEmpel />
        </Box>
        <Box className='content-center' sx={{ px: '0 !important', minHeight: '100vh !important' }}>
          <WhatYouGet />
        </Box>
        {/* Footer */}
        {/* <Footer /> */}
      </BoxWrapper>
    </>
  )
}


export default Professionals
