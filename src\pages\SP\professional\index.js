// ** MUI Imports
import { useState, useEffect } from "react";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { styled } from "@mui/material/styles";
import TableCell from "@mui/material/TableCell";
import { useTheme } from "@emotion/react";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Styled Component
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

import {
  Box,
  Button,
  CardContent,
  Table,
  TableBody,
  TableContainer,
  TableRow,
} from "@mui/material";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import PageHeader from "src/@core/components/page-header";
import SourceProfessional from "./sections/SourceProfessional";
import ProfessionalDetails from "./sections/ProfessionalDetails";
import ProfessionalServices from "./sections/ProfessionalServices";
import TypeOfDevelopment from "./sections/TypeOfDevelopment";
import ProfessionalRequirements from "./sections/ProfessionalRequirements";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const Professional = () => {
  const { entityData, getEntityProfile } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = (value) => {
    setExpanded(value); 
  };

  useEffect(() => {
    getEntityProfile();
    console.log("use effect");
  }, []);

  return (
    <>
      <style>
        {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
      </style>

      <DatePickerWrapper>
        <Grid container spacing={6} className="match-height"
        sx={{'& .MuiPaper-root.MuiAccordion-root':{margin:'0 !important'}}} >
              <Grid item xs={12} sx={{ position: "relative" }}>
                <PageHeader
                  title={
                    <Typography variant="h5">
                      Professional Requirement Registration
                    </Typography>
                  }
                  subtitle={<Typography variant="body2"></Typography>}
                />
                <Box sx={{ position: "absolute", top: 18, right: 0 }}>
                  <CloseExpandIcons expanded={expanded} onToggle={handleToggle} />
                </Box>
              </Grid>
          <Grid item xs={12}>
            <SourceProfessional
              data={entityData}
              expanded={expanded}
            ></SourceProfessional>
          </Grid>
          <Grid item xs={12}>
            <ProfessionalDetails
              data={entityData}
              expanded={expanded}
            ></ProfessionalDetails>
          </Grid>

          <Grid item xs={12}>
            <ProfessionalServices
              data={entityData}
              expanded={expanded}
            ></ProfessionalServices>
          </Grid>

          <Grid item xs={12}>
            <TypeOfDevelopment
              data={entityData}
              expanded={expanded}
            ></TypeOfDevelopment>
          </Grid>

          <Grid item xs={12}>
            <ProfessionalRequirements
              data={entityData}
              expanded={expanded}
            ></ProfessionalRequirements>
          </Grid>
        </Grid>
      </DatePickerWrapper>
    </>
  );
};

export default Professional;
