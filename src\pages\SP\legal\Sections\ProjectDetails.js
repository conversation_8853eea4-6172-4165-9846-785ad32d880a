import { useState, useEffect } from 'react'
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import { Box, Button, CardContent, Table, TableBody, TableContainer, TableRow } from '@mui/material'

// ** MUI Imports
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'

import { styled, useTheme } from '@mui/material/styles'
import TableCell from '@mui/material/TableCell'
import Typography from '@mui/material/Typography'
import Section4 from 'src/pages/SP/architect/sections/Section4'


// ** Third Party Imports
import * as yup from 'yup'
import toast from 'react-hot-toast'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'

// ** Icon Imports
import Icon from 'src/@core/components/icon'
import MUITableCell from '../../MUITableCell'
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ProjectDetails = ({data,expanded}) =>  {
  const { can } = useRBAC();
    const theme = useTheme()
    const [state3, setState3] = useState('view')

    const viewClick3 = () => {
        setState3('edit')
    }

    const editClick3 = () => {
        setState3('view')
    }

    return(
        <>
         {/* {can('legal_projectDetails_READ') && */}
            <AccordionBasic
                id={'panel-header-1'}
                ariaControls={'panel-content-1'}
                heading={'Past Projects'}
                body={
                  <>
                    {state3 === 'view' && (
                      <Grid item xs={12}>
                        
                          <TableContainer
                            sx={{ padding: '4px 6px' }}
                            className='tableBody'
                            // onClick={can('legal_projectDetails_UPDATE') ? viewClick3 : null}
                            onClick={viewClick3}
                          >
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>No. of projects completed:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfProjectsCompleted}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Constructed area of completed projects:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.constructedAreaOfCompletedProjects}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>No of ongoing projects:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfOngoingProjects}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Constructed area of ongoing projects:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.constructedAreaOfOngoingProjects}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Landmark projects names:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.landmarkProjectsNames}</Typography>
                                  </MUITableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                      </Grid>
                    )}
                    {state3 === 'edit' && <Section4 formData={data} onCancel={editClick3} />}
                  </>
                }
                expanded={expanded}
              />
          {/* } */}
        </>
    )

}
export default ProjectDetails