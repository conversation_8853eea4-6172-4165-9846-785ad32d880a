// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section6 from './Section6'
import { useTheme } from '@emotion/react'
import MUITableCell from "src/pages/SP/MUITableCell";
import { Table, TableBody, TableContainer, TableRow } from '@mui/material'
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const OtherDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
  const theme = useTheme()

  const [state6, setState6] = useState(true)

  const handleState6 = () => {
    setState6(!state6)
  }
    
    // Pre-Populating code Start
    // const [otherDetails, setOtherDetails] = useState({
    //     professionalDetails:"",
    //     redevelopment:"",
    //     documentsAvailable:""
    //   });

    return (
        <>
        {/* {can('society_otherDetails_READ') && */}
         <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Other Details'}
                body={
                  <>
                    {state6 && (
                     <TableContainer sx={{ padding:'4px 6px' }}
                            className='tableBody'
                            //onClick={can('society_otherDetails_UPDATE') ? handleState6 : null}>
                            onClick={ handleState6}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Do you have following Documents?</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.documentsAvailable}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Professional Details:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.professionalDetails}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Have You taken the steps towards redevelopment?
                                      If yes, Elaborate:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.redevelopment}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {!state6 && <Section6 formData={data} onCancel={handleState6} />}
                  </>
                }
                expanded={expanded}
              />
        {/* } */}
        </>
    );

}
export default OtherDetails;