// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { Button } from "@mui/material";
// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";
import { hexToRGBA } from "src/@core/utils/hex-to-rgba";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import YouTubeCard from "src/@core/components/custom-components/YouTubeCard";
import { Card, TextField } from "@mui/material";
import ProjectsTabs from "./ProjectsTabs";
import LinearProgress from "@mui/material/LinearProgress";
import CircularProgress from "@mui/material/CircularProgress";
import InsightsSlider from "./InsightsSlider";
import ProfileTestimonial from "./ProfileTestimonial";

// ** Demo
import { TableCell } from "@mui/material";
import { styled } from "@mui/material/styles";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import Tab from "@mui/material/Tab";
import TabList from "@mui/lab/TabList";
import TabContext from "@mui/lab/TabContext";
import { useRBAC } from "src/pages/permission/RBACContext";
import AwardView from "./sections/AwardView";
import MemberShipView from "./sections/MemberShipView";
import TestimonialView from "./sections/TestimonialsView";
import EducationalInsightsView from "./sections/EducationalInsightsView";
import AreaOfExperties from "./sections/AreaOfExpertiseView";
import axios from "axios";
import { getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import ProjectView from "./sections/ProjectView";
import FieldsView from "./sections/FieldsView";
import PreviewServicesTabs from "./PreviewServicesTabs";
import ServicesTabs from "./sections/ServiceTabs";
import StatisticsParent from "src/pages/statistics";
import { useTheme } from "@mui/material/styles";
import CustomSlider from "./CustomSlider";
import StatisticsTabs from "./StatisticsTabs";

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CardContent,
  Box,
} from "@mui/material";
import BoxWrapper from "src/pages/styles/BoxWrapper";

// Styled component for MUI TableCell
const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const basic_details = {
  name: "Architect Name",
  company: "Chidhagni Pvt Ltd",
  address: "Hyderabad",
  website: "chidhagni.com",
  contact: "9001329151",
  email: "<EMAIL>",
};

const basic_schema = {
  location: "Island, Western Suburb",
  design: "Residential, Retail",
  liasoning: "MCGM, MHADA",
  years_of_experience: "5-10 years",
  awards: "Excellence in Residential",
  brief_profile: "I am a professional in Houzer!",
  indicative_fees_per_sqft: "1000",
};

const Index = () => {
  // Destructuring methods and data from AuthContext
  const {
    getEntityProfile,
    user,
    getBasicProfileData,
    basicProfileGetData,
    micrositeBasicData,
    micrositeGetEndpoint,
    projectsData,
    fetchUserProjects,
    microSiteGet,
  } = useContext(AuthContext);

  const { can } = useRBAC();
  const router = useRouter();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [micrositeData, setmicrositeData] = useState([]);
  const [insightVideos, setInsightVideos] = useState([]);
  const [documentSubCategory, setDocumentSubCategory] = useState(null);
  const [documentCategory, setDocumentCategory] = useState(null);
  const [awardData, setAwardData] = useState(null);
  const [listValues, setListValues] = useState(null);
  const [serviceNamesWithIds, setServiceNamesWithIds] = useState([]);
  const [companyData, setCompanyData] = useState(null);
  const [servicesFiltered, setServicesFiltered] = useState([]);
  const [projectsFiltered, setProjectsFiltered] = useState([]);
  const [updatedServicesFiltered, setUpdatedServicesFiltered] = useState([]);
  const [serviceNames, setServiceNames] = useState([]);
  const [projectsImages, setProjectsImages] = useState({});
  const [value, setValue] = useState("0");
  const [locationField, setLocationField] = useState(null);
  const [finalUser, setFinalUser] = useState({});
  const [statisticsData, setStatisticsData] = useState({});
  const [serviceId, setServiceId] = useState(null);
  const [listNames, setListNames] = useState(null);
  const [servicesImages, setServicesImages] = useState([]);
  const [projectData, setProjectData] = useState(null);

  // Refs to point to specific sections/tabs
  const introductionRef = useRef(null);
  const aboutUsRef = useRef(null);
  const awardsRef = useRef(null);
  const membershipRef = useRef(null);
  const ourexpertiseRef = useRef(null);
  const projectsRef = useRef(null);
  const servicesRef = useRef(null);
  const insightsRef = useRef(null);
  const testimonialsRef = useRef(null);

  // Function to close the dialog
  const handleCancelClick = () => {
    setOpenDialog(false);
  };

  // Function to handle tab change for slider movement
  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };

  // Function to preview microsite and open dialog
  const handleMicrositePreview = () => {
    setLoading(true); // Set loading to true when the button is clicked
    fetchMicrosite(() => {
      setLoading(false);
    });
    setOpenDialog(true);
  };

  // Function to toggle expand/collapse
  const handleToggle = (value) => {
    setExpanded(value);
  };

  function isYouTubeUrl(url) {
    // Regular expression to match YouTube URLs
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)[\w-]{11}$/;
    // Test the URL against the regular expression
    return youtubeRegex.test(url);
  }

  // Fetch microsite data on component mount
  useEffect(() => {
    micrositeGetEndpoint();
  }, []); // Static array of company images
  const companyImages = [
    {
      imageURL: "/images/houzer-webp/electrical.webp",
    },
    {
      imageURL: "/images/houzer-webp/handyman-prepare.webp",
    },
    {
      imageURL: "/images/houzer-webp/electrician.webp",
    },
  ];

  useEffect(() => {
    // Setting a timeout of 5 seconds to simulate loading time
    const timer = setTimeout(() => {
      setLoading(false); // Set loading to false after 5 seconds
    }, 10000);

    // Cleanup timer
    return () => clearTimeout(timer);
  }, []);

  // Fetch list values on component mount
  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, []);

  // Get service names with ids from basicProfileGetData
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  // Get location name from list values based on location id
  useEffect(() => {
    const locationId = micrositeData?.basicProfile?.location;
    const location = listValues?.find((item) => item?.id === locationId);
    if (location) setLocationField(location.name);
  }, [micrositeData?.basicProfile?.location, listValues]);

  // Fetch basic profile data on component mount
  useEffect(() => {
    getBasicProfileData();
  }, []);

  const micrositeId = user?.id || "";
  useEffect(() => {
    const fetchData = async () => {
      try {
        const docCatId = documentCategory.id;

        // First API call with first set of query parameters
        const queryParams1 = {
          documentCategory: docCatId,
          documentSubCategory: documentSubCategory?.find(
            (obj) => obj?.documentSubCategory === "company"
          ).id,
          userId: micrositeId,
        };

        const response1 = await axios.post(
          getUrl(authConfig.getAllDocumentsEndpoint),
          null,
          { params: queryParams1 }
        );
        const documentResponse1 = response1.data;
        setCompanyData(documentResponse1);

        // Second API call with second set of query parameters for project data
        const queryParams2 = {
          documentCategory: docCatId,
          documentSubCategory: documentSubCategory?.find(
            (obj) => obj?.documentSubCategory === "projectsList"
          ).id,
          userId: micrositeId,
        };

        const response2 = await axios.post(
          getUrl(authConfig.getAllDocumentsEndpoint),
          null,
          { params: queryParams2 }
        );
        const documentResponse2 = response2.data;
        setProjectData(documentResponse2);

        // Third API call with third set of query parameters for award data
        const queryParams3 = {
          documentCategory: docCatId,
          documentSubCategory: documentSubCategory?.find(
            (obj) => obj?.documentSubCategory === "awards"
          ).id,
          userId: micrositeId,
        };

        const response3 = await axios.post(
          getUrl(authConfig.getAllDocumentsEndpoint),
          null,
          { params: queryParams3 }
        );
        const documentResponse3 = response3.data;
        setAwardData(documentResponse3);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (!!documentCategory && !!documentSubCategory && !!micrositeId) {
      fetchData();
    }
  }, [documentCategory, documentSubCategory, micrositeId]);

  // Update service names with ids from architect data and list values
  useEffect(() => {
    const namesWithIds = micrositeData?.basicProfile?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item?.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);

    setServiceNamesWithIds(namesWithIds);
  }, [micrositeData?.basicProfile?.servicesProvided, listValues]);

  useEffect(() => {
    // this scroll to middle is used to caliculate the screen size and the section fits to screen
    const scrollToMiddle = (ref, offset = 0) => {
      if (ref.current) {
        const dialogContent = document.querySelector(".MuiDialogContent-root");
        if (dialogContent) {
          const elementPosition = ref.current.offsetTop + offset;
          const scrollPosition =
            elementPosition -
            dialogContent.clientHeight / 2 +
            ref.current.clientHeight / 2;
          dialogContent.scrollTo({
            top: scrollPosition,
            behavior: "smooth",
          });
        }
      }
    };

    // Scroll to the selected tab content when the value changes
    switch (value) {
      case "0":
        introductionRef.current &&
          introductionRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(introductionRef);
        break;
      case "1":
        aboutUsRef.current &&
          aboutUsRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(aboutUsRef, -50);
        break;
      case "2":
        awardsRef.current &&
          awardsRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(awardsRef, -60);
        break;
      case "3":
        membershipRef.current &&
          membershipRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(membershipRef, -50);
        break;
      case "4":
        ourexpertiseRef.current &&
          ourexpertiseRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(ourexpertiseRef, -50);
        break;
      case "5":
        servicesRef.current &&
          servicesRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(servicesRef, -50);
        break;
      case "6":
        projectsRef.current &&
          projectsRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(projectsRef, -50);
        break;
      case "7":
        insightsRef.current &&
          insightsRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(insightsRef, -50);
        break;
      case "8":
        testimonialsRef.current &&
          testimonialsRef.current.scrollIntoView({
            behavior: "smooth",
            inline: "nearest",
          });
        scrollToMiddle(testimonialsRef, -50);
        break;
      default:
        break;
    }
  }, [value]);

  // Fetch user projects when the component mounts
  useEffect(() => {
    fetchUserProjects(user?.id);
  }, []);

  const userId = user?.id || "";

  // Function to fetch microsite data using the userId
  function fetchMicrosite(callback) {
    const fetchMicrositeRecord = async () => {
      await axios
        .get(getUrl(authConfig.microSiteGet) + "?userId=" + userId)
        .then((res) => {
          setmicrositeData(res?.data);
          callback(); // Call the callback after data is fetched
        })
        .catch((error) => {
          console.log("Error fetching micrositeData:", error);
          callback(); // Also call the callback in case of an error
        });
    };
    if (!!userId) {
      fetchMicrositeRecord();
    }
  }

  // Function to fetch statistics data using micrositeId
  const getStatisticsData = async () => {
    try {
      const res = await axios.get(
        getUrl(authConfig.getStatisticsData) + "/" + micrositeId
      );
      setStatisticsData(res.data);
    } catch (error) {
      console.error("Error fetching project images:", error);
    }
  };

  // Function to handle statistics tab change
  const StatisticsTabChange = (serviceId) => {
    getStatisticsData(micrositeId);
    setServiceId(serviceId);
  };

  // Update final user data when statistics data or serviceId changes
  useEffect(() => {
    if (statisticsData && statisticsData.length > 0) {
      // Find the user whose serviceNameId matches the serviceId
      const userWithMatchingService = statisticsData.find(
        (user) => user.serviceNameId === serviceId
      );
      if (userWithMatchingService) {
        // If a user is found, set the user data in the state variable
        setFinalUser(userWithMatchingService);
      }
    }
  }, [statisticsData, serviceId]);

  // Fetch list values and list names on component mount
  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    }).then((res) => {
      setListValues(res.data.data);
      window.localStorage.setItem(
        authConfig.listValues,
        JSON.stringify(res.data.data)
      );
    });

    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_NAMES",
    }).then((res) => {
      setListNames(res.data.data);
      window.localStorage.setItem(
        authConfig.listNames,
        JSON.stringify(res.data.data)
      );
    });
  }, []);
  console.log(".....", listNames);

  // Utility function to generate key-name pairs from key-value pairs
  const generateKeyNamePairs = (keyValuePairs, listValues) => {
    const transformedData = {};

    for (const key in keyValuePairs) {
      if (Array.isArray(keyValuePairs[key])) {
        transformedData[key] = keyValuePairs[key].map((id) => {
          const listItem = listValues?.find((item) => item?.id === id);
          return listItem ? listItem?.name : null;
        });
      }
    }

    return transformedData;
  };

  useEffect(() => {
    const extractedData = extractServiceListValues(updatedServicesFiltered);
    const generatedData = generateKeyNamePairs(extractedData, listValues);
    setServicesImages(generatedData);
  }, [updatedServicesFiltered, listValues]); //this useeffect is to get the servicesdata wth respective images in microsite preview

  useEffect(() => {
    if (!servicesFiltered) return; // Return if servicesFiltered is null or undefined

    const updatedServices = servicesFiltered?.map((service) => {
      // Find the corresponding listValue object
      const matchedListValue = listValues?.find(
        (listValue) => listValue?.id === service?.serviceNameId
      );

      // If a match is found, add the serviceName field, else leave it empty
      const serviceName = matchedListValue ? matchedListValue?.name : "";

      const serviceNameValues = [];
      service.metadata.listNames?.forEach((listItem) => {
        const matchedListName = listNames?.find(
          (listName) => listName?.id === listItem?.listNameId
        );
        if (matchedListName) {
          serviceNameValues.push(matchedListName?.name);
        }
      });

      return {
        ...service,
        serviceName,
        serviceNameValues,
      };
    });

    setUpdatedServicesFiltered(updatedServices);
  }, [servicesFiltered, listNames, listValues]);

  // Function to extract service list values from updated services
  const extractServiceListValues = (updatedServicesFiltered) => {
    if (!Array.isArray(updatedServicesFiltered)) {
      console.error("Error: 'updatedServicesFiltered' is not an array.");

      return {};
    }

    const transformedData = {};
    updatedServicesFiltered?.forEach((service) => {
      if (service && service?.metadata && service?.metadata?.listNames) {
        service.metadata.listNames.forEach((nameObj) => {
          const Name = nameObj?.listNameId
            ? listNames?.find((item) => item?.id === nameObj?.listNameId)?.name
            : null;
          if (
            Name &&
            Name.toLowerCase().includes("sub services") &&
            nameObj &&
            nameObj?.listValues
          ) {
            if (!transformedData[service.serviceName]) {
              transformedData[service.serviceName] = [];
            }
            transformedData[service.serviceName].push(
              ...nameObj?.listValues?.map((item) => item?.listValueId)
            );
          }
        });
      }
    });

    return transformedData;
  };

  // Filter and set services and projects data based on user data
  useEffect(() => {
    const filteredServices = micrositeData?.servicesOffered?.filter(
      (obj) =>
        obj?.userServicesDataGroup === "SERVICES" && obj?.isActive === true
    );
    setServicesFiltered(filteredServices);
    const filteredProjects = micrositeData?.servicesOffered?.filter(
      (obj) => obj?.userServicesDataGroup === "PROJECTS"
    );
    setProjectsFiltered(filteredProjects);
  }, [micrositeData?.servicesOffered]);

  // Handle tab change for fetching project images based on serviceId
  const TabChange = (serviceId) => {
    getProjectsImages(serviceId);
    setServiceId(serviceId);
  };

  // Function to fetch project images based on serviceId and useerId
  const getProjectsImages = async (serviceId) => {
    try {
      const res = await axios.get(getUrl(authConfig.getProjectsImages), {
        params: {
          serviceNameId: serviceId,
          userId: micrositeId,
        },
      });
      setProjectsImages(res.data);
    } catch (error) {
      console.error("Error fetching project images:", error);
    }
  };

  // Update insight videos from user data
  useEffect(() => {
    if (!!micrositeData && micrositeData.length !== 0) {
      micrositeData?.microSite?.educationalInsightsList?.map((insight) => {
        if (insight.url) {
          const urlParams = new URL(insight.url);
          setInsightVideos((prevInsightVideos) => [
            ...prevInsightVideos,
            urlParams.searchParams.get("v"),
          ]);
        }
      });
    }
  }, [micrositeData]);

  if (can("forMicrosite_READ")) {
    return (
      <div>
        <>
          <style>
            {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={6} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "end",
                  }}
                >
                  <Grid item>
                    <Button
                      onClick={handleMicrositePreview}
                      variant="contained"
                      sx={{ padding: "7px 15px !important" }}
                    >
                      Preview
                    </Button>
                  </Grid>
                  <Grid item>
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <FieldsView
                  data={micrositeBasicData}
                  expanded={expanded}
                ></FieldsView>
              </Grid>
              <Grid item xs={12}>
                <AreaOfExperties
                  data={micrositeBasicData}
                  expanded={expanded}
                ></AreaOfExperties>
              </Grid>
              <Grid item xs={12}>
                <AwardView
                  data={micrositeBasicData}
                  expanded={expanded}
                ></AwardView>
              </Grid>
              <Grid item xs={12}>
                <MemberShipView
                  data={micrositeBasicData}
                  expanded={expanded}
                ></MemberShipView>
              </Grid>
              <Grid item xs={12}>
                <TestimonialView
                  data={micrositeBasicData}
                  expanded={expanded}
                ></TestimonialView>
              </Grid>

              <Grid item xs={12}>
                <EducationalInsightsView
                  data={micrositeBasicData}
                  expanded={expanded}
                ></EducationalInsightsView>
              </Grid>
              <Grid item xs={12}>
                <ServicesTabs
                  tabContents={serviceNames}
                  data={projectsData}
                  expanded={expanded}
                />
              </Grid>
              <Grid item xs={12}>
                <StatisticsParent expanded={expanded} />
              </Grid>
            </Grid>
          </DatePickerWrapper>
          <Dialog
            fullScreen
            //  maxHeight='90%'
            //  width="600px !important"
            open={openDialog}
            onClose={(event, reason) => {
              if (reason !== "backdropClick") {
                handleCancelClick();
              }
            }}
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.5)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                marginLeft: "10px",
              }}
              textAlign={"start"}
              fontSize={"20px !important"}
              fontWeight={"bold"}
            >
              Microsite Preview
              <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                <IconButton
                  size="small"
                  onClick={handleCancelClick}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition: "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ padding: "0.6rem 0.1rem !important" }}>
              {loading ? (
                <Box sx={{ width: "100%" }}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      height:'100vh'
                    }}
                  >
                    <CircularProgress />
                  </Box>{" "}
                </Box>
              ) : (
                <>
                  {/* First Welcome Slide */}
                  <BoxWrapper
                    sx={{ padding: "0 !important" }}
                    ref={introductionRef}
                  >
                    <CardContent
                      sx={{
                        // minHeight: `calc(100vh - ${theme.spacing(theme.mixins.toolbar.minHeight / 4)})`,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "column",
                        py: (theme) => `${theme.spacing(0)} !important`,
                      }}
                    >
                      <BoxWrapper sx={{ paddingY: "0 !important", mb: 6 }}>
                        <Box
                          sx={{
                            paddingTop: "0 !important",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            variant="h3"
                            align={"center"}
                            sx={{
                              color: "black",
                              mb: 7,
                              ml: { lg: "-25px" },
                              fontWeight: 600,
                              lineHeight: 1.1,
                              "@media (min-width:1100px)": {
                                fontSize: "2.5rem",
                              },
                              "@media (min-width:1400px)": {
                                fontSize: "2.5rem",
                              },
                            }}
                          >
                            {micrositeData?.basicProfile?.companyName}
                          </Typography>
                          <Grid style={{ maxWidth: "100%", overflowX: "auto" }}>
                            <Typography
                              variant="body1"
                              align="center"
                              sx={{
                                lineHeight: 1.1,
                                mb: 2.5,
                                "@media (min-width: 1400px)": {
                                  fontSize: "1.25rem",
                                },
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              {serviceNames?.map((service) => (
                                <Box
                                  key={service.id}
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    marginRight: 10,
                                  }}
                                >
                                  <img
                                    height={40}
                                    src={`/images/microsite/${service.name}.png`}
                                    alt={service.name}
                                    onError={(e) => {
                                      e.target.src =
                                        "/images/microsite/urbanDesign.png";
                                    }}
                                    style={{ marginRight: 10 }}
                                  />
                                  {service.name}
                                </Box>
                              ))}
                            </Typography>
                          </Grid>
                        </Box>
                      </BoxWrapper>

                      <Grid
                        container
                        spacing={3}
                        sx={{
                          mb: 6,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        {micrositeData?.microSite?.strategicPartner && (
                          <Grid
                            item
                            xs={12}
                            sm={4}
                            lg={3}
                            sx={{ mr: { xs: 2, lg: 3 } }}
                          >
                            <Box
                              sx={{
                                p: 3.5,
                                backgroundColor: (theme) =>
                                  theme.palette.primary.background,
                                borderRadius: 2,
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                width: "100%", // Increase the width of the box
                                justifyContent: "center",
                              }}
                            >
                              <img
                                height={30}
                                src="/images/microsite/strategicPartner.png"
                                alt="teamSize"
                              />

                              <Typography
                                variant="h5"
                                align={"center"}
                                fontWeight={650}
                                sx={{
                                  color: "black",
                                  lineHeight: 1.2,
                                  [theme.breakpoints.up("md")]: {
                                    fontSize: "15px",
                                  },
                                  [theme.breakpoints.up("xs")]: {
                                    fontSize: "12px",
                                  },
                                  marginLeft: "10px",
                                }}
                              >
                                Houzer Strategic Partner
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                        {/* to display teamSize  */}
                        {micrositeData?.basicProfile?.teamSize && (
                          <Grid
                            item
                            xs={12}
                            sm={4}
                            lg={2}
                            sx={{ mr: { xs: 2, lg: -3 } }}
                          >
                            <Box
                              sx={{
                                p: 3.5,
                                backgroundColor: (theme) =>
                                  theme.palette.primary.background,
                                borderRadius: 2,
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                width: {
                                  xs: "100%", // For mobile screens
                                  lg: "fit-content", // For laptop screens and larger
                                },
                                justifyContent: "center",
                              }}
                            >
                              <img
                                height={30}
                                src="/images/microsite/teamSize.png"
                                alt="teamSize"
                              />

                              <Typography
                                variant="h5"
                                align={"center"}
                                fontWeight={650}
                                sx={{
                                  color: "black",
                                  lineHeight: 1.2,
                                  [theme.breakpoints.up("md")]: {
                                    fontSize: "15px",
                                  },
                                  [theme.breakpoints.up("xs")]: {
                                    fontSize: "12px",
                                  },
                                  marginLeft: "10px",
                                }}
                              >
                            Team Size: {micrositeData?.basicProfile?.teamSize}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                        {/* to display years of Experience */}
                        {micrositeData?.basicProfile?.yearsOfExperience && (
                          <Grid
                            item
                            xs={12}
                            sm={4}
                            lg={2.2}
                            sx={{ mr: { xs: 2, lg: 0 } }}
                          >
                            <Box
                              sx={{
                                // p: 3.5,
                                backgroundColor: (theme) =>
                                  theme.palette.primary.background,
                                borderRadius: 2,
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                width: {
                                  xs: "100%", // For mobile screens
                                  lg: "120%", // For laptop screens and larger
                                },
                                justifyContent: "center",
                              }}
                            >
                              <img
                                height={30}
                                src="/images/microsite/yearOfExperience.png"
                                alt="yearOfExperience"
                              />

                              <Typography
                                variant="h5"
                                align={"center"}
                                fontWeight={650}
                                sx={{
                                  color: "black",
                                  lineHeight: 1.2,
                                  [theme.breakpoints.up("md")]: {
                                    fontSize: "15px",
                                  },
                                  [theme.breakpoints.up("xs")]: {
                                    fontSize: "12px",
                                  },
                                  marginLeft: "10px",
                                }}
                              >
                                Years of Experience:{" "}
                                {micrositeData?.basicProfile?.yearsOfExperience}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        {locationField !== null && (
                          <Grid
                            item
                            xs={12}
                            sm={4}
                            lg={3}
                            sx={{
                              ml: { xs: 0, lg: 14 },
                              mr: { xs: 2, lg: -30 },
                            }}
                          >
                            <Box
                              sx={{
                                p: 3.5,
                                backgroundColor: (theme) =>
                                  theme.palette.primary.background,
                                borderRadius: 2,
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                width: {
                                  xs: "100%", // For mobile screens
                                  lg: "fit-content", // For laptop screens and larger
                                },
                                justifyContent: "center",
                              }}
                            >
                              {/* <img height={30} src='/images/microsite/teamSize.png' alt='teamSize' /> */}
                              <AddLocationAltIcon
                                sx={{
                                  fontSize: 26,
                                  verticalAlign: "bottom",
                                  mr: 1,
                                  color: "black",
                                }}
                              />

                              <Typography
                                variant="h5"
                                align={"center"}
                                fontWeight={650}
                                sx={{
                                  color: "black",
                                  lineHeight: 1.2,
                                  [theme.breakpoints.up("md")]: {
                                    fontSize: "15px",
                                  },
                                  [theme.breakpoints.up("xs")]: {
                                    fontSize: "12px",
                                  },
                                  marginLeft: "2px",
                                }}
                              >
                                {locationField}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </BoxWrapper>
                  {/* Horizontal Tabs */}

                  <Box
                    sx={{
                      position: "sticky !important",
                      top: "-20px",
                      zIndex: 1099,
                      backgroundColor: (theme) =>
                        theme.palette.primary.background,
                    }}
                  >
                    <TabContext value={value}>
                      <TabList
                        variant="scrollable"
                        scrollButtons
                        onChange={handleTabChange}
                        sx={{
                          "& .MuiTabs-scrollButtons": {
                            "@media (max-width:599.9px)": {
                              display: "flex",
                            },
                          },
                          "& .MuiTabs-scroller": {
                            "@media (min-width:900px)": {
                              display: "inline-flex",
                              justifyContent: "center",
                            },
                          },
                          boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                          borderBottom: (theme) =>
                            `1px solid ${theme.palette.divider}`,
                          "& .MuiTab-root": {
                            py: 5,
                            fontWeight: 700,
                            lineHeight: 1.5,
                            textTransform: "none !important",
                          },
                        }}
                      >
                        {micrositeData?.microSite?.briefProfile && (
                          <Tab label="About Us" value={"1"} />
                        )}
                        {micrositeData?.microSite?.awardList &&
                          micrositeData?.microSite?.awardList?.length > 0 && (
                            <Tab label="Awards and Recognitions" value={"2"} />
                          )}
                        {micrositeData?.microSite?.memberShipList &&
                          micrositeData?.microSite?.memberShipList.length >
                            0 && <Tab label="Membership" value={"3"} />}
                        {micrositeData?.microSite?.areaOfExpertiseDTO && (
                          <Tab label="Our Expertise" value={"4"} />
                        )}

                        <Tab label="Services" value={"5"} />

                        <Tab label="Projects" value={"6"} />

                        {insightVideos && insightVideos?.length > 0 && (
                          <Tab label="Educational Insights" value={"7"} />
                        )}
                        {micrositeData?.microSite?.testimonialsList &&
                          micrositeData?.microSite?.testimonialsList?.length >
                            0 && <Tab label="Testimonials" value={"8"} />}
                      </TabList>
                    </TabContext>
                  </Box>
                  {/* Second-slide */}
                  <BoxWrapper
                    // ref={aboutUsRef}
                    sx={{
                      paddingTop: "40px !important",
                    }}
                  >
                    {micrositeData?.microSite?.youtubeUrl &&
                      isYouTubeUrl(micrositeData.microSite.youtubeUrl) && (
                        <CardContent
                          sx={{
                            mb: { xs: 20, lg: 16 },
                            borderRadius: 1,
                            minHeight: 650,
                            p: (theme) => `${theme.spacing(6)} !important`,
                            backgroundColor: (theme) =>
                              theme.palette.primary.background,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            "& iframe": {
                              height: "570px !important",
                              borderRadius: "14px",
                              outline: "none",
                              border: "none",
                            },
                            "@media (max-width:900px)": {
                              minHeight: 0,
                              p: (theme) => `${theme.spacing(4)} !important`,
                              "& iframe": { height: "auto !important" },
                            },
                          }}
                        >
                          <Grid container spacing={4}>
                            <Grid item xs={12}>
                              <YouTubeCard
                                videoUrl={micrositeData?.microSite?.youtubeUrl}
                              />
                            </Grid>
                          </Grid>
                        </CardContent>
                      )}

                    {micrositeData?.microSite?.briefProfile && (
                      <Grid container spacing={14} ref={aboutUsRef}>
                        <Grid item xs={12} lg={8}>
                          <CardContent
                            sx={{
                              p: (theme) =>
                                `${theme.spacing(6, 10)} !important`,
                              backgroundColor: (theme) =>
                                theme.palette.primary.background,
                              borderRadius: 1,
                              minHeight: { lg: 555 },
                              "@media (max-width: 600px)": {
                                p: (theme) => `${theme.spacing(4)} !important`,
                              },
                            }}
                          >
                            <Typography
                              variant="h4"
                              align="center"
                              fontWeight={400}
                              sx={{
                                lineHeight: 1.1,
                                mb: { xs: 4, lg: 10 },
                                color: "black",
                                "@media (min-width: 1400px)": {
                                  fontSize: "2.375rem",
                                },
                              }}
                            >
                              About Us
                            </Typography>
                            <Typography
                              variant="body1"
                              fontWeight={600}
                              sx={{
                                textAlign: "justify",
                                pr: { lg: 20 },
                                lineHeight: 1.8,
                                mb: 2.5,
                                color: "black",
                              }}
                            >
                              {micrositeData?.microSite?.briefProfile}
                            </Typography>
                          </CardContent>
                        </Grid>
                        <Grid item xs={12} lg={4}>
                          <CardContent
                            sx={{
                              p: (theme) =>
                                `${theme.spacing(6, 20)} !important`,
                              backgroundColor: (theme) =>
                                theme.palette.primary.background,
                              borderRadius: 1,
                              paddingBottom: "88px !important",
                              "@media (max-width: 600px)": {
                                p: (theme) => `${theme.spacing(4)} !important`,
                              },
                            }}
                          >
                            <Typography
                              variant="h4"
                              align="center"
                              fontWeight={400}
                              sx={{
                                lineHeight: 1.1,
                                minHeight: { lg: "42px" },
                                color: "black",
                                mb: 4,
                                "@media (min-width: 1400px)": {
                                  fontSize: "2.375rem",
                                },
                              }}
                            >
                              {" "}
                              Company
                            </Typography>
                            <CustomSlider
                              data={companyData}
                              slides={companyImages}
                              height={300}
                              slidesToShow={1}
                            />
                          </CardContent>
                        </Grid>
                      </Grid>
                    )}
                  </BoxWrapper>

                  {/* Third-slide  */}
                  <BoxWrapper
                    sx={{
                      paddingTop: { lg: "40px !important" },
                    }}
                  >
                    <Card
                      sx={{
                        borderRadius: 1,
                        position: "relative",
                        p: (theme) => `${theme.spacing(6)} !important`,
                        display: "flex",
                        flexDirection: "column",
                        backgroundColor: (theme) =>
                          theme.palette.primary.background,

                        "@media (max-width: 1199px)": {
                          p: (theme) => `${theme.spacing(4)} !important`,
                        },
                      }}
                    >
                      <StatisticsTabs
                        onTabChange={StatisticsTabChange}
                        tabContents={serviceNamesWithIds}
                        statisticsData={finalUser}
                        listNames={listNames}
                      />
                    </Card>
                  </BoxWrapper>
                  {/* Fourth-slide Free Consultation */}
                  {micrositeData?.microSite?.awardList &&
                    micrositeData?.microSite?.awardList.length > 0 && (
                      <BoxWrapper
                        ref={awardsRef}
                        sx={{
                          paddingTop: { lg: "40px !important" },
                        }}
                      >
                        <Card
                          sx={{
                            borderRadius: 1,
                            position: "relative",
                            p: (theme) => `${theme.spacing(6)} !important`,
                            backgroundColor: (theme) =>
                              theme.palette.primary.background,
                            display: "flex",

                            "@media (max-width: 1199px)": {
                              p: (theme) => `${theme.spacing(4)} !important`,
                            },
                          }}
                        >
                          <Grid
                            container
                            spacing={4}
                            sx={{
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Grid item xs={12}>
                              <Typography
                                variant="h4"
                                align="center"
                                fontWeight={400}
                                sx={{
                                  lineHeight: 1.1,
                                  mb: { lg: 12 },
                                  color: "black",
                                  "@media (min-width: 1400px)": {
                                    fontSize: "2.375rem",
                                  },
                                }}
                              >
                                Awards and Recognitions
                              </Typography>
                            </Grid>
                            <Grid item xs={12} lg={7}>
                              <CardContent
                                sx={{
                                  p: (theme) =>
                                    `${theme.spacing(2)} !important`, // Adjust padding
                                  "@media (max-width: 600px)": {
                                    p: (theme) =>
                                      `${theme.spacing(1)} !important`, // Adjust padding
                                  },
                                }}
                              >
                                <Box
                                  component="ul"
                                  sx={{
                                    mt: 0,
                                    mb: 4,
                                    px: 2,
                                    "& li": { mb: 2, color: "black" },
                                  }}
                                >
                                  {" "}
                                  {/* Adjust padding */}
                                  {micrositeData?.microSite?.awardList?.map(
                                    (award, index) => (
                                      <li key={index}>
                                        <Typography
                                          fontSize={16}
                                          color={"black"}
                                          sx={{ textDecoration: "none" }}
                                        >
                                          <span style={{ fontWeight: 700 }}>
                                            {award.name}{" "}
                                          </span>
                                          {award.description}
                                        </Typography>
                                      </li>
                                    )
                                  )}
                                </Box>
                              </CardContent>
                            </Grid>
                            <Grid item xs={12} lg={5} sx={{}}>
                              <Grid
                                container
                                spacing={4}
                                sx={{
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <Grid item xs={10.35}>
                                  <CustomSlider
                                    data={awardData}
                                    slides={companyImages}
                                    height={360}
                                    slidesToShow={1}
                                  />
                                </Grid>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Card>
                      </BoxWrapper>
                    )}
                  {/* Fifth-slide Membership */}
                  {micrositeData?.microSite?.memberShipList &&
                    micrositeData?.microSite?.memberShipList.length > 0 && (
                      <BoxWrapper
                        ref={membershipRef}
                        sx={{
                          paddingTop: { lg: "40px !important" },
                        }}
                      >
                        <Card
                          sx={{
                            backgroundColor: (theme) =>
                              theme.palette.primary.background,
                            boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                            position: "relative",
                            p: (theme) => `${theme.spacing(6)} !important`,
                            display: "flex",
                            flexDirection: "column",
                            "@media (max-width: 1199px)": {
                              p: (theme) => `${theme.spacing(4)} !important`,
                            },
                          }}
                        >
                          <Typography
                            variant="h4"
                            align="center"
                            fontWeight={400}
                            sx={{
                              lineHeight: 1.1,
                              mb: 8,
                              color: "black",
                              "@media (min-width: 1400px)": {
                                fontSize: "2.375rem",
                              },
                            }}
                          >
                            Membership
                          </Typography>

                          <Box
                            component="ol"
                            sx={{
                              mt: 6,
                              mb: 4,
                              px: 12,
                              "& li": { mb: 2, color: "black" },
                            }}
                          >
                            {micrositeData?.microSite?.memberShipList?.map(
                              (membership, index) => (
                                <li key={index}>
                                  <Typography
                                    fontSize={18}
                                    color={"black"}
                                    sx={{ textDecoration: "none" }}
                                  >
                                    {membership?.description}
                                  </Typography>
                                </li>
                              )
                            )}
                          </Box>
                        </Card>
                      </BoxWrapper>
                    )}
                  {/* Sixth-slide Access Developers */}
                  <BoxWrapper
                    ref={servicesRef}
                    sx={{
                      paddingTop: { lg: "40px !important" },
                    }}
                  >
                    <CardContent
                      sx={{
                        boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                        backgroundColor: (theme) =>
                          theme.palette.primary.background,
                        borderRadius: 1,
                        position: "relative",
                        // p: theme => `${theme.spacing(24, 12)} !important`,
                        display: "flex",
                        alignItems: "center",
                        "@media (max-width: 1199px)": {
                          p: (theme) => `${theme.spacing(4)} !important`,
                        },
                      }}
                    >
                      <Grid container spacing={4}>
                        <Grid item xs={12}>
                          <Typography
                            variant="h4"
                            align="center"
                            fontWeight={400}
                            sx={{
                              color: "black",
                              "@media (min-width: 1400px)": {
                                fontSize: "2.375rem",
                              },
                            }}
                          >
                            Our Services
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <PreviewServicesTabs
                            transformedData={servicesImages}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </BoxWrapper>

                  {/* Seventh-slide ProfileView ContactUs */}
                  <BoxWrapper
                    ref={projectsRef}
                    sx={{
                      paddingTop: { lg: "40px !important" },
                    }}
                  >
                    <Card
                      sx={{
                        backgroundColor: (theme) =>
                          theme.palette.primary.background,
                        boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                        borderRadius: 1,
                        position: "relative",
                        p: (theme) => `${theme.spacing(6)} !important`,
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                        "@media (max-width: 1199px)": {
                          p: (theme) => `${theme.spacing(4)} !important`,
                        },
                      }}
                    >
                      <Typography
                        variant="h4"
                        align="center"
                        fontWeight={400}
                        sx={{
                          lineHeight: 1.1,
                          mb: 10,
                          color: "black",
                          "@media (min-width: 1400px)": {
                            fontSize: "2.375rem",
                          },
                        }}
                      >
                        Our Projects
                      </Typography>
                      <Grid container spacing={4}>
                        <Grid item xs={12}>
                          <ProjectsTabs
                            onTabChange={TabChange}
                            tabContents={serviceNamesWithIds}
                            data={projectsImages}
                          />
                        </Grid>
                      </Grid>
                    </Card>
                  </BoxWrapper>
                  {micrositeData?.microSite?.areaOfExpertiseDTO && (
                    <BoxWrapper
                      ref={ourexpertiseRef}
                      sx={{
                        paddingTop: { lg: "40px !important" },
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor: (theme) =>
                            theme.palette.primary.background,
                          mb: { xs: 6, lg: 10 },
                          boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                          borderRadius: 1,
                          p: (theme) => `${theme.spacing(6)} !important`,
                          display: "flex",
                          alignItems: "center",
                          flexDirection: "column",
                          "@media (max-width: 1199px)": {
                            p: (theme) => `${theme.spacing(4)} !important`,
                          },
                        }}
                      >
                        <Typography
                          variant="h4"
                          align="center"
                          fontWeight={400}
                          sx={{
                            lineHeight: 1.1,
                            mb: { xs: 4, lg: 10 },
                            color: "black",
                            "@media (min-width: 1400px)": {
                              fontSize: "2.375rem",
                            },
                          }}
                        >
                          Our Expertise
                        </Typography>
                        <Grid container spacing={8}>
                          {Object.entries(
                            micrositeData?.microSite?.areaOfExpertiseDTO || {}
                          ).map(
                            ([key, value], index) =>
                              key.toLowerCase() !== "other" && (
                                <Grid item xs={12} md={6} lg={4} key={index}>
                                  <Card sx={{ p: 8 }}>
                                    <Typography
                                      variant="body2"
                                      color={"black"}
                                      fontWeight={500}
                                      sx={{ mb: 6 }}
                                    >
                                      {key.toUpperCase()}
                                    </Typography>
                                    <LinearProgress
                                      value={value}
                                      variant="determinate"
                                      color="secondary"
                                      sx={{
                                        height: 8,
                                        backgroundColor: (theme) =>
                                          theme.palette.primary.background,
                                      }}
                                    />
                                  </Card>
                                </Grid>
                              )
                          )}
                        </Grid>
                      </Box>
                    </BoxWrapper>
                  )}

                  {/* eigth-slide EDUCATIONAL INSIGHTS */}
                  {insightVideos && insightVideos.length > 0 && (
                    <BoxWrapper
                      ref={insightsRef}
                      sx={{
                        paddingTop: { lg: "10px !important" },
                      }}
                    >
                      <Card
                        sx={{
                          mb: { lg: 8 },
                          backgroundColor: (theme) =>
                            theme.palette.primary.background,
                          boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                          borderRadius: 1,
                          position: "relative",
                          p: (theme) => `${theme.spacing(6)} !important`,
                          display: "flex",
                          alignItems: "center",
                          flexDirection: "column",
                          "@media (max-width: 1199px)": {
                            p: (theme) => `${theme.spacing(4)} !important`,
                          },
                        }}
                      >
                        <Typography
                          variant="h4"
                          align="center"
                          fontWeight={400}
                          sx={{
                            lineHeight: 1.1,
                            mb: 10,
                            color: "black",
                            "@media (min-width: 1400px)": {
                              fontSize: "2.375rem",
                            },
                          }}
                        >
                          Educational Insights
                        </Typography>
                        <Grid container spacing={4}>
                          <Grid item xs={12}>
                            <InsightsSlider videoIds={insightVideos} />
                          </Grid>
                        </Grid>
                      </Card>
                    </BoxWrapper>
                  )}
                  {/* ninth-slide testimonials */}
                  {micrositeData?.microSite?.testimonialsList &&
                    micrositeData?.microSite?.testimonialsList.length > 0 && (
                      <BoxWrapper
                        ref={testimonialsRef}
                        sx={{
                          paddingTop: { lg: "10px !important" },
                        }}
                      >
                        <CardContent
                          sx={{
                            boxShadow: "0 4px 6px 0 rgba(0,0,0,.12)",
                            backgroundColor: (theme) =>
                              theme.palette.primary.background,
                            borderRadius: 1,
                            position: "relative",
                            p: (theme) => `${theme.spacing(5, 1)} !important`,
                            "@media (max-width: 1199px)": {
                              p: (theme) => `${theme.spacing(4)} !important`,
                            },
                          }}
                        >
                          <Grid container spacing={0}>
                            <Grid item xs={12}>
                              <Typography
                                variant="h4"
                                align="center"
                                fontWeight={400}
                                sx={{
                                  mb: { lg: 1 },
                                  color: "black",
                                  "@media (min-width: 1400px)": {
                                    fontSize: "2.375rem",
                                  },
                                }}
                              >
                                Testimonials
                              </Typography>
                            </Grid>
                            <Grid item xs={12}>
                              <ProfileTestimonial
                                slides={micrositeData?.microSite?.testimonialsList.filter(
                                  (testimonial) =>
                                    testimonial.description.toUpperCase() !==
                                      "NA" &&
                                    testimonial.description.toUpperCase() !==
                                      "N/A"
                                )}
                              />
                            </Grid>
                          </Grid>
                        </CardContent>
                      </BoxWrapper>
                    )}
                </>
              )}
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "end",
                gap: "1rem",
                marginTop: "0.6rem",
                paddingBottom: "0.8rem !important",
              }}
            >
              {/* Footer actions go here */}
              <Button onClick={handleCancelClick} variant="outlined">
                Cancel
              </Button>
              <Button variant="contained">Approve</Button>
            </DialogActions>
          </Dialog>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default Index;
