import { yupResolver } from "@hookform/resolvers/yup";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  DialogContentText,
  Divider,
  Tooltip,
  InputAdornment,
  InputLabel,
  Select,
  MenuItem,
  Menu,
  ListItemIcon
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import DeleteDialog from "./sections/DeleteDialog";
import UpdateDialog from "./sections/UpdateDialog";
import EmployeeDetailsView from "./sections/EmployeeDetailsView";
import EmployeeValidations from "./sections/EmployeeValidations";
import CustomChip from "src/@core/components/mui/chip";
import { useRBAC } from "src/pages/permission/RBACContext";
import { useRouter } from "next/router";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

const userStatusObj = {
  ACTIVE: "Active",
  INACTIVE: "Inactive",
  REGISTERED: "Registered",
};

const Employees = ({ formData, setValue, data, onCancel }) => {
  const { can, rbacRoles } = useRBAC();

  const router = useRouter();

  const [userList, setUserList] = useState([]);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isOtpVerified, setIsOtpVerified] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState("");
  const [id, setId] = useState(null);
  const [editIndex, setEditIndex] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();

  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);

  const {
    employeeProfile,
    setEmployeeProfile,
    employeeData,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [resendOtpDisabled, setResendOtpDisabled] = useState(false);
  const [expanded, setExpanded] = useState(true);
  const [resendOtpCountdown, setResendOtpCountdown] = useState(0);
  const [otp, setOTP] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [disableSaveButton, setDisableSaveButton] = useState(false);

  const [employeeId, setEmployeeId] = useState("");
  const [departmentId, setDepartmentId] = useState("");
  const [selectedLocationId, setSelectedLocationId] = useState({});
  const [locationsData, setLocationsData] = useState(null);
  const [selectedDesignationId, setSelectedDesignationId] = useState("");
  const [designationsData, setDesignationsData] = useState(null);

  const handleClose = () => setOpenDialogContent(false);

  const fields = ["firstName", "lastName", "email", "contactNumber"];

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(EmployeeValidations(fields)),
    mode: "onChange",
  });

  const [employeesData, setEmployeesData] = useState(null);


  useEffect(() => {
    if (showOTPOptions) {
      setOTP("");
    }
  }, [showOTPOptions]);

  async function submit(data) {
    setSaveLoading(true);
    setDisableSaveButton(true);
    if (!isEmailVerified) {
      const message = `
          <div>
          <h3>
          Email Address need to be verified
          </h3>
          </div>
        `;
      setDialogMessage(message);
      setOpenDialogContent(true);
    } else {
      const fields = {
        firstName: data.firstName,
        lastName: data.lastName,
        mobileNumber: data.contactNumber,
        email: data.email,
        designation: selectedDesignationId,
        reportingTo: employeeId,
        department: departmentId,
        userType: "EMPLOYEE",
        status: "REGISTERED",
        employeeMetaData: {
          employeeData: {
            address: {
              street1: data.street1 || "",
              street2: data.street2 || "",
              city: data.city || "",
              state: data.state || "",
              country: data.country || "",
              pinCode: data.pinCode || "",
            },
            workLocation: selectedLocationId,
          },
        },
      };
      try {
        const response = await auth.postEmployee(
          fields,
          handleFailure,
          handleSuccess
        );
        setSaveLoading(false);
      } catch (error) {
        console.error("Employee Creation failed:", error);
        handleFailure();
      }

      setOpenDialog(false);
      reset();
      setSelectedDesignationId("");
      setEmployeeId("");
      setSelectedLocationId("");
      setDepartmentId("");
      setIsVerified(false);
      setIsOtpVerified(false);
      setOTP("");
      fetchUsers(page, pageSize);
    }
    setIsEmailVerified(false);
  }

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3> Employee Created Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
    setDisableSaveButton(false);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Employee Data. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
    setDisableSaveButton(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  const isapicalling = useRef(false);
  const handleEmailVerification = async (formData) => {
    if (isapicalling.current) {
      // API call is already in progress, return early
      return;
    }
    isapicalling.current = true;
    setLoading(true);
    setDisableVerifyEmailButton(true);
    try {
      const ipAddress = await fetchIpAddress();
      const otpData = {
        firstName: formData?.firstName,
        lastName: formData?.lastName,
        email: formData?.email,
        mobileNo: formData?.contactNumber,
        entityCategory: null,
        entityType: null,
        ipAddress: ipAddress,
      };
      const response = await axios({
        method: "POST",
        url: getUrl(authConfig.registerEndpointNewV2),
        data: otpData,
      });

      if (response.data.isVerified) {
        const message = `
        <div>
        <h3>
        Email already exists!!!
        </h3>
        </div>
        `;
        setLoading(false);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setDisableVerifyEmailButton(false);
      } else {
        setEmail(otpData?.email);
        const message = `
          <div>
          <h3>
          OTP has been sent to your Email for verification. Please check.
          </h3>
          </div>
        `;
        setLoading(false);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setShowOTPOptions(true);
        startResendCountdown();
      }

      const otpSentConfirmation = response.data.message;
    } catch (error) {
      console.error("An error occurred during email verification:", error);
      const message = `
          <div>
          <h3>
          Error sending OTP to your Email. Please try again.
          </h3>
          </div>
        `;
      setLoading(false);
      setDialogMessage(message);
      setOpenDialogContent(true);
    }
    setLoading(false);
    isapicalling.current = false;
  };

  const handleOtpVerification = async (formData) => {
    axios
      .post(getUrl(authConfig.otpVerifyEndpoint + "?isMember=false"), {
        otpCode: otp,
        email: formData?.email,
      })
      .then((response) => {
        const message = `
          <div>
          <h3>
          Email has been verified successfully.
          </h3>
          </div>
        `;
        setLoading(false);
        setIsEmailVerified(true);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setIsOtpVerified(true);
        setIsVerified(true);
        setShowOTPOptions(false);
        setDisableVerifyEmailButton(false);
        setDisableSaveButton(false);
      })
      .catch((error) => {
        const message = `
          <div>
          <h3>
          OTP doesn't match. Please try again.
          </h3>
          </div>

        `;
        setLoading(false);
        setDialogMessage(message);
        setOpenDialogContent(true);
      });
  };
  const startResendCountdown = () => {
    setResendOtpDisabled(true);
    setResendOtpCountdown(30);

    const countdownInterval = setInterval(() => {
      setResendOtpCountdown((prevCountdown) => {
        if (prevCountdown > 0) {
          return prevCountdown - 1;
        } else {
          clearInterval(countdownInterval);
          setResendOtpDisabled(false);
          return 0;
        }
      });
    }, 1000);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    setSelectedDesignationId("");
    setEmployeeId("");
    setSelectedLocationId("");
    setDepartmentId("");
    setShowOTPOptions(false);
    setStatus("");
    setEditIndex(null);
    setId(null);
    setSaveLoading(false);
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [departmentOptions, setDepartmentOptions] = useState([]);

  useEffect(() => {
    // Fetch employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdown) +
        "?selectionType=EMPLOYEE_SUBCATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfRoleTypes(res.data.data);
      })
      .catch((err) => console.log("Role Types error", err));
  }, [userList]);

  useEffect(() => {
    if(!!authConfig) {
      
      getAllListValuesByListNameId(
        authConfig.workLocation,
        handleLocationSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.designationId,
        handleDesignationSuccess,
        handleError
      );
    }    
  }, [authConfig]);
  

  useEffect(() => {
    if (!!listOfEmployees) {
      let data = [];
      listOfEmployees.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = [];
      listOfRoleTypes[0]?.metaData?.subRoleTypes?.forEach((entry) => {
        if (entry?.isActive) {
          data.push({ value: entry?.id, key: entry?.name });
        }
      });
      setDepartmentOptions(data);
    }
  }, [listOfRoleTypes]);

  const handleLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.employeeGetAllEndpoint);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.employees || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const closeViewProfileDialog = () => {
    setEmployeeProfile(null);
    setViewProfileDialogOpen(false);
  };
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize);
  };

  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchUsers(page,pageSize);
  }

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handleEmployeeChange = (newValue) => {
    setEmployeeId(newValue);
  };

  const handleDepartmentChange = (newValue) => {
    setDepartmentId(newValue);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (status) => {
    return userStatusObj[status] || "Unknown";
  };

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!can("employees_READ")) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  const columns = [
    {
      field: "name",
      headerName: "Name",
      flex: 2.3,
      minWidth: 95,
      valueGetter: (params) => {
        const row = params.row;
        return `${row.firstName} ${row.lastName}`;
      },
    },
    {
      field: "reportingTo",
      headerName: "Reporting To",
      flex: 2.2,
      minWidth: 185,
      valueGetter: (params) => {
        const row = params?.row;
        const reportingToId = row?.reportingTo;
        const reportingToEmployee = listOfEmployees?.find(employee => employee?.id === reportingToId);
        return reportingToEmployee ? `${reportingToEmployee?.name}` : "";
      },
    },
    {
      field: "email",
      minWidth: 180,
      headerName: "Email",
      flex: 3,
      renderCell: (params) => {
        const email = params?.value;
    
        return email.length > 21 ? (
          <Tooltip title={email}>
            <span>{email}</span>
          </Tooltip>
        ) : (
          <span>{email}</span>
        );
      },
    },
    { field: "mobileNumber",minWidth: 135, headerName: "Mobile No", flex: 0.1 },
    {
      field: "status",
      headerName: "Status",
      flex: 0.13,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.status)}
            color={
              row.status === "ACTIVE" || row.status === "REGISTERED"
                ? "success"
                : "error"
            }
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
  
    (can("employees_UPDATE") || can("employees_DELETE")) && 
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = useState(null);
    
        const handleMenuClick = (event) => {
          setAnchorEl(event.currentTarget);
        };
    
        const handleMenuClose = () => {
          setAnchorEl(null);
        };
    
        const onClickViewProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setViewProfileDialogOpen(true);
          setEmployeeProfile({
            ...employeeProfile,
            id: row.id,
          });
          handleMenuClose();
        };
    
        const onClickToggleStatus = () => {
          const row = params.row;
          setCurrentRow(row);
          if (row.status === "ACTIVE") {
            setOpenDeleteDialog(true);
          } else {
            setOpenUpdateDialog(true);
          }
          handleMenuClose();
        };
    
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton onClick={handleMenuClick}>
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              {can("employees_UPDATE") && (
                <MenuItem onClick={onClickViewProfile}>
                  <Icon icon="iconamoon:edit" style={{ marginRight: 8 }} />
                  Edit
                </MenuItem>
              )}
              {can("employees_DELETE") && (
                <MenuItem onClick={onClickToggleStatus}>
                  <ListItemIcon>
                    <Icon icon={params.row.status === "ACTIVE" ? "iconamoon:trash" : "tabler:circle-check"} />
                  </ListItemIcon>
                  {params.row.status === "ACTIVE" ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];
  

  return (
    <>
      {can("employees_READ") && (
        <Grid>
          <Card>
            <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"600"}>Employee List</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm="auto">
                      <FormControl>
                        <Controller
                          name="mainSearch"
                          control={control}
                          // defaultValue={name}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search by name & email"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setSearchKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                  fetchUsers(page, pageSize, searchKeyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                        fetchUsers(
                                          page,
                                          pageSize,
                                          searchKeyword
                                        );
                                      }}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    {can("employees_CREATE") && (
                      <Grid item xs={12} sm="auto">
                        <Button variant="contained" onClick={handleOpenDialog}>
                          Add New Employee
                        </Button>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </Box>

            <Divider />

            <Dialog
              open={openDialog}
              onClose={handleCloseDialog}
              maxWidth={"md"}
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start"},
                  fontSize: { xs: 19, md: 20  },
                }}
                textAlign={"center"}
              >
                Add New Employee
                <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                  <IconButton
                    size="small"
                    onClick={handleCloseDialog}
                    sx={{
                      // p: "0.438rem",
                      borderRadius: 1,
                      color:"common.white", 
                  backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  pt: (theme) => `${theme.spacing(8)} !important`,
                  pb: (theme) => `${theme.spacing(5)} !important`,
                  px: (theme) => [`${theme.spacing(8)} !important`],
                }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="firstName"
                        control={control}
                        defaultValue={formData?.firstName}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            size="small"
                            label=" First Name"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your firstName"
                            error={Boolean(errors.firstName)}
                            helperText={errors.firstName?.message}
                            aria-describedby="Section1-firstName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="lastName"
                        control={control}
                        defaultValue={formData?.lastName}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            size="small"
                            label="Last Name"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your Last name"
                            error={Boolean(errors.lastName)}
                            helperText={errors.lastName?.message}
                            aria-describedby="Section1-lastName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel id="designation-select-label">
                        Designation
                      </InputLabel>
                      <Select
                        labelId="designation-select-label"
                        id="designation-select"
                        size="small"
                        value={selectedDesignationId}
                        label="Designation"
                        onChange={handleSelectDesignationChange}
                      >
                        {designationsData?.map((designation) => (
                          <MenuItem key={designation.id} value={designation.id}>
                            {designation.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {errors.selectedDesignationId && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-selectedDesignationId"
                      >
                        {errors.selectedDesignationId?.message}
                      </FormHelperText>
                    )}
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"employeeId-select"}
                      label={"Reporting to "}
                      name="employeeId-select"
                      nameArray={employeesOptions}
                      defaultValue={employeeId}
                      value={employeeId}
                      onChange={(event) =>
                        handleEmployeeChange(event.target.value)
                      }
                      aria-describedby="employeeId-select"
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel id="location-select-label">
                        Work Location
                      </InputLabel>
                      <Select
                        labelId="location-select-label"
                        id="location-select"
                        size="small"
                        value={selectedLocationId}
                        label="Work Location"
                        placeholder="Work Location"
                        onChange={handleSelectChange}
                      >
                        {locationsData?.map((location) => (
                          <MenuItem key={location.id} value={location.id}>
                            {location.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {errors.selectedLocationId && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-selectedLocationId"
                      >
                        {errors.selectedLocationId?.message}
                      </FormHelperText>
                    )}
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="contactNumber"
                        control={control}
                        defaultValue={formData?.contactNumber}
                        render={({ field }) => (
                          <MobileNumberValidation
                            {...field}
                            size="small"
                            type="tel"
                            label="Contact Number"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.contactNumber)}
                            placeholder="Enter 10 digit Mobile Number"
                            helperText={errors.contactNumber?.message}
                            aria-describedby="Section1-contactNumber"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"departmentId-select"}
                      label={"Department"}
                      name="departmentId-select"
                      nameArray={departmentOptions}
                      defaultValue={departmentId}
                      value={departmentId}
                      onChange={(event) =>
                        handleDepartmentChange(event.target.value)
                      }
                      aria-describedby="departmentId-select"
                    />
                  </Grid>
                  <Grid item xs={12} sm={5} lg={6} md={5.7}>
                    {!isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                              <EmailTextField
                                {...field}
                                size="small"
                                type="email"
                                label="Email address"
                                error={Boolean(errors.email)}
                                inputProps={{ maxLength: 50 }}
                                InputLabelProps={{ shrink: true }}
                                placeholder="Enter email address"
                                helperText={errors.email?.message}
                              />
                            )}
                          />
                        </FormControl>
                      </>
                    )}
                    {isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <div
                            style={{ display: "flex", alignItems: "baseline" }}
                          >
                            <Typography
                              sx={{ marginBottom: 0, fontWeight: "bold" }}
                            >
                              Email:
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                marginRight: "4px",
                                marginLeft: "6px",
                              }}
                            >
                              {email}
                            </Typography>
                          </div>
                        </FormControl>

                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <CheckCircleIcon
                            sx={{
                              color: "green",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                              marginLeft: "55px",
                            }}
                          />
                          <Typography
                            sx={{
                              marginLeft: "6px",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                            }}
                          >
                            Verified
                          </Typography>
                        </Box>
                      </>
                    )}
                  </Grid>
                  {!isEmailVerified && (
                    <Grid item xs={12} sm={3} lg={2} md={2.3}>
                      <Button
                        display="flex"
                        justifyContent="center"
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          handleSubmit(handleEmailVerification)();
                        }}
                        disabled={disableVerifyEmailButton}
                        sx={{
                          px: { lg: 2.08 },
                        }}
                      >
                        {loading ? (
                          <CircularProgress color="inherit" size={22} />
                        ) : (
                          "Verify email"
                        )}
                      </Button>
                    </Grid>
                  )}
                  {showOTPOptions && !isVerified && (
                    <>
                      <Grid item xs={12} sm={4} sx={{ marginLeft: "0.1rem" }}>
                        <TextField
                          fullWidth
                          margin="dense"
                          label="OTP"
                          name="otp"
                          type="text"
                          size="small"
                          placeholder="OTP(6 Digits)"
                          inputProps={{ maxLength: 6 }}
                          value={otp}
                          onChange={(e) => {
                            setOTP(
                              e.target.value
                                .replace(/[^\d]/g, "")
                                .substring(0, 6)
                            );
                          }}
                          required
                          sx={{
                            "& .MuiInputBase-input.MuiOutlinedInput-input": {
                              padding: "8px",
                            },
                          }}
                        />
                      </Grid>
                      {!isVerified && (
                        <Grid item xs={12} sm={3} md={2.5}>
                          <FormControl fullWidth>
                            <Button
                              variant="outlined"
                              color="primary"
                              onClick={handleSubmit(handleOtpVerification)}
                              style={{
                                marginTop: "9px",
                              }}
                            >
                              Verify OTP
                            </Button>
                          </FormControl>
                        </Grid>
                      )}
                      {!isVerified && (
                        <Grid item xs={12} sm={3.5} md={3}>
                          <FormControl fullWidth>
                            <Button
                              variant="outlined"
                              color="primary"
                              onClick={() => {
                                handleSubmit(handleEmailVerification)();
                              }}
                              disabled={resendOtpDisabled}
                              style={{
                                marginTop: "9px",
                              }}
                            >
                              Resend OTP ({resendOtpCountdown}s)
                            </Button>
                          </FormControl>
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "end",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                }}
              >
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="outlined"
                  color="primary"
                  onClick={() => {
                    handleCloseDialog();
                    setDisableVerifyEmailButton(false);
                  }}
                >
                  Cancel
                </Button>

                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  disabled={disableSaveButton}
                  onClick={handleSubmit(submit)}
                >
                  {saveLoading ? (
                    <CircularProgress color="inherit" size={22} />
                  ) : (
                    "Save"
                  )}
                </Button>
              </DialogActions>
            </Dialog>
            <Divider />
            <Dialog
              open={viewProfileDialogOpen}
              onClose={closeViewProfileDialog}
              fullWidth
              maxWidth="xl"
              scroll="paper"
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start"},
                  fontSize: { xs: 19, md: 20  },
                }}
                textAlign={"center"}
              >
                Employee Profile
                <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                  <IconButton
                    size="small"
                    onClick={closeViewProfileDialog}
                    sx={{
                      // p: "0.438rem",
                      borderRadius: 1,
                      color:"common.white", 
                  backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  ":playing": (theme) => `${theme.spacing(4)} !important`,
                  px: (theme) => [
                    `${theme.spacing(6)} !important`,
                    `${theme.spacing(10)} !important`,
                  ],
                }}
              >
                <EmployeeDetailsView
                  data={employeeData}
                  employeesOptions={employeesOptions}
                  departmentOptions={departmentOptions}
                  expanded={expanded}
                  onCancel={closeViewProfileDialog}
                  fetchUsers={fetchUsers}
                />
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "end",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.85)} !important`,
                }}
              >
                <Button onClick={closeViewProfileDialog}>Close</Button>
              </DialogActions>
            </Dialog>
            <CardContent>
            <div style={{ height: 380, width: "100%" }}>                
            <DataGrid
                  rows={userList}
                  columns={columns}
                  
                  checkboxSelection
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38} 
                />
              </div>
            </CardContent>
            <Divider />

            <DeleteDialog
              open={openDeleteDialog}
              onClose={handleCloseDeleteDialog}
              data={currentRow}
            />

<UpdateDialog
              open={openUpdateDialog}
              onClose={handleCloseUpdateDialog}
              data={currentRow}
            />
          </Card>
        </Grid>
      )}
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default Employees;
