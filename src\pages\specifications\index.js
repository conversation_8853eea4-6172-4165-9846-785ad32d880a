import React, { useState } from 'react';
import { Grid, FormControl, InputLabel, Select, MenuItem, Button, Card, CardContent, Typography } from '@mui/material';

const ServiceProfiles = () => {
  // State for each dropdown
  const [serviceType, setServiceType] = useState('');
  const [listName, setListName] = useState('');
  const [uiComponent, setUiComponent] = useState('');
  const [sectionId, setSectionId] = useState('');

  // Sample options for the dropdowns (static data)
  const serviceTypes = ['Type 1', 'Type 2', 'Type 3'];
  const listNames = ['List 1', 'List 2', 'List 3'];
  const uiComponents = ['Component 1', 'Component 2', 'Component 3'];
  const sectionIds = ['Section 1', 'Section 2', 'Section 3'];

  return (
    <Card style={{ maxWidth: 800, margin: '0 auto', padding: '20px' }}>
      <CardContent>
      <div style={{ display: 'flex', justifyContent: 'start', marginBottom: '24px' }}>
          <Typography variant="h5" component="div">
            Service Profile Specifications
          </Typography>
        </div>
        <Grid container spacing={5} style={{ padding: "16px" }}>
          
          {/* ServiceType Dropdown */}
          <Grid item xs={12} sm={6} style={{ marginBottom: '14px' }}>
            <FormControl fullWidth>
              <InputLabel id="serviceType-label">Service Type</InputLabel>
              <Select
                labelId="serviceType-label"
                label="Service Type"
                id="serviceType"
                value={serviceType}
                onChange={(e) => setServiceType(e.target.value)}
                size="small"
              >
                {serviceTypes.map((type, index) => (
                  <MenuItem key={index} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* ListName Dropdown */}
          <Grid item xs={12} sm={6} style={{ marginBottom: '14px' }}>
            <FormControl fullWidth>
              <InputLabel id="listName-label">List Name</InputLabel>
              <Select
                labelId="listName-label"
                label="List Name"
                id="listName"
                value={listName}
                onChange={(e) => setListName(e.target.value)}
                size="small"
              >
                {listNames.map((name, index) => (
                  <MenuItem key={index} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* UIComponent Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="uiComponent-label">UI Component</InputLabel>
              <Select
                labelId="uiComponent-label"
                label="UI Component"
                id="uiComponent"
                value={uiComponent}
                onChange={(e) => setUiComponent(e.target.value)}
                size="small"
              >
                {uiComponents.map((component, index) => (
                  <MenuItem key={index} value={component}>
                    {component}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Section Id Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="sectionId-label">Section Id</InputLabel>
              <Select
                labelId="sectionId-label"
                label="Section Id"
                id="sectionId"
                value={sectionId}
                onChange={(e) => setSectionId(e.target.value)}
                size="small"
              >
                {sectionIds.map((id, index) => (
                  <MenuItem key={index} value={id}>
                    {id}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Create Specifications Button */}
          <Grid item xs={12} style={{ display: 'flex', justifyContent: 'end', marginTop: '24px' }}>
            <Button
              variant="contained"
              color="primary"
            >
              Create Specifications
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ServiceProfiles;
