import { Card } from "@mui/material";


import NavTabServiceProfile from "src/@core/components/custom-components/NavTabServiceProfile";
import Services from "./Services";
import SubServices from "./SubServices";
import Statistics from "./Statistics";
import { useRBAC } from "src/pages/permission/RBACContext";
const ServiceProfile = () => {
  const { can } = useRBAC();
  return (
    <Card>
      
      <NavTabServiceProfile
        tabContent1={
          <>
            <Services/>
          </>
        }
        tabContent2={
          <>
            <SubServices/>
          </>
        }
        tabContent3={
          <>
            <Statistics/>
          </>
        }
        tabContent4={
            <>
            
            </>
        }
        
      />
    </Card>
  );
};

export default ServiceProfile;
