import * as yup from "yup";

export const SocietyValidationsSection1 = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  return yup.object().shape({
    name: yup.string().when("$fields", {
      is: () => fieldsArray.includes("name"),
      then: yup
        .string()
        .nullable()
        .required("Society Name is Required")
        .matches(/^[A-Za-z][A-Za-z0-9,.-]*(?:\s[A-Za-z0-9,.-]+)*$/, {
          message:
            "Society Name must start with a character and not contain multiple spaces,special characters",
          excludeEmptyString: true,
        })
        .max(50, "Society Name must not exceed 50 characters")
        .test(
          "no-multiple-spaces",
          "Multiple spaces are not allowed in Society Name",
          (value) => !/\s{2,}/.test(value)
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    plotCTSNo: yup.string().when("$fields", {
      is: () => fieldsArray.includes("plotCTSNo"),
      then: yup
        .string()
        .nullable()
        .max(10, "plotCTSNo must not exceed 10 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    teamMember: yup.string().when("$fields", {
      is: () => fieldsArray.includes("teamMember"),
      then: yup
        .string()
        .nullable()
        .required("Team member is required")
        .max(30, "Team member must not exceed 30 characters")
        .matches(
          /^[a-zA-Z0-9\s]*$/,
          "Team member can only contain letters, digits, and whitespace"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    location: yup.string().when("$fields", {
      is: () => fieldsArray.includes("location"),
      then: yup
        .string()
        .nullable()
        .required("Location is required")
        .max(50, "Location must not exceed 50 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    pinCode: yup.string().when("$fields", {
      is: () => fieldsArray.includes("pinCode"),
      then: yup.string().nullable().required("This field is required"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    reference: yup.string().when("$fields", {
      is: () => fieldsArray.includes("reference"),
      then: yup
        .string()
        .nullable()
        .max(50, "Reference must not exceed 50 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    societyAddress: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("societyAddress"),
      then: yup
        .string()
        .nullable()
        .required("Address is required")
        .max(200, "Address must not exceed 200 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    date: yup
      .string() 
      .when("$fields", {
        is: () => fieldsArray.includes("date"),
        then: yup.string().nullable().required("Date is required"),
        otherwise: yup.string().notRequired().nullable(),
      }),

      noOfResidence: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("noOfResidence"),
        then: yup
          .string()
      .nullable()
      .matches(
        /^(?:[1-9][0-9]*|1000000\d*)$/,
        "Please enter a valid Residence"
      ),
      otherwise: yup.string().notRequired().nullable(),
    }),



    grossPlotArea: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("grossPlotArea"),
        then: yup
          .string()
      .nullable()
      .test(
        'is-positive', 
        'Gross Plot Area must be a positive number greater than 0', 
        value => !value || /^(?!0(\.0+)?$)\d+(\.\d+)?$/.test(value)
      )
      .max(100, "grossPlotArea  must not exceed 100 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),




    noOfCommercial: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("noOfCommercial"),
        then: yup
          .string()
      .nullable()
      .matches(
        /^(?:[1-9][0-9]*|1000000\d*)$/,
        "Please enter a valid No.Of.Commercial"
      ),
      otherwise: yup.string().notRequired().nullable(),
    }),

    builtUpAreaResidential: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("builtUpAreaResidential"),
        then: yup
          .string()
      .nullable()
      .max(100, "BuiltUp Area Residential must not exceed 100 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),

    builtUpAreaCommercial: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("builtUpAreaCommercial"),
        then: yup
          .string()
      .nullable()
      .max(100, "BuiltUpAreaCommercial must not exceed 100 characters"), otherwise: yup.string().notRequired().nullable(),
    }),

    fsiConsumedFsi: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("fsiConsumedFsi"),
      then: yup
    .string()
    .nullable()
    .max(100, "ConsumedFsi  must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  fsi_AvailableFsi: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("fsi_AvailableFsi"),
    then: yup
  .string()
    .nullable()
    .max(100, "AvailableFsi  must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  fsi_PermissibleFsi: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("fsi_PermissibleFsi"),
      then: yup
    .string()
    .nullable()
    .max(200, "PermissibleFsi  must not exceed 200 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  dpRestrictions: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("dpRestrictions"),
      then: yup
    .string()
    .nullable()
    .max(200, "Dp Restrictions must not exceed 200 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  buildingAge: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("buildingAge"),
      then: yup
    .string()
    .nullable()
    .max(100, "buildingAge must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  litigationsOrEncroachment: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("litigationsOrEncroachment"),
      then: yup
    .string()
    .nullable()
    .max(100, "LitigationsOrEncroachment must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  roadWidth: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("roadWidth"),
      then: yup
    .string()
    .nullable()
    .test(
      'is-positive', 
      'roadWidth must be a positive number greater than 0', 
      value => !value || /^(?!0(\.0+)?$)\d+(\.\d+)?$/.test(value)
    )
    .max(100, "roadWidth must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

    

  heightRestriction: yup
    .string()
    .when("$fields", {
      is: () => fieldsArray.includes("heightRestriction"),
      then: yup
    .string()
    .nullable()
    .required("heightRestriction is required")
    .max(100, "heightRestriction must not exceed 100 characters"),
    otherwise: yup.string().notRequired().nullable(),
  }),

  // scheme: yup
  //   .string()
  //   .required("Please select Scheme"),
  //    otherwise: yup.string().notRequired().nullable(),
  // }),

  requirements_ExtraArea: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("requirements_ExtraArea"),
    then: yup
  .string()
  .nullable()
  .max(100, "Must not exceed 100 characters"),
  otherwise: yup.string().notRequired().nullable(),
}),



  requirements_Rent: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("requirements_Rent"),
    then: yup
  .string()
  .nullable()
  .max(100, "Must not exceed 100 characters"),
  otherwise: yup.string().notRequired().nullable(),
}),



  requirements_Corpus: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("requirements_Corpus"),
    then: yup
  .string()
  .nullable()
  .max(100, "Must not exceed 100 characters"),
  otherwise: yup.string().notRequired().nullable(),
}),




  notes: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("notes"),
    then: yup
  .string()
  .nullable()
  .max(2000, "Must not exceed 2000 characters"),
  otherwise: yup.string().notRequired().nullable(),
}),

authority: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("authority"),
  then: yup
.string()
.nullable()
.matches(/^[a-zA-Z0-9\s]*$/, "This field can only contain letters, digits, and whitespace")
.max(100, "Must not exceed 100 characters"),
otherwise: yup.string().notRequired().nullable(),
}),

reference: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("reference"),
  then: yup
.string()
.nullable()
.matches(/^[a-zA-Z0-9\s]*$/, "This field can only contain letters, digits, and whitespace")
.max(100, "Must not exceed 100 characters"),
otherwise: yup.string().notRequired().nullable(),
}),


  leadGivenTo: yup
  .string()
  .when("$fields", {
    is: () => fieldsArray.includes("leadGivenTo"),
    then: yup
  .string()
  .nullable()
  .matches(/^[a-zA-Z0-9\s]*$/, "This field can only contain letters, digits, and whitespace")
  .max(100, "Must not exceed 100 characters"),
  otherwise: yup.string().notRequired().nullable(),
}),

secretaryName: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("secretaryName"),
  then: yup
.string()
.nullable()
.required("Name is required")
.matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
.max(30, "Name must not exceed 30 characters")
.min(3, "Name must have at least 3 characters"),
otherwise: yup.string().notRequired().nullable(),
}),


secretaryContactNo: yup
.string()
.when("$fields", {
   is: () => fieldsArray.includes("secretaryContactNo"), // removed the asterisk
   then: yup
    .string()
    .nullable()
    .required("Mobile number is required")
    .matches(
       /^(?:\+91\s?)?[6-9]\d{9}$/,
       "Please enter a valid contact number"
     )
    .test(
       "min-length",
       "Mobile number must be at least 10 digits",
       (value) => value && value.replace(/\D+/g, "").length >= 10
     )
    .max(13, "Contact number must not exceed 13 characters"),
   otherwise: yup.string().notRequired().nullable(),
 }),


chairmanName: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("chairmanName"),
  then: yup
.string()
.nullable()
.matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
.max(30, "Name must not exceed 30 characters")
.min(3, "Name must have at least 3 characters"),
otherwise: yup.string().notRequired().nullable(),
}),


chairmanContactNo: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("chairmanContactNo"),
  then: yup
.string()
.nullable()
.matches(
  /^(?:\+91\s?)?[6-9]\d{9}$/,
  "Please enter a valid contact number"
)
.max(13, "Contact number must not exceed 13 characters"),
otherwise: yup.string().notRequired().nullable(),
}),

emailId: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("emailId"),
  then: yup
.string()
.nullable()
.email("Please enter a valid email address")
.max(50, "Email must not exceed 50 characters"),
 otherwise: yup.string().notRequired().nullable(),
}),


referenceSocietyName: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("referenceSocietyName"),
  then: yup
.string()
.nullable()
.matches(/^[A-Za-z][A-Za-z0-9,.-]*(?:\s[A-Za-z0-9,.-]+)*$/, {
  message:
    "Society Name must start with a character and not contain multiple spaces,special characters",
  excludeEmptyString: true,
})
.max(50, "Society Name must not exceed 50 characters")
.test(
  "no-multiple-spaces",
  "Multiple spaces are not allowed in Society Name",
  (value) => !/\s{2,}/.test(value)
),
otherwise: yup.string().notRequired().nullable(),
}),


referenceContactPerson: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("referenceContactPerson"),
  then: yup
.string()
.nullable()
.matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
.max(30, "Name must not exceed 30 characters")
.min(3, "Name must have at least 3 characters"),
otherwise: yup.string().notRequired().nullable(),
}),



referenceContactNumber: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("referenceContactNumber"),
  then: yup
.string()
.nullable()
.matches(
  /^(?:\+91\s?)?[6-9]\d{9}$/,
  "Please enter a valid contact number"
)
.max(13, "Contact number must not exceed 13 characters"),
otherwise: yup.string().notRequired().nullable(),
}),

professionalDetails: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("professionalDetails"),
  then: yup
.string()
.nullable()
.max(1000, "Must not exceed 1000 characters"),
otherwise: yup.string().notRequired().nullable(),
}),

redevelopment: yup
.string()
.when("$fields", {
  is: () => fieldsArray.includes("redevelopment"),
  then: yup
.string()
.nullable()
.max(2000, "Must not exceed 2000 characters"),
otherwise: yup.string().notRequired().nullable(),
}),

      
  });
};

export default SocietyValidationsSection1;

export { yup };
