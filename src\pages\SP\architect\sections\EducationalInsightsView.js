// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
import MUITableCell from "../../MUITableCell";
import EducationalInsightsForm from "./EducationalInsightsEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const EducationalInsightsView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Educational Insights"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>YouTube Url / Video / Drive Link:</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {data?.educationalInsightsList?.map(
                      (educationalInsight, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography
                              className="data-field"
                              marginLeft={"15px"}
                            >
                              {educationalInsight?.url}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <EducationalInsightsForm
                data={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default EducationalInsightsView;
