// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Tab from "@mui/material/Tab";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";

import ProjectView from "./ProjectView";

const ServicesTabs = (props) => {
  // ** State

  const { tabContents, expanded, data, userData } = props;

  if (!tabContents || tabContents.length === 0) {
    return null;
  }
  const defaultTabId = tabContents[0].id.toString();

  // ** Hooks
  const [value, setValue] = useState(defaultTabId);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="forced scroll tabs example"
      >
        {tabContents?.map((tabContent) => (
          <Tab
            key={tabContent.id}
            value={tabContent.id.toString()}
            component="a"
            label={tabContent.name}
            onClick={(e) => e.preventDefault()}
          />
        ))}
      </TabList>
      {tabContents?.map((tabContent) => (
        <TabPanel
          key={tabContent.id}
          value={tabContent.id.toString()}
          sx={{
            pb: 1,
            pt: 3,
            px: { xs: "0", md: "0.5rem" },
            mt: "10px",
            overflowY: "auto",
          }}
        >
          <ProjectView
            data={data}
            expanded={expanded}
            serviceId={tabContent.id}
            userData={userData}
          />
        </TabPanel>
      ))}
    </TabContext>
  );
};

export default ServicesTabs;
