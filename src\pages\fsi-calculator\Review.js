// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Table from '@mui/material/Table'
import Switch from '@mui/material/Switch'
import TableRow from '@mui/material/TableRow'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import Typography from '@mui/material/Typography'
import TableContainer from '@mui/material/TableContainer'
import FormControlLabel from '@mui/material/FormControlLabel'

// ** Custom Components Imports
import CustomChip from 'src/@core/components/mui/chip'

const ReviewComplete = () => {
  return (
    <Grid container spacing={6}>
      <Grid item xs={12} lg={12} xl={12}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant='h5' sx={{ mb: 4 }}>
              Almost done! 🚀
            </Typography>
            <Typography sx={{ color: 'text.secondary' }}>
              Confirm your details and submit .
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <TableContainer>
              <Table>
                <TableBody
                  sx={{
                    '& .MuiTableCell-root': {
                      borderBottom: 0,
                      verticalAlign: 'top',
                      '&:last-of-type': { px: '0 !important' },
                      '&:first-of-type': { pl: '0 !important' },
                      py: theme => `${theme.spacing(0.75)} !important`
                    }
                  }}
                >
                  <TableRow>
                    <TableCell>
                      <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                       1. Net Plot Area
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX Sq.Mtrs </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX Sq.Ft </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXX %</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                      2. Total of Used FSI by Members
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX Sq.Mtrs</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX Sq.Ft</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX %</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                        3. No. of Members            
                      </Typography>
                    </TableCell>
                    <TableCell>
                    <Typography sx={{ color: 'text.secondary' }}>XXXX</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                      4. Road width (Mtrs)
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={{ color: 'text.secondary' }}>XXXX</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                        5. Basic FSI Calculation
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Table>
                        <TableBody>
                            <TableRow>
                                <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                        Base FSI
                                    </Typography>
                                    </TableCell>
                                    <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                        XXXX
                                    </Typography>
                                    </TableCell>
                                    <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                    XXXX
                                    </Typography>
                                </TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                        Premium FSI
                                    </Typography>
                                    </TableCell>
                                    <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                        XXXX
                                    </Typography>
                                    </TableCell>
                                    <TableCell>
                                    <Typography noWrap sx={{ fontWeight: 500, color: 'text.secondary' }}>
                                    XXXX
                                    </Typography>
                                </TableCell>
                            </TableRow>

                        </TableBody>
                      </Table>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
      </Grid>
      
    </Grid>
  )
}

export default ReviewComplete
