import * as yup from "yup";

export const BrokerValidationSection1 = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  return yup.object().shape({
    name: yup.string().when("$fields", {
      is: () => fieldsArray.includes("name"),
      then: yup
        .string()
        .required("Name is required")
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "Name must not exceed 30 characters")
        .min(3, "Name must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    companyName: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("name"),
        then: yup
          .string()
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "Company name must not exceed 30 characters")
          .min(3, "Company name must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    email: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("email"),
        then: yup
          .string()
          .nullable()
          .required("Email address is required")
          .email("Please enter a valid email address")
          .max(50, "Email must not exceed 50 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    mobileNumber: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("mobileNumber"),
        then: yup
          .string()
          .required("Mobile number is required")
          .nullable()
          .matches(
            /^(?:\+91\s?)?[6-9]\d{9}$/,
            "Please enter a valid contact number"
          )
          .max(13, "Contact number must not exceed 13 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    address: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("address"),
        then: yup
          .string()
          .required("Address is required")
          .nullable()
          .max(200, "Address must not exceed 200 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    teamSize: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("teamSize"),
        then: yup
          .string()
          .required("Team size is required")
          .nullable()
          .matches(
            /^(?:[1-9]|[1-9][0-9]|100)$/,
            "Please enter a valid team size (1 to 100)"
          ),
        otherwise: yup.string().notRequired().nullable(),
      }),
    reraNumber: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("reraNumber"),
        then: yup
          .string()
          .required("Rera Number is required")
          .nullable()
          .max(30, "RERA number must not exceed 30 characters")
          .matches(
            /^[A-Z0-9]+$/,
            "RERA number must contain only uppercase letters and numbers"
          ),
        otherwise: yup.string().notRequired().nullable(),
      }),
    websiteUrl: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("websiteUrl"),
        then: yup
          .string()
          .url('Please enter a valid Website URL: https://www.example.com')
          .nullable()
          .max(50, "Website URL must not exceed 50 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    yearsOfExperience: yup.string().when("$fields", {
      is: () => fieldsArray.includes("yearsOfExperience"),
      then: yup
        .string()
        .required("Please select years of Experience")
        .nullable(),
      otherwise: yup.string().notRequired().nullable(),
    }),
    briefProfile: yup.string().when("$fields", {
      is: () => fieldsArray.includes("briefProfile"),
      then: yup
        .string()
        .nullable()
        .max(
          1000,
          "Brief profile must have a maximum length of 1000 characters."
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    awards: yup.string().when("$fields", {
      is: () => fieldsArray.includes("awards"),
      then: yup
        .string()
        .nullable()
        .max(200, "Awards must have a maximum length of 200 characters."),
      otherwise: yup.string().notRequired().nullable(),
    }),
  });
};

export default BrokerValidationSection1;

export { yup };
