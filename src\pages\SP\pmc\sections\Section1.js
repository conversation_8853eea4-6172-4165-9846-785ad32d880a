// ** React Imports
import { forwardRef } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import InputAdornment from '@mui/material/InputAdornment'
import { Box } from '@mui/system'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'

// ** Icon Imports
import Icon from 'src/@core/components/icon'
import { Typography } from '@mui/material'
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import { PmcValidations } from './PmcValidations'
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";



const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false,
  Textarea: ''
}

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const Section1 = ({ onCancel,formData }) => {

  //Hooks
  const auth = useAuth();

  const fields = ["name", "companyName", "address", "mobileNumber","email" , "websiteUrl"];

  const { register, handleSubmit, setError, control, formState: { errors } } =useForm({
    resolver: yupResolver(PmcValidations(fields
      )),
    mode: "onChange",
  });


  async function submit(data) {

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value])
    );
    const response = await auth.updateEntity(trimmedData,() => {
      console.error("PMC Company Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      
        <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
        <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
             
              defaultValue={formData?.name}
              render={({ field }) => (
                <NameTextField
                    {...field}
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your name"
                  error={Boolean(errors.name)}
                  aria-describedby="validation-name"
                  helperText={errors.name?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon icon="tabler:user" style={{ fontSize: "16px" }} />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name='companyName'
                control={control}
                rules={{ required: true }}
                defaultValue={formData?.companyName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label='Company Name'
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ maxLength: 50 }}
                   
                    placeholder='Enter company name'
                    error={Boolean(errors.companyName)}
                    aria-describedby='validation-companyName'
                    helperText={errors.companyName?.message}
                  />
                )}
              />
           
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Typography className='data-field'>
                 System Code
                 </Typography>    
                  <Typography style={{ fontWeight: 'bold' }}>
                 {formData?.systemCode}
                 </Typography>           
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name='address'
                control={control}
                rules={{ required: true }}
                defaultValue={formData?.address}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label='Address'
                    InputLabelProps={{ shrink: true }}
                   
                    error={Boolean(errors.address)}
                    aria-describedby='broker-validation-basic-address'
                    helperText={errors.address?.message}
                  />
                )}
              />
                         </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name='mobileNumber'
                control={control}
                
                defaultValue={formData?.mobileNumber}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type='tel'
                    label='Contact Number'
                    InputLabelProps={{ shrink: true }}
               
                    error={Boolean(errors.mobileNumber)}
                    placeholder='+91 12 345 678 90'
                  
                    aria-describedby='validation-mobileNumber'
                    helperText={errors.mobileNumber?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Icon icon='tabler:phone' style={{ fontSize: '16px' }} />
                        </InputAdornment>
                      )
                    }}
                  />
                )}
              />

            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name='email'
                control={control}
               defaultValue={formData?.email}
                render={({ field }) => (
                  <EmailTextField
                    {...field}
                    type='email'
                    label='Email '                
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.email)}
                    placeholder='Enter email address'
                    aria-describedby='validation-email'
                    helperText={errors.email?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Icon icon='tabler:mail' style={{ fontSize: '16px' }} />
                        </InputAdornment>
                      )
                    }}
                  />
                )}
              />
             
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              defaultValue={formData?.websiteUrl}
              render={({ field }) => (
                <TextField
                {...field}
                  type="url"
                
                  label="Website URL"
              
                  InputLabelProps={{ shrink: true }}
               
                  error={Boolean(errors.websiteUrl)}
                  placeholder="https://www.example.com"
                  aria-describedby="validation-websiteUrl"
                  helperText={errors.websiteUrl?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Icon
                          icon="tabler:LinkIcon"
                          style={{ fontSize: "16px" }}
                        />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          
          </FormControl>
          </Grid>

          <Grid item xs={12}>
          <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
        </Grid>
     
    </Box>
  )
}

export default Section1
