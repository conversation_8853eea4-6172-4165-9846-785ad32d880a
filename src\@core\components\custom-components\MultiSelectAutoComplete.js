import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import CustomAutocomplete from './CustomAutoComplete';
import CustomTextField from './CustomTextField';

const MultiSelectAutoComplete = (props) => {
  const { id, label, nameArray, value, onChange } = props;

  const [selectedValues, setSelectedValues] = useState(value || []);
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  useEffect(() => {
    setSelectedValues(value || []);
  }, [value]);

  const handleOptionSelect = (event, newValue) => {
    setSelectedValues(newValue);
    onChange({ target: { value: newValue } });
  };

  const handleRemoveSelectedOption = (optionToRemove) => {
    const newSelectedValues = selectedValues.filter(option => option !== optionToRemove);
    setSelectedValues(newSelectedValues);
    onChange({ target: { value: newSelectedValues } });
  };

  const handleDropdownOpen = () => {
    setDropdownOpen(true);
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false);
  };

  return (
    <Box sx={{ position: 'relative', zIndex: 1 }}>
      <CustomAutocomplete
        multiple
        autoHighlight
        id={id}
        options={nameArray.filter(option => !selectedValues.some(selected => selected.key === option.key))}
        getOptionLabel={option => option.key || ''}
        value={selectedValues}
        onChange={(event, newValue) => handleOptionSelect(event, newValue)}
        onOpen={handleDropdownOpen}
        onClose={handleDropdownClose}
        open={isDropdownOpen}
        renderOption={(props, option) => (
          <Box
            component='li'
            {...props}
            onClick={(event) => handleOptionSelect(event, [...selectedValues, option])}
          >
            {option && option.key}
          </Box>
        )}
        renderInput={params => (
          <CustomTextField
            {...params}
            size='small'
            placeholder={`${label}`}
            sx={{ borderRadius: 1 }}
            inputProps={{
              ...params.inputProps,
            }}
          />
        )}
      />
      {/* <Box>
        {selectedValues.map((option) => (
          <Box
            key={option.key}
            onClick={() => handleRemoveSelectedOption(option)}
          >
            {option.key}
          </Box>
        ))}
      </Box> */}
    </Box>
  );
};

export default MultiSelectAutoComplete;
