import React, { useState,useEffect } from "react";
import {
  Box,
  Button,
  Grid,
  Typography,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Di<PERSON>r,
  Drawer,
  InputLabel,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const FilterCard = ({ open, onClose, searchKeyword, setSearchKeyword, category, setCategory, handleSearch }) => {
  const [emailValue, setEmailValue] = useState(searchKeyword);
  const [userType, setUserType] = useState(null); 
  const [userTypeLabel, setUserTypeLabel] = useState("Users");
  const [roleTypeId, setRoleTypeId] = useState("");
  const {
    register,
    clearErrors,
    reset,
  } = useForm();
  const handleEmailChange = (event) => {
    setEmailValue(event.target.value);
  };
  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [roleTypeOptions, setRoleTypeOptions] = useState([]);
  useEffect(() => {
    // Fetch Categories
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=USERS_CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfRoleTypes(res.data.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = [];
      listOfRoleTypes.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setRoleTypeOptions(data);
    }
  }, [listOfRoleTypes]);
  const handleUserTypeChange = (newValue) => {
   const roleType = listOfRoleTypes.filter((entry) => entry.id === newValue);
   setRoleTypeId(newValue);
    setUserType(roleType);
  };

  const handleSearchClick = () => {
    handleSearch(emailValue, userTypeLabel);
    setSearchKeyword(emailValue);  
    setCategory(roleTypeId); 
    onClose();                    
  };

  
  const handleCancel = () => {
    reset({
      category: "",  
    });

    setEmailValue("");  
    setRoleTypeId("");   
    setUserType(null);  
    handleSearch("", ""); 
    onClose();
  };
  
  const handleClose = () => {
    toggle();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose} variant="temporary">
      <Box
        width={{ xs: "100%", md: 700 }}
        height="100%"
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="space-between"
        style={{ flex: 1 }}
      >
        <Box p={3} width="100%">
          <Typography variant="h6">Search Filter</Typography>
          <Divider style={{ margin: "15px" }} />

          <Grid
            marginTop={2}
            container
            spacing={4}
            marginBottom={5}
            style={{
              alignItems: "center",
              width: "100%",
            }}
          >
            <Grid item xs={12} sm={4}>
                    <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"roleTypeId-select"}
                      label={"Select Category"}
                      name="roleTypeId-select"
                      nameArray={roleTypeOptions}
                      defaultValue={roleTypeId}
                      value={roleTypeId}
                      onChange={(event) =>
                        handleUserTypeChange(event.target.value)
                      }
                      aria-describedby="roleTypeId-select"
                    />
                  </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email Id"
                value={emailValue}
                size="small"
                onChange={handleEmailChange}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          style={{ justifyContent: "flex-end" }}
          display="flex"
          flexDirection={{ xs: "column", md: "row" }}
          marginTop={{ xs: 2, md: 5 }}
          marginBottom={{ xs: 2, md: 5 }}
          marginRight={{ xs: 0, md: 10 }}
        >
          <Button
            variant="contained"
            sx={{ marginLeft: { xs: 0, md: 2 } }}
            onClick={handleSearchClick}
          >
            Search
          </Button>
          &nbsp;
          <Button  onClick={handleCancel}  sx={{ marginLeft: { xs: 0, md: 2 } }}>
            Clear All
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default FilterCard;
