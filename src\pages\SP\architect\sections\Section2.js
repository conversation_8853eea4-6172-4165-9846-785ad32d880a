// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  TextField,
} from "@mui/material";
import SelectProject from "src/@core/components/custom-components/SelectProject";
//import { C } from '@fullcalendar/core/internal-common'

import { useAuth } from "src/hooks/useAuth";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

// ** Icon Imports

const location = [
  {
    value: "ISLAND",
    key: "Island",
  },
  {
    value: "WESTERN_SUBURB",
    key: "Western Suburb",
  },
  {
    value: "CENTRAL_SUBURB",
    key: "Central Suburb",
  },
  {
    value: "THANE",
    key: "Thane",
  },
  {
    value: "ALL",
    key: "All",
  },
  {
    value: "OTHER",
    key: "Other",
  },
];

const designs = [
  {
    value: "RESIDENTIAL",
    key: "Residential",
  },
  {
    value: "COMMERCIAL",
    key: "Commercial",
  },
  {
    value: "RETAIL",
    key: "Retail",
  },
  {
    value: "INDUSTRIAL",
    key: "Industrial",
  },
  {
    value: "ALL",
    key: "All",
  },
  {
    value: "OTHER",
    key: "Other",
  },
];
const communication = [
  {
    value: "MCGM",
    key: "MCGM",
  },
  {
    value: "MHADA",
    key: "MHADA",
  },
  {
    value: "SRA_COLLECTOR",
    key: "SRA Collector",
  },
  {
    value: "ALL",
    key: "All",
  },
  {
    value: "OTHER",
    key: "Other",
  },
];
const experience = [
  {
    value: "LESS_THAN_5_YEARS",
    key: "Less than 5 years",
  },
  {
    value: "_5_TO_10_YEARS",
    key: "5 to 10 years",
  },
  {
    value: "MORE_THAN_10_YEARS",
    key: "More than 10 years",
  },
  {
    value: "ZERO_YEARS",
    key: "Zero years",
  },
];

const Section2 = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [yearsOfExperience, setYearsOfExperience] = useState(
    formData?.yearsOfExperience
  );
  const [areaofOperation, setAreaofOperation] = useState(
    formData?.areaofOperation
  );
  const [design, setDesign] = useState(formData?.design);
  const [liasoning, setLiasoning] = useState(formData?.liasoning);
  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
  } = useForm();

  async function submit(data) {
    // Replace empty string values with null
    const cleanedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string"
          ? value.trim() === "" && key !== "awards"
            ? null
            : value.trim()
          : value,
      ])
    );

    console.log("cleanedData", cleanedData);

    const response = await auth.updateEntity(cleanedData, () => {
      console.error("Architect Details failed");
    });

    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"areaofOperation"}
              label={"Area of Operation"}
              name="areaofOperation"
              nameArray={location}
              defaultValue={formData?.areaofOperation}
              value={areaofOperation}
              onChange={(e) => {
                setAreaofOperation(e.target.value);
              }}
              error={Boolean(errors.areaofOperation)}
              aria-describedby="validation-areaofOperation"
            />
            {errors.areaofOperation && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-areaofOperation"
              >
                Please select area of operation
              </FormHelperText>
            )}
          </Grid>
          {areaofOperation === "OTHER" && (
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="otherArea"
                  control={control}
                  defaultValue={formData?.otherArea}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Other Area of Operation"
                      placeholder="Enter area of operation"
                      error={Boolean(errors.otherArea)}
                      aria-describedby="validation-otherArea"
                    />
                  )}
                />
                {errors.otherArea && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-otherArea"
                  >
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12} sm={4}>
            <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"design"}
              label={"Design"}
              name="design"
              nameArray={designs}
              defaultValue={formData?.design}
              value={design}
              onChange={(e) => setDesign(e.target.value)}
              error={Boolean(errors.design)}
              aria-describedby="validation-design"
            />
            {errors.design && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-design"
              >
                Please select the design
              </FormHelperText>
            )}
          </Grid>
          {design === "OTHER" && (
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="otherDesign"
                  control={control}
                  defaultValue={formData?.otherDesign}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Other Design"
                      placeholder="Enter design"
                      error={Boolean(errors.otherDesign)}
                      aria-describedby="validation-otherDesign"
                    />
                  )}
                />
                {errors.otherDesign && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-otherDesign"
                  >
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12} sm={4}>
            <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"liasoning"}
              label={"Liasoning"}
              name="liasoning"
              nameArray={communication}
              defaultValue={formData?.liasoning}
              value={liasoning}
              onChange={(e) => setLiasoning(e.target.value)}
              error={Boolean(errors.liasoning)}
              aria-describedby="validation-liasoning"
            />
            {errors.liasoning && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-liasoning"
              >
                Please select liasoning
              </FormHelperText>
            )}
          </Grid>
          {liasoning === "OTHER" && (
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="otherLiasoning"
                  control={control}
                  defaultValue={formData?.otherLiasoning}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Other liasoning"
                      placeholder="Enter liasoning"
                      error={Boolean(errors.otherLiasoning)}
                      aria-describedby="validation-otherLiasoning"
                    />
                  )}
                />
                {errors.otherLiasoning && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-otherLiasoning"
                  >
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          )}
          <Grid item xs={12} sm={4}>
            <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"yearsOfExperience"}
              label={"Years of Experience"}
              name="yearsOfExperience"
              nameArray={experience}
              defaultValue={formData?.yearsOfExperience}
              value={yearsOfExperience}
              onChange={(e) => setYearsOfExperience(e.target.value)}
              error={Boolean(errors.yearsOfExperience)}
              aria-describedby="validation-yearsOfExperience"
            />
            {errors.yearsOfExperience && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-yearsOfExperience"
              >
                Please select years of Experience
              </FormHelperText>
            )}
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="teamSize"
                control={control}
                rules={{
                  required: true,
                  pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
                }}
                defaultValue={formData?.teamSize}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    type="number"
                    value={value}
                    label="Team Size"
                    InputLabelProps={{ shrink: true }}
                    onChange={onChange}
                    error={Boolean(errors.teamSize)}
                    placeholder="Enter team size(1-100)"
                    aria-describedby="validation-teamSize"
                    inputProps={{ min: 1, max: 100 }}
                  />
                )}
              />
              {errors.teamSize?.type === "required" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-teamSize"
                >
                  Team Size is required
                </FormHelperText>
              )}
              {errors.teamSize?.type === "pattern" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-teamSize"
                >
                  Please enter a valid Team Size (1-100)
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="awards"
                control={control}
                defaultValue={formData?.awards}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Awards"
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ maxLength: 1000 }}
                    aria-describedby="Section2_awards"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="briefProfile"
                control={control}
                rules={{ required: true }}
                defaultValue={formData?.briefProfile}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Brief Profile in your words"
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ maxLength: 1000 }}
                    error={Boolean(errors.briefProfile)}
                    aria-describedby="Section2_briefProfile"
                  />
                )}
              />
              {errors.briefProfile && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="Section2_briefProfile"
                >
                  This field is required
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Section2;
