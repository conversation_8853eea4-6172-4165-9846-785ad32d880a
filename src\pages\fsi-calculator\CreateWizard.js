// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Avatar from '@mui/material/Avatar'
import Button from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import StepLabel from '@mui/material/StepLabel'
import Typography from '@mui/material/Typography'
import MuiStep from '@mui/material/Step'
import MuiStepper from '@mui/material/Stepper'
import CardContent from '@mui/material/CardContent'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

// ** Step Components
import BasicDetails from './BasicDetails'
import StepReview from './OtherDetails'
import StepDealUsage from './MemberDetails'
import StepDealDetails from './PlotDetails'

// ** Util Import
import { hexToRGBA } from 'src/@core/utils/hex-to-rgba'
import PerfectScrollbar from 'react-perfect-scrollbar'
import useMediaQuery from '@mui/material/useMediaQuery'

// ** Styled Components
import StepperWrapper from 'src/@core/styles/mui/stepper'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import PlotDetails from './PlotDetails'
import MemberDetails from './MemberDetails'
import OtherDetails from './OtherDetails'
import ReviewComplete from './Review'

const steps = [
  {
    title: 'Basic Details',
    icon: 'tabler:users'
  },
  {
    icon: 'tabler:id',
    title: 'Plot Details'
  },
  {
    title: 'Member Expectations',
    icon: 'tabler:credit-card'
  },
  {
    icon: 'tabler:checkbox',
    title: 'Other Details'
  },
  {
    icon: 'tabler:checkbox',
    title: 'Review and Submit'
  }
]

const Stepper = styled(MuiStepper)(({ theme }) => ({
  height: '100%',
  minWidth: '15rem',
  '& .MuiStep-root:not(:last-of-type) .MuiStepLabel-root': {
    paddingBottom: theme.spacing(5)
  },
  [theme.breakpoints.down('md')]: {
    minWidth: 0
  }
}))

const StepperHeaderContainer = styled(CardContent)(({ theme }) => ({
  borderRight: `1px solid ${theme.palette.divider}`,
  [theme.breakpoints.down('md')]: {
    borderRight: 0,
    borderBottom: `1px solid ${theme.palette.divider}`
  }
}))

const StepperQAContainer = styled(CardContent)(({ theme }) => ({
  position: 'relative',
  [theme.breakpoints.up('md')]: {
    width: '100%'
  },
  [theme.breakpoints.up('lg')]: {
    width: 'calc(100% - 17rem)'
  },
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}))

const ScrollWrapper = ({ children, hidden }) => {
  if (hidden) {
    return <Box sx={{ maxHeight: 330, overflowY: 'auto', overflowX: 'hidden' }}>{children}</Box>
  } else {
    return <PerfectScrollbar options={{ wheelPropagation: true }}>{children}</PerfectScrollbar>
  }
}

const BoxFooter = styled('div')(({ theme }) => ({
  zIndex: 11,
  width: '100%',

  // borderTop: `1px solid ${theme.palette.divider}`,
  bottom: 0,
  right: 0,
  position: 'absolute',
  [theme.breakpoints.down('lg')]: {
    width: '100%'
  }
}))

const Step = styled(MuiStep)(({ theme }) => ({
  '& .MuiStepLabel-root': {
    paddingTop: 0
  },
  '&:not(:last-of-type) .MuiStepLabel-root': {
    paddingBottom: theme.spacing(6)
  },
  '&:last-of-type .MuiStepLabel-root': {
    paddingBottom: 0
  },
  '& .MuiStepLabel-iconContainer': {
    display: 'none'
  },
  '& .step-subtitle': {
    color: `${theme.palette.text.disabled} !important`
  },
  '& + svg': {
    color: theme.palette.text.disabled
  },
  '&.Mui-completed .step-title': {
    color: theme.palette.text.disabled
  },
  '& .MuiStepLabel-label': {
    cursor: 'pointer'
  }
}))

const CreateWizard = () => {
  // ** States
  const [activeStep, setActiveStep] = useState(0)

  const hidden = useMediaQuery(theme => theme.breakpoints.down('lg'))

  // Handle Stepper
  const handleNext = () => {
    setActiveStep(activeStep + 1)
  }

  const handlePrev = () => {
    if (activeStep !== 0) {
      setActiveStep(activeStep - 1)
    }
  }

  const getStepContent = step => {
    switch (step) {
      case 0:
        return <BasicDetails />
      case 1:
        return <PlotDetails />
      case 2:
        return <MemberDetails />
      case 3:
        return <OtherDetails />
      case 4:
        return <ReviewComplete/>
      default:
        return null
    }
  }

  const renderContent = () => {
    return getStepContent(activeStep)
  }

  const renderFooter = () => {
    const stepCondition = activeStep === steps.length - 1

    return (
      <Box sx={{ mt: 6, display: 'flex', justifyContent: 'space-between', position: 'sticky', bottom: 0, width: '100%', zIndex: 1 }}>
        <Button
          color='secondary'
          variant='outlined'
          onClick={handlePrev}
          disabled={activeStep === 0}
          startIcon={<Icon icon='tabler:chevron-left' />}
          sx={{ marginLeft: '15px', marginBottom: '15px' }}
        >
          Previous
        </Button>
        <Button
          variant='contained'
          color={stepCondition ? 'success' : 'primary'}
          {...(!stepCondition ? { endIcon: <Icon icon='tabler:chevron-right' /> } : {})}
          onClick={() => (stepCondition ? alert('Submitted..!!') : handleNext())}
          sx={{ marginRight: '15px', marginBottom: '15px' }}
        >
          {stepCondition ? 'Submit' : 'Next'}
        </Button>
      </Box>
    )
  }

  return (
    <DatePickerWrapper>
      <Card
        sx={{
          height: { lg: 600 },
          display: 'flex',
          flexDirection: { xs: 'column', lg: 'row' },
          marginX: { lg: 'auto' },
          '@media (min-width: 1600px)': { height: 630 }
        }}
      >
        <StepperHeaderContainer>
          <StepperWrapper sx={{ height: '100%' }}>
            <Stepper
              connector={<></>}
              orientation='vertical'
              activeStep={activeStep}
              sx={{ height: '100%', minWidth: '15rem' }}
            >
              {steps.map((step, index) => {
                const RenderAvatar = activeStep >= index ? CustomAvatar : Avatar

                return (
                  <Step
                    key={index}
                    onClick={() => setActiveStep(index)}
                    sx={{ '&.Mui-completed + svg': { color: 'primary.main' } }}
                  >
                    <StepLabel>
                      <div className='step-label'>
                        <RenderAvatar
                          variant='rounded'
                          {...(activeStep >= index && { skin: 'light' })}
                          {...(activeStep === index && { skin: 'filled' })}
                          {...(activeStep >= index && { color: 'primary' })}
                          sx={{
                            ...(activeStep === index && { boxShadow: theme => theme.shadows[3] }),
                            ...(activeStep > index && { color: theme => hexToRGBA(theme.palette.primary.main, 0.4) })
                          }}
                        >
                          <Icon icon={step.icon} />
                        </RenderAvatar>
                        <div>
                          <Typography className='step-title'>{step.title}</Typography>
                          <Typography className='step-subtitle'>{step.subtitle}</Typography>
                        </div>
                      </div>
                    </StepLabel>
                  </Step>
                )
              })}
            </Stepper>
          </StepperWrapper>
        </StepperHeaderContainer>
        <StepperQAContainer
          sx={{

            px: { xs: 2.75, md: 4.75 },
            pt: theme => `${theme.spacing(2.5)} !important`,
            pb: theme => `${theme.spacing(16)} !important`,
            '@media (max-width:600px)': {
              pb: theme => `${theme.spacing(40)} !important`
            }
          }}
        >
          <ScrollWrapper hidden={hidden}>
            <Box>{renderContent()}</Box>
          </ScrollWrapper>
          <BoxFooter>{renderFooter()}</BoxFooter>
        </StepperQAContainer>
      </Card>

    </DatePickerWrapper>
  )
}


export default CreateWizard
