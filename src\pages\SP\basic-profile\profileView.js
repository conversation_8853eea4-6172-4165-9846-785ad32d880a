import Typography from "@mui/material/Typography";
import { useContext, useState, React, Fragment, useEffect } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Card,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableCell,
} from "@mui/material";
import ProfileEdit from "./ProfileEdit";
import { AuthContext } from "src/context/AuthContext";

const fieldLabelStyle = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ProfileView = ({ data, userData, employeesData }) => {
  const { listValues } = useContext(AuthContext);

  const servicesProvidedNames = data?.servicesProvided
    ?.map((serviceId) => {
      const serviceObject = listValues?.find((item) => item.id === serviceId);
      return serviceObject ? serviceObject.name : null;
    })
    .filter(Boolean);

  const locationName = data?.locationId
    ? listValues?.find((item) => item.id === data.locationId)?.name
    : null;

  const portalsRegisteredName = data?.portalsRegistered
    ? listValues?.find((item) => item.id === data.portalsRegistered)?.name
    : null;

  const designation = data?.designation
    ? listValues.find((item) => item.id === data?.designation)?.name
    : null;

  const sourceGroup = data?.sourceGroup
    ? listValues.find((item) => item?.id === data?.sourceGroup)?.name
    : null;

  const subSourceGroup = data?.subSourceGroup
    ? listValues.find((item) => item?.id === data?.subSourceGroup)?.name
    : null;

  const leadStatus = data?.leadStatus
    ? listValues.find((item) => item?.id === data?.leadStatus)?.name
    : null;

  const leadPriority = data?.leadPriority
    ? listValues.find((item) => item?.id === data?.leadPriority)?.name
    : null;

  const packageType = data?.packageType
    ? listValues.find((item) => item?.id === data?.packageType)?.name
    : null;

  const portalsRegistered = data?.portalsRegistered
    ? listValues.find((item) => item?.id === data?.portalsRegistered)?.name
    : null;

  const [assignedTo, setAssignedTo] = useState(data?.assignedTo);
  const handleAssignedToChange = (event) => {
    const selectedId = event.target.value;
    setAssignedTo(selectedId);
  };

  useEffect(() => {
    if (!!data && !!data.assignedTo) {
      setAssignedTo(data.assignedTo);
    }
  }, [data]);

  const [assignedToName, setAssignedToName] = useState("");
  const [createdBy, setCreatedBy] = useState(data?.createdBy);
  const [createdUserName, setCreatedUserName] = useState("");
  useEffect(() => {
    if (!!assignedTo && employeesData && employeesData.length > 0) {
      setAssignedToName(
        employeesData?.find((item) => item.id == assignedTo)?.name
      );
    }
  }, [assignedTo, employeesData]);

  useEffect(() => {
    if (employeesData && employeesData.length > 0) {
      setCreatedUserName(
        employeesData?.find((item) => item.id == createdBy)?.name
      );
    }
  }, [createdBy, employeesData]);

  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const sections = [
    {
      title: "Basic Information",
      fields: [
        {
          label: "Individual Name",
          variable: "individualName",
        },
        {
          label: "Designation",
          variable: "designation",
        },
        {
          label: "Other Designation",
          variable: "otherDesignation",
        },
        {
          label: "Mobile Number",
          variable: "mobileNumber",
        },
        {
          label: "Alternate Mobile Number",
          variable: "alternateMobileNumber",
        },
        {
          label: "Email",
          variable: "email",
        },
        {
          label: "Website",
          variable: "websiteUrl",
        },
        {
          label: "Social Media Presence",
          variable: "socialMediaPresence",
        },
      ],
    },
    {
      title: "Business Information",
      fields: [
        {
          label: "Type of Profession",
          variable: "servicesProvided",
        },
        {
          label: "Other Profession Type",
          variable: "anyOtherServiceProvided",
        },
        {
          label: "Company Type",
          variable: "companyType",
        },
        {
          label: "Company Name",
          variable: "companyName",
        },
        {
          label: "Address",
          variable: "address",
        },
        {
          label: "Location",
          variable: "locationId",
        },
        {
          label: "Portals Registered",
          variable: "portalsRegistered",
        },
        {
          label: "Other Portals Registered",
          variable: "anyOtherPortalRegistered",
        },
        {
          label: "PAN Number",
          variable: "panNo",
        },
        {
          label: "GST/Not GST/Both",
          variable: "gstNo",
        },
        {
          label: "TAN Number",
          variable: "tanNo",
        },
        {
          label: "CIN No (If Applicable)",
          variable: "cinNo",
        },
        {
          label: "Year of Experience",
          variable: "yearsOfExperience",
        },
        {
          label: "No of Team Members",
          variable: "teamSize",
        },
        {
          label: "No of Sales Team Members",
          variable: "noOfSalesTeamMembers",
        },
        {
          label: "Type of Client Served",
          variable: "typeOfClientServed",
        },
        {
          label: "Last 3 years' Turnover (Rs. Lacs)",
          variable: "lastThreeYearsTurnOver",
        },
        {
          label: "Completed Projects/Cases",
          variable: "completedProjectsOrCases",
        },
        {
          label: "Ongoing Projects/Cases",
          variable: "onGoingProjectsOrCases",
        },
      ],
    },
    {
      title: "Bank Details",
      fields: [
        {
          label: "Bank Name",
          variable: "bankName",
        },
        { label: "Branch", variable: "branch" },
        {
          label: "Account Number",
          variable: "accountNumber",
        },

        {
          label: "IFSC Code",
          variable: "ifscCode",
        },
      ],
    },
  ];

  if (userData && userData.id !== undefined) {
    sections.push({
      title: "Status and Assignment Details",
      fields: [
        {
          label: "Assigned To",
          variable: "assignedTo",
        },
        { label: "Lead Status", variable: "leadStatus" },
        { label: "Lead Priority", variable: "leadPriority" },
        {
          label: "Reference",
          variable: "reference",
        },
        {
          label: "Other Reference",
          variable: "otherReference",
        },
        {
          label: "Created On",
          variable: "createdOn",
        },
        {
          label: "Created By",
          variable: "createdBy",
        },
        {
          label: "Smart Summary",
          variable: "remarks",
        },
        {
          label: "Package Type",
          variable: "packageType",
        },
      ],
    });
  }

  return (
    <>
      {state === "view" && (
        <TableContainer
        sx={{
          padding: "4px 6px",
          width: "100%",
          overflowX: "auto",
          '&:hover': {
            cursor: "pointer",
          }
        }}
          onClick={viewClick}
        >
          <Table sx={{ width: "100%", tableLayout: "fixed" }}>
            {sections.map((section, sectionIndex) => (
              <TableBody
                key={sectionIndex}
                sx={{
                  "& .MuiTableCell-root": {
                    p: "10.8px 9px !important",
                    wordWrap: "break-word",
                    whiteSpace: "pre-wrap",
                  },
                }}
              >
                <TableRow sx={{ backgroundColor: "#f2f7f2", height: "32px !important",mt:2 }}>
                  <TableCell colSpan={2} sx={{ padding: "4px" }}>
                    <Typography variant="body1" fontWeight={"bold"}>
                      {section?.title}
                    </Typography>
                  </TableCell>
                </TableRow>
                {section.fields.map((field, fieldIndex) => (
                  <TableRow key={fieldIndex}>
                    <TableCell >
                      <Typography style={fieldLabelStyle}>
                        {field?.label}:
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        style={{
                          fontWeight: "bold",
                          wordWrap: "break-word",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        {field.label === "Location"
                          ? locationName
                          : field.label === "Portals Registered"
                          ? portalsRegisteredName
                          : field.label === "Type of Profession"
                          ? servicesProvidedNames?.join(", ")
                          : field.label === "Designation"
                          ? designation || data?.otherDesignation
                          : field.label === "Lead Status"
                          ? leadStatus
                          : field.label === "Lead Priority"
                          ? leadPriority
                          : field.label === "Assigned To"
                          ? assignedToName
                          : field.label === "Created By"
                          ? createdUserName
                          : field.label === "Specify Profession Type"
                          ? data?.anyOtherServiceProvided
                          : field.label === "Specify Portals Registered"
                          ? data?.anyOtherPortalRegistered
                          : field.label === "Package Type"
                          ? packageType
                          : field.label === "Portals Registered"
                          ? portalsRegistered
                          : data?.[field?.variable]}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ))}
          </Table>
        </TableContainer>
      )}
      {state === "edit" && (
        <ProfileEdit
          userData={userData}
          formData={data}
          onCancel={editClick}
          employeesData={employeesData}
          handleAssignedToChange={handleAssignedToChange}
          assignedTo={assignedTo}
          assignedToName={assignedToName}
          createdBy={createdUserName}
        />
      )}
    </>
  );
};

export default ProfileView;
