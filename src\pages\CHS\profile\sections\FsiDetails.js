// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section1 from 'src/pages/SP/broker/sections/Section1'
import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section2 from './Section2'
import Section3 from './Section3'
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const FsiDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
    const theme = useTheme()

    const [state3, setState3] = useState(true)

    const handleState3 = () => {
      setState3(!state3)
    }
    
    
    // Pre-Populating code Start
    // const [fsiDetails, setFsiDetails] = useState({
    //     buildingAge:"",
    //     heightRestriction:"",
    //     fsiConsumedFsi:"",
    //     fsi_AvailableFsi:"",
    //     fsi_PermissibleFsi:"",
    //     scheme:"",
    //     dpRestrictions:"",
    //     litigationsOrEncroachment:""
    //   });

    return (
        <>
         {/* {can('society_fsiDetails_READ') && */}
          <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'FSI'}
                body={
                  <>
                    {state3 && (                     
                          <TableContainer sx={{ padding:'4px 6px' }}
                            className='tableBody'
                           // onClick={can('society_fsiDetails_UPDATE') ? handleState3 : null}>
                            onClick={ handleState3}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Consumed FSI:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsiConsumedFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Available FSI:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsi_AvailableFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Permissable FSI :</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsi_PermissibleFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Scheme:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                  <Typography className='data-field'>
                                    {data?.scheme === '_33_7B' ? '33(7B)' : data?.scheme}
                                 </Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>DP Restrictions:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.dpRestrictions}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Litigation/Encroachment:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.litigationsOrEncroachment}</Typography>
                                  </MUITableCell>
                                </TableRow>

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Building Age:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.buildingAge}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Height Restriction:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.heightRestriction}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        

                    )}
                    {!state3 && <Section3 formData={data} onCancel={handleState3} />}
                  </>
                }
                expanded={expanded}
              />
              {/* } */}
        </>
    );

}
export default FsiDetails;