import React, { useState, useEffect } from "react";
import {
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Button,
  Card,
  Grid,
  Divider,
} from "@mui/material";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import auth from "src/configs/auth";

const Unsubscribe = () => {
  const [reason, setReason] = useState("");
  const [warning, setWarning] = useState("");
  const [emailId, setEmailId] = useState("");
  const [unsubscribeCode, setUnsubscribeCode] = useState("");
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    setEmailId(queryParams.get("emailId") || "");
    setUnsubscribeCode(queryParams.get("unsubscribeCode") || "");
  }, []);

  const handleReasonChange = (event) => {
    setReason(event.target.value);
    setWarning("");
  };

  const handleSubmit = async () => {
    if (!reason) {
      setWarning("Please select a reason before unsubscribing.");
      return;
    }

    try {
      const response = await axios({
        method: "patch",
        url: getUrl(auth.unSubscribeEndpoint),
        data: {
          unsubscribeCode: unsubscribeCode,
          email: emailId,
          reason: reason,
        },
      });
      setIsSuccessful(true);
      setResponseMessage(response.data.message);
    } catch (error) {
      console.error("Error in API call:", error);
    }
  };

  const reasons = [
    { value: "too-frequent", label: "Too many emails" },
    { value: "not-relevant", label: "Content is not relevant" },
    { value: "email-quality", label: "Email quality has decreased" },
    { value: "information-overload", label: "Information overload" },
    { value: "repetitive-content", label: "Content is repetitive" },
    { value: "not-adding-value", label: "Newsletter doesn't add value" },
    { value: "prefer-social-media", label: "Prefer following on social media" },
    { value: "other", label: "Other" },
  ];

  if (isSuccessful) {
    return (
      <Grid container justifyContent="center">
        <Card
          style={{
            alignItems: "center",
            padding: "2rem",
          }}
        >
          <Typography variant="h6" gutterBottom style={{ textAlign: "center" }}>
            {responseMessage}
          </Typography>
        </Card>
      </Grid>
    );
  }

  return (
    <Grid container justifyContent="center">
      <Card
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          minWidth: "750px",
          padding: "2rem",
        }}
      >
        <Typography variant="h4" gutterBottom>
          {emailId}
        </Typography>
        <Typography variant="subtitle1" marginBottom={"1rem"}>
         is subscribed to out Newsletter
        </Typography>
       
        <Typography variant="h4" fontWeight={"bold"} color={"red"}>
        Unsubscribe from our Newsletters
        </Typography>
        <Typography variant="subtitle2">
         To help us Improve our services, we would be grateful if you could tell us why
        </Typography>
        {warning && (
          <Typography
            variant="body2"
            style={{ color: "red", marginTop: "1rem" }}
          >
            {warning}
          </Typography>
        )}
        <FormControl component="fieldset" style={{ marginTop: "1rem" }}>
          <RadioGroup value={reason} onChange={handleReasonChange}>
            {reasons.map((reason) => (
              <FormControlLabel
                key={reason.value}
                value={reason.value}
                control={<Radio />}
                label={reason.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          style={{ margin: "2rem" }}
        >
          Unsubscribe
        </Button>
      </Card>
    </Grid>
  );
};

Unsubscribe.guestGuard = true;
export default Unsubscribe;
