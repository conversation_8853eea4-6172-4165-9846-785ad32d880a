// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";


// ** Styled Component
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContentText,
  DialogTitle,
  Grid,
  Table,
  TableBody,
  TableContainer,
  TableRow,
} from "@mui/material";

//import MUITableCell from "../../MUITableCell";

import PretenderingTab from "src/@core/components/custom-components/PretenderingTab";
import TenderingTab from "src/@core/components/custom-components/TenderingTab";
import ConstructionTab from "src/@core/components/custom-components/ConstructionTab";
import MUITableCell from "src/pages/SP/MUITableCell";
import ServiceArchitectEdit from "./ServicesArchitectEdit";


const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServicesArchitectView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };



  const [ArchitectServices, setServices] = useState({
    projectPlanningAndFeasibilityStudies: {
      isSupported: false,
      price: "",
    },
    projectDesignAndDevelopment: {
      isSupported: false,
      price: "",
    },
    projectScheduling: {
      isSupported: false,
      price: "",
    },
    costEstimationAndBudgeting: {
      isSupported: false,
      price: "",
    },
    qualityControlAndAssurance: {
      isSupported: false,
      price: "",
    },
    procurementAndVendorManagement: {
      isSupported: false,
      price: "",
    },
    riskManagement: {
      isSupported: false,
      price: "",
    },
    projectMonitoringAndReporting: {
      isSupported: false,
      price: "",
    },
    others:{
      isSupported: false,
      price: "",
    }
  });




  const [dialogOpen, setDialogOpen] = useState(false);




  const handleDialogClose = (confirm) => {
  
    setDialogOpen(false);
  };

  return (
    <>
      <Grid>
        <AccordionBasic
          id={"panel-header-1"}
          ariaControls={"panel-content-1"}
          heading={"Architect Services"}
          body={             
                  <>
                    {state3 === "view" && (
                      <TableContainer
                        sx={{ padding: "4px 6px" }}
                        className="tableBody"
                        onClick={viewClick3}
                      >
                        <Table>
                          <TableBody
                            sx={{
                              "& .MuiTableCell-root": {
                                p: `${theme.spacing(1.35, 1.125)} !important`,
                              },
                            }}
                          >
                            <TableRow>
                              <MUITableCell>
                                <Typography sx={{ fontWeight: 600 }}>
                                  Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography sx={{ fontWeight: 600 }}>
                                  (Yes/No):
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                 Project Planning and Feasibility Studies
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.projectPlanningAndFeasibilityStudies
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Project Design and Development
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.projectDesignAndDevelopment
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Project Scheduling
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.projectScheduling
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Cost Estimation and Budgeting
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.costEstimationAndBudgeting
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                 Quality Control and Assurance
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.qualityControlAndAssurance
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Procurement and Vendor Management
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.procurementAndVendorManagement
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Risk Management   
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.riskManagement?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Project Monitoring and Reporting
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.projectMonitoringAndReporting
                                    ?.isSupported
                                    ? "Yes"
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                            <TableRow>
                              <MUITableCell>
                                <Typography style={field}>
                                  Others
                                </Typography>
                              </MUITableCell>
                              <MUITableCell>
                                <Typography className="data-field">
                                  {data?.architectServices?.others
                                    ?.isSupported
                                    ? `Yes, ${data?.architectServices?.otherText}`
                                    : "No"}
                                </Typography>
                              </MUITableCell>
                            </TableRow>

                         
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                   
            {state3 === "edit" &&  (data?.architectServices != null ? (
                        <ServiceArchitectEdit

                          setter={setServices}
                          defaultData={data.architectServices}
                          onCancel={editClick3}
                        />
                      ) : (
                        <ServiceArchitectEdit
                          setter={setServices}
                          defaultData={ArchitectServices}
                           onCancel={editClick3}
                        />
                      ))
            }
                   
                  </>
            
          }
          expanded={expanded}
        />

     
      </Grid>
    </>
  );
};

export default ServicesArchitectView;
