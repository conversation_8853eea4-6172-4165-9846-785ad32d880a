import Head from 'next/head';
import { useAuth } from '../src/context/AuthContext';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import DashboardPage from '../src/pages/dashboard';
import LoadingSpinner from '../src/components/common/LoadingSpinner';

export default function Dashboard() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, loading, router]);

  if (loading) {
    return <LoadingSpinner loading={true} message="Loading dashboard..." overlay />;
  }

  if (!isAuthenticated) {
    return <LoadingSpinner loading={true} message="Redirecting to login..." overlay />;
  }

  return (
    <>
      <Head>
        <title>Dashboard - Sample App</title>
        <meta name="description" content="Sample App Dashboard" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <DashboardPage />
    </>
  );
}
