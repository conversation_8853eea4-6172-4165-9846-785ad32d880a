import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";

import NavTabsRequisitions from "src/@core/components/custom-components/NavTabsRequisitions";
import WorkOrderDetails from "./WorkOrderDetails";
import { useRBAC } from "src/pages/permission/RBACContext";

const WorkOrders = () => {
  const { can } = useRBAC();
  if(can('workOrders_READ')){
  return (
    <Card>
      <NavTabsRequisitions
        tabContent1={
          <>
           <WorkOrderDetails/> 
          </>
        }
        tabContent2={
          <>
            <WorkOrderDetails/> 
          </>
        }
        tabContent3={
            <>
             <WorkOrderDetails/>  
            </>
        }
        tabContent4={
            <>
             <WorkOrderDetails/>  
            </>
        }
        tabContent5={
            <>
              <WorkOrderDetails/> 
            </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default WorkOrders;
