// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import SelectBasicReadiness from "src/@core/components/custom-components/SelectBasicReadiness";
import { FormControl, Radio, RadioGroup } from "@mui/material";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
const PreTenderingStage = ({
  setNextActive,
  posts,
  handleFsiCalculationsChange,
  handleDataAndDocumentationChange,
  handleSiteAnalysisReportChange,
  handleCreationOfDesignLayoutChange,
  handleMarketAnalysisChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  // ** Hook
  const theme = useTheme();
  const {
    settings: { direction },
  } = useSettings();
  const [preTendering, setPreTendering] = useState([]);

  const [dataAndDoc, setDataAndDoc] = useState(
    defaultData?.hasDataAndDocumentation
  );
  const [siteAnalysis, setSiteAnalysis] = useState(
    defaultData?.hasSiteAnalysisReport
  );
  const [designLayout, setDesignLayout] = useState(
    defaultData?.hasCreationOfDesignLayout
  );
  const [marketAnalysis, setMarketAnalysis] = useState(
    defaultData?.hasMarketAnalysis
  );
  const [fsiCalculations, setFsiCalculations] = useState(
    defaultData?.hasFsiCalculations
  );

  const handleChange = (event) => {
    const { name, value } = event.target;

    switch (name) {
      case "fsi-calculations":
        setFsiCalculations(value);
        handleFsiCalculationsChange(value);
        break;
      case "data-and-doc":
        setDataAndDoc(value);
        handleDataAndDocumentationChange(value);
        break;
      case "site-analysis":
        setSiteAnalysis(value);
        handleSiteAnalysisReportChange(value);
        break;
      case "design-layout":
        setDesignLayout(value);
        handleCreationOfDesignLayoutChange(value);
        break;
      case "market-analysis":
        setMarketAnalysis(value);
        handleMarketAnalysisChange(value);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setPreTendering(data);
    }
  }, [posts]);
  useEffect(() => {
    if (
      fsiCalculations &&
      dataAndDoc &&
      siteAnalysis &&
      designLayout &&
      marketAnalysis && email &&contactNumber &&name&&societyName
    ) {
      setNextActive(true);
    }
    else {
      setNextActive(false);
    }
  }, [fsiCalculations, dataAndDoc, siteAnalysis, designLayout, marketAnalysis,name,societyName,contactNumber,email]);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 2,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold" , fontSize:{xs:'1rem !important',lg:'1.2rem !important'}}}>
                Have the following processes been completed ?
              </Typography>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              a. Data & Documentation
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={dataAndDoc}
                  name="data-and-doc"
                  onChange={handleChange}
                  aria-label="data-and-doc"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='documentation-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={dataAndDoc === "Yes"}
                    />
                    <FormControlLabel 
                      value="No"
                      control={<Radio id='documentation-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={dataAndDoc === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              b. Site Analysis Report
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={siteAnalysis}
                  name="site-analysis"
                  onChange={handleChange}
                  aria-label="site-analysis-report"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='analysisReport-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={siteAnalysis === "Yes"}
                    />
                    <FormControlLabel 
                      value="No"
                      control={<Radio id='analysisReport-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={siteAnalysis === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              c. Project Feasibility and FSI Calculation
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={fsiCalculations}
                  name="fsi-calculations"
                  onChange={handleChange}
                  aria-label="fsi-calculations"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='fsiCalculations-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          Yes
                        </Typography>
                      }
                      checked={fsiCalculations === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio  id='fsiCalculations-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={fsiCalculations === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              d. Creation of Design Layout & Technical Feasibility Report
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={designLayout}
                  name="design-layout"
                  onChange={handleChange}
                  aria-label="design-layout"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='designLayout-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          Yes
                        </Typography>
                      }
                      checked={designLayout === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio  id='designLayout-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          No
                        </Typography>
                      }
                      checked={designLayout === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              e. Market Analysis
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={marketAnalysis}
                  name="market-analysis"
                  onChange={handleChange}
                  aria-label="market-analysis"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio  id='marketAnalysis-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={marketAnalysis === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio  id='marketAnalysis-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          No
                        </Typography>
                      }
                      checked={marketAnalysis === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          
          {preTendering.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(2)} !important`,
                  mb: `${theme.spacing(2)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {preTendering.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={preTendering} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default PreTenderingStage;
