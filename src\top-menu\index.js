import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";

const topmenu = () => {
  const { can } = useRBAC();
  const { user, topMenuData } = useContext(AuthContext);
  const [navArray, setNavArray] = useState([]);


  const processChildren = (children) => {
    return children
      .filter(child => child.accessCode === null || can(child.accessCode))
      .map(child => ({
        title: child.title,
        path: child.path,
        icon: child.icon,
        children: child.children ? processChildren(child.children) : null
      }));
  };

  useEffect(() => {

    const updatedNavArray = topMenuData?.topMenuDataSetDTO?.topMenuData
      .filter(item => item.accessCode === null || can(item.accessCode))
      .map(item => {
        let updatedItem = {
          title: item.title,
          path: item.path,
          icon: item.icon,
          children: item.children ? processChildren(item.children) : null
        };
        return updatedItem;
      });
    setNavArray(updatedNavArray);
  }, [user, topMenuData, can]);


  return navArray;
};

export default topmenu;
