import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import FormControlLabel from '@mui/material/FormControlLabel';
import IconButton from '@mui/material/IconButton';
import Switch from '@mui/material/Switch';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import TimePicker from 'react-time-picker';
import 'react-time-picker/dist/TimePicker.css';
import 'react-clock/dist/Clock.css';
import { styled, createTheme, ThemeProvider } from '@mui/material/styles';

import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import PeopleIcon from '@mui/icons-material/People';
import EventIcon from '@mui/icons-material/Event';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PlaceIcon from '@mui/icons-material/Place';
import RepeatIcon from '@mui/icons-material/Repeat';
import PublicIcon from '@mui/icons-material/Public';

const DatePickerWrapper = styled('div')(({ theme }) => ({
  display: 'inline-block',
  '& .react-datepicker-wrapper': {
    width: 'auto',
  },
  '& .react-datepicker__input-container input': {
    width: '100px',
    padding: '8px 10px',
    borderRadius: '4px',
    border: '1px solid #ccc',
    fontSize: '16px',
  },
}));

const theme = createTheme({
  palette: {
    primary: {
      main: '#33303C', // Valid format
    },
    secondary: {
      main: '#ff4081', // Valid format
    },
  },
});

const MeetingScheduler = () => {
  const [title, setTitle] = useState('');
  const [attendees, setAttendees] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [startTime, setStartTime] = useState('08:00');
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState('08:30');
  const [isInPerson, setIsInPerson] = useState(false);
  const [isTeamsMeeting, setIsTeamsMeeting] = useState(false);
  const [description, setDescription] = useState('');

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ p: 4, width: '100%', maxWidth: 600, margin: 'auto', border: '1px solid #ccc', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Button variant="contained" color="primary" startIcon={<SaveIcon />}>
            Save
          </Button>
          <Button variant="text" startIcon={<DeleteIcon />}>
            Discard
          </Button>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton>
            <AddIcon />
          </IconButton>
          <TextField
            fullWidth
            placeholder="Add a title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            variant="standard"
            sx={{ ml: 2 }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton>
            <PeopleIcon />
          </IconButton>
          <TextField
            fullWidth
            placeholder="Invite attendees"
            value={attendees}
            onChange={(e) => setAttendees(e.target.value)}
            variant="standard"
            sx={{ ml: 2 }}
          />
        </Box>

        <Grid container spacing={2} alignItems="center" mb={3}>
          <Grid item>
            <IconButton>
              <AccessTimeIcon />
            </IconButton>
          </Grid>
          <Grid item>
            <DatePickerWrapper>
              <DatePicker
                selected={startDate}
                onChange={(date) => setStartDate(date)}
                dateFormat="dd-MM-yyyy"
                customInput={<input />}
              />
            </DatePickerWrapper>
          </Grid>
          <Grid item>
            <TimePicker
              onChange={setStartTime}
              value={startTime}
              format="HH:mm"
              clearIcon={null}
              clockIcon={null}
              disableClock
            />
          </Grid>
          <Grid item>
            <FormControlLabel
              control={<Switch />}
              label={<Typography variant="body2">All day</Typography>}
            />
          </Grid>
          <Grid item>
            <IconButton>
              <PublicIcon />
            </IconButton>
          </Grid>
        </Grid>

        <Grid container spacing={2} alignItems="center" mb={3}>
          <Grid item>
            <IconButton>
              <EventIcon />
            </IconButton>
          </Grid>
          <Grid item>
            <DatePickerWrapper>
              <DatePicker
                selected={endDate}
                onChange={(date) => setEndDate(date)}
                dateFormat="dd-MM-yyyy"
                customInput={<input />}
              />
            </DatePickerWrapper>
          </Grid>
          <Grid item>
            <TimePicker
              onChange={setEndTime}
              value={endTime}
              format="HH:mm"
              clearIcon={null}
              clockIcon={null}
              disableClock
            />
          </Grid>
          <Grid item>
            <FormControlLabel
              control={<IconButton><RepeatIcon /></IconButton>}
              label={<Typography variant="body2">Don't repeat</Typography>}
            />
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton>
            <PlaceIcon />
          </IconButton>
          <FormControlLabel
            control={
              <Switch
                checked={isInPerson}
                onChange={(e) => setIsInPerson(e.target.checked)}
              />
            }
            label="In-person event"
            sx={{ ml: 2 }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={isTeamsMeeting}
                onChange={(e) => setIsTeamsMeeting(e.target.checked)}
              />
            }
            label="Teams meeting"
            sx={{ flexGrow: 1 }}
          />
        </Box>

        <TextField
          fullWidth
          placeholder="Search for a room or location"
          variant="standard"
          sx={{ mb: 3 }}
        />

        <TextField
          fullWidth
          placeholder="Add a description or attach documents"
          multiline
          rows={4}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          variant="standard"
          sx={{ mb: 3 }}
        />

        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
        >
          Save
        </Button>
      </Box>
    </ThemeProvider>
  );
};

export default MeetingScheduler;