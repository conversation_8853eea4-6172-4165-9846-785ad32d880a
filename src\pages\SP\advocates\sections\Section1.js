import { forwardRef } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'


// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'

// ** Hooks
// ** Icon Imports
import { Box } from '@mui/system'
import { useAuth } from 'src/hooks/useAuth'



const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false
}

const names = [
  'Less than 5 years',
  '5-10 years',
  'More than 10 years'
]

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})


const Section1 = ({ onCancel, formData }) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();



  async function submit(data) {

    const response = await auth.updateEntity(data,() => {
      console.error("brokerDetails failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>


      <Grid container spacing={5}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  label='Name'
                  onChange={onChange}
                  placeholder='Enter your name'
                  error={Boolean(errors.name)}
                  aria-describedby='Section1-name'
                />
              )}
            />
            {errors.name && (
              <FormHelperText sx={{ color: 'error.main' }} id='Section1-name'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} >
          <FormControl fullWidth>
            <Controller
              name='companyName'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.companyName}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  label='Company Name'
                  onChange={onChange}
                  placeholder='Enter company name'
                  error={Boolean(errors.companyName)}
                  aria-describedby='Section1-company'
                />
              )}
            />
            {errors.companyName && (
              <FormHelperText sx={{ color: 'error.main' }} id='Section1-company'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name='address'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Address'
                  error={Boolean(errors.address)}
                  aria-describedby='Section1-address'
                />
              )}
            />
            {errors.address && (
              <FormHelperText sx={{ color: 'error.main' }} id='Section1-address'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='mobileNumber'
              control={control}
              rules={{ required: true, pattern: /^[0-9]{10,20}$/ }}
              defaultValue={formData?.mobileNumber}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  type='tel'
                  label='Contact Number'
                  onChange={onChange}
                  error={Boolean(errors.mobileNumber)}
                  placeholder='+91 1234567890'
                  inputProps={{ maxLength: 12 }}
                  aria-describedby='Section1-mobileNumber'
                />
              )}
            />
            {errors.mobileNumber?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-mobileNumber'>
                Contact number is required
              </FormHelperText>
            )}
            {errors.mobileNumber?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-mobileNumber'>
                Please enter a valid  contact number
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='email'
              control={control}
              rules={{ required: true, pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/ }}
              defaultValue={formData?.email}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  type='email'
                  label='Email'
                  onChange={onChange}
                  error={Boolean(errors.email)}
                  placeholder='Enter email address'
                  aria-describedby='validation-email'
                 
                />
              )}
            />
            {errors.email?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-email'>
                Email address is required
              </FormHelperText>
            )}
            {errors.email?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-email'>
                Please enter a valid email address
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name='websiteUrl'
                control={control}
                rules={{ pattern: /^(ftp|http|https):\/\/[^ "]+$/ }}
                defaultValue={formData?.websiteUrl}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    type='url'
                    value={value}
                    label='Website URL'
                    onChange={onChange}
                    error={Boolean(errors.websiteUrl)}
                    placeholder='https://www.example.com'
                    aria-describedby='validation-websiteUrl'
                    
                  />
                )}
              />
              {errors.websiteUrl?.type === 'required' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-websiteUrl'>
                  Website URL is required
                </FormHelperText>
              )}
              {errors.websiteUrl?.type === 'pattern' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-websiteUrl'>
                  Please enter a valid Website URL
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

        
        <Grid item xs={12}>
        <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
      </Grid>

    </Box>

  )
}

export default Section1