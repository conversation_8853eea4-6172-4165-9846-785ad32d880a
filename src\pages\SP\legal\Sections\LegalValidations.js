import * as yup from "yup";

export const LegalValidations = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];

  return yup.object().shape({
    name: yup.string().when("$fields", {
      is: () => fieldsArray.includes("name"),
      then: yup
        .string()
        .required("Name is required")
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "Name must not exceed 30 characters")
        .min(3, "Name must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    companyName: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("name"),
        then: yup
          .string()
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "Company name must not exceed 30 characters")
          .min(3, "Company name must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    email: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("email"),
        then: yup
          .string()
          .nullable()
          .required("Email address is required")
          .email("Please enter a valid email address")
          .max(50, "Email must not exceed 50 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    mobileNumber: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("mobileNumber"),
        then: yup
          .string()
          .required("Mobile number is required")
          .nullable()
          .matches(
            /^(?:\+91\s?)?[6-9]\d{9}$/,
            "Please enter a valid contact number"
          )
          .max(13, "Contact number must not exceed 13 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    address: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("address"),
        then: yup
          .string()
          .required("Address is required")
          .nullable()
          .max(500, "Address must not exceed 500 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    websiteUrl: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("websiteUrl"),
        then: yup
          .string()
          .nullable()
          .max(30, "Website URL must not exceed 30 characters")
          .url("Please enter a valid URL format"),

        otherwise: yup.string().notRequired().nullable(),
      }),
    noOfProjectsCompleted: yup.string().when("$fields", {
      is: () => fieldsArray.includes("noOfProjectsCompleted"),
      then: yup
        .string()
        .required("No. of projects is required")
        .nullable()
        .matches(
          /^(?:0|[1-9][0-9]*|1000000)$/,
          "Please enter a valid projects"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    constructedAreaOfCompletedProjects: yup.string().when("$fields", {
      is: () => fieldsArray.includes("constructedAreaOfCompletedProjects"),
      then: yup
        .string()
        .required("Constructed area of completed projects is required")
        .nullable()
        .max(
          500,
          "Constructed area of completed projects  must not exceed 500 characters"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    noOfOngoingProjects: yup.string().when("$fields", {
      is: () => fieldsArray.includes("noOfOngoingProjects"),
      then: yup
        .string()
        .required("No. of ongoing projects is required")
        .nullable()
        .matches(
          /^(?:0|[1-9][0-9]*|1000000)$/,
          "Please enter a valid ongoing projects"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    constructedAreaOfOngoingProjects: yup.string().when("$fields", {
      is: () => fieldsArray.includes("constructedAreaOfOngoingProjects"),
      then: yup
        .string()
        .required("Constructed area of ongoing projects is required")
        .nullable()
        .max(
          500,
          "Constructed area of ongoing projects must not exceed 200 characters"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
    landmarkProjectsNames: yup.string().when("$fields", {
      is: () => fieldsArray.includes("landmarkProjectsNames"),
      then: yup
        .string()
        .nullable()
        .max(
          500,
          "Landmark projects names must not exceed 500 characters"
        ),
      otherwise: yup.string().notRequired().nullable(),
    }),
  });
};

export default LegalValidations;

export { yup };
