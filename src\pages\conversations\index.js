// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Custom Components Imports
import { useContext } from "react";
import PageHeader from "src/@core/components/page-header";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";

import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";

import FormHelperText from "@mui/material/FormHelperText";

// ** Demo
import {
  Box,
  Button,
  Card,
  CardContent,
  IconButton,
  TableCell,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";

import CloseIcon from "@mui/icons-material/Close";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Controller } from "react-hook-form";
import axios from "axios";
import { useRBAC } from "src/pages/permission/RBACContext";

import authConfig from "src/configs/auth";
import ServiceConversationDetails from "./sections/ServiceConversationDetails";
import ServiceInfo from "./sections/ServiceInfo";
import ServiceProviderConversation from "./sections/ServiceProviderConversation";
const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const Index = (currentRow, setCurrentRow,employeeData ) => {
  const {
    register,
    control,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const { can } = useRBAC();

  const router = useRouter();



  const { getAllListValuesByListNameId,setBasicProfileAllProfiles,basicProfileAllProfiles } = useContext(AuthContext);

 

  const [conversationTypeData, setConversationTypeData] = useState(null);
  const [outcomeConversationData, setOutcomeConversationData] = useState(null);
  const [targetData, setTargetData] = useState(null);
  const [shallRemindData, setShallRemindData] = useState(null);

  const [openConversationDialog, setOpenConversationDialog] = useState(false);

  const handleOpenConversationDialog = () => {
       setOpenConversationDialog(true);
  };

  const handleCloseConversationDialog = () => {
    setOpenConversationDialog(false);
    setCurrentRow(null);
  };

  const handleTargetSuccess = (data) => {
    setTargetData(data?.listValues);
  };

  const handleOutcomeConversationSuccess = (data) => {
    setOutcomeConversationData(data?.listValues);
  };

  const handleConversationTypeSuccess = (data) => {
    setConversationTypeData(data?.listValues);
  };

  const handleShallRemindSuccess = (data) => {
    setShallRemindData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };



  useEffect(() => {
    if(!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.shallRemind,
        handleShallRemindSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.target,
        handleTargetSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.outcomeConversation,
        handleOutcomeConversationSuccess,
        handleError
      );
      
      getAllListValuesByListNameId(
        authConfig.conversationType,
        handleConversationTypeSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const [conversationList, setConversationList] = useState([]);
  const [rowCount, setRowCount] = useState(0);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {

    const url = getUrl(authConfig.getAllConversationsByUserId+"/"+(currentRow?.currentRow?.userId || currentRow?.currentRow?.id));
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setConversationList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      
    }
  };

  useEffect(()=>{
    if(!!currentRow && !!currentRow?.currentRow) {
      fetchUsers();
    }
  }, [currentRow]);

  useEffect(()=>{
    basicProfileAllProfiles
  }, [setBasicProfileAllProfiles]);


  return (
    <div>
      <>
        <style>
          {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
        </style>
        <DatePickerWrapper>
          <Grid container spacing={6} className="match-height">
            <Grid item xs={12}>
              <Grid
                container
                spacing={2}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Grid item xs={8}>
                  <PageHeader
                    subtitle={<Typography variant="h6"></Typography>}
                  />
                </Grid>
               
                <Grid item xs={12}>
                  <ServiceInfo
                    data={currentRow?.currentRow}
                    expanded={currentRow?.expanded}
                    employeeData={currentRow?.employeeData}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Card sx={{ width: "100%" }}>
                    <CardContent>
                      <Grid container spacing={1}>
                        <Grid
                          item
                          xs={12}
                          sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            mt: 4,
                            mb: 2,
                          }}
                        >
                          <ServiceProviderConversation
                            userId={currentRow?.currentRow?.userId || currentRow?.currentRow?.id }
                            data={currentRow?.currentRow?.basicProfile||currentRow?.currentRow?.basicProfileData}
                            openConversationDialog={openConversationDialog}
                            setOpenConversationDialog={setOpenConversationDialog}
                            handleOpenConversationDialog={
                              handleOpenConversationDialog
                            }
                            outcomeConversationData={outcomeConversationData}
                            targetData={targetData}
                            shallRemindData={shallRemindData}
                            
                            conversationTypeData={conversationTypeData}
                            fetchUsers={fetchUsers}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <ServiceConversationDetails
                            expanded={currentRow?.expanded}
                            conversationList={conversationList}
                            rowCount={rowCount}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    </div>
  );
};

export default Index;
