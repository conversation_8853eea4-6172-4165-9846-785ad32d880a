// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'


import { useTheme } from '@emotion/react'

// ** Demo
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import Section4 from './Section4'


// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const TypeOfDevelopment = ({data,expanded}) => {


  const [state, setState] = useState('view')

  const viewClick = () => {
    setState('edit')
  }

  const editClick = () => {
    setState('view')
  }
    // ** Hook
    const theme = useTheme()

    // const [caDetails, setCaDetails] = useState({
    //   name: "",
    //   companyName: "",
    //   address: "",
    //   mobileNumber: "",
    //   email: "",
    //   websiteUrl:"",
    //   teamSize: "",
    //   yearsOfExperience:"",
    //   briefProfile:"",
    //   awards:""
    // });


    return (
        <>
             <AccordionBasic
              id={'panel-header-2'}
              ariaControls={'panel-content-2'}
              heading={'Type of Development'}
              body={
                <>
                  {state === 'view' && (
                    
                        <TableContainer
                          sx={{ padding:'4px 6px' }}
                          className='tableBody'
                          onClick={viewClick}
                        >
                          <Table>
                            <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Structure:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.structure}</Typography>
                                </MUITableCell>
                              </TableRow>
                             
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Type:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.type}</Typography>
                                </MUITableCell>
                              </TableRow>
                              
                            
                              <TableRow>
                            <MUITableCell>
                              <Typography style={field}>Other:</Typography>
                            </MUITableCell>
                            <MUITableCell>
                                <Typography className='data-field'>{data?.other}</Typography>
                            </MUITableCell>
                          </TableRow>
                              
                              
                             
                              
                            </TableBody>
                          </Table>
                        </TableContainer>
                      
                  )}

                  {state === 'edit' && <Section4 formData={data} onCancel={editClick} />}
                </>
              }
              expanded={expanded}
            />
        </>
    );

}
export default TypeOfDevelopment;