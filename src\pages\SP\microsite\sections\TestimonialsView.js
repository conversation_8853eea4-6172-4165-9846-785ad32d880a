// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
// import MUITableCell from "../../MUITableCell";
import TestimonialForm from "./TestimonialsEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const TestimonialView = ({ data, expanded, userData }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };
  const hasTestimonials =
    data && data.testimonialsList && data.testimonialsList.length > 0;

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Testimonials"}
        body={
          <>
            {state3 === "view" && (
              <>
                {hasTestimonials ? (
                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                    onClick={viewClick3}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>Designation</TableCell>
                          <TableCell>Testimonial</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        {data.testimonialsList.map((testimonial, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography
                                className="data-field"
                                marginLeft={"18px"}
                              >
                                {testimonial.name}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography className="data-field" style={{paddingLeft:"12px"}}>
                                {testimonial.designation}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography className="data-field"  style={{paddingLeft:"12px"}}>
                                {testimonial.description}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography
                    variant="body1"
                    sx={{ textAlign: "left", mt: 3, cursor: "pointer" }}
                    onClick={viewClick3}
                  >
                    Click here to add Testimonials
                  </Typography>
                )}
              </>
            )}

            {state3 === "edit" && (
              <TestimonialForm
                data={data}
                onCancel={editClick3}
                userData={userData}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};

export default TestimonialView;
