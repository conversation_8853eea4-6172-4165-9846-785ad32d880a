import React, { useState } from "react";
// ** MUI Imports
import Button from "@mui/material/Button";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
  Grid,
} from "@mui/material";
import { hexToRGBA } from "src/@core/utils/hex-to-rgba";
import CustomChip from "src/@core/components/mui/chip";
import CheckBoxF from "../custom-components/CheckBoxF";
import Link from "next/link";

const BoxWrapper = styled(Box)(({ theme }) => ({
  position: "relative",
  padding: theme.spacing(6),
  paddingTop: theme.spacing(16),
  borderRadius: theme.shape.borderRadius,
  width: "800px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  border: `1px solid ${theme.palette.divider}`,
  height: "100%",
  [theme.breakpoints.down(821)]: {
    width: "650px",
    height: "auto",
  },
  [theme.breakpoints.down(431)]: {
    width: "auto",
    height: "auto",
  },
}));

const BoxFeature = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  "& > :not(:last-child)": {
    marginBottom: theme.spacing(2.5),
  },
}));
const CustomLink = styled(Link)({
  textDecoration: "none",
});

const FixedButton = styled(Button)(({ theme }) => ({
  height: "40px",
  width: "145px",
  whiteSpace: "nowrap",
}));

const PlanDetails = (props) => {
  const {  data } = props;
  const [openDialog, setOpenDialog] = useState(false);
  const [isChecked, setIsChecked] = useState(false);

  const renderFeaturesAndExclusions = () => {
    const benefits = [];
    const exclusions = [];

    if (data?.listing) benefits.push('Listing');
    if (data?.microSite) benefits.push('Microsite');
    if (data?.audioVisualIncluded) benefits.push(`Audio Visual (${data.audioVisualDurationInMinutes}mins)`);
    if (data?.editorialIncluded) benefits.push(`Editorial (${data.editorialDurationInMinutes}mins)`);
    if (data?.podcastIncluded) {
      benefits.push(`Podcast (${data.podcastDurationInMinutes ? data.podcastDurationInMinutes + ' mins' : ''})`);
  } else {
      exclusions.push('Podcast');
  }    if (!data?.editorialIncluded) exclusions.push('Editorial');
    if (!data?.audioVisualIncluded) exclusions.push('Audio Visual');

    return (
      <Box>
        {benefits.map((item, index) => (
          <Box key={`benefit-${index}`} sx={{ display: "flex", alignItems: "start" }}>
            <IconButton sx={{ padding: "0px", color: "primary.main",fontSize:'1rem !important',  transform: 'scale(0.7)' // Reduces the size to 75% of the original
 }}>
              <CheckCircleIcon />
            </IconButton>
            <Typography sx={{ color: "text.secondary", ml: 1 }}>{item}</Typography>
          </Box>
        ))}
        {exclusions.map((item, index) => (
          <Box key={`exclusion-${index}`} sx={{ display: "flex", alignItems: "start"}}>
            <IconButton sx={{ padding: "0px", color: "error.main",fontSize:'1rem !important',  transform: 'scale(0.7)'}}>
              <CancelIcon />
            </IconButton>
            <Typography sx={{ color: "text.secondary", ml: 1 }}>{item}</Typography>
          </Box>
        ))}
      </Box>
    );
  };

  const handleTermsAndConditions = () => {
    setOpenDialog(true);
  };

  const handleCancelClick = () => {
    setIsChecked(false);
    setOpenDialog(false);
  };

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
  };

  const proceedPayment = () => {
    
  };

  return (
    <BoxWrapper
      sx={(theme) => ({
        border: !data?.popularPlan
          ? `1px solid ${theme.palette.divider}`
          : `1px solid ${hexToRGBA(theme.palette.primary.main, 0.5)}`,
        paddingTop: { sm: "0.8rem !important", xs: "0" },
        flexDirection: "row",
        alignItems: "center",
        [theme.breakpoints.down(450)]: {
          flexDirection: "column",
        },
      })}
    >
      {data?.popularPlan ? (
        <CustomChip
          rounded
          size="small"
          skin="light"
          label="Popular"
          color="primary"
          sx={{
            top: 5,
            left: 8,
            position: "absolute",
            "& .MuiChip-label": {
              px: 1.75,
              fontWeight: 500,
              fontSize: "0.75rem",
            },
          }}
        />
      ) : null}
      <Box sx={{ textAlign: "center", width: "202px" }}>
        <Typography
          sx={{
            mb: 1.5,
            fontWeight: 500,
            lineHeight: 1.385,
            fontSize: "1.625rem",
            marginTop: "0.8rem",
          }}
        >
          {data?.name}
        </Typography>
        <Typography sx={{ color: "text.secondary" }}>
          {data?.subtitle}
        </Typography>
        <Box sx={{ margin: "0px 20px 20px 10px", position: "relative" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              marginTop: { xs: "10px", sm: "0px" },
            }}
          >
            <Typography
              sx={{
                mt: 2.5,
                mr: 0.5,
                fontWeight: 500,
                color: "primary.main",
                alignSelf: "flex-start",
              }}
            >
              ₹
            </Typography>
            <Typography
              variant="h3"
              sx={{ fontWeight: 500, color: "primary.main" }}
            >
              { data?.price}
            </Typography>
            <Typography
              sx={{ mb: 1.5, alignSelf: "flex-end", color: "text.disabled" }}
            >
              /year
            </Typography>
          </Box>
        </Box>
      </Box>
      <BoxFeature
        sx={{
          marginTop: { xs: "0px", sm: "15px" },
          marginLeft: { xs: 0, sm: "3rem !important" },
          width: "155px",
        }}
      >
        {renderFeaturesAndExclusions()}
      </BoxFeature>
      <FixedButton
        fullWidth
        variant="contained"
        sx={{ textTransform: "none" }}
        onClick={handleTermsAndConditions}
      >
        Choose Plan
      </FixedButton>
      <Dialog
        fullWidth
        maxWidth="sm"
        scroll="paper"
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleCancelClick();
          }
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          textAlign={"center"}
          fontSize={"18px !important"}
          fontWeight={"bold"}
        >
          {"Terms and Conditions"}
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleCancelClick}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(6, 8)} !important`,
          }}
        >
          <Typography sx={{ fontSize: "0.9rem !important" }}>
            Before proceeding to payment, please ensure you have read,
            understood, and signed our{" "}
            <CustomLink
  sx={{ textDecoration: "none !important" }}
  href="/pdf/Professional_Agreement.pdf"
  target="_blank"
>
              Terms and Conditions and Privacy Policy
            </CustomLink>
            .
          </Typography>
          <Box
            sx={{
              display: "flex",
              paddingTop: "0.6rem !important",
              alignItems: "start",
            }}
          >
            <CheckBoxF checked={isChecked} onChange={handleCheckboxChange} />
            <Typography sx={{ fontSize: "0.7rem !important" }}>
              By checking this box, you confirm that you have signed the
              required document and agree to its terms and conditions.
            </Typography>
          </Box>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              marginTop: "1rem",
            }}
          >
            <Button
              disabled={!isChecked}
              variant="contained"
              onClick={proceedPayment}
              color="primary"
              sx={{ textAlign: "center" }}
            >
              Proceed to Payment
            </Button>
          </Grid>
        </DialogContent>
      </Dialog>
    </BoxWrapper>
  );
};

export default PlanDetails;
