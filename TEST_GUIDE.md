# Testing Guide - Sample App

## 🧪 Manual Testing Instructions

### 1. Registration Flow Test

**Steps:**
1. Navigate to `http://localhost:3001/register`
2. Fill out the registration form:
   - Full Name: `Test User`
   - Email: `<EMAIL>`
   - Password: `TestPass123!`
   - Confirm Password: `TestPass123!`
   - Check "Accept Terms and Conditions"
3. Click "Create Account"
4. Verify success message appears
5. Verify redirect to dashboard

**Expected Results:**
- Password strength indicator shows "Strong"
- Form validation works for all fields
- Success message displays
- User is redirected to dashboard
- User data is stored in localStorage

### 2. Login Flow Test

**Steps:**
1. Navigate to `http://localhost:3001/login`
2. Use demo credentials:
   - Email: `<EMAIL>`
   - Password: `Demo123!`
3. Check "Remember me" (optional)
4. Click "Sign In"
5. Verify redirect to dashboard

**Expected Results:**
- Login form validates email format
- Password visibility toggle works
- Demo credentials auto-fill works
- Success login redirects to dashboard
- Authentication state persists

### 3. Forgot Password Test

**Steps:**
1. Navigate to `http://localhost:3001/forgot-password`
2. Enter email: `<EMAIL>`
3. Click "Send Reset Link"
4. Verify success message
5. Click "Back to Login"

**Expected Results:**
- Email validation works
- Success message displays
- Form resets after submission
- Navigation back to login works

### 4. Protected Route Test

**Steps:**
1. Open new incognito/private browser window
2. Navigate directly to `http://localhost:3001/dashboard`
3. Verify redirect to login page
4. Login with demo credentials
5. Verify access to dashboard

**Expected Results:**
- Unauthenticated users redirected to login
- Login preserves intended destination
- Authenticated users can access dashboard

### 5. Social Login Test (Mock)

**Steps:**
1. Navigate to login page
2. Click "Google" or "Facebook" button
3. Verify mock authentication works
4. Check dashboard access

**Expected Results:**
- Social login buttons trigger mock authentication
- Success creates user session
- User redirected to dashboard

### 6. Logout Test

**Steps:**
1. From dashboard, click user avatar
2. Select "Logout" from menu
3. Verify redirect to login page
4. Try accessing dashboard directly

**Expected Results:**
- Logout clears authentication state
- User redirected to login
- Protected routes no longer accessible

### 7. Responsive Design Test

**Test on different screen sizes:**
- Mobile (320px - 599px)
- Tablet (600px - 899px)
- Desktop (900px+)

**Expected Results:**
- Forms remain usable on all screen sizes
- Navigation adapts to screen size
- Text remains readable
- Buttons remain clickable

### 8. Form Validation Test

**Registration Form:**
- Empty fields show required errors
- Invalid email shows format error
- Weak password shows strength indicator
- Mismatched passwords show error
- Unchecked terms shows error

**Login Form:**
- Empty email shows required error
- Invalid email shows format error
- Empty password shows required error

**Forgot Password Form:**
- Empty email shows required error
- Invalid email shows format error

### 9. Error Handling Test

**Network Errors:**
1. Disconnect internet
2. Try to login/register
3. Verify error message displays

**Invalid Credentials:**
1. Use wrong email/password
2. Verify error message displays
3. Form remains usable

### 10. Browser Compatibility Test

**Test in multiple browsers:**
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

**Expected Results:**
- All functionality works consistently
- Styling appears correctly
- No console errors

## 🔧 Automated Testing Setup

### Unit Tests

Create `__tests__/auth.test.js`:

```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { AuthProvider } from '../src/context/AuthContext'
import LoginPage from '../src/pages/login'

const MockAuthProvider = ({ children }) => (
  <AuthProvider>{children}</AuthProvider>
)

describe('Authentication', () => {
  test('login form renders correctly', () => {
    render(
      <MockAuthProvider>
        <LoginPage />
      </MockAuthProvider>
    )
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  test('form validation works', async () => {
    render(
      <MockAuthProvider>
        <LoginPage />
      </MockAuthProvider>
    )
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })
  })
})
```

### Integration Tests

Create `__tests__/integration.test.js`:

```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import App from '../src/App'

describe('Authentication Flow', () => {
  test('complete login flow', async () => {
    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    )
    
    // Should redirect to login
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument()
    })
    
    // Fill login form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'Demo123!' }
    })
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }))
    
    // Should redirect to dashboard
    await waitFor(() => {
      expect(screen.getByText(/welcome to your dashboard/i)).toBeInTheDocument()
    })
  })
})
```

### E2E Tests with Cypress

Install Cypress:
```bash
npm install --save-dev cypress
```

Create `cypress/e2e/auth.cy.js`:
```javascript
describe('Authentication E2E', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3001')
  })

  it('should complete registration flow', () => {
    cy.visit('/register')
    
    cy.get('[data-testid="fullName"]').type('Test User')
    cy.get('[data-testid="email"]').type('<EMAIL>')
    cy.get('[data-testid="password"]').type('TestPass123!')
    cy.get('[data-testid="confirmPassword"]').type('TestPass123!')
    cy.get('[data-testid="termsAccepted"]').check()
    
    cy.get('[data-testid="submit"]').click()
    
    cy.url().should('include', '/dashboard')
    cy.contains('Welcome to Your Dashboard').should('be.visible')
  })

  it('should complete login flow', () => {
    cy.visit('/login')
    
    cy.get('[data-testid="email"]').type('<EMAIL>')
    cy.get('[data-testid="password"]').type('Demo123!')
    
    cy.get('[data-testid="submit"]').click()
    
    cy.url().should('include', '/dashboard')
    cy.contains('Welcome to Your Dashboard').should('be.visible')
  })

  it('should protect routes', () => {
    cy.visit('/dashboard')
    
    cy.url().should('include', '/login')
    cy.contains('Welcome Back').should('be.visible')
  })
})
```

## 📊 Performance Testing

### Lighthouse Audit

1. Open Chrome DevTools
2. Go to Lighthouse tab
3. Run audit for:
   - Performance
   - Accessibility
   - Best Practices
   - SEO

**Target Scores:**
- Performance: >90
- Accessibility: >95
- Best Practices: >90
- SEO: >90

### Bundle Analysis

```bash
# Install bundle analyzer
npm install --save-dev @next/bundle-analyzer

# Add to next.config.mjs
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer(nextConfig)

# Run analysis
ANALYZE=true npm run build
```

## 🐛 Common Issues & Solutions

### 1. Authentication State Not Persisting
**Solution:** Check localStorage in browser DevTools

### 2. Forms Not Validating
**Solution:** Verify react-hook-form setup and validation rules

### 3. Routing Issues
**Solution:** Check Next.js file structure and route configuration

### 4. Styling Problems
**Solution:** Verify Material-UI theme provider setup

### 5. API Errors
**Solution:** Check network tab in DevTools for request/response

## ✅ Test Checklist

- [ ] Registration form works
- [ ] Login form works
- [ ] Password reset works
- [ ] Protected routes work
- [ ] Social login works (mock)
- [ ] Logout works
- [ ] Form validation works
- [ ] Error handling works
- [ ] Responsive design works
- [ ] Browser compatibility verified
- [ ] Performance acceptable
- [ ] Accessibility compliant

---

**Testing Complete!** 🎉 Your authentication system is ready for production.
