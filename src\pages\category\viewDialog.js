// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "@mui/material/TableCell";
import Section from "./Section";
import { da } from "date-fns/locale";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ViewDialog = ({ data,expanded }) => {
  const theme = useTheme();

  const [state, setState] = useState("view");

  const [viewData, setViewData] = useState(data);

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const activeSubRoles = viewData?.metadata?.subRoleTypes?.filter(
    (subRole) => subRole.isActive === true
  );

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Basic"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px", cursor:'pointer' }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Category:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {viewData?.roleType}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Status:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                        {viewData?.isActive ? "Active" : "InActive"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Has Sub-Categories:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {viewData?.hasSubTypes.toString()}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Sub-Categories:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        {viewData?.hasSubTypes && (
                          <Typography className="data-field">
                            {activeSubRoles
                              ?.map((subRole) => subRole.name)
                              .join(", ")}
                          </Typography>
                        )}
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <Section
                onCancel={editClick}
                data={viewData}
                setData={setViewData}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default ViewDialog;
