// ** MUI Imports
import { useState } from 'react'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { styled, useTheme } from '@mui/material/styles'
import TableCell from '@mui/material/TableCell'

// ** Styled Component
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import NavTabSimple from 'src/@core/components/custom-components/NavTabSimple'
import { Box, Button, CardContent, Table, TableBody, TableContainer, TableRow } from '@mui/material'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import Section2 from './Section2'

import PageHeader from 'src/@core/components/page-header'
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const ProfessionalDetails = ({data,expanded}) => {
    const theme = useTheme()

    const [state, setState] = useState('view')

    const viewClick = () => {
        setState('edit')
    }

    const editClick = () => {
        setState('view')
    }

    // const [pmcDetails, setPmcDetails] = useState({
    //     name: "",
    //     companyName: "",
    //     address: "",
    //     mobileNumber: "",
    //     email: "",
    //     websiteUrl:""
      
    // });

    return(
      <>
       
        <AccordionBasic
          id={'panel-header-1'}
          ariaControls={'panel-content-1'}
          heading={'Professional Details'}
          body={
            <>
              {state === 'view' && (
                <Grid item xs={12}>
                    <TableContainer
                      sx={{ padding:'4px 6px' }}
                      className='tableBody'
                      onClick={viewClick}
                    >
                      <Table>
                        <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>Name:</Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className='data-field' >{data?.name}</Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>Contact Number</Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className='data-field' >{data?.mobileNumber}</Typography>
                            </MUITableCell>
                          </TableRow>
                         
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>Address:</Typography>
                            </MUITableCell>
                            <MUITableCell>
                                <Typography className='data-field' >{data?.address}</Typography>
                            </MUITableCell>
                          </TableRow>
                              
                          
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>Email:</Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className='data-field' >{data?.email}</Typography>
                            </MUITableCell>
                          </TableRow>
                          
                        </TableBody>
                      </Table>
                    </TableContainer>
                    </Grid>
                  )}
                  {state === 'edit' && <Section2 formData={data} onCancel={editClick} />}
                </>
              }
              expanded={expanded}
            />
         
        </>
      )

}
export default ProfessionalDetails