# Sample App - Next.js Authentication System

A comprehensive authentication application built with Next.js, Material-UI, and React Hook Form. This project demonstrates a complete authentication flow including user registration, login, password reset, and social authentication.

## 🚀 Features

### Authentication Features
- ✅ **User Registration** - Complete registration form with validation
- ✅ **User Login** - Secure login with remember me option
- ✅ **Password Reset** - Forgot password functionality with email notifications
- ✅ **Social Authentication** - Google and Facebook login integration (mock implementation)
- ✅ **Protected Routes** - Route guards and authentication checks
- ✅ **Persistent Sessions** - Authentication state persistence with localStorage

### UI/UX Features
- ✅ **Material-UI Components** - Modern, responsive design with MUI v5
- ✅ **Form Validation** - Comprehensive validation with react-hook-form
- ✅ **Password Strength Indicator** - Real-time password strength feedback
- ✅ **Loading States** - Smooth loading indicators and transitions
- ✅ **Error Handling** - User-friendly error messages and feedback
- ✅ **Responsive Design** - Mobile-first design that works on all devices

### Technical Features
- ✅ **React Context API** - Global state management for authentication
- ✅ **Error Boundaries** - Component-level error handling
- ✅ **TypeScript Ready** - Built with JavaScript but easily convertible to TypeScript
- ✅ **API Integration** - Axios-based API service with interceptors
- ✅ **Security Best Practices** - Input sanitization and XSS protection

## 🛠️ Tech Stack

- **Framework**: Next.js 15.x (Pages Router)
- **UI Library**: Material-UI (MUI) v5.x
- **Form Handling**: React Hook Form
- **HTTP Client**: Axios
- **State Management**: React Context API
- **Styling**: Material-UI Theme + CSS Modules
- **Package Manager**: npm

## 📦 Installation

### Prerequisites
- Node.js 18.x or higher
- npm 9.x or higher

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sample-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
   NEXT_PUBLIC_APP_NAME=Sample App
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Demo Credentials

For testing purposes, use these demo credentials:

**Email**: `<EMAIL>`
**Password**: `Demo123!`

Or click the demo button on the login page to auto-fill these credentials.

## 📁 Project Structure

```
sample-app/
├── pages/                          # Next.js pages
│   ├── _app.js                     # App wrapper with providers
│   ├── index.js                    # Home page (redirects)
│   ├── login.js                    # Login page
│   ├── register.js                 # Registration page
│   ├── forgot-password.js          # Password reset page
│   └── dashboard.js                # Protected dashboard page
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   ├── ErrorBoundary.js    # Error boundary component
│   │   │   ├── LoadingSpinner.js   # Loading indicator
│   │   │   └── ProtectedRoute.js   # Route protection wrapper
│   │   └── forms/
│   │       └── PasswordStrengthIndicator.js # Password strength component
│   ├── context/
│   │   └── AuthContext.js          # Authentication context
│   ├── pages/
│   │   ├── login/
│   │   │   └── index.js            # Login page component
│   │   ├── register/
│   │   │   └── index.js            # Registration page component
│   │   ├── forgot-password/
│   │   │   └── index.js            # Password reset component
│   │   └── dashboard/
│   │       └── index.js            # Dashboard component
│   ├── services/
│   │   └── api.js                  # API service and configuration
│   └── utils/
│       └── validation.js           # Validation utilities and rules
├── public/                         # Static assets
├── styles/                         # Global styles
└── package.json                    # Dependencies and scripts
```

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Testing (when implemented)
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

## 🌐 API Endpoints

The application uses mock API responses for demonstration. In a real implementation, these endpoints should be implemented:

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/social-login` - Social authentication
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh-token` - Token refresh

### User Endpoints
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `PUT /api/user/change-password` - Change password

## 🔐 Environment Variables

Create a `.env.local` file with the following variables:

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api

# App Configuration
NEXT_PUBLIC_APP_NAME=Sample App

# Social Authentication (when implementing real social login)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id

# Security
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Heroku
- DigitalOcean App Platform

---

**Built with ❤️ using Next.js and Material-UI**
