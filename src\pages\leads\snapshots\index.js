import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
  Box,
  Divider,
  DialogContentText,
  Tooltip,
  Menu,
} from "@mui/material";
import CustomChip from "src/@core/components/mui/chip";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import SnapshotUploadDialog from "src/@core/components/custom-components/SnapshotUploadDialog";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import DeleteDialog from "./DeleteDialog";
import SnapshotEditDialog from "./SnapshotEditDialog";
import ActivateDialog from "./ActivateDialog";
import { useRBAC } from "src/pages/permission/RBACContext";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LeadSnapshots = () => {
  const { can } = useRBAC();

  const {
    uploadSnapshots,
    updateSnapshots,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    snapshotsData,
    setSnapshotsData,
    snapshotsDataDetails,
  } = useContext(AuthContext);

  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rowCount, setRowCount] = useState(0);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState();
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogFailure, setDialogFailure] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [disableButton, setDisableButton] = useState(false);
  const [employeesData, setEmployeesData] = useState([]);
  const [categoriesData, setCategoriesData] = useState([]);

  const [categoryId, setCategoryId] = useState("");

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [leadStatus, setLeadStatus] = useState("");
  const [leadPriority, setLeadPriority] = useState("");
  const [status, setStatus] = useState("");
  const [assignedTo, setAssignedTo] = useState("");

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUserList(page, pageSize);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchUserList(page, pageSize);
  };

  const handleAddSnapshots = () => {
    setOpenDialog(true);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const fetchUserList = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.snapshotsGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.leadSnapshotDTOResponses || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchUserList(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const documentDetails = {
    documentCategory: getDocumentCategoryId("lead"),
    documentSubCategory: getDocumentSubCategoryId("snapshots"),
    categoryTypeId: categoryId,
    documentFrom: "EMPLOYEE",
    documentTo: "HOUZER",
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });
    formData.append("documentDetails", JSON.stringify(documentDetails));

    // API call
    await uploadSnapshots(
      formData,
      () => {
        setOpenDialog(false);
        const message = `Successfully Uploaded`;
        setDialogMessage(message);
        setDialogSuccess(true);

        setSelectedFiles([]);
      },
      () => {
        console.log("Failure");
        const message = `Failed to upload`;
        setDialogMessage(message);
        setDialogSuccess(true);
        setOpenDialog(false);
      }
    );

    fetchUserList();
    setLoading(false);
    setDisableButton(false);
  };

  const handlePatchSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();
    if (selectedFiles.length > 0) {
      selectedFiles.forEach((file) => {
        formData.append("file", file);
      });
    }
    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append(
      "leadSnapshotResponseDTO",
      JSON.stringify({
        assignedTo: assignedTo,
        status: status,
        leadStatus: leadStatus,
        leadPriority: leadPriority,
      })
    );

    // API call
    await updateSnapshots(
      snapshotsDataDetails?.id,
      formData,
      () => {
        setEditDialogOpen(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },
      () => {
        console.log("Failure");
        setDialogFailure(true);
      }
    );

    fetchUserList();
    setLoading(false);
    setDisableButton(false);
    setEditDialogOpen(false);
  };

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res?.data?.data);
      })
      .catch((err) => console.log("Employees error", err));

    // Fetch all categories
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setCategoriesData(res.data.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const handleFailureClose = () => {
    // New function to close failure dialog
    setDialogFailure(false);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      field: "location",
      headerName: "File Name",
      flex: 0.26,
      minWidth: 150,
      valueGetter: (params) => {
        const metaData = params?.row?.metaData;
        if (Array.isArray(metaData)) {
          return metaData
            .filter((item) => item?.isActive) // Filter by isActive property
            .map((item) => {
              const location = item?.location || "";
              const parts = location.split("/");
              return parts[parts.length - 1];
            })
            .join(", ");
        }
        return "";
      },
    },
    {
      field: "type",
      headerName: "Type",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const assignedTo = categoriesData?.find(
          (item) => item?.id === params?.row?.categoryTypeId
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 0.13,
      minWidth: 140,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "assignedTo",
      headerName: "Assigned To",
      flex: 0.13,
      minWidth: 140,
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setSnapshotsData({
            ...snapshotsData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setEditDialogOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  if (can("leadSnapshots_READ")) {
    return (
      <>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6" fontWeight={"600"}>
                  Lead Snapshots
                </Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid item xs={12} sm="auto">
                    <Button variant="contained" onClick={handleAddSnapshots}>
                      Add Snapshots
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>

          <CardContent>
            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={userList}
                columns={columns}
                checkboxSelection
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={
                  userList && userList.length > 0 ? rowsPerPageOptions : []
                }
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={40}
                disableSelectionOnClick
                pagination={rowCount > 0}
                headerHeight={40}
              />
            </div>
          </CardContent>
        </Card>

        <SnapshotUploadDialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          setCategoryId={setCategoryId}
          loading={loading}
          disableButton={disableButton}
        />

        <SnapshotEditDialog
          open={editDialogOpen}
          onClose={() => setEditDialogOpen(false)}
          onSave={() => handlePatchSave()}
          data={snapshotsDataDetails}
          selectedFiles={selectedFiles}
          loading={loading}
          fetchUserList={fetchUserList}
          setSelectedFiles={setSelectedFiles}
          categoryId={categoryId}
          setCategoryId={setCategoryId}
          disableButton={disableButton}
          leadStatus={leadStatus}
          setLeadStatus={setLeadStatus}
          leadPriority={leadPriority}
          setLeadPriority={setLeadPriority}
          status={status}
          setStatus={setStatus}
          assignedTo={assignedTo}
          setAssignedTo={setAssignedTo}
        />

        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />

        <ActivateDialog
          open={openActivateDialog}
          onClose={handleCloseActivateDialog}
          data={currentRow}
        />

        <Dialog
          open={dialogSuccess}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleClose}
                sx={{ margin: "auto", width: 100 }}
              >
                Ok
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    );
  } else {
    return null;
  }
};

export default LeadSnapshots;
