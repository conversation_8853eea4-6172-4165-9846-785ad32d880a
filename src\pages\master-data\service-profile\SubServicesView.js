// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "@mui/material/TableCell";
import SubServicesEdit from "./SubServicesEdit";


const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const SubServicesView = ({ data,expanded,fetchUsers }) => {
  const theme = useTheme();

 

  const [state, setState] = useState("view");

  const [viewData, setViewData] = useState(data);


  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const activeSubServices = viewData?.values?.filter(
    (subService) => subService.isActive === true
  );

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={`${viewData?.serviceName} Sub Services`}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px", cursor:'pointer' }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Service name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {viewData?.serviceName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Label name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {viewData?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Status:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                        {viewData?.isActive ? "Active" : "InActive"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Has Sub-Services:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                        {viewData?.values.length > 0 ? "True" : "False"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Sub-Services:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        {viewData?.values && (
                            <Typography className="data-field">
                                {activeSubServices?.map((item) => item.name).join(", ")}
                            </Typography>
                        )}
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <SubServicesEdit
                onCancel={editClick}
                formData={viewData}
                fetchUsers={fetchUsers}
                setData={setViewData}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default SubServicesView;
