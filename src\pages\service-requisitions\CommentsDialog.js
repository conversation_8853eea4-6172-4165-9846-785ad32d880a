import { useContext, useEffect, useState } from "react";
import {
  Button,
  Box,
  IconButton,
  DialogContentText,
  Tooltip,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  Typography,
  TablePagination,
  TableHead,
  TableFooter,
} from "@mui/material";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import { useForm } from "react-hook-form";
import "react-datepicker/dist/react-datepicker.css";

import TextField from "@mui/material/TextField";
import CloseIcon from "@mui/icons-material/Close";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Controller } from "react-hook-form";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import { DataGrid } from "@mui/x-data-grid";
import MUITableCell from "../SP/MUITableCell";
import { useTheme } from "@emotion/react";

const CommentsDialog = ({ setConvList, list, role }) => {
  const auth = useAuth();
  const theme = useTheme();

  const {
    register,
    control,
    setValue,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [conversationList, setConversationList] = useState(list);

  const [conversation, setConversation] = useState({});

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCancel = () => {
    setOpenDialog(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  useEffect(() => {
    setConvList(conversationList);
  }, [conversationList]);

  async function submit(data) {
    const newConversation = {
      followUpDate: data?.followUpDate,
      comments: data?.comments,
      followUpAction: data?.followUpAction,
    };

    setConversationList((prevList) => [...prevList, newConversation]);

    setOpenDialog(false);
    reset();
  }

  return (
    <>
      <Button
        color="primary"
        size="medium"
        variant="contained"
        onClick={handleOpenDialog}
      >
        Add Comments
      </Button>

      <Dialog open={openDialog} onClose={handleCancel} maxWidth={"md"}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start"},
            fontSize: { xs: 19, md: 20  },
          }}
          textAlign={"center"}
        >
          Add Comments
        
          <Box sx={{ position: "absolute", top: "4px", right: "10px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
          </DialogTitle>
        <DialogContent>
          <Grid container spacing={4}>
            {!role && (
              <>
                <Grid item xs={12} md={3}>
                  <FormControl>
                    <Controller
                      name="followUpDate"
                      control={control}
                      rules={{ required: "FollowUp Date is required" }} // Adding required validation rule
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          fullWidth
                          label="Follow Up Date"
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(errors.followUpDate)}
                          helperText={errors.followUpDate?.message}
                          aria-describedby="followUpDate"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="followUpAction"
                      control={control}
                      rules={{ required: "FollowUp Action is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          rows={2}
                          multiline
                          label="Follow Up Action"
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(errors.followUpAction)}
                          helperText={errors.followUpAction?.message}
                          aria-describedby="followUpAction"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </>
            )}

            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="comments"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Comments"
                      InputLabelProps={{ shrink: true }}
                      aria-describedby="comments"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
          }}
        >
          <Button
            size="medium"
            onClick={handleCancel}
            variant="outlined"
            color="primary"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            onClick={handleSubmit(submit)}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Complete SP Conversation Details
        </DialogTitle>
        <DialogActions>
          <Box sx={{ position: "absolute", top: "13px", right: "40px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogActions>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {!role && (
                   <TableRow>
                   <MUITableCell>
                     <Typography sx={{ fontWeight: 600 }}>
                       Follow Up Date
                     </Typography>
                   </MUITableCell>
                   <MUITableCell>
                     <Typography>{conversation.followUpDate}</Typography>
                   </MUITableCell>
                 </TableRow>
                )}
               
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>Comments</Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.comments}</Typography>
                  </MUITableCell>
                </TableRow>
                {!role && (
                  <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      FollowUp Action
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.followUpAction}</Typography>
                  </MUITableCell>
                </TableRow>
                )}
                
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "center",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CommentsDialog;
