// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
import MUITableCell from "../../MUITableCell";

import MembershipEdit from "./MembershipEdit";
 
const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
 
const MembershipView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");
 
  const viewClick3 = () => {
    setState3("edit");
  };
 
  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Membership"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
               <Table>
  <TableHead>
    <TableRow>
     
      <TableCell>Description</TableCell>
     
    </TableRow>
  </TableHead>
  <TableBody
    sx={{
      "& .MuiTableCell-root": {
        p: `${theme.spacing(1.35, 1.125)} !important`,
      },
    }}
  >
    {data?.memberShipList?.map((membership, index) => (
      <TableRow key={index}>
        <TableCell>
          <Typography className="data-field" marginLeft={"12px"}>
            {membership?.description}
          </Typography>
        </TableCell>
       
      
      </TableRow>
    ))}
  </TableBody>
</Table>

              </TableContainer>
            )}
 
            {state3 === "edit" && (
              <MembershipEdit data={data} onCancel={editClick3} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default MembershipView;