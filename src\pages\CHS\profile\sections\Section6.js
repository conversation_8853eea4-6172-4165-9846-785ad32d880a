// ** React Imports
import { forwardRef, useEffect } from "react";

// ** MUI Imports
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import { useAuth } from "src/hooks/useAuth";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import SelectMultipleBasic from "src/@core/components/custom-components/SelectMultipleBasic";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";
import { yupResolver } from "@hookform/resolvers/yup";


// ** Icon Imports

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const documentsAvailable = [
  {
    value: "STRUCTURAL_AUDIT",
    name: "Structural Audit",
  },
  {
    value: "CONVEYANCE",
    name: "Conveyance",
  },
  {
    value: "FEASIBILITY_REPORT",
    name: "Feasibility Report",
  },
  {
    value: "DP_COPY",
    name: "DP Copy",
  },
  {
    value: "PROPERTY_CARD",
    name: "Property Card",
  },
  {
    value: "PLOT_LAYOUT",
    name: "Plot-Layout",
  },
  {
    value: "SURVEY_OLD_CC_OLD_PLAN",
    name: "Survey old CC old plan",
  },
];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section6 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();

  const fields = ["professionalDetails","redevelopment"]

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(SocietyValidationsSection1(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    if (Array.isArray(data?.documentsAvailable)) {
      data.documentsAvailable = data.documentsAvailable.join(",");
    }

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );


    const userUniqueId =
    formData && formData.userId !== undefined ? formData?.userId : user?.id;
  const response = await auth.updateEntity(trimmedData, userUniqueId)
  onCancel();
  }

  useEffect(() => {
    if (formData?.documentsAvailable) {
      const valuesArray = formData?.documentsAvailable.split(",");
      setValue("documentsAvailable", valuesArray); // 'select' should be replaced with your field name
    }
  }, [formData, setValue]);

  return (
    <Card>
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <SelectMultipleBasic
              register={register}
              id={"documentsAvailable"}
              label={"Do you have following Documents?"}
              nameArray={documentsAvailable}
              defaultValue={formData?.documentsAvailable}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="professionalDetails"
                control={control}
                defaultValue={formData?.professionalDetails}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Professional Details"
                    InputLabelProps={{ shrink: true }}
                    helperText={errors.professionalDetails?.message}
                    error={Boolean(errors.professionalDetails)}
                    aria-describedby="validation-basic-professional-details"
                    inputProps={{
                      title:'Enter any additional details'
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="redevelopment"
                control={control}
                defaultValue={formData?.redevelopment}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Have You taken the steps towards redevelopment?
                      If yes, elaborate."
                    InputLabelProps={{ shrink: true }}
                    helperText={errors.redevelopment?.message}
                    error={Boolean(errors.redevelopment)}
                    aria-describedby="validation-basic-redevelopment"
                    inputProps={{
                      title: "If yes, please elaborate on the actions you have taken or plan to take towards redevelopment.",
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default Section6;