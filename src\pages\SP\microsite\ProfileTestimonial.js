import Slider from 'react-slick'

import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Box, Card, Typography, CardContent } from '@mui/material'

import Icon from 'src/@core/components/icon'
import CustomAvatar from 'src/@core/components/mui/avatar'

const ProfileTestimonial = ({ slides }) => {
  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  const sliderSettings = {
    infinite: true,
    dots: true,
    speed: 1500,
    slidesToShow: 1,
    slidesToScroll: 1, // Number of slides to scroll at a time
    autoplay: true,
    autoplaySpeed: 3500
  }

  return (
    <>
      <Slider {...sliderSettings}>
        {slides?.map(slide => (
          <div key={slide.index}>
            <CardContent
              sx={{
                borderRadius: 1,
                width: '100%',
                '& .MuiTypography-root, & svg': {
                  color: 'black'
                },
                px: { xs: 6, lg: 12 },
                gap: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                justifyContent: 'center'
              }}
            >
              <Box
                sx={{
                  px: { lg: 10 },
                  mb: { lg: 0 },
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%'
                }}
              >
                <Typography fontSize={18} color={'black'} sx={{ textDecoration: 'none' }}>
                  {slide.description}
                </Typography>
              </Box>
              <Box sx={{ px: { lg: 10 }, mb: 2, mt: 2, display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
                <CustomAvatar
                  skin='light'
                  sx={{ color: 'black', mr: 3, width: 48, height: 48, backgroundColor: '#8FAD88' }}
                >
                  <Icon icon='tabler:user' fontSize={'2rem'} />
                </CustomAvatar>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                  <Typography variant='h5' fontWeight={700}>
                    {slide.name}
                  </Typography>
                  <Typography variant='h6' sx={{ textAlign: 'start' }} fontWeight={600}>
                    {slide.designation}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </div>
        ))}
      </Slider>
      <style jsx global>
        {`
          .slick-slide {
            padding: 0 10px;
          }
          .slick-slide img {
            border-radius: 50%;
            height: 250px;
            width: auto;
          }
          .slide-item {
            outline: none; // Remove outline on the slides
          }
          .slick-prev,
          .slick-next {
            width: 22px;
            height: 22px;
          }
          .slick-prev:before,
          .slick-next:before {
            font-size: 30px !important;
            color: black;
          }
          .slick-dots {
            bottom: -44px;
            padding-bottom: 10px;
            display: none !important;
          }
          .slick-dots li {
            margin: 0;
            width: 17px;
            height: 17px;
          }
          .slick-dots li button:before {
            font-size: 13px;
            color: #fdf4e9;
          }
          .slick-dots li.slick-active button:before {
            color: #fdf4e9;
          }

          @media only screen and (max-width: 600px) {
            .slick-prev,
            .slick-next {
              display: none !important;
            }
            .slick-slide {
              padding: 0 4px;
            }
          }
        `}
      </style>
    </>
  )
}

export default ProfileTestimonial
