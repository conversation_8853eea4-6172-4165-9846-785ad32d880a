// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
//import { C } from '@fullcalendar/core/internal-common'

import { useAuth } from "src/hooks/useAuth";

const ServicesOfferedEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [liasoningConsultancy, setLiasoningConsultancy] = useState(
    formData?.liasoningConsultancy
  );

  const [designConsultancy, setDesignConsultancy] = useState(
    formData?.designConsultancy
  );

  const [conceptPlanning, setConceptPlanning] = useState(
    formData?.conceptPlanning
  );

  const [architectureDesign, setArchitectureDesign] = useState(
    formData?.architectureDesign
  );

  const [forSmoothExecution, setForSmoothExecution] = useState(
    formData?.forSmoothExecution
  );

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(true);

  const handleCheckboxChange = (e, checkboxName) => {
    setValue(checkboxName, e.target.checked);

    if (
      checkboxName === "conceptPlanning" ||
      checkboxName === "architectureDesign"
    ) {
      const otherCheckboxName =
        checkboxName === "conceptPlanning"
          ? "architectureDesign"
          : "conceptPlanning";
      const otherCheckboxValue = getValues(otherCheckboxName);

      if (!e.target.checked || !otherCheckboxValue) {
        setValue("designConsultancy", false);
        setDesignConsultancy(false);
      } else if (e.target.checked && otherCheckboxValue) {
        setValue("designConsultancy", true);
        setDesignConsultancy(true);
      }
    } else if (checkboxName === "forSmoothExecution") {
      if (e.target.checked) {
        setValue("liasoningConsultancy", true);
        setLiasoningConsultancy(true);
      } else {
        setValue("liasoningConsultancy", false);
        setLiasoningConsultancy(false);
      }
    }
    setIsSaveButtonDisabled(false);
  };

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="designConsultancy"
                control={control}
                defaultValue={
                  formData?.designConsultancy
                    ? formData.designConsultancy
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          handleCheckboxChange(e, "designConsultancy");
                          if (e.target.checked) {
                            setValue("conceptPlanning", true);
                            setConceptPlanning(true);
                            setValue("architectureDesign", true);
                            setArchitectureDesign(true);
                          } else {
                            setValue("conceptPlanning", false);
                            setConceptPlanning(false);
                            setArchitectureDesign(false);
                            setValue("architectureDesign", false);
                          }
                        }}
                      />
                    }
                    label={
                      <span style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                        Design Consultancy
                      </span>
                    }
                  />
                )}
              />
            </FormControl>

            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl>
                  <Controller
                    name="conceptPlanning"
                    control={control}
                    defaultValue={
                      formData?.conceptPlanning
                        ? formData.conceptPlanning
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              handleCheckboxChange(e, "conceptPlanning");
                            }}
                          />
                        }
                        label="Concept planning, design & feasibility"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl>
                  <Controller
                    name="architectureDesign"
                    control={control}
                    defaultValue={
                      formData?.architectureDesign
                        ? formData.architectureDesign
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            onChange={(e) => {
                              field.onChange(e);
                              handleCheckboxChange(e, "architectureDesign");
                            }}
                          />
                        }
                        label="Architecture design & execution support"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="liasoningConsultancy"
                control={control}
                defaultValue={
                  formData?.liasoningConsultancy
                    ? formData.liasoningConsultancy
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          handleCheckboxChange(e, "liasoningConsultancy");
                          if (e.target.checked) {
                            setValue("forSmoothExecution", true);
                          } else {
                            setValue("forSmoothExecution", false);
                          }
                        }}
                      />
                    }
                    label={
                      <span style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                        Liaisoning Consultancy
                      </span>
                    }
                  />
                )}
              />
            </FormControl>
            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl>
                  <Controller
                    name="forSmoothExecution"
                    control={control}
                    defaultValue={
                      formData?.forSmoothExecution
                        ? formData.forSmoothExecution
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            onChange={(e) => {
                              field.onChange(e);
                              handleCheckboxChange(e, "forSmoothExecution");
                            }}
                          />
                        }
                        label="For smooth execution and completion of a project by coordinating between the sanctioning authorities and principal architect and other stake holders"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSaveButtonDisabled}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ServicesOfferedEdit;
