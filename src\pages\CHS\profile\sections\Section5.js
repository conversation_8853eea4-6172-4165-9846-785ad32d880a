// ** React Imports
import { forwardRef } from "react";

// ** MUI Imports
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import Typography from "@mui/material/Typography";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import { yupResolver } from "@hookform/resolvers/yup";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";



// ** Icon Imports

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section5 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();
  
  const fields=["secretaryName","secretaryContactNo","chairmanName","chairmanContactNo","emailId","referenceSocietyName","referenceContactPerson","referenceContactNumber"]
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(SocietyValidationsSection1(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }

    const userUniqueId =
    formData && formData.userId !== undefined ? formData?.userId : user?.id;
  const response = await auth.updateEntity(trimmedData, userUniqueId)
  onCancel();
  }

  return (
    <Card>
      <CardHeader
        title={
          <Typography variant="body1" sx={{ mt: 6, mb: 1 }}>
            <strong>Contact No./Email ID:</strong>
          </Typography>
        }
      />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="secretaryName"
                control={control}
                defaultValue={formData?.secretaryName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label="*Secretary Name"
                    placeholder="Enter Secretary Name"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.secretaryName)}
                    helperText={errors.secretaryName?.message}
                    aria-describedby="validation-basic-secretary-name"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="secretaryContactNo"
                control={control}
                defaultValue={formData?.secretaryContactNo}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type="tel"
                    label="Secretary Contact No"
                    error={Boolean(errors.secretaryContactNo)}
                    size='small'
                    placeholder="+91 95 155 990 22"
                    InputLabelProps={{ shrink: true }}
                    helperText={errors.secretaryContactNo?.message}
                    aria-describedby="validation-secretaryContactNo"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="chairmanName"
                control={control}
                defaultValue={formData?.chairmanName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label="Chairman Name"
                    placeholder="Enter Chairman Name"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.chairmanName)}
                    helperText={errors.chairmanName?.message}
                    aria-describedby="validation-basic-chairman-name"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="chairmanContactNo"
                control={control}
                defaultValue={formData?.chairmanContactNo}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type="tel"
                    label="Chairman Contact No"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.chairmanContactNo)}
                    helperText={errors.chairmanContactNo?.message}
                    placeholder="+91 95 155 990 22"
                    aria-describedby="validation-chairmanContactNo"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="emailId"
                control={control}
                defaultValue={formData?.emailId}
                render={({ field }) => (
                  <EmailTextField
                    {...field}
                    type="email"
                    label="Email(Secretary/Chairman)"
                    InputLabelProps={{ shrink: true }}
                    size='small'
                    placeholder="<EMAIL>"
                    error={Boolean(errors.emailId)}
                    helperText={errors.emailId?.message}
                    aria-describedby="validation-basic-emailId"
                  />
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </CardContent>

      <CardHeader
        title={
          <Typography variant="body1" sx={{ mt: 6, mb: 1 }}>
            <strong>Reference from Society:</strong>
          </Typography>
        }
      />
      <CardContent>
        <Grid container spacing={6}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="referenceSocietyName"
                control={control}
                defaultValue={formData?.referenceSocietyName}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Society Name"
                    placeholder="Enter society name"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.referenceSocietyName)}
                    helperText={errors.referenceSocietyName?.message}
                    aria-describedby="validation-basic-referenceSocietyName"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="referenceContactPerson"
                control={control}
                defaultValue={formData?.referenceContactPerson}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label="Contact Person"
                    placeholder="Enter contact person name"
                    InputLabelProps={{ shrink: true }}
                    size='small'
                    error={Boolean(errors.referenceContactPerson)}
                    helperText={errors.referenceContactPerson?.message}
                    aria-describedby="validation-basic-referenceContactPerson"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="referenceContactNumber"
                control={control}
                defaultValue={formData?.referenceContactNumber}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type="tel"
                    label="Contact Number"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.referenceContactNumber)}
                    helperText={errors.referenceContactNumber?.message}
                    placeholder="+91 95 155 990 22"
                    aria-describedby="validation-referenceContactNumber"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default Section5;