import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import ViewQuotations from "src/pages/quotations/ViewQuotations";
import {
  Card,
  CardContent,
  MenuItem,
  Menu,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";


import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { useForm } from "react-hook-form";


const QuotationDetails = () => {
  const sampleData = [
    {
      id: 1,
      name: "Service Provider A",
      quotedAmount: "5000",
      availableDate: "2024-08-30",
      quotationStatus: "Approved",
      typeOfWork: "Consulting",
    },
  ];
  

  const [expanded, setExpanded] = useState(true);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  // Constants
  const rowsPerPageOptions = [5, 10, 15, 20, 25, 50, 100];
  const [menu, setMenu] = useState(null);
  const handleCloseMenuItems = () => {
    setMenu(null);
  };
  // Define columns
  const columns = [
    {
      field: "name",
      minWidth: 95,
      headerName: "SP Name",
      flex: 0.13,
      //   valueGetter: (params) => params.row.additionalDetails.title,
    },
    {
      field: "quotedAmount",
      minWidth: 185,
      headerName: "Quotation Amount ",
      flex: 0.16,
    },
    {
      field: "availableDate",
      minWidth: 145,
      headerName: "Available Date",
      flex: 0.15,
    },
    {
      field: "quotationStatus",
      minWidth: 165,
      headerName: "Quotation Status",
      flex: 0.15,
    },
    {
      field: "typeOfWork",
      minWidth: 155,
      headerName: "Type of Service",
      flex: 0.15,
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        
          //   setTaskData({
          //     ...taskData,
          //     id: row.id,
          //   });
        };
        const onClickViewProfile = () => {
          setOpenViewDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
               <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
              <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>View Quote</MenuItem>
              
            </Menu>
            </Tooltip>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  // Use States
  const {
  
    reset,
   
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");


  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

    // const url = getUrl(authConfig.getAllTasks)
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.tasks || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

 

  const handleCloseEditDialog = () => {
    reset();
    setOpenViewDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Card>
        <CardContent>
          <div style={{ height: 380, width: "100%" }}>
            <DataGrid
              rows={sampleData}
              columns={columns}
              checkboxSelection
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={userList.length > 0 ? rowsPerPageOptions : []}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={38}
              headerHeight={38}
            />
          </div>
        </CardContent>
      </Card>

      <ViewQuotations
        open={openViewDialog}
        onClose={handleCloseEditDialog}
        data={sampleData}
      />
    </>
  );
};

export default QuotationDetails;
