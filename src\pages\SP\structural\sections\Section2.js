
// ** React Imports
import { forwardRef, useState, useEffect, useMemo } from 'react'

// ** MUI Imports

import Grid from '@mui/material/Grid'

import Button from '@mui/material/Button'

import TextField from '@mui/material/TextField'

import FormControl from '@mui/material/FormControl'
import OutlinedInput from '@mui/material/OutlinedInput'

import InputAdornment from '@mui/material/InputAdornment'
import { Box } from '@mui/system'


// ** Third Party Imports
import toast from 'react-hot-toast'
import DatePicker from 'react-datepicker'
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'

// ** Icon Imports

import SelectBasic from 'src/@core/components/custom-components/SelectBasic'
import SelectMultipleBasic from 'src/@core/components/custom-components/SelectMultipleBasic'
import { FormHelperText } from '@mui/material'
import SelectCategory from 'src/@core/components/custom-components/SelectCategory'



const location = [
  {
    value:'ISLAND',
    key:'Island'
  },
  {
    value:'WESTERN_SUBURB',
    key:'Western Suburb'
  },
  {
    value:'CENTRAL_SUBURB',
    key:'Central Suburb'
  },
  {
    value:'THANE',
    key:'Thane'
  },
  {
    value:'ALL',
    key:'All'
  },
  {
    value:'OTHER',
    key:'Other'
  }
];

const experience = [
  {
    value:'LESS_THAN_5_YEARS',
    key:'less than 5 years'
  },
  {
    value:'_5_TO_10_YEARS',
    key:'5 to 10 years'
  },
  {
    value:'MORE_THAN_10_YEARS',
    key:'more than 10 years'
  }
];



const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

//const areaOfOperation = ['Island', 'Western Suburb', 'Central Suburb', 'Thane', 'All', 'Other']


const Section2 = ({ onCancel,formData } ) => {
  const auth = useAuth();

  const [yearsOfExperience,setYearsOfExperience] = useState(formData?.yearsOfExperience)
  const [areaofOperation,setAreaofOperation] = useState(formData?.areaofOperation)

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    clearErrors,
    formState: { errors }
  } = useForm();


  

  async function submit(data) {

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value])
    );
    const hasWhiteSpace = Object.values(trimmedData).some((value) => typeof value === 'string' && value === '');
    if (hasWhiteSpace) {
      toast.error('Fields cannot contain only white spaces');
      return;
    }
  

    if (Array.isArray(data?.areaofOperation)) {
      data.areaofOperation = data.areaofOperation.join(",");
    }

    const response = await auth.updateEntity(trimmedData, () => {
      console.error("Structural Company Details failed");
    });

    onCancel();
  }

  // const yearsOfExperience = ['Less than 5 years', '5-10 years', 'More than 10 years']

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name='teamSize'
              control={control}
              rules={{ required: true, pattern: /^(?:[1-9]|[1-9][0-9]|100)$/ }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type='number'
                  value={value}
                  label='Team Size'
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder='Enter team size(1-100)'
                  aria-describedby='validation-teamSize'
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.teamSize?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                Team Size is required
              </FormHelperText>
            )}
            {errors.teamSize?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                Please enter a valid Team Size (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
        <SelectCategory 
          clearErrors={clearErrors}
            register={register} 
            id={'yearsOfExperience'} 
            label={'Years Of Experience'} 
            nameArray={experience} 
            defaultValue={formData?.yearsOfExperience} 
            value={yearsOfExperience}
            onChange={(e) => setYearsOfExperience(e.target.value)}
            error={Boolean(errors.yearsOfExperience)}
          />
              {errors.yearsOfExperience && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-yearsOfExperience'>
                  Please select years of Experience
                </FormHelperText>
              )}
        </Grid>
        <Grid item xs={12} sm={4}>
          <SelectCategory 
          clearErrors={clearErrors}
          register={register} id={'areaofOperation'}
            label={'Area of operation'}
            nameArray={location}
            defaultValue={formData?.areaofOperation}
            value={areaofOperation}
            onChange={(e) => setAreaofOperation(e.target.value)}
            error={Boolean(errors.areaofOperation)}
            aria-describedby='validation-areaofOperation'
          />
              {errors.areaofOperation && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-areaofOperation'>
                  Please select area of operation
                </FormHelperText>
              )}
        </Grid>
        {(areaofOperation === 'OTHER') && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name='otherArea'
                    control={control}
                    defaultValue={formData?.otherArea}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label='Other Area of Operation'
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 30 }}
                        placeholder='Enter area of operation'
                        error={Boolean(errors.otherArea)}
                        aria-describedby='validation-otherArea'
                      />
                    )}
                  />
                  {errors.otherArea && (
                    <FormHelperText sx={{ color: 'error.main' }} id='validation-otherArea'>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
        )}

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name='awards'
              control={control}
              defaultValue={formData?.awards}
              render={({ field}) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Awards'
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ maxLength: 1000 }}
                  aria-describedby='broker-validation-basic-awards'
                />
              )}
            />
            
          </FormControl>
        </Grid>
        <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='briefProfile'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.briefProfile}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Brief Profile in your words'
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ maxLength: 1000 }}
                  error={Boolean(errors.briefProfile)}
                  aria-describedby='broker-validation-brief-profile'
                />
              )}
            />
            {errors.briefProfile && (
              <FormHelperText sx={{ color: 'error.main' }} id='broker-validation-brief-profile'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button variant='outlined' color='primary' size='medium' sx={{ mr:3 }} onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Section2
