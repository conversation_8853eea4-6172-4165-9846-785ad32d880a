// ** MUI Imports
import Grid from "@mui/material/Grid";
import { styled } from "@mui/material/styles";

// ** MUI Imports
import MuiAccordion from "@mui/material/Accordion";
import Typography from "@mui/material/Typography";
import AccordionDetails from "@mui/material/AccordionDetails";
import MuiAccordionSummary from "@mui/material/AccordionSummary";

// ** Icon Imports
import Icon from "src/@core/components/icon";
import  React, { useCallback, useState,useEffect } from "react";

// Styled component for Accordion component
const Accordion = styled(MuiAccordion)(({ theme }) => ({}));

// Styled component for AccordionSummary component
const AccordionSummary = styled(MuiAccordionSummary)(({ theme }) => ({
  marginBottom: -1,
  padding: theme.spacing(0, 4),
  minHeight: theme.spacing(6),
  transition: "min-height 0.15s ease-in-out",
  border: '2px solid #f2f7f2',
  "& .MuiAccordionSummary-content": {
    margin: theme.spacing(2.275, 0),
    "&.Mui-expanded": {
      margin: theme.spacing(1.75, 0),
    },
  },
  "&.Mui-expanded": {
    backgroundColor: "rgb(242, 247, 242)",
    transition: "background-color 0.15s ease-in-out",
    borderBottom:
      theme.palette.mode === "light"
        ? `1px solid ${theme.palette.grey[300]}`
        : `1px solid ${theme.palette.divider}`,
    minHeight: theme.spacing(6),
    "& .MuiAccordionSummary-content": {
      "& .MuiTypography-root": {
        color: theme.palette.primary.main,
        fontWeight: 600,
      },
    },
  },
  "& .MuiAccordionSummary-expandIconWrapper": {
    "&.Mui-expanded": {
      color: theme.palette.primary.main,
    },
  },
}));


const AccordionBasic = ({ id, ariaControls, heading, body, expanded: externalExpanded }) => {
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    setExpanded(externalExpanded);
  }, [externalExpanded]);

  const handleOnChange = useCallback((event, isExpanded) => {
    setExpanded(isExpanded);
    console.log('Accordion clicked!');
  }, []);

  return (
    <>
      <Accordion expanded={expanded} onChange={handleOnChange}>
        <AccordionSummary 
          id={id}
          aria-controls={ariaControls}
          expandIcon={<Icon fontSize="1.1rem" icon="tabler:chevron-down" />}
        >
          <Typography variant="body1" sx={{color:'black'}} >{heading}</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography sx={{ color: 'text.secondary'}}>{body}</Typography>
        </AccordionDetails> 
      </Accordion>
    </>
  );
};

export default AccordionBasic;


