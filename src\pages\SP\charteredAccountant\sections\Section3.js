// ** React Imports
import { useState, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Divider } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import { useForm, Controller } from "react-hook-form";

// ** Icon Imports
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  email: "",
  lastName: "",
  password: "",
  firstName: "",
};

const Section3 = ({ onCancel, setter, defaultData,setUnsavedChanges }) => {
  const { updateEntityServices } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  const [formData, setFormData] = useState(defaultData);

  const [homeBuyerLitigationSupported, setHomeBuyerLitigationSupported] =
    useState(defaultData.homeBuyerLitigation.isSupported);

    const [homeBuyerDueDiligenceSupported, setHomeBuyerDueDiligenceSupported] =
    useState(defaultData.homeBuyerDueDiligence.isSupported);

  const [
    homeBuyerLitigationPriceSupported,
    setHomeBuyerLitigationPriceSupported,
  ] = useState(defaultData.homeBuyerLitigation.price);

  const [
    homeBuyerDueDiligencePriceSupported,
    setHomeBuyerDueDiligencePriceSupported,
  ] = useState(defaultData.homeBuyerDueDiligence.price);

  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (event) => {
    const { checked } = event.target;
   
    setHomeBuyerLitigationSupported(checked);
    setHomeBuyerDueDiligenceSupported(checked);
    
    setSelectAll(checked);
    
    setFormData(prev => ({
        ...prev,
        homeBuyerLitigation: { ...prev.homeBuyerLitigation, isSupported: checked },
        homeBuyerDueDiligence: { ...prev.homeBuyerDueDiligence, isSupported: checked }
    }));
};


  useEffect(() => {
    if (
      homeBuyerLitigationSupported &&
      homeBuyerDueDiligenceSupported
    ) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
}, [
    homeBuyerLitigationSupported,
    homeBuyerDueDiligenceSupported,
]);


  const handleOnChange = (event) => {
    const { name, checked } = event.target;
    switch (name) {
      case "homeBuyerLitigation":
        setFormData({
          ...formData,
          homeBuyerLitigation: {
            ...formData.homeBuyerLitigation,
            isSupported: checked,
          },
        });
        break;
      case "homeBuyerDueDiligence":
        setFormData({
          ...formData,
          homeBuyerDueDiligence: {
            ...formData.homeBuyerDueDiligence,
            isSupported: checked,
          },
        });
        break;
    }
    setUnsavedChanges(true)
  };

  async function onSubmit() {
    setUnsavedChanges(false)
    const response = await updateEntityServices(
      { homeBuyerServices: formData },
      () => {
        console.error("serviceDetails failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(formData);
    }

    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Home Buyer Litigation:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="homeBuyerLitigation"
            control={control}
            //defaultValue={formData.homeBuyerLitigation.isSupported}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={homeBuyerLitigationSupported}
                    onChange={(event) => {
                      setHomeBuyerLitigationSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="homeBuyerLitigation"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Home Buyer Due Diligence:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="homeBuyerDueDiligence"
            control={control}
            //defaultValue={formData.homeBuyerDueDiligence.isSupported}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={homeBuyerDueDiligenceSupported}
                    onChange={(event) => {
                      setHomeBuyerDueDiligenceSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="homeBuyerDueDiligence"
                  />
                }
              />
            )}
          />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={onSubmit}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default Section3;
