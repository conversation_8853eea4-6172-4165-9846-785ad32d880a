// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";

// ** Custom Components Imports
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import {
  FormControl,
  FormHelperText,
  Radio,
  RadioGroup,
  TextField,
} from "@mui/material";
import { useForm } from "react-hook-form";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";

const NeedAssessment = ({
  handleSocietyConsideredChange,
  posts,
  setNextActive,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  // ** Hook
  const theme = useTheme();

  const {
    settings: { direction },
  } = useSettings();

  const [value, setValue] = useState(
    defaultData?.hasYourSocietyConsideredForRedevelopment
  );

  const [needAssessment, setNeedAssessment] = useState([]);

  useEffect(() => {
    setValue(defaultData?.hasYourSocietyConsideredForRedevelopment);
  }, [defaultData]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const handleChange = (event) => {
    const value = event.target.value;
    setValue(event.target.value);
    handleSocietyConsideredChange(value);
  };

  useEffect(() => {
    if (posts) {
      let data = [];
      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });
      setNeedAssessment(data);
    }
  }, [posts]);
  useEffect(() => {
    if (value && email && contactNumber && name && societyName) {
      setNextActive(true);
    } else {
      setNextActive(false);
    }
  }, [value, email, contactNumber, name, societyName]);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <div style={{ margin: "6px" }}></div>
          {/* <Grid container spacing={6}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                type="text"
                value={societyName}
                label="Society Name"
                onChange={handleSocietyName}
                error={Boolean(errors.societyName)}
                placeholder="Enter Society Name"
                inputProps={{ maxLength: 30 }}
                InputLabelProps={{ shrink: true }}
              />
              {errors.societyName?.type === "required" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-societyName"
                >
                  Society Name is required
                </FormHelperText>
              )}
              {errors.societyName?.type === "pattern" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-societyName"
                >
                  Invalid Society Name format
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                type="text"
                value={name}
                label="Name"
                onChange={handleName}
                error={Boolean(errors.name)}
                placeholder="Enter your Name"
                inputProps={{ maxLength: 30 }}
                InputLabelProps={{ shrink: true }}
              />
              {errors.name?.type === "required" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-name"
                >
                  Name is required
                </FormHelperText>
              )}
              {errors.name?.type === "pattern" && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-name"
                >
                  Invalid Name format
                </FormHelperText>
              )}
            </Grid>
          </Grid> */}
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              my: 3.25,
              padding:{xs:'0.6rem'}

              
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}>
                Has your society considered going for Redevelopment?
              </Typography>
            </Box>

            <Box>
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={value}
                  name="simple-radio"
                  aria-label="simple-radio"
                  
                >
                  <FormControlLabel
                    control={
                      <Radio
                      id='needAssessment-yes'
                        checked={value === "Yes"}
                        value="Yes"
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:'1rem !important' }}>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel
                    control={
                      <Radio
                      id='needAssessment-no'
                        checked={value === "No"}
                        value="No"
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:'1rem !important' }}>
                        No
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          {/* <Divider sx={{ mt: `${theme.spacing(3)} !important`, mb: `${theme.spacing(3)} !important`, }} /> */}
          {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          {needAssessment.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(1.5)} !important`,
                  mb: `${theme.spacing(1.5)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {needAssessment.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={needAssessment} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default NeedAssessment;
