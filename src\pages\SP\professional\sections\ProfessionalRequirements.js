// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'


import { useTheme } from '@emotion/react'

// ** Demo
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import Section5 from './Section5'


// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const ProfessionalRequirements = ({data,expanded}) => {


  const [state, setState] = useState('view')

  const viewClick = () => {
    setState('edit')
  }

  const editClick = () => {
    setState('view')
  }
    // ** Hook
    const theme = useTheme()

    // const [caDetails, setCaDetails] = useState({
    //   name: "",
    //   companyName: "",
    //   address: "",
    //   mobileNumber: "",
    //   email: "",
    //   websiteUrl:"",
    //   teamSize: "",
    //   yearsOfExperience:"",
    //   briefProfile:"",
    //   awards:""
    // });


    return (
        <>
             <AccordionBasic
              id={'panel-header-2'}
              ariaControls={'panel-content-2'}
              heading={'Requirements'}
              body={
                <>
                  {state === 'view' && (
                    
                        <TableContainer
                          sx={{ padding:'4px 6px' }}
                          className='tableBody'
                          onClick={viewClick}
                        >
                          <Table>
                            <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Location:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.location}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Minimum Plot Size:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.minimumPlotSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Maximum Plot Size:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.maximumPlotSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Min Size:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.minSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Max Size:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.maxSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Are you looking for employ agency for Sole Selling:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.maxSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Are you looking to raise Institutional Finance:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.maxSize}</Typography>
                                </MUITableCell>
                              </TableRow>

                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Remarks:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.remarks}</Typography>
                                </MUITableCell>
                              </TableRow>

                             

                             
                              
                            
                            
                              
                              
                             
                              
                            </TableBody>
                          </Table>
                        </TableContainer>
                      
                  )}

                  {state === 'edit' && <Section5 formData={data} onCancel={editClick} />}
                </>
              }
              expanded={expanded}
            />
        </>
    );

}
export default ProfessionalRequirements;