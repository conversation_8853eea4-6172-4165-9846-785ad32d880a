import React, { useEffect, useState, useContext } from "react";
import { useForm, Controller } from "react-hook-form";
import {
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const EducationalInsightsForm = ({ data, onCancel }) => {
  const { user, entityData, setEntityData, patchArchitectAdditionalData } = useContext(AuthContext);

  const { control, handleSubmit, reset, setValue, unregister, getValues } =
    useForm();

  const [entries, setEntries] = useState(
    []
  );

  const entityId = user?.entityId;

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`educationalInsights[${index}].url`, entry.url);
    });
  }, [entries, setValue]);

  useEffect(() => {
    if(data && data.educationalInsightsList) {
      setEntries(data?.educationalInsightsList);
    }
  }, [data]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.educationalInsights || [];

    setEntries([...currentEntries, { name: "", description: "" }]);
  };
  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);

    unregister(`educationalInsights[${index}].url`);

    reset({
      ...getValues(),
      educationalInsights: newEntries,
    });
  };

  const onSubmit = async (data) => {
    const insightsData =
      data.educationalInsights.length > 0
        ? data.educationalInsights
        : [{ url: "" }];

      const response = await patchArchitectAdditionalData({ educationalInsightsList: insightsData },() => {
        console.log("Success educationalInsights.")
      }, () => {
        console.error("educationalInsights failed");
      });
      onCancel();
    reset();
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Paper>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell style={{ width: "90%" }}>
                  YouTube Url / Video / Drive Link
                </TableCell>
                <TableCell>Delete</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {entries?.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Controller
                      name={`educationalInsights[${index}].url`}
                      control={control}
                      defaultValue={entry.url}                                          
                      rules={{
                        required: 'URL/Link is required',
                        pattern: {
                          value: /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)\S*$/,
                          message: 'Please enter a valid YouTube URL',
                        },
                      }}
                      render={({ field,fieldState }) => (
                        <TextField
                          {...field}
                          label="YouTube Url / Video / Drive Link"
                          variant="outlined"
                          fullWidth
                          error={Boolean(fieldState?.error?.message)} 
                          helperText={fieldState?.error?.message || ' '}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => removeEntry(index)}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}

              <Grid style={{ display: "flex", justifyContent: "flex-start" }}>
                <Button
                  onClick={addEntry}
                  color="primary"
                  variant="contained"
                  sx={{
                    mb: { xs: 2, lg: 4 },
                    mt: { xs: 2, lg: 4 },
                  }}
                >
                  Add
                </Button>
              </Grid>
              <TableRow>
                <TableCell colSpan={2}>
                  <Grid
                    container
                    justifyContent="center"
                    sx={{
                      mt: { xs: 2, lg: 4 },
                      mb: { xs: 2, lg: 4 },
                    }}
                  >
                    <Button
                      size="medium"
                      sx={{ mr: 3 }}
                      variant="outlined"
                      color="primary"
                      onClick={() => onCancel()}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      color="primary"
                      variant="contained"
                      disabled={entries == null || entries == []}
                    >
                      Submit
                    </Button>
                  </Grid>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Paper>
      </form>
    </>
  );
};

export default EducationalInsightsForm;
