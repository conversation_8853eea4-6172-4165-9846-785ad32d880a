import {
    <PERSON>,
    Card,
    CardContent,
    CircularProgress,
    DialogContentText,
    Divider,
    Menu,
    MenuItem,
    Tooltip
  } from "@mui/material";
  import Button from "@mui/material/Button";
  import Dialog from "@mui/material/Dialog";
  import DialogActions from "@mui/material/DialogActions";
  import DialogContent from "@mui/material/DialogContent";
  import DialogTitle from "@mui/material/DialogTitle";
  import Grid from "@mui/material/Grid";
  import IconButton from "@mui/material/IconButton";
  import Typography from "@mui/material/Typography";
  import { DataGrid } from "@mui/x-data-grid";
  import axios from "axios";
  import { useContext, useEffect, useState, useRef } from "react";
  import { get, useForm } from "react-hook-form";
  import Icon from "src/@core/components/icon";
  import CustomAvatar from "src/@core/components/mui/avatar";
  import authConfig from "src/configs/auth";
  import { AuthContext } from "src/context/AuthContext";
  import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
  import { useAuth } from "src/hooks/useAuth";
  import CustomChip from "src/@core/components/mui/chip";
  import { useRBAC } from "src/pages/permission/RBACContext";
  import { useRouter } from "next/router";
  import RequisitionDetails from "./RequistionDetails";
  import { format } from "date-fns";
  import EditRequisition from "./EditRequisition";
  import ViewRequisition from "./ViewRequisition";
  import DeleteDialog from "./DeleteDialog";
  import ActivateDialog from "./ActivateDialog";
  
  const userStatusObj = {
    true: 'Active',
    false: 'InActive'
  }
  
  const ServiceRequisitions = ({ role }) => {
  
    const router = useRouter();
    const { can } = useRBAC();
  
    const [requisitionsList, setRequisitionsList] = useState([]);
    
    const [openDialogContent, setOpenDialogContent] = useState(false);
  
    const [openDialog, setOpenDialog] = useState(false);
    const auth = useAuth();
  
    const { user,listValues, requisitionData, requisitionDataDetails, setRequisitionData } = useContext(AuthContext)
  
    const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);
  
  
    const [keyword, setKeyword] = useState("");
    const [searchKeyword, setSearchKeyword] = useState("");
    const [resendOtpDisabled, setResendOtpDisabled] = useState(false);
  
    const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
    
    const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
    const [page, setPage] = useState(1);
    const [currentRow, setCurrentRow] = useState();
    const [rowCount, setRowCount] = useState(0);
    const [dialogMessage, setDialogMessage] = useState("");
    const [employeesData, setEmployeesData] = useState([]);
  
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openActivateDialog, setOpenActivateDialog] = useState(false);
  
    const handleClose = () => setOpenDialogContent(false);
  
  
    const {
      register,
      handleSubmit,
      setError,
      clearErrors,
      control,
      reset,
      formState: { errors },
    } = useForm();
  
  
    const [menu, setMenu] = useState(null);
  
        const handleCloseMenuItems = () => {
          setMenu(null);
        };
  
    const handleSuccess = () => {
      const message = `
      <div> 
        <h3> Requisition Created Successfully.</h3>
      </div>
    `;
      setDialogMessage(message);
      setOpenDialogContent(true);
    };
  
    const handleFailure = () => {
      const message = `
      <div> 
        <h3> Failed to Add Requisition Data. Please try again later.</h3>
      </div>
    `;
      setDialogMessage(message);
      setOpenDialogContent(true);
    };
  
    const handleButtonClick = () => {
      setOpenDialogContent(false);
    };
  
    const handleOpenDialog = () => {
      setOpenDialog(true);
    };
  
  
    const handleCloseDialog = () => {
      reset();
      setOpenDialog(false);
    };
  
    useEffect(() => {
      // Fetch all employees
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setEmployeesData(res?.data?.data);
        })
        .catch((err) => console.log("Employees error", err));
    }, []);
  
    const fetchRequisitions = async (currentPage, currentPageSize, searchKeyword) => {
      let url;
      if(role){
        url = getUrl(authConfig.requisitionsByUserId + "/" + user.id)
      }
      else{
        url = getUrl(authConfig.requisitionGetAll);
      }
      
  
      const headers = getAuthorizationHeaders();
  
      const data = {
        page: currentPage,
        pageSize: currentPageSize,
        searchKeyword: searchKeyword,
      };
  
      try {
        const response = await axios({
          method: "post",
          url: url,
          headers: headers,
          data: data,
        });
  
        if (response.data) {
          setRequisitionsList(response.data?.requisitionsResponse || []);
          setRowCount(response.data?.rowCount || 0);
        } else {
          console.error("Unexpected API response format:", response);
        }
      } catch (error) {
        console.error("Error fetching users:", error);
      }
    };
  
    const closeViewProfileDialog = () => {
      setViewProfileDialogOpen(false);
      fetchRequisitions(page, pageSize);
    };
    const handleCloseDeleteDialog = () => {
      setOpenDeleteDialog(false);
      fetchRequisitions(page, pageSize);
    };
  
    const handleCloseActivateDialog = () => {
      setOpenActivateDialog(false);
      fetchRequisitions(page, pageSize);
    };
  
    useEffect(() => {
      fetchRequisitions(page, pageSize, searchKeyword);
    }, [page, pageSize, searchKeyword]);
  
   
  
    const handlePageChange = (direction) => {
      if (direction === page) {
        setPage(page + 1);
      } else {
        setPage(page - 1);
      }
    };
  
    const handlePageSizeChange = (params) => {
      if (params) {
        setPageSize(params);
        setPage(1);
      }
    };
  
    const mapIsActiveToLabel = (isActive) => {
      return userStatusObj[isActive] || 'Unknown';
    }
  
   
  
    const columns = [
      {
        field: "systemCode",
        headerName: "Requisition Number",
        flex: 3,
        minWidth: 140
      },
      !role
      ? {
        field: "name",
        headerName: "Society Name",
        flex: 2.2,
        minWidth: 185,
        valueGetter: (params) => params?.row?.usersMetaData?.name,
      }
      : null,
      {
        field: "createdOn",
        minWidth: 80,
        headerName: "Request Date",
        flex: 2,
        renderCell: ({ row }) => {
          return format(new Date(row.createdOn), 'dd-MM-yyyy');
        }
      },
      !role
      ?{
        field:"assignedTo",
        minWidth:180,
        headerName:"Assigned To",
        flex: 2.4,
        renderCell: (params) => {
          const assignedTo = employeesData?.find(
            (item) => item?.id === params?.row?.assignedTo
          );
          return <span>{assignedTo ? assignedTo?.name : ""}</span>;
        },
      }
      : null,
      {
        field: "priority",
        headerName: "Priority",
        flex: 0.13,
        minWidth: 100,
        renderCell: (params) => {
          const priority = listValues?.find(
            (item) => item?.id === params?.row?.requisitionData?.priority
          );
          return <span>{priority ? priority?.name : " "}</span>;
        },
      },
      !role
      ? {
        field: "status",
        headerName: "Status",
        flex: 0.13,
        minWidth: 100,
        renderCell: (params) => {
          const status = listValues?.find(
            (item) => item?.id === params?.row?.status
          );
          return <span>{status ? status?.name : "Unknown"}</span>;
        },
      }
      : null,
      {
        field: "isActive",
        headerName: "Is Active",
        flex: 0.13,
        minWidth: 100,
        renderCell: ({ row }) => {
          return (
            <CustomChip
              rounded={true}
              skin='light'
              size='small'
              label={mapIsActiveToLabel(row.isActive)}
              color={row.isActive === true ? "success" : "error"}
              sx={{ textTransform: 'capitalize' }}
            />
          )
        }
      },
      {
        flex: 0.05,
        field: "actions",
        headerName: "Actions",
        sortable: false,
        minWidth: 95,
        disableClickEventBubbling: true,
        renderCell: (params) => {
          const handleClickMenu = (event) => {
            event.stopPropagation();
            setMenu(event.currentTarget);
            const row = params.row;
            setCurrentRow(row);
            setRequisitionData({
              ...requisitionData,
              id: row.id,
            });
          };
  
          const onClickViewProfile = () => {
            setViewProfileDialogOpen(true);
            handleCloseMenuItems();
          };
          const onClickDeleteProfile = () => {
            setOpenDeleteDialog(true);
            handleCloseMenuItems();
          };
  
          const onClickActivateProfile = () => {
            setOpenActivateDialog(true);
            handleCloseMenuItems();
          };
  
          return (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Tooltip title="Actions">
                <CustomAvatar
                  skin="light"
                  variant="rounded"
                  sx={{
                    mr: { xs: 2, lg: 4 },
                    width: 34,
                    height: 34,
                    cursor: "pointer",
                  }}
                  onClick={handleClickMenu}
                >
                  <Icon icon="bi:three-dots-vertical" />
                </CustomAvatar>
              </Tooltip>
              <Menu
                id="actions-menu"
                anchorEl={menu}
                keepMounted
                open={Boolean(menu)}
                onClose={handleCloseMenuItems}
              >
                <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
                {currentRow?.isActive ? (
                  <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
                ) : (
                  <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
                )}
              </Menu>
            </div>
          );
        },
      },
    ].filter(Boolean); 
    return (
      <>
          <Grid>
            <Card>
              <Box
                sx={{
                  py: 3,
                  px: 6,
                  rowGap: 2,
                  columnGap: 4,
                  display: "flex",
                  flexWrap: "wrap",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                    <Typography variant="h6" fontWeight={"600"}>Requisitions List</Typography>
                  </Grid>
  
                  <Grid item xs={12} sm={8}>
                    <Grid
                      container
                      spacing={2}
                      alignItems="center"
                      justifyContent="flex-end"
                    >
                     
                        <Grid item xs={12} sm="auto">
                          <Button variant="contained" onClick={handleOpenDialog}>
                            Add Requisition
                          </Button>
                        </Grid>
                     
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
  
              <Divider />
  
  
          {role ? (
            <RequisitionDetails
            open={openDialog}
            role={role}
            onClose={handleCloseDialog}
            fetchRequisitions={fetchRequisitions}
         />
          ):(
            <RequisitionDetails
            open={openDialog}
            onClose={handleCloseDialog}
            fetchRequisitions={fetchRequisitions}
         />
          )}
          
              
              
              <Divider />
  
              {role ? (
            <ViewRequisition
            role={role}
            data={requisitionDataDetails}
            open={viewProfileDialogOpen}
            onClose={closeViewProfileDialog}
            fetchRequisitions={fetchRequisitions}
            employeesData={employeesData}
          />
          ):(
            <ViewRequisition
                    data={requisitionDataDetails}
                    open={viewProfileDialogOpen}
                    onClose={closeViewProfileDialog}
                    fetchRequisitions={fetchRequisitions}
                    employeesData={employeesData}
                  />
          )}
              
              
              <CardContent>
              <div style={{ height: 380, width: "100%" }}>                
              <DataGrid
                    rows={requisitionsList}
                    columns={columns}
                    checkboxSelection
                    pagination
                    pageSize={pageSize}
                    page={page - 1}
                    rowsPerPageOptions={rowsPerPageOptions}
                    rowCount={rowCount}
                    paginationMode="server"
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    rowHeight={38}
                    headerHeight={38} 
                  />
                </div>
              </CardContent>
              <Divider />
  
             
            </Card>
          </Grid>
          <DeleteDialog
                open={openDeleteDialog}
                onClose={handleCloseDeleteDialog}
                data={currentRow}
              />
               <ActivateDialog
                open={openActivateDialog}
                onClose={handleCloseActivateDialog}
                data={currentRow}
              />
        <Dialog
          open={openDialogContent}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    );
  };
  
  export default ServiceRequisitions;
  