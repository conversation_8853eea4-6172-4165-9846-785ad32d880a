import { useState, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Styled Component
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";

import { styled, useTheme } from "@mui/material/styles";

import ServicesCA from "./sections/ServicesCA";
import { ca } from "date-fns/locale";
import BasicProfileCA from "./sections/BasicProfileCA";
import Remarks from "src/@core/components/custom-components/Remarks";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import { useRouter } from "next/router";

import { useRBAC } from "src/pages/permission/RBACContext";

const statusColors = {
  inactive: "secondary",
};

const MUITableCell = styled(TableCell)(({ theme }) => ({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const CARegistrationForm = ({ data }) => {
  const { entityData, getEntityProfile } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");
  const router = useRouter();
  const { can } = useRBAC();

  const [expanded, setExpanded] = useState(true);

  
  useEffect(() => {
    getEntityProfile();
    console.log("use effect -CharteredAccountant")
  },[])

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "CHARTERED_ACCOUNTANT") {
      router.push("/401");
    }
  }, []);

  if (entityCategory === "CHARTERED_ACCOUNTANT") {
    return (
      <div>
        <>
          <style>
            {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
      `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={2} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Chartered Accountant Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <BasicProfileCA
                  data={entityData}
                  expanded={expanded}
                ></BasicProfileCA>
              </Grid>

              <Grid item xs={12}>
                <ServicesCA data={entityData} expanded={expanded}></ServicesCA>
              </Grid>
              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'ca_otherServices_READ'}
                  permission={'ca_otherServices_UPDATE'}
                ></OtherServices>              
              </Grid>
              <Grid item xs={12}>
            
                <Remarks data={entityData} expanded={expanded} readPermission={'ca_remarks_READ'} permission={'ca_remarks_UPDATE'}></Remarks>
              
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default CARegistrationForm;
