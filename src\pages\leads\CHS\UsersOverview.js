import { DataGrid } from "@mui/x-data-grid";
import React, { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import * as XLSX from "xlsx";
import * as FileSaver from "file-saver";
import SearchIcon from "@mui/icons-material/Search";
import {
  Button,
  Card,
  CardContent,
  Grid,
  Menu,
  MenuItem,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  FormControlLabel,
  Checkbox,
  FormControl,
  TextField,
  InputAdornment,
  CardHeader,
} from "@mui/material";
import { useTheme, useMediaQuery } from '@mui/material';
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Controller, useForm } from "react-hook-form";

import CreateUser from "./CreateUser";
import { Box } from "@mui/system";
import ProfileView from "../../SP/basic-profile/profileView";

import { AuthContext } from "src/context/AuthContext";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import PageHeader from "src/@core/components/page-header";
import CloseExpandIcons from "../../all-profiles-old/CloseExpandIcons";
import MemberShipView from "../../SP/microsite/sections/MemberShipView";
import AwardView from "../../SP/microsite/sections/AwardView";
import TestimonialView from "../../SP/microsite/sections/TestimonialsView";
import EducationalInsightsView from "../../SP/microsite/sections/EducationalInsightsView";
import FieldsView from "../../SP/microsite/sections/FieldsView";
import AreaOfExperties from "../../SP/microsite/sections/AreaOfExpertiseView";
import ServiceTabs from "../../SP/microsite/sections/ServiceTabs";

import LandDetails from "../../CHS/profile/sections/LandDetails";
import FsiDetails from "../../CHS/profile/sections/FsiDetails";
import Requirements from "../../CHS/profile/sections/Requirements";
import ContactsReferences from "../../CHS/profile/sections/ContactsReferences";
import OtherDetails from "../../CHS/profile/sections/OtherDetails";
import SocietyDetails from "../../CHS/profile/sections/SocietyDetails";
import Index from "../../conversations";
import StatisticsParent from "../../statistics";
import AssignmentAndStatus from "../../CHS/profile/sections/AssignmentAndStatus";
import ParentComponent from "src/pages/SP/service-profile";

const ExcelDownMenu = ({ children }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleExcelClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Tooltip title="Export to Excel">
        <IconButton onClick={handleExcelClick} size="medium">
          <Icon icon="vscode-icons:file-type-excel" fontSize="2.2rem" />
        </IconButton>
      </Tooltip>

      <Menu
        keepMounted
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            marginTop: "4px",
          },
        }}
      >
        {children}
      </Menu>
    </>
  );
};

const UsersOverView = () => {
  const { can, rbacRoles } = useRBAC();
  const {
    micrositeBasicData,
    setMicrositeBasicData,
    micrositeGetEndpoint,
    getBasicProfileData,
    basicProfileGetData,
    fetchUserProjects,
    projectsData,
    getUserBasicProfileData,
    userData,
    entityData,
    getSocietyProfile,
    setBasicProfileAllProfiles,
    user,
    fetchOne,
  } = useContext(AuthContext);
  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  // const [expanded, setExpanded] = useState(true);

  // const handleToggle = (value) => {
  //   setExpanded(value);
  // };
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationDialogMessage, setConfirmationDialogMessage] = useState("");
  const [isActivating, setIsActivating] = useState(false);

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleOpenConfirmationDialog = (message, isActivating) => {
    setConfirmationDialogMessage(message);
    setIsActivating(isActivating);
    setConfirmationDialogOpen(true);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmStatusChange = async () => {
    setConfirmationDialogOpen(false);
    try {
      if (!currentRow || !currentRow.id) {
        throw new Error("Current row ID is not defined");
      }
  
      const url = isActivating
        ? getUrl(authConfig.allProfilesUpdateActiveStatus) + `/${currentRow.id}`
        : getUrl(authConfig.allProfilesUpdateInActiveStatus) + `/${currentRow.id}`;
  
      const method = isActivating ? "patch" : "delete";
      const response = await axios({
        method: method,
        url: url,
        headers: getAuthorizationHeaders(),
      });
  
      if (response.status !== 200 && response.status !== 204) {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
  
      console.log(isActivating ? "Activated status:" : "De-activated status:", response.data);
      setDialogMessage(isActivating ? "User activated successfully" : "User de-activated successfully");
  
      // Refresh data
      fetchUsers(page, pageSize, searchKeyword, searchData);
    } catch (error) {
      console.error("Error changing status", error);
      setDialogMessage(isActivating ? "Error activating status" : "Failed to Deactivate Status");
  
      if (error.response) {
        console.error("Error response data:", error.response.data);
      }
    } finally {
      setDialogOpen(true);
    }
  };
  

  // Constants
  const columns = [
    {
      field: "firstName",
      minWidth: 100,
      headerName: "Name",
      flex: 0.25
    },
    {
      field: "societyName",
      minWidth: 100,
      headerName: "Society Name",
      flex: 0.3,
	  valueGetter: (params) => params?.row?.societyData?.name,
    }, 
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.25,
      renderCell: (params) => {
        const email = params?.value;
    
        return email.length > 21 ? (
          <Tooltip title={email}>
            <span>{email}</span>
          </Tooltip>
        ) : (
          <span>{email}</span>
        );
      },
    },
    { field: "mobileNumber",minWidth: 115, headerName: "Mobile No", flex: 0.1 },          
    {
      flex: 0.15,
      minWidth: 100,
      field: "assignedTo",
      headerName: "Assigned to",
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    
    {
      flex: 0.01,
      minWidth: 85,
      field: "createdBy",
      headerName: "Created By",
      renderCell: (params) => {
        const createdBy = params?.value;
    
        return (
          <Tooltip title={createdBy}>
            <span>{createdBy}</span>
          </Tooltip>
        );
      },
    },
    //Active or In-active status
    {
      field: "status",
      minWidth: 80,
      headerName: "status",
      flex: 0.12,
      renderCell: (params) => {
        const status = params?.row.status;
        return (
          <Tooltip title={status === "ACTIVE" ? "ACTIVE" : "INACTIVE"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: status === "ACTIVE" ? 13 : 13,
                height: status === "ACTIVE" ? 13 : 13,
                m: 5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              color={status === "ACTIVE" ? "success" : "error"}
            >
              <Icon
                icon={
                  status === "ACTIVE"
                    ? "fluent-emoji-flat:green-circle"
                    : "fluent-emoji-flat:red-circle"
                }
                style={{ width: 15, height: 15 }}
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.11,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation(); 
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setBasicProfileAllProfiles(params.row.id);
        };

        const onEditClick = () => {
          openEditDialog();
          handleCloseMenu();
        };

        const onConversationsClick = () => {
          openConversationDialog();
          handleCloseMenu();
        };

        const handleActivateStatus = () => {
          handleOpenConfirmationDialog("Are you sure you want to activate this user?", true);
          handleCloseMenuItems();
        };

        const handleDeactivateStatus = () => {
          handleOpenConfirmationDialog("Are you sure you want to de-activate this user?", false);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 30,
                  height: 30,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onEditClick}>Edit</MenuItem>
              {currentRow?.userCategory != authConfig?.userCategorySociety && (
                <MenuItem onClick={onConversationsClick}>
                  {" New Conversation"}
                </MenuItem>
              )}
              {currentRow?.status === "INACTIVE" && (
                <MenuItem onClick={() => handleActivateStatus()}>Activate</MenuItem>
              )}
              {currentRow?.status === "ACTIVE" && (
                <MenuItem onClick={() => handleDeactivateStatus()}>De-activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];
  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down(395));
  const isMobile = useMediaQuery(theme.breakpoints.down(435));

  const auth = useAuth();
  const [currentRow, setCurrentRow] = useState(null);
  const [userList, setUserList] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const totalGridHeight = pageSize * 52 + 80;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [searchData, setSearchData] = useState({});
  const [rowCount, setRowCount] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expanded, setExpanded] = useState(true);

  const [employeesData, setEmployeesData] = useState(null);
  const [keyword, setKeyword] = useState("");
  const [placeholderText, setPlaceholderText] = useState("Search by Society name");
  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));


      const phrases = ["Society name","name", "email", "mobile number"];
      let phraseIndex = 0;
      const intervalId = setInterval(() => {
        phraseIndex = (phraseIndex + 1) % phrases.length;
        setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
      }, 1000); 
  
      return () => clearInterval(intervalId);


  }, []);
  



  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword, searchData);
  }, [page, pageSize, searchKeyword, searchData]);

  const openConversationDialog = () => {
    setConversationDialogOpen(true);
  };

  const closeConversationDialog = () => {
    setMenu(null);
    setConversationDialogOpen(false);
  };

  const openEditDialog = () => {
    setEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setMenu(null);
    setEditDialogOpen(false);
    setCurrentRow(null);
    setMicrositeBasicData(null);
    setActiveTab(0);
    fetchUsers();
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);
    const fetchServiceProviders = async (
      currentPage,
      currentPageSize,
      searchKeyword,
      dataSearch
    ) => {
      setLoading(true);
    }
    const url = getUrl(authConfig.getAllProfilesEndpointNewTable)+"?profileType=SOCIETY";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const [listValues, setListValues] = useState(null);
  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, []);

  const [userId, setUserId] = useState(null);

  useEffect(() => {
    if (!!currentRow && !!currentRow?.basicProfileData) {
      setUserId(currentRow?.id);
    }
  }, [currentRow]);

  useEffect(() => {
    console.log("************** UserID getting changed: userId:", userId);
  }, [userId]);

  useEffect(() => {
    if (!!currentRow && !!currentRow.userCategory) {
      if (currentRow?.userCategory == authConfig?.userCategorySociety) {
        getSocietyProfile(currentRow?.id);
      } else if (
        currentRow.userCategory == authConfig.userCategoryServiceProvider
      ) {
        getBasicProfileData(currentRow?.id);
      }
    }
  }, [currentRow]);

  useEffect(() => {
    if (activeTab === 0) {
      getBasicProfileData(userId);
    } else if (activeTab === 1) {
      console.log(
        "All Porfiles Page: Modal Popup : activeTab === 1; userId:",
        userId,
        "currentRow:",
        currentRow
      );
      micrositeGetEndpoint(userId);
      getBasicProfileData(userId);
      fetchUserProjects(userId);
    }
  }, [activeTab]);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState(columns);
  const [empanelDialog, setEmpanelDialog] = useState(false);

  const [strategicDialog, setStrategicDialog] = useState(false);

  const [strategicDialogOpen, setStrategicDialogOpen] = useState(false);

  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicrositeEmpanelled, setIsMicrositeEmpanelled] = useState(false);

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [entityType, setEntityType] = useState("");
  
  const [selectedRoleDialog, setSelectedRoleDialog] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [role, setRole] = useState(null);
  const open = Boolean(anchorEl);

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleActionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMessageClose = () => {
    setSubmitSuccess(false);
    setIsListingEmpanelled(false);
    setIsMicrositeEmpanelled(false);
    setPleaseVerifyEmailMessage(false);
  };

  const handleStrategicDialogClose = () => {
    setStrategicDialogOpen(false);
  };

 
    const handleYes = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyEmpanelled = true;
      const successIds = [];
      const failedIds = [];
      const updatePromises = selectedRows.map((row) => {
        if (!row.isListingEmpanelled || !row.isMicrositeEmpanelled) {
          allAlreadyEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: row.isListingEmpanelled
              ? true
              : isListingEmpanelled,
            isMicrositeEmpanelled: row.isMicrositeEmpanelled
              ? true
              : isMicrositeEmpanelled,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              console.error(`API call failed for userId: ${row.id}`, error);
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyEmpanelled) {
        message += `All Selected Rows are Already Empanelled!`;
      } else if (successFlag) {
        message += `Empanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Empanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchUsers(page, pageSize, searchKeyword);
    };

    const handleNo = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyUnEmpanelled = true;
      const successIds = [];
      const failedIds = [];

      const updatePromises = selectedRows.map((row) => {
        if (row.isListingEmpanelled || row.isMicrositeEmpanelled) {
          allAlreadyUnEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: isListingEmpanelled
              ? false
              : row.isListingEmpanelled,

            isMicrositeEmpanelled: isMicrositeEmpanelled
              ? false
              : row.isMicrositeEmpanelled,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              console.log(`API call successful for userId: ${row.id}`);
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              console.error(`API call failed for userId: ${row.id}`, error);
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyUnEmpanelled) {
        message += `All Selected Rows are Already UnEmpanelled!`;
      } else if (successFlag) {
        message += `Unempanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Unempanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchUsers(page, pageSize, searchKeyword);
    };

    

    const handleAssignStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (!row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: true,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
              console.error("Employee Details failed", error);
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div> <h3>Action Not Required</h3><p>Selected entities are already strategic partners.</p></div>
        `;

        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div><h3>Update Successful</h3><p>Status updated to strategic partner.</p></div>

      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>      
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchUsers(page, pageSize, searchKeyword);
      }
    };

    const handleRemoveStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: false,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
              console.error("Employee Details failed", error);
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div>
        <h3>Action Not Required</h3>
        <p>No changes were made as the selected entity or entities are not designated as strategic partners.</p>
      </div>`;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div>
        <h3>Update Successful</h3>
        <p>The strategic partner status has been successfully removed.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchUsers(page, pageSize, searchKeyword);
      }
    };

    const handleSelection = (selectionModel) => {
      // Assuming `userList` is an array of objects and each object has a unique `id` that corresponds to the `selectionModel`
      const selectedData = userList.filter((row) =>
        selectionModel.includes(row.id)
      );
      const selectedRoles = selectedData.map((data) => data.role);
      setRole(selectedRoles);
      setSelectedRows(selectedData);
    };

    const handleSociety = () => {
      if (role.includes("Society")) {
        setDialogMessage(
          "Please deselect Society Profile(s) to Empanel/UnEmpanel Service Provider(s)"
        );
        setSelectedRoleDialog(true);
      }
    };

    const handleClose = () => {
      setPleaseVerifyEmailMessage(false);
      setDisableVerifyEmailButton(false);
      setShowForm(false);
      setShowOTPOptions(false);

      reset({
        firstName: "",
        lastName: "",
        mobileNumber: "",
        email: "",
        entityType: "",
      });
      setEntityType("");
    };

    const exportToCSV = (csvData, fileName) => {
      const message = ` 
      <div>
        <h3>The data have been exported successfully</h3>
      </div>
      `;
      setDialogMessage(message);
      setSubmitSuccess(true);

      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
      const fileExtension = ".xlsx";

      const filteredData = csvData.map((row) =>
        Object.keys(row)
          .filter((key) => selectedColumns.find((col) => col.field === key))
          .reduce((obj, key) => {
            obj[key] = row[key];
            return obj;
          }, {})
      );
      const ws = XLSX.utils.json_to_sheet(filteredData);

      const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const data = new Blob([excelBuffer], { type: fileType });
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");

      const formattedDate = `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;

      const newFileName = `${fileName}_${formattedDate}${fileExtension}`;

      FileSaver.saveAs(data, newFileName);
    };
    const handleColumnSelection = (columnField) => {
      setSelectedColumns((prevState) =>
        prevState.find((col) => col.field === columnField)
          ? prevState.filter((col) => col.field !== columnField)
          : [...prevState, columns.find((col) => col.field === columnField)]
      );
    };

    const handleSocietyDialogClose = () => {
      setSelectedRoleDialog(false);
    };


    return (
      <Grid>

        <Dialog
          open={dialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
          }}
        >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
         
            <DialogActions>
              <Button
                onClick={handleCloseDialog}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
        </Box>
      </Dialog>
        
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.dark",
          }}
          >
        <Dialog
        open={confirmationDialogOpen}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
         <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
           <DialogContent>
            <DialogContentText
            id="alert-dialog-description"
            color="primary.main">
              <div dangerouslySetInnerHTML={{ __html: confirmationDialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{
          justifyContent: "center",
        }}>
            <Button onClick={handleConfirmStatusChange}  style={{ margin: "0 10px auto", width: 100 }}
             variant="contained"
            >
              Yes
            </Button>

            <Button onClick={handleCloseConfirmationDialog} 
            style={{ margin: "0 10px auto", width: 100 }}
            >
              No
            </Button>
            
          </DialogActions>
        </Box>
         
        </Dialog>
        </Box>

        
      
        <Card>
          <Box
            sx={{
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ textAlign: "flex-start", mt: { xs: 4, lg: 6 } }}
              >
                <Typography variant="h6" fontWeight={"600"}>
                  List of Societies
                </Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid container justifyContent="flex-end">
                  <Grid
                    item
                    xs={12}
                    sm="auto"
                    sx={{ paddingTop: {xs:"0px",sm:"25px"}, mr: isMobile ? '0' : "8px", textAlign: isMobile ? 'right': 'left' }}
                  >
                    <FormControl>
                      <Controller
                        name="mainSearch"
                        control={control}
                        // defaultValue={name}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            placeholder={placeholderText}
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                                fetchUsers(page, pageSize, searchKeyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                height: "40px",
                              },
                              "& .MuiInputBase-input::placeholder": {
                                fontSize: "0.9rem", // Adjust the size as needed
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                      fetchUsers(page, pageSize, searchKeyword);
                                    }}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid>
                    <Grid sx={{ mr: "8px", paddingTop: isMobile ? '3px' : '15px' }}>
                      <ExcelDownMenu>
                        {columns?.map((column) => (
                          <MenuItem key={column.field}>
                            {column.headerName === "Edit" ? null : (
                              <label>
                                <input
                                  type="checkbox"
                                  checked={
                                    !!selectedColumns.find(
                                      (col) => col.field === column.field
                                    )
                                  }
                                  onChange={() =>
                                    handleColumnSelection(column.field)
                                  }
                                />
                                {column.headerName}
                              </label>
                            )}
                          </MenuItem>
                        ))}
                        <Box sx={{ textAlign: "center", margin: 2 }}>
                          <Button
                            size="medium"
                            type="button"
                            variant="contained"
                            onClick={() => exportToCSV(userList, "users_data")}
                            disabled={selectedColumns.length === 0}
                          >
                            Download
                          </Button>
                        </Box>
                      </ExcelDownMenu>
                    </Grid>
                  </Grid>               
                  <Grid item>
                    <Button
                      aria-controls="simple-menu"
                      aria-haspopup="true"
                      onClick={() => handleOptionSelect("SOCIETY")}
                      variant="contained"
                      sx={{ mr: 0, mt: isMobile ? '0.8rem' : '1.5rem', ml: { sx: 0,} }}
                    >
                      Add New Society
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>

          <CardContent>
          <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={userList || []}
                columns={columns}
                getRowId={(row) => row.id}               
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38}
                components={{
                  NoRowsOverlay: () => (
                    <Typography variant="body1" align="center" sx={{ marginTop: '40px' }}>
                      {userList.length === 0 && searchKeyword ? "No Matches Found" : "No Rows"}
                    </Typography>
                  ),
                }}
              />
            </div>
          </CardContent>
        </Card>
      
        <CreateUser
          selectedOption={selectedOption}
          openDialog={openDialog}
          handleDialogClose={handleDialogClose}
          fetchUsers={fetchUsers}
          reset={reset}
        />

        <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "row", // Change to row for horizontal alignment
              alignItems: "center",
              justifyContent: "space-between", // Changed to evenly distribute space
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <div style={{ flex: 1, textAlign: "center" }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                sx={{ fontSize: "18px" }}
              >
                Conversation Details
              </Typography>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <CloseExpandIcons
                expanded={expanded}
                onToggle={handleToggle}
                sx={{ mt: 4 }}
              />
              <IconButton
                size="small"
                onClick={closeConversationDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mt: 5,
                  mb: 3,
                  mr: 2,
                  color: "common.white",
                  backgroundColor: "action.selected",
                  "&:hover": {
                    backgroundColor: 
                    '#66BB6A',
                     transition: 'background 0.5s ease, transform 0.5s ease',                       
                    },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </div>
          </DialogTitle>

          <DialogContent>
            <Index
              currentRow={currentRow}
              setCurrentRow={setCurrentRow}
              expanded={expanded}
              closeConversationDialog={close}
              employeeData={employeesData}
            />
          </DialogContent>

          <DialogActions sx={{ justifyContent: "center" }}>
            <Grid item xs={12} sx={{ mt: 2 }}>
              {/* <Button
                size="medium"
                sx={{ mr: 3 }}
                onClick={() => handleClose()}
                variant="outlined"
                color="primary"
              >
                Close
              </Button> */}
            </Grid>
          </DialogActions>
        </Dialog>

        <Dialog fullScreen open={isEditDialogOpen} onClose={closeEditDialog}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <Typography sx={{ fontWeight: "bold", mb: 4 }}>
              {currentRow?.userCategory}
            </Typography>
            {currentRow?.userCategory !== authConfig?.userCategorySociety && (
              <>
                <Tabs value={activeTab} onChange={handleTabChange}>
                  <Tab label="Basic Profile" />
                  <Tab label="For Microsite" />
                  <Tab label="Service Profile" />
                </Tabs>
              </>
            )}

            <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
              <IconButton
                size="small"
                onClick={closeEditDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color:"common.white", 
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: 
                    '#66BB6A',
                     transition: 'background 0.5s ease, transform 0.5s ease',                       
                    },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent>
            {currentRow?.userCategory === authConfig?.userCategorySociety ? (
              <Box>
                <Grid container spacing={2} className="match-height">
                  <Grid item xs={12}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Grid item xs={12} sx={{ position: "relative" }}>
                        <PageHeader
                          title={
                            <Typography variant="h5">
                              Society Profile
                            </Typography>
                          }
                          subtitle={<Typography variant="body2"></Typography>}
                        />
                        <Box sx={{ position: "absolute", top: 1, right: 0 }}>
                          <CloseExpandIcons
                            expanded={expanded}
                            onToggle={handleToggle}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <SocietyDetails
                      data={entityData}
                      userData={currentRow}
                      expanded={expanded}
                      employeesData={employeesData}
                    ></SocietyDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <LandDetails
                      data={entityData}
                      expanded={expanded}
                    ></LandDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <FsiDetails
                      data={entityData}
                      expanded={expanded}
                    ></FsiDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <Requirements
                      data={entityData}
                      expanded={expanded}
                    ></Requirements>
                  </Grid>
                  <Grid item xs={12}>
                    <ContactsReferences
                      data={entityData}
                      expanded={expanded}
                    ></ContactsReferences>
                  </Grid>
                  <Grid item xs={12}>
                    <OtherDetails
                      data={entityData}
                      expanded={expanded}
                    ></OtherDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <AssignmentAndStatus
                      data={entityData}
                      expanded={expanded}
                      employeesData={employeesData}
                    />
                  </Grid>
                </Grid>
              </Box>
            ) : (
              <>
                <Box>
                  {activeTab === 0 && (
                    <Grid>
                      <ProfileView
                        data={basicProfileGetData}
                        userData={currentRow}
                        expanded={expanded}
                        employeesData={employeesData}
                      />
                    </Grid>
                  )}
                  {activeTab === 1 && (
                    <DatePickerWrapper>
                      <Grid container spacing={6} className="match-height">
                        <Grid item xs={12}>
                          <Grid
                            container
                            spacing={2}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <Grid item xs={8}>
                              <PageHeader
                                subtitle={
                                  <Typography variant="body2"></Typography>
                                }
                              />
                            </Grid>
                            <Grid
                              item
                              xs={0.5}
                              sx={{
                                width: "auto",
                                textAlign: "end",
                                justifyItems: "end",
                              }}
                            >
                              <CloseExpandIcons
                                expanded={expanded}
                                onToggle={handleToggle}
                              />
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item xs={12}>
                          <FieldsView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></FieldsView>
                        </Grid>
                        <Grid item xs={12}>
                          <AreaOfExperties
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AreaOfExperties>
                        </Grid>
                        <Grid item xs={12}>
                          <AwardView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AwardView>
                        </Grid>
                        <Grid item xs={12}>
                          <MemberShipView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TestimonialView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></TestimonialView>
                        </Grid>

                        <Grid item xs={12}>
                          <EducationalInsightsView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></EducationalInsightsView>
                        </Grid>
                        <Grid item xs={12}>
                          <ServiceTabs
                            tabContents={serviceNames}
                            data={projectsData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <StatisticsParent
                            userDataAllProfile={currentRow}
                            expanded={expanded}
                          />
                        </Grid>
                      </Grid>
                    </DatePickerWrapper>
                  )}
                  {activeTab === 2 && (
                    <Grid>
                      <ParentComponent userDataAllProfile={currentRow} />
                    </Grid>
                  )}
                </Box>
              </>
            )}
          </DialogContent>
          <DialogActions>
            {/* <Button onClick={closeEditDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={closeEditDialog} color="primary">
            Save
          </Button> */}
          </DialogActions>
        </Dialog>

        <Dialog
          open={submitSuccess || pleaseVerifyEmailMessage}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
          onClose={(event, reason) => {
            if (reason == "backdropClick") {
              handleMessageClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
                <Button
                  variant="contained"
                  onClick={handleMessageClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Okay
                </Button>
            </DialogActions>
          </Box>
        </Dialog>

        
        <Dialog
          open={selectedRoleDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleSocietyDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={handleSocietyDialogClose}
                sx={{
                  margin: "10px",
                  backgroundColor: "primary.main",
                  "&:disabled": {
                    backgroundColor: "white",
                    color: "grey",
                  },
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Grid>
    );
};

export default UsersOverView;
