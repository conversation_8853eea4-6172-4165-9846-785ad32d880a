// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  TextField,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";
import { setRequestMeta } from "next/dist/server/request-meta";

const ArchitecturalDesignEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [residential, setResidential] = useState(formData?.residential);
  const [commercial, setCommercial] = useState(formData?.commercial);
  const [retail, setRetail] = useState(formData?.retail);
  const [mix, setMix] = useState(formData?.mix);

  const [industriNova, setIndustriNova] = useState(formData?.industriNova);

  const [allArchitecturalDesign, setAllArchitecturalDesign] = useState(
    formData?.allArchitecturalDesign
  );
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(true);

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    const isAnyCheckboxChanged =
      residential !== formData?.residential ||
      commercial !== formData?.commercial ||
      retail !== formData?.retail ||
      mix !== formData?.mix ||
      industriNova !== formData?.industriNova;
    setIsSaveButtonDisabled(!isAnyCheckboxChanged);
  }, [residential, commercial, retail, mix, industriNova, formData]);

  useEffect(() => {
    if (residential && commercial && retail && mix && industriNova) {
      setValue("allArchitecturalDesign", true);
      setAllArchitecturalDesign(true);
    } else {
      setValue("allArchitecturalDesign", false);
      setAllArchitecturalDesign(false);
    }
  }, [residential, commercial, retail, mix, industriNova]);

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }

    console.log("trim", trimmedData);

    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" ArchitecturalDesign Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 1.5 }}>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="allArchitecturalDesign"
                control={control}
                defaultValue={
                  formData?.allArchitecturalDesign
                    ? allArchitecturalDesign
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("allArchitecturalDesign", true);
                            setAllArchitecturalDesign(true);

                            setValue("residential", true);
                            setResidential(true);
                            setValue("commercial", true);
                            setCommercial(true);
                            setValue("retail", true);
                            setRetail(true);
                            setValue("mix", true);
                            setMix(true);
                            setValue("industriNova", true);
                            setIndustriNova(true);
                          } else {
                            setValue("allArchitecturalDesign", false);
                            setAllArchitecturalDesign(false);

                            setValue("residential", false);
                            setResidential(false);
                            setValue("commercial", false);
                            setCommercial(false);
                            setValue("retail", false);
                            setRetail(false);
                            setValue("mix", false);
                            setMix(false);
                            setValue("industriNova", false);
                            setIndustriNova(false);
                          }
                        }}
                      />
                    }
                    label={<span>ALL</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="residential"
                control={control}
                defaultValue={formData?.residential ? residential : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("residential", true);
                            setResidential(true);
                          } else {
                            setValue("residential", false);
                            setResidential(false);
                          }
                        }}
                      />
                    }
                    label={<span>Residential</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="commercial"
                control={control}
                defaultValue={formData?.commercial ? commercial : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("commercial", true);
                            setCommercial(true);
                          } else {
                            setValue("commercial", false);
                            setCommercial(false);
                          }
                        }}
                      />
                    }
                    label={<span>Commercial</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="retail"
                control={control}
                defaultValue={formData?.retail ? retail : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("retail", true);
                            setRetail(true);
                          } else {
                            setValue("retail", false);
                            setRetail(false);
                          }
                        }}
                      />
                    }
                    label={<span>Retail</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="mix"
                control={control}
                defaultValue={formData?.mix ? mix : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("mix", true);
                            setMix(true);
                          } else {
                            setValue("mix", false);
                            setMix(false);
                          }
                        }}
                      />
                    }
                    label={<span>Mix</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="industriNova"
                control={control}
                defaultValue={formData?.industriNova ? industriNova : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("industriNova", true);
                            setIndustriNova(true);
                          } else {
                            setValue("industriNova", false);
                            setIndustriNova(false);
                          }
                        }}
                      />
                    }
                    label={<span>Industri Nova</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSaveButtonDisabled}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ArchitecturalDesignEdit;
