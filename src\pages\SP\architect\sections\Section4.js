// ** React Imports

// ** MUI Imports

import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";

import FormControl from "@mui/material/FormControl";
import { useAuth } from "src/hooks/useAuth";
import { Box } from "@mui/system";



// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

// ** Icon Imports
import ArchitectValidationSection1, { yup } from "./ArchitectValidationSection1";

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const schema = yup.object().shape({
  email: yup.string().email().required(),
  lastName: yup
    .string()
    .min(3, (obj) => showErrors("lastName", obj.value.length, obj.min))
    .required(),
  password: yup
    .string()
    .min(8, (obj) => showErrors("password", obj.value.length, obj.min))
    .required(),
  firstName: yup
    .string()
    .min(3, (obj) => showErrors("firstName", obj.value.length, obj.min))
    .required(),
});

const PastProjects = ({ onCancel, formData }) => {
  const auth = useAuth();
  const fields = ["noOfProjectsCompleted","constructedAreaOfCompletedProjects","noOfOngoingProjects","constructedAreaOfOngoingProjects","landmarkProjectsNames"];

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(ArchitectValidationSection1(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    console.log("submitted data",data)
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
  
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="noOfProjectsCompleted"
                control={control}
                defaultValue={formData?.noOfProjectsCompleted}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    label="No. of projects"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.noOfProjectsCompleted)}
                    placeholder="Enter no. of projects "
                    helperText={errors.noOfProjectsCompleted?.message}
                    aria-describedby="validation-noOfProjectsCompleted"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="constructedAreaOfCompletedProjects"
                control={control}
                defaultValue={formData?.constructedAreaOfCompletedProjects}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Constructed area of completed projects"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Name of Constructed area of completed projects in sqft"
                    error={Boolean(errors.constructedAreaOfCompletedProjects)}
                    helperText={errors.constructedAreaOfCompletedProjects?.message}
                    aria-describedby="validation-schema-constructed-area-of-completed-projects"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="noOfOngoingProjects"
                control={control}
                defaultValue={formData?.noOfOngoingProjects}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    label="No. of ongoing projects"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.noOfOngoingProjects)}
                    placeholder="Enter no. of ongoing projects"
                    helperText={errors.noOfOngoingProjects?.message}
                    aria-describedby="validation-noOfOngoingProjects"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="constructedAreaOfOngoingProjects"
                control={control}
                defaultValue={formData?.constructedAreaOfOngoingProjects}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Constructed area of ongoing projects"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Name of Constructed area of ongoing projects in sqft"
                    error={Boolean(errors.constructedAreaOfOngoingProjects)}
                    helperText={errors.constructedAreaOfOngoingProjects?.message}
                    aria-describedby="validation-schema-constructed-area-of-ongoing-projects"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} >
            <FormControl fullWidth>
              <Controller
                name="landmarkProjectsNames"
                control={control}
                defaultValue={formData?.landmarkProjectsNames}
                render={({ field }) => (
                  <TextField
                  rows={4}
                  multiline
                    {...field}
                    label="Landmark projects names"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Name of Landmark projects names"
                    error={Boolean(errors.landmarkProjectsNames)}
                    helperText={errors.landmarkProjectsNames?.message}
                    aria-describedby="validation-schema-landmark-projects-names"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="submit"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default PastProjects;
