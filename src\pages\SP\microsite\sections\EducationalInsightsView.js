import React, { useState } from "react";
import Typography from "@mui/material/Typography";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useTheme } from "@emotion/react";
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
import EducationalInsightsForm from "./EducationalInsightsEdit";

const EducationalInsightsView = ({ data, expanded, userData }) => {
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  const hasEducationInsights =
    data && data.educationalInsightsList && data.educationalInsightsList.length > 0;

  return (
    <>
      {(userData && userData.id !== undefined) && (
        <AccordionBasic
          id={"panel-header-2"}
          ariaControls={"panel-content-2"}
          heading={"Educational Insights"}
          body={
            <>
              {state3 === "view" ? (
                <>
                  {hasEducationInsights ? (
                    <TableContainer
                      sx={{ padding: "4px 6px" }}
                      className="tableBody"
                      onClick={viewClick3}
                    >
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>YouTube Url</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          {data.educationalInsightsList.map(
                            (educationalInsight, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                  <Typography
                                    className="data-field"
                                    marginLeft={"15px"}
                                  >
                                  {educationalInsight.url}
                                </Typography>
                              </TableCell>
                            </TableRow>
                            )
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography
                      variant="body1"
                      sx={{ textAlign: "left", mt: 3, cursor: "pointer" }}
                      onClick={viewClick3}
                    >
                      Click here to add Educational Insights
                    </Typography>
                  )}
                </>
              ) : (
                <EducationalInsightsForm
                  userData={userData}
                  data={data}
                  onCancel={editClick3}
                />
              )}
            </>
          }
          expanded={expanded}
        />
      )}
    </>
  );
};

export default EducationalInsightsView;
