import React, { useEffect, useState } from "react";

import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useContext } from "react";
import DocumentUploadDialog from "src/@core/components/custom-components/DocumentDialogUpload";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";

const ArchitectAwardsEdit = (props) => {
  const {
    getAllDocuments,
    uploadDocuments,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    shortFormData,
    patchArchitectAdditionalData,
  } = useContext(AuthContext);
  const [documents, setDocuments] = useState([]);
  const [dailogSuccess, setDialogSuccess] = useState(false);
  const [selectedAward, setSelectedAward] = useState(null);
  const [fieldChanged, setFieldChanged] = useState(false);

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        fetchAllDocuments();
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };

    fetchDocuments();
  }, [allSubCategories, allCategories]);

  useEffect(() => {}, [documents]);

  const { onCancel, data } = props;

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    unregister,
    getValues,
    formState: { errors },
  } = useForm();

  const [entries, setEntries] = useState([]);

  useEffect(() => {
    console.log("Data Set first", data);
    if (data && data.awardList) {
      console.log("Data Set Inside");
      console.log("Data:", data);

      setEntries(data.awardList);
    }
    console.log("Data Set Exit");
  }, [data]);

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`awards[${index}].name`, entry.name);

      setValue(`awards[${index}].description`, entry.description);
    });
  }, [entries, setValue]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.awards || [];

    setEntries([...currentEntries, { name: "", description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);

    unregister(`awards[${index}].name`);

    unregister(`awards[${index}].description`);

    reset({
      ...getValues(),
      awards: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    const awardsData =
      data?.awards?.length > 0 ? data.awards : [{ name: "", description: "" }];

    const response = await patchArchitectAdditionalData(
      { awardList: data.awards },
      () => {
        console.log("Success Awards.");
      },
      () => {
        console.error("serviceDetails failed");
      }
    );

    onCancel();

    reset();
  };

  // async function patchAward(data) {
  //   try {
  //     const response = await axios({
  //       method: "patch",
  //       url: getUrl(
  //         authConfig.entityPatch + "/" + entityId + "/architectData/"
  //       ),
  //       headers: getAuthorizationHeaders(),
  //       data: { awardList: data },
  //     });

  //     const awardResponse = response.data?.architectRegisterDTO;
  //     setEntityData(awardResponse);
  //     console.log("RESPONSE", awardResponse);
  //   } catch (error) {
  //     console.error("Error:", error);
  //   }
  //   onCancel();
  // }

  const [modalPopup, setModalPopup] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);

  // Awards
  const memberId =
    user.entityCategory === "SUPER_ADMIN" ? shortFormData.id : null;

  const documentDetails = {
    userId: memberId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("awards"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const handleSave = async () => {
    const formData = new FormData();

    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });

    formData.append("documentDetails", JSON.stringify(documentDetails));

    console.log("selectedFiles", selectedFiles);
    console.log("formData", formData);

    // API call
    await uploadDocuments(
      formData,
      () => {
        console.log("Success");
        setModalPopup(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },
      () => {
        console.log("Failure");
      }
    );
    fetchAllDocuments();
  };
  const handleClose = () => {
    setDialogSuccess(false);
  };

  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);

  const [awardToDelete, setAwardToDelete] = useState(false);

  async function handleDelete() {
    await documentDelete(awardToDelete);
    setAwardToDelete(null);
    setConfirmDeleteDialogOpen(false);
    fetchAllDocuments();
  }

  const documentJson = {
    userId: memberId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("awards"),
  };

  const fetchAllDocuments = async () => {
    if (allSubCategories.length > 0 && allCategories.length > 0) {
      const allDocs = await getAllDocuments(documentJson);

      setDocuments(allDocs);
    }
  };

  const handleViewIconClick = (award) => {
    setSelectedAward(award);
  };

  const handleDialogClose = () => {
    setSelectedAward(null);
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Paper>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Delete</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {entries?.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Controller
                      name={`awards[${index}].name`}
                      control={control}
                      defaultValue={entry.name}
                      rules={{ required: "This field is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Name"
                          variant="outlined"
                          fullWidth
                          error={Boolean(errors?.awards?.[index]?.name)}
                          helperText={
                            errors?.awards?.[index]?.name?.message || ""
                          }
                          onChange={(e) => {
                            field.onChange(e);
                            setFieldChanged(true);
                          }}
                        />
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <Controller
                      name={`awards[${index}].description`}
                      control={control}
                      defaultValue={entry.description}
                      rules={{ required: "Description is required" }}
                      render={({ field }) => (
                        <>
                          <TextField
                            {...field}
                            label="Description"
                            variant="outlined"
                            fullWidth
                            error={Boolean(
                              errors?.awards?.[index]?.description
                            )}
                            helperText={
                              errors?.awards?.[index]?.description?.message ||
                              ""
                            }
                            onChange={(e) => {
                              field.onChange(e);
                              setFieldChanged(true);
                            }}
                          />
                        </>
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => removeEntry(index)}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              <Grid style={{ display: "flex", justifyContent: "flex-start" }}>
                <Button
                  onClick={addEntry}
                  color="primary"
                  variant="contained"
                  sx={{
                    mb: { xs: 2, lg: 4 },
                    mt: 2,
                  }}
                >
                  Add
                </Button>
              </Grid>

              <TableRow>
                <Grid item xs={12}>
                  <Button
                    aria-controls="simple-menu"
                    aria-haspopup="true"
                    onClick={() => {
                      setModalPopup(true);
                    }}
                    variant="contained"
                    sx={{ px: 4, mt: 2, mb: 2 }}
                  >
                    Upload Awards Images
                  </Button>
                </Grid>

                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Award Files</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {documents?.data?.map((award, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography className="data-field">
                            {award && award.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => {
                              setConfirmDeleteDialogOpen(true);
                              setAwardToDelete(award);
                            }}
                            color="error"
                          >
                            <Icon icon="iconamoon:trash" />
                          </IconButton>
                          <IconButton
                            onClick={() => handleViewIconClick(award)}
                            color="error"
                            disabled={selectedAward}
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableRow>
            </TableBody>
          </Table>

          <Grid container justifyContent="center" sx={{ mt: 4, mb: 4 }}>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              variant="contained"
              disabled={!fieldChanged}
            >
              Submit
            </Button>
          </Grid>
          <ViewDialogByLocation
            location={selectedAward}
            setSelectedLocation={setSelectedAward}
            onClose={handleDialogClose}
          />
          <Grid>
            <Dialog
              open={dailogSuccess}
              onClose={handleClose}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
              PaperProps={{
                sx: {
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                  backgroundColor: (theme) => theme.palette.primary.background,
                },
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  borderRadius: 1,
                  textAlign: "center",
                  border: (theme) => `1px solid ${theme.palette.divider}`,
                  borderColor: "primary.main",
                }}
              >
                <DialogContent>
                  <DialogContentText
                    id="alert-dialog-description"
                    color="primary.main"
                  >
                    Successfully uploaded
                  </DialogContentText>
                </DialogContent>
                <DialogActions>
                  <Button
                    variant="contained"
                    onClick={handleClose}
                    sx={{ margin: "auto", width: 100 }}
                  >
                    Ok
                  </Button>
                </DialogActions>
              </Box>
            </Dialog>

            <DeleteConfirmationDialog
              open={confirmDeleteDialogOpen}
              onClose={() => setConfirmDeleteDialogOpen(false)}
              onConfirm={handleDelete}
            />
          </Grid>
        </Paper>

        <DocumentUploadDialog
          open={modalPopup}
          onClose={() => setModalPopup(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
        />
      </form>
    </>
  );
};

export default ArchitectAwardsEdit;
