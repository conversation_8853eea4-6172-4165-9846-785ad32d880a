// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "../../MUITableCell";
import IntroductionFieldsEdit from "./IntroductionFieldsEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const IntroductionFieldsView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Introduction Fields"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Introduction Fields:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          (Yes/No):
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Architecture</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.architecture ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Urban Design</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.urbanDesign ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Interiors</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.interiors ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Master Planning</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.masterPlanning ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Structural Repairs</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.structuralRepairs ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Landscape Integration</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.landscapeIntegration ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Others (Please Mention)</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.others ? `Yes, ${data.otherText}` : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Youtube Url</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.introductionYoutubeUrl}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <IntroductionFieldsEdit formData={data} onCancel={editClick3} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default IntroductionFieldsView;
