import React from "react";
import { useState, useEffect, useContext } from "react";
import { DataGrid } from "@mui/x-data-grid";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import {
  Box,
  InputAdornment,
  DialogContentText,
  InputLabel,
  Select,
  ListItemIcon,
} from "@mui/material";
import DialogActions from "@mui/material/DialogActions";
import SearchIcon from "@mui/icons-material/Search";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import { yupResolver } from "@hookform/resolvers/yup";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { Card, CardContent, Divider, Tooltip } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";
import CustomChip from "src/@core/components/mui/chip";
import UpdateDialog from "./UpdateDialog";

// import DeleteDialog from "./deleteDialog";
import SiteMapData from "src/@core/components/custom-components/SiteMapData";
import DeleteDialog from "./DeleteDialog";
import { useRBAC } from "src/pages/permission/RBACContext";
import { useRouter } from "next/router";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import * as yup from "yup";

import { Menu, MenuItem } from "@mui/material";

import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const accessList = [
  { name: "Create", value: "CREATE" },
  { name: "Read", value: "READ" },
  { name: "Update", value: "UPDATE" },
  { name: "Delete", value: "DELETE" },
];

const Roles = () => {
  const { can, rbacRoles } = useRBAC();
  const router = useRouter();
  //RoleType States
  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [selectedRoleTypeSubRoles, setSelectedRoleTypeSubRoles] = useState([]);
  const [roleTypeOptions, setRoleTypeOptions] = useState([]);
  const [subRoleTypeOptions, setSubRoleTypeOptions] = useState([]);
  const [roleTypeId, setRoleTypeId] = useState("");
  const [subRoleTypeId, setSubRoleTypeId] = useState("");
  const [showSubRoleTypes, setShowSubRoleTypes] = useState(false);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState(true);

  //Roles States
  const [rolesList, setRolesList] = useState([]);

  // const [allServicesList, setAllServicesList] = useState([]);
  // const [allBusinessLine, setAllBusinessLine] = useState([]);
  // const [societiesData, setSocietiesData] = useState(null);
  const [selectedSocietiesDataId, setSelectedSocietiesDataId] = useState();

  //Get All use States
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [currentRow, setCurrentRow] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [keyword, setKeyword] = useState("");

  const [id, setId] = useState(null);
  const [editIndex, setEditIndex] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);
  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);
  const [modalMode, setModalMode] = useState("Create");

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [societiesValue, setSocietiesValue] = useState("");
  const [showDataAccessSection, setShowDataAccessSection] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuIndex, setMenuIndex] = useState(null);

  const schema = yup.object().shape({
    name: yup.string().required(),
  });

  //AuthContext
  const {
    postRole,
    patchRoles,
    roleUser,
    setRoleUser,
    roleData,
    setRoleData,
    allSiteMapDataFromSettings,
    getAllListValuesByListNameId,
    allCategories,
  } = useContext(AuthContext);

  const {
    reset,
    setValue,
    register,
    control,
    getValues,
    handleSubmit,
    clearErrors,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
      societiesData: "",
      servicesAccessGivenTo: [],
      businessLine: [],
    },
    mode: "onChange",
    resolver: yupResolver(schema),
  });

  const [expandedSections, setExpandedSections] = useState({});

  // Function to toggle expand/collapse of "Section Name" rows

  // const [selectedDocumentCategoryId, setSelectedDocumentCategoryId] =
  //   useState([]);

  const handleSelectDocumentCategory = (event) => {
    setSelectedDocumentCategoryId(event.target.value);
  };

  useEffect(() => {
    // Fetch Categories
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ROLE_TYPES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfRoleTypes(res.data.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = [];
      listOfRoleTypes.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setRoleTypeOptions(data);
    }
  }, [listOfRoleTypes]);

  // useEffect(() => {
  //   if(!!authConfig) {
  //     getAllListValuesByListNameId(
  //       authConfig.allServicesListNameId,
  //       handleServicesSuccess,
  //       handleError
  //     );
  //     getAllListValuesByListNameId(
  //       authConfig.societiesId,
  //       handleSocietiesChange,
  //       handleError
  //     );
  //     getAllListValuesByListNameId(
  //       authConfig.allBusinessLineId,
  //       handleBusinessLine,
  //       handleError
  //     );
  //   }
  // }, [authConfig]);

  const handleOpenDialog = () => {
    setOpenDialog(true);
    setModalMode("Create");
    setValue("name", "");
    setRoleTypeId("");
    setSubRoleTypeId("");
  };

  const handleDialogClose = () => {
    setOpenDialogContent(false);
  };

  const handleOpenMenu = (event, index) => {
    setAnchorEl(event.currentTarget);
    setMenuIndex(index);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setMenuIndex(null);
  };

  const handleClickEdit = (row) => {
    setOpenDialog(true);
    setModalMode("Update");
    setRoleUser({
      ...roleUser,
      id: row.id,
    });
    handleCloseMenu();
  };

  const handleClickDeactivate = (row) => {
    setCurrentRow(row);
    setOpenDeleteDialog(true);
    handleCloseMenu();
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    // setEditIndex(null);
    setId(null);
    reset({
      name: "",
      services: [],
      businessLine: [],
      societiesData: "",
    });
    setSubRoleTypeOptions([]);
    setRoleData(null);

    // setRenderedData([]);
  };

  const handleSocietiesChange = (data) => {
    setSocietiesData(data?.listValues);
  };

  const handleBusinessLine = (data) => {
    setAllBusinessLine(data?.listValues);
  };

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleError = (error) => {
    console.error("All Services:", error);
  };

  //Fetching Roles
  const fetchRoles = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.getAllRolesEndpoint);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setRolesList(response.data?.roles || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
    }
  };

  useEffect(() => {
    console.log(`Fetching data for page: ${page}, pageSize: ${pageSize}`);
    fetchRoles(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const [menu, setMenu] = useState(null);
  
  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!can("userRoles_READ")) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  const columns = [
    { field: "name", headerName: "User Role", flex: 0.69, minWidth: 120 },
    {
      field: "roleType",
      headerName: "Category",
      flex: 0.69,
      minWidth: 120,
    },

    { field: "createdBy", headerName: "Created by", flex: 1, minWidth: 120 },
    { field: "updatedBy", headerName: "Updated by", flex: 1, minWidth: 120 },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.8,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    (can("userRoles_UPDATE") || can("userRoles_DELETE")) && {
      field: "actions",
      headerName: "Actions",
      flex: 0.4,
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,

      renderCell: (params) => {
        const handleMenuClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setRoleUser({
            ...roleUser,
            id: row.id,
          });
        };

       
        const handleEditClick = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
          setModalMode("Update");
          
        };

        const handleDeactivateClick = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const handleActivateClick = () => {
          setOpenUpdateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
             <Tooltip title="Actions">
                <CustomAvatar
                  skin="light"
                  variant="rounded"
                  sx={{
                    mr: { xs: 2, lg: 4 },
                    width: 34,
                    height: 34,
                    cursor: "pointer",
                  }}
                  onClick={handleMenuClick}
                >
                  <Icon icon="bi:three-dots-vertical" />
                </CustomAvatar>
              </Tooltip>
              <Menu
                id="actions-menu"
                anchorEl={menu}
                keepMounted
                open={Boolean(menu)}
                onClose={handleCloseMenuItems}
              >
              {can("userRoles_UPDATE") && (
                <MenuItem onClick={handleEditClick}>Edit</MenuItem>
              )}
              {can("userRoles_DELETE") && (
                currentRow?.isActive ? (
                  <MenuItem onClick={handleDeactivateClick}>DeActivate</MenuItem>
                ) : (
                  <MenuItem onClick={handleActivateClick}>Activate</MenuItem>
                )
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchRoles(page, pageSize, searchKeyword);
  };

  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchRoles(page, pageSize, searchKeyword);
  };

  const handleRoleTypeChange = (newValue) => {
    const roleType = listOfRoleTypes.filter((entry) => entry.id === newValue);

    let hasSubRole = false;
    if (roleType && roleType.length) {
      if (roleType[0].hasSubtypes) {
        hasSubRole = true;
      }
    }
    setRoleTypeId(newValue);
    if (hasSubRole) {
      let subRoleTypes = roleType[0].metaData.subRoleTypes || [];
      setShowSubRoleTypes(true);
      setSelectedRoleTypeSubRoles(subRoleTypes);
      let data = [];
      subRoleTypes.map((item) => data.push({ key: item.name, value: item.id }));

      setSubRoleTypeOptions(data);
    } else {
      setSubRoleTypeId("");
      setShowSubRoleTypes(false);
    }
  };

  const handleSubRoleTypeChange = (newValue) => {
    setSubRoleTypeId(newValue);
  };

  // This Will Execute for the Edit mode, When any Role is edited, roleData gets set from the Authcontent
  // then the variabled here should be set.
  useEffect(() => {
    if (!!roleData) {
      console.log("Pre-Populating  roleData:", roleData);

      if (modalMode == "Update") {
        if (roleData?.roleType?.hasSubTypes) {
          let roleTypeId = roleData?.roleType?.id;
          let roleType = listOfRoleTypes.find((item) => item.id === roleTypeId);
          let data = [];
          roleType?.metaData?.subRoleTypes?.map((item) => {
            data.push({ key: item.name, value: item.id });
          });
          setSubRoleTypeOptions(data);
          setSubRoleTypeId(roleData?.roleType?.metadata?.subRoleTypes?.[0]?.id);

          setShowSubRoleTypes(true);
        } else {
          setSubRoleTypeId("");
          setShowSubRoleTypes(false);
        }
        setValue("name", roleData?.name);
        setRoleTypeId(roleData?.roleType?.id);

        console.log(
          "Pre-Populating societiesData:",
          roleData?.additionalPrivileges?.societiesData ? "Yes" : "No",
          roleData?.additionalPrivileges?.societiesData
        );

        setValue(
          "servicesAccessGivenTo",
          roleData?.additionalPrivileges?.services
        );
        setValue(
          "societiesData",
          roleData?.additionalPrivileges?.societiesData ? "Yes" : "No"
        );
        setValue("businessLine", roleData?.additionalPrivileges?.businessLine);
      }
    }
  }, [roleData]);

  async function submit(submittedData) {
    console.log("UserRoles useForm Data:", submittedData);

    console.log("UserRoles useForm Data:", submittedData?.services);

    // Flag to check if any checkbox is selected
    let noCheckBoxSelected = true;
    const transformedData = allSiteMapDataFromSettings.map((page) => {
      const pageData = {
        name: page.name,
        code: page.code,
        children: [],
        permissions: accessList
          .filter((theme) => getValues(`${page.code}.${theme.value}`))
          .map((theme) => theme.value),
      };

      if (pageData.permissions.length) {
        noCheckBoxSelected = false;
      }

      if (page.children) {
        pageData.children = page.children.map((child) => {
          return {
            code: child.code,
            permissions: accessList
              .filter((theme) => getValues(`${child.code}.${theme.value}`))
              .map((theme) => theme.value),
          };
        });
      }

      return pageData;
    });

    let jsonToBackend = {
      name: submittedData?.name,
      additionalPrivileges: {
        // societiesData: submittedData?.societiesData == "Yes" ? true : false,
        // businessLine: submittedData?.businessLine,
        // services: submittedData?.servicesAccessGivenTo,
        // documentCategoryIds: selectedDocumentCategoryId,
      },
      roleTypeId: roleTypeId,
      subRoleTypeId: subRoleTypeId ? subRoleTypeId : "",
      roleMetaData: {
        siteMapData: {
          pages: transformedData,
        },
      },
    };
    // For Edit Mode we have to include the ID in the Payload.
    if (modalMode == "Update") {
      jsonToBackend.id = roleData?.id;
    }

    console.log("Submitting data:", jsonToBackend);
    if (noCheckBoxSelected) {
      const message = `
    <div> 
      <h3> Please Select any permissions.</h3>
    </div>
  `;
      setDialogMessage(message);
      setOpenDialogContent(true);
    } else {
      const response =
        modalMode == "Create"
          ? await postRole(jsonToBackend, () => {})
          : await patchRoles(jsonToBackend, roleData?.id, () => {});
      let message = "";
      if (response) {
        message =
          modalMode == "Create"
            ? `
      <div> 
        <h3> Role Created Successfully.</h3>
      </div>
    `
            : `
    <div> 
    <h3> Role Updated Successfully.</h3>
  </div>
  `;
      } else {
        message =
          modalMode == "Create"
            ? `
              <div> 
                <h3> Role Creation Failed.</h3>
              </div>
            `
            : `<div> 
            <h3> Role Update Failed.</h3>
          </div>`;
      }
      setDialogMessage(message);
      setOpenDialogContent(true);

      setOpenDialog(false);
      reset({
        name: "",
      });
      setSubRoleTypeOptions([]);
      fetchRoles(page, pageSize, searchKeyword);
    }
  }

  const isRoleNameExists = (name) => {
    const lowerCaseName = name.toLowerCase();
    return rolesList.some((role) => role.name.toLowerCase() === lowerCaseName);
  };

  return (
    <>
      {can("userRoles_READ") && (
        <Grid>
          <Card>
            <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6">User Roles</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm="auto">
                      <FormControl>
                        <Controller
                          name="mainSearch"
                          control={control}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              inputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={setSearchKeyword(keyword)}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    {can("userRoles_CREATE") && (
                      <Grid item xs={12} sm="auto">
                        <Button variant="contained" onClick={handleOpenDialog}>
                          Add New User Role
                        </Button>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </Box>
            <Divider />
            <Divider />

            {/* <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        /> */}

            <Dialog fullScreen open={openDialog} onClose={handleCloseDialog}>
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start" },
                  fontSize: { xs: 19, md: 20 },
                }}
                textAlign={"center"}
              >
                {modalMode == "Create" ? "Add New Role" : "Update Role"}
                <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                  <IconButton
                    size="small"
                    onClick={handleCloseDialog}
                    sx={{
                      // p: "0.438rem",
                      borderRadius: 1,
                      color: "common.white",
                      backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: "#66BB6A",
                        transition: "background 0.5s ease, transform 0.5s ease",
                      },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>

              <DialogContent
                sx={{
                  position: "relative",
                  p: (theme) => `${theme.spacing(10, 8)} !important`,
                }}
              >
                <Grid container spacing={5} alignItems={"center"}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="name"
                        control={control}
                        rules={{
                          required: "User Role is required",
                          pattern: {
                            value: /^(?=(.*[A-Za-z]){5})[A-Za-z\s]+$/,
                            message:
                              "User Role must contain at least 5 letters and only characters are allowed",
                          },
                          validate: {
                            roleNameExists: (value) =>
                              isRoleNameExists(value)
                                ? "User Role already exists"
                                : undefined,
                          },
                        }}
                        render={({ field, fieldState }) => (
                          <TextField
                            {...field}
                            label="User Role"
                            placeholder="Enter User Role"
                            size="small"
                            // InputLabelProps={{ shrink: true }}
                            inputProps={{ maxLength: 50 }}
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="role-name"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"roleTypeId-select"}
                      label={"Select Category"}
                      name="roleTypeId-select"
                      nameArray={roleTypeOptions}
                      defaultValue={roleTypeId}
                      value={roleTypeId}
                      onChange={(event) =>
                        handleRoleTypeChange(event.target.value)
                      }
                      aria-describedby="roleTypeId-select"
                    />
                  </Grid>

                  {showSubRoleTypes && subRoleTypeOptions?.length > 0 && (
                    <Grid item xs={12} sm={4}>
                      <SelectCategory
                        register={register}
                        clearErrors={clearErrors}
                        id={"subRoleTypeId-select"}
                        label={"Select Sub Category"}
                        name="subRoleTypeId-select"
                        nameArray={subRoleTypeOptions}
                        defaultValue={subRoleTypeId}
                        value={subRoleTypeId}
                        onChange={(event) =>
                          handleSubRoleTypeChange(event.target.value)
                        }
                        aria-describedby="subRoleTypeId-select"
                      />
                    </Grid>
                  )}

                  <>
                    {/* New Fields */}
                    {/* <Grid item xs={12}>
                        <Typography fontWeight={500}>Data Access</Typography>
                        <Divider />
                      </Grid> */}

                    {/* <Grid item xs={12} sm={4}>
                        <FormControl fullWidth>
                          <InputLabel style={{ zIndex: 0 }}>
                            Service Providers
                          </InputLabel>
                          <Controller
                            name="servicesAccessGivenTo"
                            control={control}
                            render={({ field }) => (
                              <>
                              <Select
                                {...field}
                                multiple
                                size="small"
                                labelId="service-providers"
                                label="Select Services Providers"
                                value={
                                  Array.isArray(field.value) ? field.value : []
                                }
                                onChange={(event) => {
                                  field.onChange(event.target.value);
                                }}
                                renderValue={(selected) => (
                                  <span>
                                    {selected
                                      ?.map(
                                        (selectedValue) =>
                                          allServicesList?.find(
                                            (service) =>
                                              service?.id === selectedValue
                                          )?.listValue
                                      )
                                      .join(", ")}
                                  </span>
                                )}
                              >
                                {allServicesList?.map((service) => (
                                  <MenuItem
                                    key={service?.id}
                                    value={service?.id}
                                  >
                                    {service.listValue}
                                  </MenuItem>
                                ))}
                              </Select>
                              </>
                            )}
                          />
                        </FormControl>
                      </Grid> */}
                    {/* <Grid item xs={12} sm={2}>
                        <FormControl
                          fullWidth>
                          <InputLabel id="societiesData-select-label">
                            Societies Data
                          </InputLabel>
                          <Controller
                            name="societiesData"
                            control={control}
                            render={({ field }) => (
                              <Select
                                {...field}
                                labelId="societiesData-select-label"
                                id="societiesData-select"
                                size="small"
                                label="Societies Data"
                                onChange={(event) => {
                                  field.onChange(event.target.value);
                                }}
                                renderValue={(selected) => (
                                  <span>
                                    {console.log("Pre-Populating for:selected: ", selected, "; listValue:", societiesData?.find((service) =>
                                              service?.listValue === selected
                                          )?.listValue)}

                                    {societiesData?.find((service) =>
                                              service?.listValue === selected
                                          )?.listValue
                                          }
                                  </span>
                                )}
                              >
                                {console.log(">>>societiesData:", societiesData)}
                                {societiesData?.map((data) => (
                                  <MenuItem key={data.id} value={data.listValue}>
                                    {data.listValue}
                                  </MenuItem>
                                ))}
                              </Select>
                            )}
                          />
                          {errors.selectedSocietiesDataId && (
                            <FormHelperText>
                              {errors.selectedSocietiesDataId?.message}
                            </FormHelperText>
                          )}
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={2}>
                        <Box>
                          <FormControl fullWidth>
                            <InputLabel style={{ zIndex: 0 }}>
                              Business Line
                            </InputLabel>
                            <Controller
                              name="businessLine"
                              control={control}
                              render={({ field }) => (<>
                                <Select
                                  {...field}
                                  multiple
                                  size="small"
                                  labelId="businessLine"
                                  label="Business Line"                              
                                  value={
                                    Array.isArray(field.value) ? field.value : []
                                  }
                                  onChange={(event) => {
                                    field.onChange(event.target.value);
                                  }}
                                  renderValue={(selected) => (
                                    <span>
                                      {selected
                                        ?.map(
                                          (selectedValue) =>
                                            allBusinessLine?.find(
                                              (service) =>
                                                service.id === selectedValue
                                            )?.listValue
                                        )
                                        .join(", ")}
                                    </span>
                                  )}
                                >
                                  {allBusinessLine?.map((service) => (
                                    <MenuItem
                                      key={service?.id}
                                      value={service?.id}
                                    >
                                      {service?.listValue}
                                    </MenuItem>
                                  ))}
                                </Select>
                                </>
                              )}
                            />
                          </FormControl>
                        </Box>
                      </Grid> */}
                  </>
                </Grid>
                {/* <Grid item xs={12} sx={{ paddingTop: "20px" }}>
                  <Typography fontWeight={500}>
                    Document Category Access
                  </Typography>
                  <Divider />
                </Grid> */}

                {/* <Divider sx={{ mt: 10, mb: 10 }}></Divider>
                <Grid
                  marginTop={5}
                  id="document-access"
                  container
                  spacing={2}
                  style={{
                    alignItems: "start",
                    width: "100%",
                  }}
                >
                  <Grid item xs={12} sm={2}>
                    <FormControl
                      fullWidth
                      error={!!errors.selectedDocumentCategoryId}
                    >
                      <InputLabel id="documentsCategories-select-label">
                        Document Categories
                      </InputLabel>
                      <Select
                        labelId="documentsCategories-select-label"
                        id="documentsCategories-select"
                        multiple
                        size="small"
                        value={selectedDocumentCategoryId || []}
                        onChange={handleSelectDocumentCategory}
                        label="Document Categories"
                      >
                        {allCategories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.documentCategory}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.selectedDocumentCategoryId && (
                        <FormHelperText
                          id="validation-selectedDocumentCategoryId"
                          error
                        >
                          {errors.selectedDocumentCategoryId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid> */}
                <Divider sx={{ mt: 10, mb: 10 }}></Divider>
                <>
                  {/* <Typography
                      sx={{
                        fontWeight: "bold",
                        mb: 5,
                        textDecoration: "underline",
                      }}
                    >
                      Site Map Data
                    </Typography>

                    <SiteMapData
                      setValue={setValue}
                      control={control}
                      getValues={getValues}
                      defaultData={
                        modalMode == "Create"
                          ? []
                          : roleData?.roleMetaData?.siteMapData?.pages
                      }
                    /> */}

                  <AccordionBasic
                    id={"siteMapData"}
                    ariaControls={"siteMapData"}
                    heading={"SiteMap Data"}
                    body={
                      <>
                        <SiteMapData
                          setValue={setValue}
                          control={control}
                          getValues={getValues}
                          defaultData={
                            modalMode == "Create"
                              ? []
                              : roleData?.roleMetaData?.siteMapData?.pages
                          }
                        />
                      </>
                    }
                    expanded={isAccordionExpanded}
                  />
                </>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "end",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                  background: "#f4f4f4",
                }}
              >
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="outlined"
                  color="primary"
                  onClick={handleCloseDialog}
                >
                  Cancel
                </Button>
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  onClick={handleSubmit(submit)}

                  // disabled={!formState.isValid || !(selectedSubRole?.id) ||(selectedRoleType?.id && !(selectedSubRole?.id))}
                >
                  Submit
                </Button>
              </DialogActions>
            </Dialog>
            <Divider />
            <CardContent>
              <div style={{ height: 400, width: "100%" }}>
                <DataGrid
                  rows={rolesList}
                  columns={columns}
                  checkboxSelection
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              </div>
            </CardContent>
          </Card>
          <DeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />

          <UpdateDialog
            open={openUpdateDialog}
            onClose={handleCloseUpdateDialog}
            data={currentRow}
          />
        </Grid>
      )}
      <Dialog
        open={openDialogContent}
        onClose={handleDialogClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleDialogClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default Roles;
