// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { styled, useTheme } from "@mui/material/styles";
import TableCell from "@mui/material/TableCell";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const Submission = ({ data }) => {
  const theme = useTheme();

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs:'1rem !important',lg:'1.2rem !important'}}}>
              Review & Submit
            </Typography>
          </Box>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            1. Initiating Redevelopment
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Has your society considered going for Redevelopment? :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.needAssessment?.hasYourSocietyConsideredForRedevelopment}
            </Typography>
          </Box>
          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            2. Types of Development
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Are you aware of the types of Redevelopment?
              <br /> What are the challenges and benefits of each type?
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {
                data.typesOfRedevelopment
                  ?.hasAwareOfTypesOfRedevelopment_ChallengesBenefits
              }
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            3. Redevelopment Committee
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Have you formed a Redevelopment Committee? :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.committee?.haveFormedRedevelopmentCommittee}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            4. Consents
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Total No. of Members &nbsp; =
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.consents?.totalNoOfMembers}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              No. of Consents Given &nbsp; =
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.consents?.noOfConsents}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Percentage of Members Consent &nbsp; =
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.consents?.percentageOfMembersConsent}
            </Typography>
          </Box>
          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            5. Conveyance
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Does your society have Conveyance? :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.conveyance?.doesSocietyHaveConveyance}
            </Typography>
          </Box>
<Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
           6. Valid Tenure of Managing Committee
          </Typography>

<Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
            Is the existing managing committee in accordance with the provisions of this Act and rules and bye-laws. Is their terms are valid and not expired ?
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
            {data.managingCommittee?.isExistingManagingCommitteeProvisionsAct}
            </Typography>
          </Box>

          <Box display='flex' alignItems='center' justifyContent='space-between' sx={{ ml: { md: 4 }, width: { md: '55%' } }}>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
              Tenure in number of Years
            </Typography>
            <Typography variant='body1' fontWeight='bold' mx={1}>
              {data?.managingCommittee?.tenureYears}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' justifyContent='space-between' sx={{ ml: { md: 4 }, width: { md: '55%' } }}>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
              Valid till date
            </Typography>
            <Typography variant='body1' fontWeight='bold' mx={1}>
              {data?.managingCommittee?.validTillDate}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            7. Redevelopment Documents
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>a. Property Card :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasPropertyCard}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>b. Conveyance Deed :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasConveyanceDeed}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>c. City Survey Plan :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasCitySurveyPlan}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>d. Survey Plan :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasSurveyPlan}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              e. Last Approved Municipal set of drawings :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasLastApprovedMunicipalDrawings}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>f. DP Remarks :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.documents?.hasDpRemarks}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            8. Appointment of professionals
          </Typography>

          <Box display='flex' alignItems='center' justifyContent='space-between' sx={ { ml: { md: 4 }, width: { md: '55%' } } }>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
              a. Architect :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.appointmentOfProfessionals?.haveAppointedAnyArchitectOrPmc}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' justifyContent='space-between' sx={{ ml: { md: 4 }, width: { md: '55%' } }}>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
            b. PMC :
            </Typography>
            <Typography variant='body1' fontWeight='bold' mx={1}>
              {data.appointmentOfProfessionals.haveAppointedAnyPmc}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' justifyContent='space-between' sx={{ ml: { md: 4 }, width: { md: '55%' } }}>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
            c. Advocate :
            </Typography>
            <Typography variant='body1' fontWeight='bold' mx={1}>
              {data.appointmentOfProfessionals.haveAppointedAnyAdvocate}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' justifyContent='space-between' sx={{ ml: { md: 4 }, width: { md: '55%' } }}>
            <Typography variant='body1' sx={{fontSize:{xs:'0.9rem !important'}}}>
            d. CA :
            </Typography>
            <Typography variant='body1' fontWeight='bold' mx={1}>
              {data.appointmentOfProfessionals.haveAppointedAnyCa}
            </Typography>
          </Box>
          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            9. Pre-Tendering Stage
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>a. Data & Documentation :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.preTenderingStage?.hasDataAndDocumentation}
            </Typography>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>b. Site Analysis Report :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.preTenderingStage?.hasSiteAnalysisReport}
            </Typography>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              c. Project Feasibility and FSI Calculation :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.preTenderingStage?.hasFsiCalculations}
            </Typography>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              d. Creation of Design Layout & Technical Feasibility Report:
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.preTenderingStage?.hasCreationOfDesignLayout}
            </Typography>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>e. Market Analysis :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.preTenderingStage?.hasMarketAnalysis}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            10. Tendering Stage
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>a. Tendering Documents :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.tenderingStage?.hasTenderingDocuments}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              b. Pre Qualification of Developers :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.tenderingStage?.hasPreQualificationOfDevelopers}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              c. Scrutinization of Tender Documents :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.tenderingStage?.hasScrutinizationOfTenderDocuments}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              d. Appointment of Developers :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.tenderingStage?.hasAppointmentOfDevelopers}
            </Typography>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1">e. Signing of DA/DM :</Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.tenderingStage?.hasSigningOfDaOrDm}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            color="textPrimary"
            fontWeight="bold"
            mt={"8px"}
            mb={"5px"}
            sx={{fontSize:{xs:'0.9rem !important'}}}
          >
            11. Financial Closure
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mb: 2, ml: { md: 4 }, width: { md: "55%" } }}
          >
            <Typography variant="body1" sx={{fontSize:{xs:'0.9rem !important'}}}>
              Have you secured Financial Closure for the proposed redevelopment
              project? :
            </Typography>
            <Typography variant="body1" fontWeight="bold" mx={1}>
              {data.financialClosure?.haveSecuredFinancialClosure}
            </Typography>
          </Box>
      
        </Grid>
      </Grid>
    </>
  );
};

export default Submission;
