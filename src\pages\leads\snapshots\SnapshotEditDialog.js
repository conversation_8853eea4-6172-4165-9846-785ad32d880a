import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  DialogContentText,
  FormHelperText,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import Icon from "src/@core/components/icon";
import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SnapshotInput from "src/@core/components/custom-components/SnapshotFrom";
import { AuthContext } from "src/context/AuthContext";

import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";
import ViewSnapshotByLocation from "src/@core/components/custom-components/ViewSnapshotByLocation";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
const SnapshotEditDialog = ({
  open,
  onClose,
  onSave,
  data,
  loading,
  selectedFiles,
  setSelectedFiles,
  leadStatus,
  setLeadStatus = () => {},
  leadPriority,
  setLeadPriority = () => {},
  status,
  setStatus = () => {},
  categoryId,
  setCategoryId,
  //employeesData,
  assignedTo,
  setAssignedTo = () => {},
  formData,
  fetchUserList,
  formValues,
}) => {
  const { snapshotDelete } = useContext(AuthContext);
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
    setValue,
  } = useForm();
  const { register, watch } = useForm({
    defaultValues: {
      leadStatus: data?.leadSnapshotResponseDTO?.leadStatus || "",
      leadPriority: data?.leadSnapshotResponseDTO?.leadPriority || "",
      assignedTo: data?.leadSnapshotResponseDTO?.assignedTo || "",
      status: data?.leadSnapshotResponseDTO?.status || "",
    },
  });

  const handleClose = () => {
    onClose();
    fetchUserList();
    setSelectedFiles([]);
  };

  const [categoriesData, setCategoriesData] = useState([]);
  const [documents, setDocuments] = useState([]);

  const [awardToDelete, setAwardToDelete] = useState({});
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);

  const [leadStatusList, setLeadStatusList] = useState([]);
  const [leadPriorityList, setLeadPriorityList] = useState([]);
  const [statusList, setStatusList] = useState([]);

  const [selectedLeadPriority, setSelectedLeadPriority] = useState([]);

  const [selectedAssignedTo, setSelectedAssignedTo] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setCategoriesData(res.data.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const [selectedAward, setSelectedAward] = useState(null);
  const [dialogSuccess, setDialogSuccess] = useState(false);

  const fetchDocuments = () => {
    const locations = data?.metaData
      ?.filter((item) => item?.isActive)
      ?.map((item) => ({
        id: item?.id,
        location: item?.location,
      }));
    setDocuments(locations || []);
  };

  useEffect(() => {
    fetchDocuments();
    if (
      data &&
      setLeadStatus &&
      setLeadPriority &&
      setStatus &&
      setAssignedTo &&
      setCategoryId
    ) {
      setLeadStatus(data.leadStatus || "");
      setLeadPriority(data.leadPriority || "");
      setStatus(data.status || "");
      setAssignedTo(data.assignedTo || "");
      setCategoryId(data.categoryTypeId || "");
    }
  }, [data]);

  const handleCategoryChange = (event) => {
    setCategoryId(event.target.value);
  };

  const handleViewIconClick = (award) => {
    setSelectedAward(award);
  };

  const handleDialogClose = () => {
    setSelectedAward(null);
  };

  const [selectedAssignments, setSelectedAssignments] = useState([]);
  const handleEmployeeAssignmentsChange = (event) => {
    setAssignedTo(event.target.value);
  };

  const handleAssignedToChange = (event) => {
    const value = event.target.value;
    setAssignedTo(value);
  };

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusList(data?.listValues);
  };

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityList(data?.listValues);
  };

  const handleLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatus(value);
  };

  const handleLeadPriorityChange = (event) => {
    const value = event.target.value;
    setLeadPriority(value);
  };
  const handleError = (error) => {
    console.error("leadsnapshot error:", error);
  };
  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };
  const handleStatusChange = (event) => {
    const value = event.target.value;
    setStatus(value);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        handleLeadStatusSuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handleLeadPrioritySuccess,
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const handleDelete = async () => {
    const success = await snapshotDelete(awardToDelete, data);
    if (success) {
      setDocuments((prevDocs) =>
        prevDocs.filter((doc) => doc.id !== awardToDelete.id)
      );
      setConfirmDeleteDialogOpen(false);
      setDisableButton(false);
    }
  };

  const handleSave = (formValues) => {
    console.log("Form Values:", formValues);
    if (onSave) {
      onSave({
        ...formData,
        selectedFiles,
        leadSnapshotResponseDTO: {
          assignedTo: formValues.assignedTo,
          status: formValues.status,
          leadStatus: formValues.leadStatus,
          leadPriority: formValues.leadPriority,
        },
      });
    }
  };

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="md"
        scroll="paper"
        open={open}
        onClose={(_event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Add Snapshots
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            ":playing": (theme) => `${theme.spacing(4)} !important`,
            px: (theme) => [
              `${theme.spacing(6)} !important`,
              `${theme.spacing(10)} !important`,
            ],
            wordWrap: "break-word !important",
            whiteSpace: "pre-wrap !important",
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel id="leadStatusId"> Lead Status</InputLabel>
                <Controller
                  name="leadStatus"
                  control={control}
                  defaultValue={leadStatus}
                  render={({ field }) => (
                    <Select
                      {...field}
                      size="small"
                      labelId="leadStatusId-label"
                      label="Lead Status"
                      id="leadStatusId"
                      value={leadStatus} // Controlled by state
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e); // Update state
                      }}
                    >
                      {leadStatusList?.map((status) => (
                        <MenuItem key={status.id} value={status.id}>
                          {status.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel id="priorityId">Priority</InputLabel>
                <Controller
                  name="leadPriority"
                  control={control}
                  defaultValue={leadPriority}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelId="priorityId-label"
                      size="small"
                      label="Priority"
                      id="priorityId"
                      value={leadPriority} // Controlled by state
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e); // Update state
                      }}
                    >
                      {leadPriorityList?.map((priority) => (
                        <MenuItem key={priority.id} value={priority.id}>
                          {priority.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={6}>
              <FormControl fullWidth error={Boolean(errors.statusId)}>
                <InputLabel id="statusId-label">Status</InputLabel>
                <Controller
                  name="status"
                  control={control}
                  defaultValue={status}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelId="statusId-label"
                      size="small"
                      label="Status"
                      id="statusId"
                      value={status} // Controlled by state
                      onChange={(e) => {
                        field.onChange(e);
                        handleStatusChange(e); // Update state
                      }}
                    >
                      {statusData?.map((status) => (
                        <MenuItem key={status.id} value={status.id}>
                          {status.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
                {errors.statusId && (
                  <FormHelperText
                    id="validation-statusId"
                    sx={{ color: "error.main" }}
                  >
                    {errors.statusId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={6}>
              <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                <InputLabel id="assignedTo"> Assigned To</InputLabel>
                <Controller
                  name="assignedTo"
                  control={control}
                  defaultValue={assignedTo}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelId="assignedTo-label"
                      size="small"
                      label="assignedTo"
                      id="assignedTo"
                      value={assignedTo} // Controlled by state
                      onChange={(e) => {
                        field.onChange(e);
                        handleAssignedToChange(e); // Update state
                      }}
                    >
                      {employeesData
                        ?.map((data) => ({
                          id: data.id,
                          label: data.name,
                        }))
                        .map((emp) => (
                          <MenuItem key={emp.id} value={emp.id}>
                            {emp.label}
                          </MenuItem>
                        ))}
                    </Select>
                  )}
                />
                {errors.assignedTo && (
                  <FormHelperText
                    id="validation-assignedTo"
                    sx={{ color: "error.main" }}
                  >
                    {errors.assignedTo.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>

          <Grid container spacing={2} mt={0.3}>
            <Grid item xs={6}>
              <SnapshotInput
                selectedFiles={selectedFiles}
                setSelectedFiles={setSelectedFiles}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel id="categoryId">Category Type</InputLabel>
                <Controller
                  name="categoryId"
                  control={control}
                  defaultValue={categoryId}
                  rules={{ required: "Category is required" }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelId="categoryId-label"
                      label="Category Type"
                      id="categoryId"
                      size="small"
                      value={categoryId} // Ensure the correct value is set
                      onChange={(e) => {
                        field.onChange(e);
                        handleCategoryChange(e);
                      }}
                    >
                      {categoriesData
                        ?.filter(
                          (data) =>
                            data.name !== "SuperAdmin" &&
                            data.name !== "Employee" &&
                            data.isActive
                        )
                        .map((data) => ({
                          id: data.id,
                          label: data.name,
                        }))
                        .map((cat) => (
                          <MenuItem key={cat.id} value={cat.id}>
                            {cat.label}
                          </MenuItem>
                        ))}
                    </Select>
                  )}
                />
                {errors.categoryId && (
                  <FormHelperText
                    id="validation-categoryId"
                    sx={{ color: "error.main" }}
                  >
                    {errors.categoryId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Table sx={{ width: "100%" }}>
                <TableHead>
                  {documents?.length > 0 && (
                    <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                      <TableCell sx={{ padding: "5px" }}>File name</TableCell>
                      <TableCell sx={{ padding: "5px" }}>Action</TableCell>
                    </TableRow>
                  )}
                </TableHead>
                <TableBody>
                  {documents?.map((snapshot, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography className="data-field">
                          {snapshot.location &&
                            snapshot.location?.split("/").pop()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => {
                            setConfirmDeleteDialogOpen(true);
                            setAwardToDelete(snapshot);
                          }}
                          color="error"
                        >
                          <Icon icon="iconamoon:trash" />
                        </IconButton>
                        <IconButton
                          onClick={() => handleViewIconClick(snapshot)}
                          color="error"
                          disabled={!!selectedAward}
                        >
                          <Icon icon="iconamoon:eye" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.85)} !important`,
          }}
        >
          <Button variant="outlined" onClick={handleClose}>
            Close
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleSave)}>
            {loading ? <CircularProgress color="inherit" size={24} /> : "Save"}
          </Button>
        </DialogActions>
      </Dialog>
      <ViewSnapshotByLocation
        location={selectedAward}
        setSelectedLocation={setSelectedAward}
        onClose={handleDialogClose}
      />
      <DeleteConfirmationDialog
        open={confirmDeleteDialogOpen}
        onClose={() => setConfirmDeleteDialogOpen(false)}
        onConfirm={handleDelete}
      />
      <Dialog
        open={dialogSuccess}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default SnapshotEditDialog;
