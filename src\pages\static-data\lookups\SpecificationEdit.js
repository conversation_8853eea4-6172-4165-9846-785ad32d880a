import {
    Box,
    Button,
    IconButton, Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormControl,
    Grid,
    DialogContentText,
    Typography
} from "@mui/material";

import Icon from "src/@core/components/icon";

import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { useAuth } from "src/hooks/useAuth";

const SpecificationEdit = ({
  open,
  onClose,
  formData,
  fetchSpecifications,
}) => {
  const auth = useAuth();
  const { getAllListValuesByListNameId, listValues } = useContext(AuthContext);

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm();

  const [service, setService] = useState("");
  const [listNameId, setListNameId] = useState("");

  useEffect(() => {
    setListNameId(formData?.listNamesId);
    setValue("uiComponentsId", formData?.uiComponentsId);
    setValue("sectionId", formData?.sectionId);

    const serviceRetrieved = formData?.listValuesId
      ? listValues.find((item) => item.id === formData?.listValuesId)?.name
      : null;

    setService(serviceRetrieved);
  }, [formData]);

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  // State for dropdown options
  const [allUiComponentsId, setAllUiComponentsId] = useState([]);
  const [allSectionsId, setAllSectionsId] = useState([]);
  const [listNamesOptions, setListNamesOptions] = useState([]);
  const [listOfListNames, setListOfListNames] = useState([]);

  // Fetch list names
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=LIST_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfListNames(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  // Convert list names to options
  useEffect(() => {
    if (listOfListNames) {
      const data = listOfListNames.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setListNamesOptions(data);
    }
  }, [listOfListNames]);

  useEffect(() => {
    if (authConfig) {
      getAllListValuesByListNameId(
        authConfig.uiComponentsId,
        (data) =>
          setAllUiComponentsId(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        (error) => console.error("Error fetching UI components:", error)
      );

      getAllListValuesByListNameId(
        authConfig.allSectionsId,
        (data) =>
          setAllSectionsId(
            data?.listValues.map((section) => ({
              value: section.id,
              key: section.listValue,
            }))
          ),
        (error) => console.error("Error fetching sections:", error)
      );
    }
  }, [authConfig]);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Specification Updated Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Update Specification. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    const fields = {
      listValuesId: formData?.listValuesId,
      listNamesId: listNameId,
      sectionId: data?.sectionId,
      uiComponentsId: data?.uiComponentsId,
    };

    const response = await auth.patchSpecification(
      formData?.id,
      fields,
      handleFailure,
      handleSuccess
    );

    const currentPage = 1;
    const currentPageSize = 10;

    fetchSpecifications(currentPage, currentPageSize);

    handleCancel();
  }

  const handleCancel = () => {
    onClose();
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };
  return (
    <>
      <Dialog
        open={open}
        onClose={handleCancel}
        fullWidth
        maxWidth="md"
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Edit SR Specification
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid container spacing={2}>
            {/* Service Type field */}
            <Grid container item xs={12} sm={6} spacing={2}>
              <Grid item>
                <Typography className="data-field">Service : </Typography>
              </Grid>
              <Grid item>
                <Typography style={{ fontWeight: "bold" }}>
                  {service}
                </Typography>
              </Grid>
            </Grid>

            {/* List Name field */}
            <Grid item xs={12} sm={6}>
              <SelectAutoComplete
                id="listNamesId"
                label="Select Label Name"
                nameArray={listNamesOptions}
                value={listNameId}
                onChange={(e) => setListNameId(e.target.value)}
                error={Boolean(errors.listNamesId)}
              />
            </Grid>

            {/* UI Component field */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.uiComponents)}>
                <Controller
                  name="uiComponentsId"
                  control={control}
                  rules={{ required: "UI Component is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="uiComponents"
                      label="UI Component"
                      nameArray={allUiComponentsId}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.uiComponents && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.uiComponents.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Sections field */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.sections)}>
                <Controller
                  name="sectionId"
                  control={control}
                  rules={{ required: "Sections is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="sections"
                      label="Sections"
                      nameArray={allSectionsId}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.sections && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.sections.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default SpecificationEdit;
