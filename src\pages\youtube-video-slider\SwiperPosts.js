import { useState } from 'react';
import Box from '@mui/material/Box';
import clsx from 'clsx';
import { useKeenSlider } from 'keen-slider/react';
import { useMediaQuery } from '@mui/material';
import PlayYoutubeVideo from './PlayYoutubeVideo';
import Badge from '@mui/material/Badge'
import Icon from 'src/@core/components/icon'

const SwiperPosts = props => {
  const { direction, videoUrls } = props;
  const [loaded, setLoaded] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'));

  const [sliderRef, instanceRef] = useKeenSlider(
    {
      loop: true,
      slides: {
        perView: hidden ? 1 : 4,
        spacing: 14
      },
      rtl: direction === 'rtl',



      //controls
      slideChanged(slider) {
        setCurrentSlide(slider.track.details.rel)
      },
      created() {
        setLoaded(true)
      }
    },)


  return (
    <>
      <Box className='navigation-wrapper'>
        <Box ref={sliderRef} className='keen-slider'>
          {videoUrls.map((url, index) => (
            <Box className='keen-slider__slide' key={index}>
              <PlayYoutubeVideo videoUrl={url} />
            </Box>
          ))}
        </Box>

         {/* Control Arrow Icons */}
         {loaded && instanceRef.current && instanceRef.current.track && instanceRef.current.track.details && instanceRef.current.track.details.slides && (
          <>
            <Icon
              icon='tabler:chevron-left'
              className={clsx('arrow arrow-left', {
                'arrow-disabled': currentSlide === 0
              })}
              onClick={e => e.stopPropagation() || instanceRef.current.prev()}
            />

            <Icon
              icon='tabler:chevron-right'
              className={clsx('arrow arrow-right', {
                'arrow-disabled': currentSlide === (instanceRef.current.track.details.slides.length - 1)
              })}
              onClick={e => e.stopPropagation() || instanceRef.current.next()}
            />
          </>
        )}

      </Box>

        {/* Pagination swiper-dots */}
        {loaded && instanceRef.current && instanceRef.current.track && instanceRef.current.track.details &&  instanceRef.current.track.details.slides && (
        <Box className='swiper-dots' sx={{ my: 7 }}>
          {[...Array(instanceRef.current.track.details.slides.length).keys()].map(idx => {
            return (
              <Badge
                key={idx}
                variant='dot'
                component='div'
                className={clsx({
                  active: currentSlide === idx
                })}
                onClick={() => {
                  instanceRef.current?.moveToIdx(idx)
                }}
              ></Badge>
            )
          })}
        </Box>
      )}

    </>
  );
};

export default SwiperPosts;
