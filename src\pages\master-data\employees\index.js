import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";
import NavTabEmployees from "src/@core/components/custom-components/NavTabEmployees";
import DesignationTab from "./DesignationTab";
import WorkCityTab from "./WorkCityTab";
import WorkLocationTab from "./WorkLocationTab";
import { useRBAC } from "src/pages/permission/RBACContext";

const EmployeeTabs = () => {
  const { can } = useRBAC();
  if(can('masterDataEmployees_READ')){

  return (
    <Card>
      <NavTabEmployees
        tabContent1={
          <>
            <DesignationTab />
          </>
        }
        tabContent2={
          <>
            <WorkLocationTab />
          </>
        }
        tabContent3={
          <>
            <WorkCityTab />
           
          </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default EmployeeTabs;
