import dynamic from 'next/dynamic';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const DonutChart = () => {
  const state = {
    series: [10,9,8,7,6,5,12,11,10,12], // Example data values representing the distribution of service requests
    options: {
      chart: {
        type: 'donut',
        height: 350, 
        width: '100%',  
      },
      labels: [
        'Civil Contractors', 'Electrical', 'Elevator', 'Facility Management', 
         'Pest Control', 
        'Plumbing', 'CCTV and Security Devices', 
        'Water Tank Cleaning', 'Painting','Other'
      ], // Service categories
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: '100%',  
            height: 330,  
          },
          legend: {
            position: 'bottom',
            fontSize: '10px',  // Adjust font size for better fit on mobile
          }
        }
      }],
      legend: {
        position: 'right',
        offsetY: 0,
        itemMargin: {
          horizontal: 5,
          vertical: 5
        }
      },
      title: {
        text: 'Service Providers Distribution',
        align: 'center',
        style: {
          fontSize: '14px',
          color: '#444'
        }
      },
      colors: ['#FF4560', '#00E396', '#008FFB', '#FF6D00', '#775DD0', '#FEB019', '#FF4560', '#00E396', '#008FFB', '#FF6D00', '#775DD0', '#FEB019', '#FF4560', '#00E396', '#008FFB'],
      plotOptions: {
        pie: {
          donut: {
            size: '65%' // Adjust the size of the donut hole
          }
        }
      },
    },
  };

  return (
    <div style={{ overflow: 'hidden' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="donut" width="100%" />
      </div>
    </div>
  );
};

export default DonutChart;
