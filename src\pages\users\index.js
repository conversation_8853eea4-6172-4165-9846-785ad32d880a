import React, { useState, useEffect, useContext } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Card,
  CardContent,
  Grid,
  Typography,
  Button,
  Divider,
  Menu,
  MenuItem,
  Tooltip,
  useMediaQuery,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import AddDialog from "./AddDialog";
import Icon from "src/@core/components/icon";
import DeleteDialog from "./DeleteDialog";
import { useRouter } from "next/router";
import "react-datepicker/dist/react-datepicker.css";
import CustomAvatar from "src/@core/components/mui/avatar";
import FilterCard from "./FilterCard";
import { useRBAC } from "../permission/RBACContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import { getURL } from "next/dist/shared/lib/utils";
import authConfig from "src/configs/auth";
import { useButtonAction } from "src/context/ButtonActionContext";
import UserEditDialog from "./UserEditDialog";

const Users = () => {
  const { selectDropdown, getAllUsersNewTable, getSettingsByType } =
    useContext(AuthContext);
  const { can, rbacRoles } = useRBAC();

  const [isDialogOpen, setDialogOpen] = useState(false);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const router = useRouter();
  const [employeesData, setEmployeesData] = useState(null); //Includes names of Employee.
  const [users, setUsers] = useState(null);
  const [roles, setRoles] = useState(null);
  const [isFilterCardEnabled, setIsFilterCardEnabled] = useState(false);

  const handleYesButtonClick = async () => {
    setDeleteDialogOpen(false);
  };

  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [currentRow, setCurrentRow] = useState(null);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [category,setCategory]=useState("");
  const [rowCount, setRowCount] = useState(0);

  /*****start - This code is used by the quickmenu*****/
  const { buttonClicked, setButtonClicked } = useButtonAction();

  useEffect(() => {
    if (buttonClicked) {
      // Execute the handleOpenDialog function
      setDialogOpen(true);
      setCurrentRow(null);
      // Reset buttonClicked state after handling
      setButtonClicked(false);
    }
  }, [buttonClicked, setButtonClicked]);
  /*****end - This code is used by the quickmenu*****/

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    getEmployees();
    setEditedUserAccess({});
    setOpenMenu(false);
    setCurrentRow(null);
  };

  const handleEditDialogClose = () => {
    setEditDialogOpen(false);
    setOpenMenu(false);
    setCurrentRow(null);
    fetchUserData(page, pageSize, searchKeyword);
    console.log("handleEditDialogClose called.");
  };

  const getEmployees = async () => {
    try {
      const employeesData = await selectDropdown("EMPLOYEES", null);
      setEmployeesData(employeesData?.data?.data);
    } catch (error) {
      console.error("Error fetching EMPLOYEES data:", error);
    }
  };

  useEffect(() => {
    getEmployees();
  }, []);

  useEffect(() => {
    const fetchRolesData = async () => {
      try {
        if (roles === null) {
          const rolesData = await selectDropdown("ROLES");
          setRoles(rolesData?.data?.data);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchRolesData();
  }, []);

  const fetchUserData = async (page, pageSize, searchKeyword, category) => {
    try {
      let data = { page, pageSize, searchKeyword, category };
      data.filter = false;
      const getAllUsersResponse = await getAllUsersNewTable(data);
      setUsers(getAllUsersResponse?.users);
      setRowCount(getAllUsersResponse?.rowCount);
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  useEffect(() => {

    fetchUserData(page, pageSize, searchKeyword, category);
  }, [page, pageSize, searchKeyword, category]);


  const [menu, setMenu] = useState(null);
  const [openMenu, setOpenMenu] = useState(false);

  const handleCloseMenuItems = () => {
    setMenu(null);
    setEditedUserAccess({});
    setOpenMenu(false);
  };

  const handleSearch = (newSearchKeyword, newCategory) => {
    setSearchKeyword(newSearchKeyword);
    setCategory(newCategory);
  };
  const columns = [

    { field: "name", headerName: "Name", flex: 0.69,
      minWidth: 120,
    valueGetter: (params) => {
        const row = params.row;
        return `${row.firstName} ${row.lastName}`;
      },
},
    { field: "email", headerName: "Email", flex: 1,
      minWidth: 120, },
    { field: "userCategory", headerName: "Category", flex: 1,
      minWidth: 100, },

    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.59,
      minWidth: 110,
    },
    (can("users_UPDATE") || can("users_DELETE")) && {
      field: "actions",
      headerName: "Actions",
      // flex: 0.12,
      sortable: false,
      flex: 0.43,
      minWidth: 110,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation(); // Prevent the click from bubbling up to the grid
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setOpenMenu(true);
        };

        const onEdit = () => {
          setEditDialogOpen(true);
        };

        const onDelete = () => {
          setDeleteDialogOpen(true);
        };

        return (
          <>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Tooltip title="Actions">
                <CustomAvatar
                  skin="light"
                  variant="rounded"
                  sx={{
                    mr: { xs: 2, lg: 4 },
                    width: 34,
                    height: 34,
                    cursor: "pointer",
                  }}
                  onClick={handleClickMenu}
                >
                  <Icon icon="bi:three-dots-vertical" />
                </CustomAvatar>
              </Tooltip>
              <Menu
                id="actions-menu"
                anchorEl={menu}
                keepMounted
                open={openMenu}
                onClose={handleCloseMenuItems}
              >
                {can("users_UPDATE") && (
                  <MenuItem onClick={onEdit}>Edit</MenuItem>
                )}

                {can("users_DELETE") && (
                  <MenuItem onClick={onDelete}>Delete</MenuItem>
                )}
              </Menu>
            </div>
          </>
        );
      },
    },
  ];

  const [editedUserAccess, setEditedUserAccess] = useState({});

  const getEditingUserAccess = async (userId, roleId) => {
    await axios({
      method: "get",
      url: getUrl(authConfig.roleEndpoint) + "/" + userId + "/" + roleId,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEditedUserAccess(res?.data);
      })
      .catch((err) =>
        console.error("Error while fetching the getEditingUserAccess", err)
      );
  };

  useEffect(() => {
    if (!!currentRow) {
      getEditingUserAccess(currentRow.id, currentRow.roleId);
    } else {
      setEditedUserAccess({});
    }
  }, [currentRow]);

  if (can("users_READ")) {
    return (
      <Grid>
        <FilterCard
          searchKeyword={searchKeyword}
          setSearchKeyword={setSearchKeyword}
          category={category}
          setCategory={setCategory}
          open={isFilterCardEnabled}
          onClose={() => setIsFilterCardEnabled(false)}
          handleSearch={handleSearch} 
        />
        <Card>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h5" margin={5}>
                Users
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid item xs={12} sm="auto">
                  <Tooltip title="Filter">
                    <CustomAvatar
                      skin="light"
                      variant="rounded"
                      sx={{
                        margin: isMobile ? { xs: 2, sm: 4 } : 0,
                        width: 34,
                        height: 34,
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setIsFilterCardEnabled((prev) => !prev);
                      }}
                    >
                      <Icon icon="iconamoon:funnel" />
                    </CustomAvatar>
                  </Tooltip>
                </Grid>
                <Grid item xs={12} sm="auto">
                  <Button
                    variant="contained"
                    color="primary"
                    sx={{ margin: 5 }}
                    onClick={() => {
                      setDialogOpen(true);
                      setCurrentRow(null);
                    }}
                  >
                    Add New
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Divider />
          <CardContent>
            <DataGrid
              rows={users || []}
              columns={columns}
              getRowId={(row) => row.id}
              autoHeight
              checkboxSelection
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={38}
              headerHeight={38}
              // onSelectionModelChange={handleSelection}
            />
          </CardContent>
        </Card>
        {isDialogOpen && (
          <AddDialog
            employeesData={employeesData}
            roles={roles}
            open={isDialogOpen}
            onClose={handleDialogClose}
          />
        )}
        {isEditDialogOpen && (
          <UserEditDialog
            open={isEditDialogOpen}
            onClose={handleEditDialogClose}
            currentRow={currentRow}
            defaultData={editedUserAccess}
            roles={roles}
          />
        )}
        <DeleteDialog
          open={isDeleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          handleYesButtonClick={handleYesButtonClick}
        />
      </Grid>
    );
  } else {
    return null;
  }
};

export default Users;
