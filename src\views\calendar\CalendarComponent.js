import FullCalendar from '@src/@core/styles/libs/fullcalendar/index.js';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import CalendarWrapper from 

const CalendarComponent = () => {
  return (
        <CalendarWrapper>
          <FullCalendar
            plugins={[dayGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            dayMaxEvents={2} // Limits the number of events displayed per day to 2
            eventDisplay="block"
          />
        </CalendarWrapper>
  );
};
export default CalendarComponent;
