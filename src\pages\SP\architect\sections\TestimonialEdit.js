import React, { useEffect, useState } from "react";

import { useForm, Controller } from "react-hook-form";
import {
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useContext } from "react";

const TestimonialForm = ({ data, onCancel }) => {
  const { patchArchitectAdditionalData } = useContext(AuthContext);

  const { control, handleSubmit, reset, setValue, unregister, getValues } =
    useForm();

  const [entries, setEntries] = useState([]);
  const [fieldChanged, setFieldChanged] = useState(false);

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`testimonials[${index}].name`, entry.name);
      setValue(`testimonials[${index}].designation`, entry.designation);
      setValue(`testimonials[${index}].description`, entry.description);
    });
  }, [entries, setValue]);

  useEffect(() => {
    if (data && data.testimonialsList) {
      setEntries(data.testimonialsList);
    }
  }, [data]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.testimonials || [];

    setEntries([...currentEntries, { name: "", description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);

    unregister(`testimonials[${index}].name`);
    unregister(`testimonials[${index}].designation`);
    unregister(`testimonials[${index}].description`);

    reset({
      ...getValues(),
      testimonials: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    const testimonialsData =
      data.testimonials.length > 0
        ? data.testimonials
        : [{ name: "", designation: "", description: "" }];

    const response = await patchArchitectAdditionalData(
      { testimonialsList: testimonialsData },
      () => {
        console.log("Success memberships.");
      },
      () => {
        console.error("memberships failed");
      }
    );

    onCancel();
    reset();
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Paper>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Designation</TableCell>
                <TableCell>Testimonials</TableCell>
                <TableCell>Delete</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {entries?.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Controller
                      name={`testimonials[${index}].name`}
                      control={control}
                      defaultValue={entry.name}
                      rules={{ required: "Name is required" }}
                      render={({ field, fieldState }) => (
                        <TextField
                          {...field}
                          label="Name"
                          variant="outlined"
                          fullWidth
                          error={Boolean(fieldState?.error?.message)}
                          helperText={fieldState?.error?.message || " "}
                          onChange={(e) => {
                            field.onChange(e);
                            setFieldChanged(true);
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <Controller
                      name={`testimonials[${index}].designation`}
                      control={control}
                      defaultValue={entry.designation}
                      rules={{ required: "Designation is required" }}
                      render={({ field, fieldState }) => (
                        <TextField
                          {...field}
                          label="Designation"
                          variant="outlined"
                          fullWidth
                          error={Boolean(fieldState?.error?.message)}
                          helperText={fieldState?.error?.message || " "}
                          onChange={(e) => {
                            field.onChange(e);
                            setFieldChanged(true);
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <Controller
                      name={`testimonials[${index}].description`}
                      control={control}
                      defaultValue={entry.description}
                      rules={{ required: "Testimonial is required" }}
                      render={({ field, fieldState }) => (
                        <TextField
                          {...field}
                          label="Testimonial"
                          multiline
                          rows={4}
                          variant="outlined"
                          fullWidth
                          error={Boolean(fieldState?.error?.message)}
                          helperText={fieldState?.error?.message || " "}
                          onChange={(e) => {
                            field.onChange(e);
                            setFieldChanged(true);
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => removeEntry(index)}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              <Grid style={{ display: "flex", justifyContent: "flex-start" }}>
                <Button
                  onClick={addEntry}
                  color="primary"
                  variant="contained"
                  sx={{
                    mb: { xs: 2, lg: 4 },
                    mt: { xs: 2, lg: 4 },
                  }}
                >
                  Add
                </Button>
              </Grid>

              <TableRow>
                <TableCell colSpan={4}>
                  <Grid
                    container
                    justifyContent="center"
                    sx={{
                      mt: { xs: 2, lg: 4 },
                      mb: { xs: 2, lg: 4 },
                    }}
                  >
                    <Button
                      size="medium"
                      sx={{ mr: 3 }}
                      variant="outlined"
                      color="primary"
                      onClick={() => onCancel()}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      color="primary"
                      variant="contained"
                      disabled={!fieldChanged}
                    >
                      Submit
                    </Button>
                  </Grid>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Paper>
      </form>
    </>
  );
};

export default TestimonialForm;
