// ** React Imports
import { forwardRef, useEffect, useState, useContext } from "react";

// ** MUI Imports
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";
import authConfig from "src/configs/auth";
// ** Third Party Imports
import { useForm, Controller } from "react-hook-form";
import {
  FormHelperText,
  InputLabel,
  Typography,
  Select,
  Button,
} from "@mui/material";

import MenuItem from "@mui/material/MenuItem";

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section7 = ({
  onCancel,
  formData,
  employeesData,
  createdByName,
  assignedTo,
  handleAssignedToChange,
}) => {
  //Hooks
  const auth = useAuth();

  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
    setValue,
  } = useForm({});
  const [sourceGroup, setSourceGroup] = useState(formData?.sourceGroup);
  const [leadStatusData, setLeadStatusData] = useState(null);
  const[leadPriorityData,setLeadPriorityData]=useState(null);
  const [groupData, setGroupData] = useState(null);
  const [subGroupData, setSubGroupData] = useState(null);

  const [leadStatus, setLeadStatus] = useState(formData?.leadStatus);
  const [leadPriority, setLeadPriority] = useState(formData?.leadPriority);
  const [subSourceGroup, setSubSourceGroup] = useState(
    formData?.subSourceGroup
  );
  async function submit(data) {
    console.log("Submitted data in society", data);
    console.log("assined TO", assignedTo);
    try {
      const trimmedData = Object.fromEntries(
        Object.entries(data).map(([key, value]) => [
          key,
          typeof value === "string" ? value.trim() : value,
        ])
      );
      trimmedData.assignedTo = assignedTo;
      trimmedData.sourceGroup = sourceGroup;
      trimmedData.subSourceGroup = subSourceGroup;
      trimmedData.leadStatus = leadStatus;
      trimmedData.leadPriority=leadPriority;
    
      const userUniqueId =
        formData && formData.userId !== undefined
          ? formData.userId
          : auth.user?.id;

      const response = await auth.updateEntity(trimmedData, userUniqueId);

      onCancel();
    } catch (error) {
      console.error("Error updating entity:", error);
    }
  }

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {

    if(!!authConfig) {
    getAllListValuesByListNameId(
      authConfig.leadStatusListNamesId,
      handleLeadStatusSuccess,
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.leadPriorityListNamesId,
      handleLeadPrioritySuccess,
      handleError
    );


    getAllListValuesByListNameId(
      authConfig.groupDataListNameId,
      handleGroupDataSuccess,
      handleError
    );
  }
  }, [authConfig]);

  useEffect(() => {
    if(!!authConfig){
      if(!!sourceGroup) {
        getAllListValuesByListNameId(
          sourceGroup,
          handleSubGroupDataSuccess,
          handleError
        );
      }
    }
    }, [authConfig,sourceGroup]);

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusData(data?.listValues);
  };

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityData(data?.listValues);
  };

  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };


  const handleGroupDataSuccess = (data) => {
    setGroupData(data?.listValues);
  };
  const handleSubGroupDataSuccess = (data) => {
    setSubGroupData(data?.listValues);
  };

  const handleLeadStatusChange = (event) => {
    const selectedId = event.target.value;
    setLeadStatus(selectedId);
  };

  const handleSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSourceGroup(selectedId);
  };

  const handleSubSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSubSourceGroup(selectedId);
  };

  const [createdOn, setCreatedOn] = useState(formData?.createdOn);

  return (
    <Card>
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            {formData && formData.userId !== undefined ? (
              <FormControl fullWidth>
                <InputLabel id="assigned-to">Assigned To</InputLabel>
                <Select
                  labelId="assigned-to"
                  id="assigned-to"
                  size="small"
                  value={assignedTo}
                  defaultValue={formData?.assignedTo}
                  label="Assigned To"
                  placeholder="Assigned To"
                  onChange={handleAssignedToChange}
                >
                  {employeesData
                    ?.map((data) => ({
                      id: data.id,
                      label: data.name,
                    }))
                    .map((emp) => (
                      <MenuItem key={emp.id} value={emp.id}>
                        {emp.label}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth>
                <Typography>Assigned To</Typography>
                {/* <Typography variant="h6">{assignedToName}</Typography> */}
              </FormControl>
            )}
          </Grid>
          {formData && formData.userId !== undefined && (
            <>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="lead-status">Lead Status</InputLabel>
                  <Select
                    labelId="lead-status"
                    id="lead-status"
                    size="small"
                    value={leadStatus}
                    InputLabelProps={{ shrink: true }}
                    defaultValue={formData?.leadStatus}
                    onChange={handleLeadStatusChange}
                  >
                    {leadStatusData?.map((status) => (
                      <MenuItem key={status.id} value={status.id}>
                        {status.listValue}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.leadStatus && (
                    <FormHelperText
                      id="validation-leadStatus"
                      sx={{ color: "error.main" }}
                    >
                      {errors.leadStatus.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel id="lead-priority">Lead Priority</InputLabel>
                <Select
                  labelId="lead-priority"
                  id="lead-priority"
                  size="small"
                  value={leadPriority}
                  defaultValue={formData?.leadPriority}
                  onChange={handleLeadPriorityChange}
                >
                  {leadPriorityData?.map((priority) => (
                    <MenuItem key={priority.id} value={priority.id}>
                      {priority.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.leadPriority && (
                  <FormHelperText
                    id="validation-leadPriority"
                    sx={{ color: "error.main" }}
                  >
                    {errors.leadPriority.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="source-group">Source Group</InputLabel>
                  <Select
                    labelId="source-group"
                    id="source-group"
                    size="small"
                    value={sourceGroup}
                    defaultValue={formData?.sourceGroup}
                    onChange={handleSourceGroupChange}
                  >
                    {groupData?.map((item) => (
                      <MenuItem key={item.id} value={item.id}>
                        {item.listValue}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.sourceGroup && (
                    <FormHelperText
                      id="validation-sourceGroup"
                      sx={{ color: "error.main" }}
                    >
                      {errors.sourceGroup.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="Sub Source Group">
                    Sub Source Group
                  </InputLabel>
                  <Select
                    labelId="Sub Source Group"
                    id="Sub Source Group"
                    size="small"
                    value={subSourceGroup}
                    defaultValue={formData?.subSourceGroup}
                    onChange={handleSubSourceGroupChange}
                  >
                    {subGroupData &&
                      subGroupData.map((item) => (
                        <MenuItem key={item.id} value={item.id}>
                          {item.listValue}
                        </MenuItem>
                      ))}
                  </Select>
                  {errors.subSourceGroup && (
                    <FormHelperText
                      id="validation-subSourceGroup"
                      sx={{ color: "error.main" }}
                    >
                      {errors.subSourceGroup.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                
               
                </Grid>
             
              <Grid item xs={12} sm={4} sx={{ml:2}}>
                
                <FormControl fullWidth style={{ marginTop: "16px" }}>
                <Typography style={{ marginBottom: "8px" }}>
             <strong>Created On:</strong>  {createdOn
                      ? new Date(createdOn).toLocaleDateString("en-GB")
                      : ""}
                </Typography>
              </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
              <FormControl fullWidth style={{ marginTop: "16px" }}>
                <Typography style={{ marginBottom: "8px" }}>
             <strong>Created By:</strong> {createdByName}
                </Typography>
              </FormControl>
              </Grid>

              <Grid item  xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="remarks"
                    control={control}
                    defaultValue={formData?.remarks}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        rows={4}
                        multiline
                        label="Remarks"
                        InputLabelProps={{ shrink: true }}
                        error={Boolean(errors.remarks)}
                        helperText={errors.remarks?.message}
                        aria-describedby="statusAssignmentDetails_remarks"
                        inputProps={{
                          title:'Enter any remarks'
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default Section7;
