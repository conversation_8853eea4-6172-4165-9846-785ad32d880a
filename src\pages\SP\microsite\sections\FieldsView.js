// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "../../MUITableCell";
import FieldsEdit from "./FieldEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const FieldsView = ({ data, expanded, userData }) => {
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Introduction Fields"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "1px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {userData && userData.id !== undefined && (
                      <TableRow>
                        <MUITableCell>
                          <Typography>Youtube Url</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {data?.youtubeUrl}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}
                    <TableRow>
                      <MUITableCell>
                        <Typography>Brief Profile</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.briefProfile}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <FieldsEdit
                userData={userData}
                formData={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default FieldsView;
