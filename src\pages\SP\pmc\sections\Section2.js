

// ** React Imports
import { forwardRef,useState } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import { useAuth } from 'src/hooks/useAuth'
import { Box } from '@mui/system'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'

// ** Icon Imports

import SelectProject from 'src/@core/components/custom-components/SelectProject'
import SelectMultipleBasic from 'src/@core/components/custom-components/SelectMultipleBasic'
import { toast } from 'react-hot-toast'
import SelectCategory from 'src/@core/components/custom-components/SelectCategory'

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false,
  Textarea: ''
}

const names = [
  {
    value:'LESS_THAN_5_YEARS', 
    key:'Less than 5 years'
  },
  {
    value:'_5_TO_10_YEARS',
    key:'5-10 years'
  },
  {
    value:'MORE_THAN_10_YEARS',
    key:'More than 10 years'
  }
];
const area = [
  {
    value:'ISLAND', 
    key:'Island'
  },
  {
    value:'WESTERN_SUBURB',
    key:'Western Subrub'
  },
  {
    value:'CENTRAL_SUBURB',
    key:'Central Subrub'
  },
  {
    value:'THANE',
    key:'Thane'
  },
  {
    value:'ALL',
    key:'All'
  },
  {
    value:'OTHER',
    key:'Other'
  }
];
const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const areaofOperation = ['Island', 'Western Suburb', 'Central Suburb', 'Thane', 'All', 'Other']

const areasOfExpertise = [
  'Residential',
  'Commercial',
  'Retail',
  'Industrial',
  'Religious places',
  'Financial COPMOF, Cash flows',
  'Technical Feasibility  '
]

const Section2 = ({ onCancel,formData }) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, clearErrors,formState: { errors } } = useForm();
  const [yearsOfExperience,setYearsOfExperience] = useState(formData?.yearsOfExperience)
  const [areaofOperation,setAreaofOperation] = useState(formData?.areaofOperation)


  async function submit(data) {

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value])
    );
  

    const response = await auth.updateEntity(trimmedData,() => {
      console.error("PMC Team Details failed");
    });
    onCancel();
  }
  

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name='teamSize'
              control={control}
              rules={{ required: true, pattern: /^(?:[1-9]|[1-9][0-9]|100)$/ }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type='number'
                  value={value}
                  label='Team Size'
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder='Enter team size(1-100)'
                  aria-describedby='validation-teamSize'
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.teamSize?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                This field is required
              </FormHelperText>
            )}
            {errors.teamSize?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                Please enter a valid Team Size (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
        <SelectCategory
          clearErrors={clearErrors}
            register={register} 
            id={'yearsOfExperience'} 
            label={'Years Of Experience'} 
            nameArray={names} 
            defaultValue={formData?.yearsOfExperience} 
            value={yearsOfExperience}
            onChange={(e) => setYearsOfExperience(e.target.value)}
            error={Boolean(errors.yearsOfExperience)}
            aria-describedby='validation-yearsOfExperience'
          />
              {errors.yearsOfExperience && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-yearsOfExperience'>
                  Please select years of Experience
                </FormHelperText>
              )}
          </Grid>
        <Grid item xs={12} sm={4}>
        <SelectCategory 
          clearErrors={clearErrors} 
            register={register} 
            id={'areaofOperation'}
            label={'Area of operation'}
            nameArray={area}
            defaultValue={formData?.areaofOperation}
            value={areaofOperation}
            onChange={(e) => setAreaofOperation(e.target.value)}
            error={Boolean(errors.areaofOperation)}
            aria-describedby='validation-areaofOperation'
          />
              {errors.areaofOperation && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-areaofOperation'>
                  Please select area of operation
                </FormHelperText>
              )}
        </Grid>
        {(areaofOperation === 'OTHER') && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name='otherArea'
                    control={control}
                    required={true}
                    defaultValue={formData?.otherArea}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label='Other Area of Operation'
                        placeholder='Enter area of operation'
                        InputLabelProps={{ shrink: true }}
                        error={Boolean(errors.otherArea)}
                        aria-describedby='validation-otherArea'
                      />
                    )}
                  />
                  {errors.otherArea && (
                    <FormHelperText sx={{ color: 'error.main' }} id='validation-otherArea'>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
        )}
        <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='awards'
              control={control}
              defaultValue={formData?.awards}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Awards'
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ maxLength: 500 }}                   
                  error={Boolean(errors.awards)}
                  aria-describedby='broker-validation-basic-awards'
                />
              )}
            />
            {errors.awards && (
              <FormHelperText sx={{ color: 'error.main' }} id='broker-validation-basic-awards'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button variant='outlined' color='primary' size='medium' sx={{ mr:3 }} onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Section2
