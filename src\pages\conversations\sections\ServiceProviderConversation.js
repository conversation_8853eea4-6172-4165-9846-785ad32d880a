import React, { useContext, useEffect, useState, useRef } from "react";
import Typography from "@mui/material/Typography";
import {
  Button,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Box,
  IconButton,
  Divider,
  DialogContentText,
  Tooltip
} from "@mui/material";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { useForm } from "react-hook-form";
import FormHelperText from "@mui/material/FormHelperText";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import MUITableCell from "src/pages/SP/MUITableCell";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import authConfig from "src/configs/auth";
import TextField from "@mui/material/TextField";
import CloseIcon from "@mui/icons-material/Close";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Controller } from "react-hook-form";

import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

const ServiceProviderConversation = ({
  userId,
  data,
  conversationTypeData,
  outcomeConversationData,
  targetData,
  shallRemindData,
  handleOpenConversationDialog,
  openConversationDialog,
  fetchUsers,
  setOpenConversationDialog,
}) => {
  const auth = useAuth();

  const {
    register,
    control,
    setValue,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const { getAllListValuesByListNameId,basicProfileDataAllProfiles,setBasicProfileAllProfiles } = useContext(AuthContext);

  const [selectedConversationId, setSelectedConversationId] = useState(null);
  const [selectedOutcomeConversationId, setSelectedOutcomeConversationId] =
    useState(null);
  const [targetId, setTargetId] = useState(null);
  const [shallRemindId, setShallRemindId] = useState(null);
  const [conversationDate, setConversationDate] = useState(null);
  const [nextConversationDate, setNextConversationDate] = useState(null);

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const [leadStatus, setLeadStatus] = useState(data?.leadStatus);

  const [openDialog, setOpenDialog] = useState();

  const handleLeadStatusChange = (event) => {
    const selectedId = event.target.value;
    setLeadStatus(selectedId);
  };

  const [leadPriority, setLeadPriority] = useState(data?.leadPriority);
  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };

  const handleAddDialogClose = () => {
    reset({
      typeOfConversation: [],
      conversationDate: "",
      outcomeOfConversation: [],
      nextConversationDate: "",
      nextConversationTime: "",
      target: [],
      shallRemind: [],
      remarks: "",
    });
    setSelectedConversationId(null);
    setConversationDate(null);
    setNextConversationDate(null);
    setShallRemindId(null);
    setTargetId(null);
    setSelectedOutcomeConversationId(null);
    setOpenConversationDialog(false);
    setLeadStatus(basicProfileDataAllProfiles?.leadStatus);
    setLeadPriority(basicProfileDataAllProfiles?.leadPriority);   
  };

  const handleCancel = () => {
    const message = `
      <div> 
        <h3> Changes you made may not be saved. Is it okay?</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleCancelDialog = () => {
    setOpenDialog(false);
    handleAddDialogClose();
  };

  const handleCancelClose = () => {
    setOpenDialog(false);
  };


  const handleDialogClose = () => {
    reset({
      typeOfConversation: [],
      conversationDate: "",
      outcomeOfConversation: [],
      nextConversationDate: "",
      nextConversationTime: "",
      target: [],
      shallRemind: [],
      remarks: "",
    });
    setSelectedConversationId(null);
    setConversationDate(null);
    setNextConversationDate(null);
    setShallRemindId(null);
    setTargetId(null);
    setSelectedOutcomeConversationId(null);
    setOpenConversationDialog(false);
   
  };

  const handleConversationDateChange = (date) => {
    setConversationDate(date);
    setValue("conversationDate", date, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const handleNextConversationDateChange = (date) => {
    setNextConversationDate(date);
    setValue("nextConversationDate", date, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const handleShallRemindChange = (event) => {
    const selectedId = event.target.value;
    setShallRemindId(selectedId);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedConversationId(selectedId);
  };
  const handleTargetChange = (event) => {
    const selectedId = event.target.value;
    setTargetId(selectedId);
  };

  const handleSelectOutcomeConversation = (event) => {
    const selectedId = event.target.value;
    setSelectedOutcomeConversationId(selectedId);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusData(data?.listValues);
  };

  const [leadPriorityData, setLeadPriorityData] = useState(null);

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityData(data?.listValues);
  };


  useEffect(() => {
    if(!!authConfig) {

      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        handleLeadStatusSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handleLeadPrioritySuccess,
        handleError
      );
      
    }
  }, [authConfig]);

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const apiCall = useRef(false);
  async function submit(data) {
    
     if (apiCall.current) {
      // API call is already in progress, return early
      return;
    }
    apiCall.current = true;

    const nextTimeFormatted = convertTo12HourFormat(data.nextConversationTime);   

    const fields = {
      userId: userId,
      leadStatus: leadStatus,
      leadPriority: leadPriority,
      smartSummary: data?.smartSummary,
      typeOfConversation: data?.typeOfConversation,
      conversationDate: data?.conversationDate,
      outcomeOfConversation: data?.outcomeOfConversation,
      nextConversationDate: data?.nextConversationDate,
      nextConversationTime: nextTimeFormatted,
      target: data?.target,
      shallRemind: data?.shallRemind,
      remarks: data?.remarks,
    };

    try {
      setBasicProfileAllProfiles(userId);
      await auth.conversationPost(fields, handleFailure, handleSuccess);
      fetchUsers();
      handleDialogClose();
      setSubmitSuccess(true);
   
    } catch (error) {
      console.error("Master Data Creation failed:", error);
      handleFailure();
    }
    apiCall.current = false;
  }

  function convertTo12HourFormat(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const adjustedHours = hours % 12 || 12;
    return `${adjustedHours}:${minutes < 10 ? '0' + minutes : minutes} ${period}`;
}

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Conversation added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Conversation. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    setBasicProfileAllProfiles(null);
  };



 useEffect(() => {
  setValue("smartSummary", basicProfileDataAllProfiles?.remarks);
  setValue("leadStatus",basicProfileDataAllProfiles?.leadStatus);
  setValue("leadPriority",basicProfileDataAllProfiles?.leadPriority);
}, [basicProfileDataAllProfiles?.remarks,basicProfileDataAllProfiles?.leadStatus,basicProfileDataAllProfiles?.leadPriority, setValue]);
useEffect(() => {
  const handleBeforeUnload = (event) => {
    if (openConversationDialog) {
      event.preventDefault();
      event.returnValue = "";
    }
  };

  window.addEventListener("beforeunload", handleBeforeUnload);

  return () => {
    window.removeEventListener("beforeunload", handleBeforeUnload);
  };
}, [openConversationDialog]);


  return (
    <>
      <Button
        color="primary"
        size="medium"
        variant="contained"
        onClick={handleOpenConversationDialog}
      >
        Add Conversation
      </Button>

      <Dialog
        open={openConversationDialog}
        onClose={handleCancel}
        maxWidth="lg"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "19px",
              md: "20px",
            },
            // fontWeight: "bold",
          }}
        >
          Add SP Conversation Details
        </DialogTitle>
        <DialogActions>
          <Box sx={{ position: "absolute", top: "13px", right: "40px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogActions>
        <DialogContent>
          <Grid container spacing={4}>
            <Grid item xs={12} sx={{ mt: 0 }}>
              <Typography sx={{ mt: 0 }} fontWeight={"bold"}>
                {" "}
                Conversation details
              </Typography>
              <Divider> </Divider>
            </Grid>
            <Grid item xs={12} md={2.425}>
              <FormControl fullWidth error={Boolean(errors.typeOfConversation)}>
                <InputLabel id="conversation-type-select-label">
                  Type
                </InputLabel>
                <Select
                  labelId="conversation-type-select-label"
                  id="conversation-type-select"
                  {...register("typeOfConversation", {
                    required: "Conversation Type is required",
                  })}
                  size="small"
                  value={selectedConversationId}
                  label="Conversation Type"
                  onChange={handleSelectChange}
                  defaultValue={""}
                >
                  {conversationTypeData?.map((conversation) => (
                    <MenuItem key={conversation.id} value={conversation.id}>
                      {conversation.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.typeOfConversation && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-typeOfConversation"
                  >
                    {errors.typeOfConversation.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}>
              <FormControl>

              <Controller
                name="conversationDate"
                control={control}
                defaultValue={new Date().toISOString().split('T')[0]}
                rules={{ required: "Date is required" }} // Add validation rules here
                render={({ field }) => (
                      <TextField
                        label="date"
                        {...field}
                        fullWidth
                        size="small"
                        type="date"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={Boolean(errors.conversationDate)}
                        helperText={errors.conversationDate?.message}
                      />
                )}
              />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}>
              <FormControl
                fullWidth
                error={Boolean(errors.outcomeOfConversation)}
              >
                <InputLabel id="outcome-conversation-type-select-label">
                  OutCome
                </InputLabel>
                <Select
                  labelId="outcome-conversation-type-select-label"
                  id="outcome-conversation-type-select"
                  {...register("outcomeOfConversation", {
                    required: "Outcome is required",
                  })}
                  size="small"
                  value={selectedOutcomeConversationId}
                  label="OutCome Of Conversation"
                  defaultValue={""}
                  onChange={handleSelectOutcomeConversation}
                >
                  {outcomeConversationData?.map((conversation) => (
                    <MenuItem key={conversation.id} value={conversation.id}>
                      {conversation.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.outcomeOfConversation && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-outcomeOfConversation"
                  >
                    {errors.outcomeOfConversation.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}></Grid>

            <Grid item xs={12} md={2.425}>
              <FormControl fullWidth>
                <Controller
                  name="nextConversationTime"
                  control={control}
                  defaultValue=""
                  rules={{ required: "Next Conversation Time is required" }} 
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Next Conversation Time"
                      type="time" 
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.nextConversationTime)}
                      helperText={errors.nextConversationTime?.message}
                      aria-describedby="nextConversationTime"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}>
              <FormControl >
                <Controller
                  name="nextConversationDate"
                  control={control}
                  defaultValue=""
                  rules={{ required: "Next Conversation Date is required" }} 
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      fullWidth
                      label="Next Conversation Date"
                      type="date" 
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.nextConversationDate)}
                      helperText={errors.nextConversationDate?.message}
                      aria-describedby="nextConversationDate"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2.425}>
              <FormControl fullWidth error={Boolean(errors.target)}>
                <InputLabel id="target-select-label">Target</InputLabel>
                <Select
                  labelId="target-select-label"
                  id="target-select"
                  {...register("target", {
                    required: "Target is required",
                  })}
                  size="small"
                  value={targetId}
                  label="Target"
                  onChange={handleTargetChange}
                  defaultValue={""}
                >
                  {targetData?.map((conversation) => (
                    <MenuItem key={conversation.id} value={conversation.id}>
                      {conversation.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.target && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-target"
                  >
                    {errors.target.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}>
              <FormControl fullWidth error={Boolean(errors.shallRemind)}>
                <InputLabel id="shall-remind-select-label">
                  Shall Remind
                </InputLabel>
                <Tooltip title="Select whether you want to remind">
  <Select
    labelId="shall-remind-select-label"
    id="shall-remind-select"
    {...register("shallRemind", {
      required: "Shall Remind is required",
    })}
    size="small"
    value={shallRemindId}
    label="Shall Remind"
    onChange={handleShallRemindChange}
  >
    {shallRemindData?.map((conversation) => (
      <MenuItem key={conversation.id} value={conversation.id}>
        {conversation.listValue}
      </MenuItem>
    ))}
  </Select>
</Tooltip>
                {errors.shallRemind && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-shallRemind"
                  >
                    {errors.shallRemind.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="remarks"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Comments"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.remarks)}
                      helperText={errors.remarks?.message}
                      aria-describedby="statusAssignmentDetails_remarks"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sx={{ mt: 4 }}>
              <Typography sx={{ mt: 0 }} fontWeight={"bold"}>
                {" "}
                Do you want to update - Basic Profile details
              </Typography>
              <Divider> </Divider>
            </Grid>

            <Grid item xs={12} md={2.425}>
              {/* Lead Status Field */}
              <FormControl fullWidth>
                <InputLabel id="lead-status">Lead Status</InputLabel>
                <Select
                  labelId="lead-status"
                  id="lead-status"
                  size="small"
                  value={leadStatus}
                  defaultValue={basicProfileDataAllProfiles?.leadStatus}
                  onChange={handleLeadStatusChange}
                >
                  {leadStatusData?.map((status) => (
                    <MenuItem key={status.id} value={status.id}>
                      {status.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.leadStatus && (
                  <FormHelperText
                    id="validation-leadStatus"
                    sx={{ color: "error.main" }}
                  >
                    {errors.leadStatus.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2.425}>
              {/* Lead Priority Field */}
              <FormControl fullWidth>
                <InputLabel id="lead-status">Lead Priority</InputLabel>
                <Select
                  labelId="lead-priority"
                  id="lead-priority"
                  size="small"
                  value={leadPriority}
                  defaultValue={basicProfileDataAllProfiles?.leadPriority}
                  onChange={handleLeadPriorityChange}
                >
                  {leadPriorityData?.map((status) => (
                    <MenuItem key={status.id} value={status.id}>
                      {status.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.leadPriority && (
                  <FormHelperText
                    id="validation-leadPriority"
                    sx={{ color: "error.main" }}
                  >
                    {errors.leadPriority.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="smartSummary"
                  control={control}
                  defaultValue={basicProfileDataAllProfiles?.remarks}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Smart Summary"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.smartSummary)}
                      helperText={errors.smartSummary?.message}
                      aria-describedby="statusAssignmentDetails_smartSummary"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={handleCancel}
            variant="outlined"
            color="primary"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            onClick={handleSubmit(submit)}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      <Dialog open={openDialog} onClose={handleCancelClose} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description" PaperProps={{ sx: { p: (theme) => `${theme.spacing(2.5)} !important`, backgroundColor: (theme) => theme.palette.primary.background } }}>
        <Box sx={{ width: "100%", borderRadius: 1, textAlign: "center", border: (theme) => `1px solid ${theme.palette.divider}`, borderColor: "primary.main" }}>
          <DialogContent>
            <DialogContentText id="alert-dialog-description" color="primary.main">
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" onClick={handleCancelDialog} sx={{ margin: "auto"}}>Discard Changes</Button>
            <Button variant="contained" onClick={handleCancelClose} sx={{ margin: "auto"}}>Return to Form</Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ServiceProviderConversation;
