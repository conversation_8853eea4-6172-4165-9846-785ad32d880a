// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

import { useTheme } from "@emotion/react";

// ** Demo
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import Section1 from "./Section1";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const BasicProfileCA = ({data,expanded}) => {

  const { can } = useRBAC();

  const [state, setState] = useState('view')


  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState('view')
  }
    // ** Hook
    const theme = useTheme()

    // const [caDetails, setCaDetails] = useState({
    //   name: "",
    //   companyName: "",
    //   address: "",
    //   mobileNumber: "",
    //   email: "",
    //   websiteUrl:"",
    //   teamSize: "",
    //   yearsOfExperience:"",
    //   briefProfile:"",
    //   awards:""
    // });


    return (
        <>
         {/* {can('ca_basicProfile_READ') &&  */}
             <AccordionBasic
              id={'panel-header-2'}
              ariaControls={'panel-content-2'}
              heading={'Basic Profile'}
              body={
                <>
                  {state === 'view' && (
                    
                        <TableContainer
                          sx={{ padding:'4px 6px' }}
                          className='tableBody'
                         // onClick={can('ca_basicProfile_UPDATE') ? viewClick : null}
                          onClick={viewClick}
                        >
                          <Table>
                            <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Name:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.name}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Company Name:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.companyName}</Typography>
                                </MUITableCell>
                              </TableRow>
                             
                              <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>System Code:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.systemCode}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Contact Number:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.mobileNumber}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Email:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.email}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Address:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.address}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Website Url:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.websiteUrl}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Team size:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.teamSize}</Typography>
                                </MUITableCell>
                              </TableRow>
                              
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Awards:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.awards}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Brief Profile:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.briefProfile}</Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Year Of Experience:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className='data-field' >{data?.yearsOfExperience}</Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      
                  )}

                  {state === 'edit' && <Section1 formData={data} onCancel={editClick} />}
                </>
              }
              expanded={expanded}
            />
          {/* } */}
        </>
    );

}
export default BasicProfileCA;
