// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";
// ** Third Party Imports
import { Controller, useForm } from "react-hook-form";
// ** Icon Imports
import {
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Switch,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useContext, useEffect } from "react";
import Icon from "src/@core/components/icon";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";
import { position } from "stylis";

const StatisticsEdit = ({ onCancel, setData, names, formData, fetchUsers, fetchAll }) => {
  const [isActive, setIsActive] = useState(names?.isActive);
  const [updatedServices, setUpdatedServices] = useState([]);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [subService, setSubService] = useState("");
  const [subServices, setSubServices] = useState([]);
  const [subServiceId, setSubServiceId] = useState("");
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteServiceId, setDeleteServiceId] = useState(null);

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  // Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    formState: { errors },
  } = useForm();

  const handleOpenDialog = () => {
    setOpenCreateDialog(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
    setSubServices([]);
    setSubServiceId("");
  };

  async function submitCreate(data) {
    let successCount = 0; // Track the number of successful API calls
    const existingSubServices = []; // Array to store names of existing sub-services

    for (const value of subServices) {
      const fields = {
        name: value,
        serviceListValueId: formData?.id,
        isStatistics: true,
        uiComponentsId: authConfig.uiComponentId
      };

      try {
        const response = await auth.postQuestions(fields);

        if (response.id) {
          successCount++;
        } else if (response.message) {
          existingSubServices.push(value);
        } else {
          successCount++;
        }
        await fetchAll(formData?.id);
      } catch (error) {
        console.error("Error in creating questions:", error);
      }
    }

    let message;
    if (successCount > 0) {
      message = `<div><h3>${successCount} Question(s) added successfully.</h3></div>`;
    } else {
      if (existingSubServices.length > 0) {
        const existingSubServicesStr = existingSubServices.join(", ");
        message = `<div><h3> Failed to add Question(s) <br><bold>${existingSubServicesStr}</bold><br> already exist in the database!! </h3></div>`;
      } else {
        message = `<div><h3> Failed to add Question(s). Please try again</h3></div>`;
      }
    }

    // Show the dialog with the combined message
    setDialogMessage(message);
    setOpenDialogContent(true);

    setOpenCreateDialog(false);
    setSubServices([]);
    setSubServiceId("");

    fetchUsers();
  }

  async function submit(data) {
    for (const value of updatedServices) {
      const fields = {
        id: value.id, // Using the id from the value object
        name: value.name, // Using the name from the value object
        isActive: value.isActive,
        isStatistics: true
      };
      try {
        const response = await auth.patchListNames(fields);
      } catch (error) {
        console.error("Master Data Details failed for", value.name, error);
      }
    }
    fetchUsers();
    onCancel();
  }

  async function handleDeleteSubServiceConfirmed() {
    if (deleteServiceId !== null) {
      const deletedService = names?.find((service) => service?.id === deleteServiceId);

      setUpdatedServices((prevServices) => [...prevServices, { ...deletedService, isActive: false }]);

      const updatedNames = names.map((service) => {
        if (service.id === deleteServiceId) {
          return { ...service, isActive: false };
        }
        return service;
      });

      setData((prevData) => {
        const newSubServices = prevData?.listNames?.map((subService) => {
          if (subService?.id === deleteServiceId) {
            return { ...subService, isActive: false };
          }
          return subService;
        });

        return {
          ...prevData,
          listNames: newSubServices,
        };
      });

      fetchUsers();
    }
    setOpenDeleteDialog(false);
    setDeleteServiceId(null);
  }

  const handleSubServiceChange = (valueId, newValue) => {
    const updatedServiceIndex = updatedServices.findIndex(
      (service) => service?.id === valueId
    );
    if (updatedServiceIndex !== -1) {
      setUpdatedServices((prevServices) => {
        const updatedServicesCopy = [...prevServices];
        updatedServicesCopy[updatedServiceIndex] = {
          ...updatedServicesCopy[updatedServiceIndex],
          name: newValue,
        };
        return updatedServicesCopy;
      });
    } else {
      const serviceToUpdate = names.find(
        (service) => service?.id === valueId
      );
      if (serviceToUpdate) {
        setUpdatedServices((prevServices) => [
          ...prevServices,
          { ...serviceToUpdate, name: newValue },
        ]);
      }
    }
    setData((prevState) => ({
      ...prevState,
      listNames: prevState.listNames.map((label) =>
        label.id === valueId ? { ...label, name: newValue } : label
      ),
    }));
  };

  const handleAddItem = () => {
    if (subService.trim() !== "") {
      const lowerCaseSubService = subService.toLowerCase(); // Convert to lowercase
      if (
        !subServices.some(
          (type) => type.toLowerCase() === lowerCaseSubService
        )
      ) {
        setSubServices([...subServices, subService]); // Add to the list
      }
      setSubService("");
    }
  };

  const handleDeleteItem = (indexToDelete) => {
    setSubServices(subServices.filter((_, index) => index !== indexToDelete));
  };

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button variant="contained" onClick={handleOpenDialog}>
              Add New Question
            </Button>
          </Box>
        </Grid>

        <Grid item xs={12} sm={12}>
          <FormControl fullWidth>
            {formData?.listNames?.map((label) => {
              if (label?.isActive) {
                return (
                  <div key={label.id} style={{ display: "flex", alignItems: "center" }}>
                    <TextField
                      value={label?.name}
                      onChange={(e) => handleSubServiceChange(label?.id, e.target.value)}
                      fullWidth
                      margin="normal"
                      inputProps={{ maxLength: 30 }}
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            sx={{ p: 0, width: 26, height: 26 }}
                            size="small"
                            color="error"
                            onClick={() => {
                              setOpenDeleteDialog(true);
                              setDeleteServiceId(label.id);
                            }}
                          >
                            <Icon icon="tabler:trash" />
                          </IconButton>
                        ),
                      }}
                    />
                  </div>
                );
              }
              return null;
            })}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button size="medium" sx={{ mr: 3 }} variant="outlined" color="primary" onClick={() => onCancel()}>
              Cancel
            </Button>
            <Button size="medium" type="submit" variant="contained" onClick={handleSubmit(submit)}>
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
      <Dialog
        fullWidth
        maxWidth="md"
        scroll="paper"
        open={openCreateDialog}
        onClose={handleCloseCreateDialog}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start"},
            fontSize: { xs: 19, md: 20  },
          }}
          textAlign={"center"}
        >
          Add {formData?.name} questions
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleCloseCreateDialog}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: '#66BB6A',
                  transition: 'background 0.5s ease, transform 0.5s ease',
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1.125rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(10, 8)} !important`,
            "@media (max-width:900px)": {
              p: (theme) => `${theme.spacing(8, 6)} !important`,
            },
          }}
        >
          <Grid container spacing={4} alignItems={"center"}>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      size="small"
                      {...field}
                      label="Question"
                      value={subService}
                      onChange={(e) => setSubService(e.target.value)}
                      placeholder="Enter question"
                      aria-describedby="Section-name"
                      inputProps={{ maxLength: 30 }}
                      helperText={errors.name?.message || ""}
                      error={Boolean(errors.name)}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <Button
                variant="contained"
                onClick={handleSubmit(handleAddItem)}
                style={{
                  width: "50px",
                  height: "40px",
                }}
              >
                Add
              </Button>
            </Grid>
            <Grid item xs={12} sm={12}>
              <TableContainer
                component={Card}
                sx={{
                  pt: { xs: 2, md: 3 },
                  px: { xs: 2, md: 3 },
                  width: { xs: "77%" },
                }}
              >
                <Table size="small">
                  <TableHead sx={{ whiteSpace: "nowrap" }}>
                    <TableRow>
                      <MUITableCell sx={{ fontWeight: "bold", noWrap: "nowrap" }}>
                        Question
                      </MUITableCell>
                      <MUITableCell style={{ textAlign: "right" }} sx={{ fontWeight: "bold" }}>
                        Actions
                      </MUITableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subServices?.map((item, index) => (
                      <TableRow key={index}>
                        <MUITableCell>{item}</MUITableCell>
                        <MUITableCell style={{ textAlign: "right" }}>
                          <IconButton
                            sx={{ p: 0, width: 26, height: 26 }}
                            size="small"
                            color="error"
                            onClick={() => handleDeleteItem(index)}
                          >
                            <Icon icon="tabler:trash" />
                          </IconButton>
                        </MUITableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCloseCreateDialog}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submitCreate)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText id="alert-dialog-description" color="primary.main">
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Are you sure you want to delete this question?"}
        </DialogTitle>
        <DialogActions sx={{ justifyContent: "center" }}>
          <Button 
            onClick={handleDeleteSubServiceConfirmed} 
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            autoFocus
          >
            Yes
          </Button>
          <Button 
            onClick={() => setOpenDeleteDialog(false)} 
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
          >
            No
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StatisticsEdit;
