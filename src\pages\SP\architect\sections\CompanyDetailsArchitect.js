// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

import { useTheme } from "@emotion/react";

// ** Demo
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import Section1 from "./Section1";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "../../MUITableCell";

import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const CompanyDetailsArchitect = ({ data, expanded }) => {
  const { can } = useRBAC();

  // ** Hook
  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  return (
    <>
      {/* {can('architect_companyDetails_READ') &&  */}
      <AccordionBasic
        id={"basic-details-1"}
        ariaControls={"basic-details-1"}
        heading={"Company details"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                //  onClick={can('architect_companyDetails_UPDATE') ? viewClick : null}
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Company:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.companyName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>System Code:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.systemCode}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Address: </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.address}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Contact No:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.mobileNumber}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Email Id:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.email}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Website URL: </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.websiteUrl}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.location}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <Section1 formData={data} onCancel={editClick} />
            )}
          </>
        }
        expanded={expanded}
      />
      {/* } */}
    </>
  );
};
export default CompanyDetailsArchitect;
