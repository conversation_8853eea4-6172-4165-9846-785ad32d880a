import {
  Box,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  InputAdornment,
  Menu,
  MenuItem,
  Tooltip,
  FormControlLabel,
  Switch,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
  Typography
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from 'src/@core/components/mui/chip';
import DeleteServiceDialog from "./DeleteServiceDialog";
import ServiceDataView from "./ServiceDataView";
import SearchIcon from "@mui/icons-material/Search";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { format, formatDistanceToNow } from "date-fns";
import UpdateServiceDialog from "./UpdateServiceDialog";

const userStatusObj = {
  true: 'Active',
  false: 'InActive'
}

const Services = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();
  const { servicesData, setServicesData, servicesDataDetails } = useContext(AuthContext);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [5, 10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);
  const [keyword, setKeyword] = useState("");
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    }
  });

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenuAnchorEl(null);
    setSelectedRow(null);
  };

  const handleMenuOpen = (event, row) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedRow(null);
  };

  async function submit(data) {  
    const fields = {
      name: data?.name.trim(),
      listNamesId:authConfig.allServicesListNameId,
    };
    try {
      const response = await auth.postService(
        fields,
        handleFailure,
        handleSuccess
      );
      reset();
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    setOpenDialog(false);
    reset();
    fetchUsers(page, pageSize,searchKeyword);
  }

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3> Service added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if(err.response.status == 400){
      message = `
    <div>
      <h3>Service already exists!</h3>
    </div>
  `;
    }else{
      message = `
    <div> 
      <h3> Failed to Add Service. Please try again later.</h3>
    </div>
  `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchUsers(page,pageSize,searchKeyword)
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.servicesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      getAllServices:true,
      getAllZones:false
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.listValuesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize,searchKeyword);
  };

  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchUsers(page,pageSize,searchKeyword);
  }

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || 'Unknown';
  }

  const columns = [
    { field: "name", headerName: "Name", flex: 1.5, minWidth: 120 },
    { 
      field: "createdOn", 
      headerName: "Created on", 
      flex: 1.5, 
      minWidth: 100, 
      renderCell: ({ row }) => {
        const formattedDate = format(new Date(row.createdOn), 'dd-MM-yyyy hh:mm a');
        const timeAgo = formatDistanceToNow(new Date(row.createdOn));
        return (
          <Tooltip title={`Created ${timeAgo} ago`}>
            <span>{formattedDate}</span>
          </Tooltip>
        );
      }
    },
    { 
      field: "updatedOn", 
      headerName: "Updated on", 
      flex: 1.5, 
      minWidth: 100, 
      renderCell: ({ row }) => {
        const formattedDate = format(new Date(row.updatedOn), 'dd-MM-yyyy hh:mm a');
        const timeAgo = formatDistanceToNow(new Date(row.updatedOn), { addSuffix: true });
        return (
          <Tooltip title={`Updated ${timeAgo}`}>
            <span>{formattedDate}</span>
          </Tooltip>
        );
      }
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.9,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: 'capitalize' }}
          />
        )
      }
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        return (
          <>
            <IconButton onClick={(event) => handleMenuOpen(event, params.row)}>
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              anchorEl={menuAnchorEl}
              open={Boolean(menuAnchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem
                onClick={() => {
                  handleCloseMenuItems();
                  setOpenDialog(true);
                  setServicesData({
                    ...servicesData,
                    id: selectedRow.id,
                  });
                }}
              >
                <Icon icon="iconamoon:edit" /> Edit
              </MenuItem>
              {selectedRow?.isActive ? (
                <MenuItem
                  onClick={() => {
                    handleCloseMenuItems();
                    setOpenDeleteDialog(true);
                    setCurrentRow(selectedRow);
                  }}
                >
                  <Icon icon="iconamoon:trash" /> Deactivate
                </MenuItem>
              ) : (
                <MenuItem
                  onClick={() => {
                    handleCloseMenuItems();
                    setOpenUpdateDialog(true);
                    setCurrentRow(selectedRow);
                  }}
                >
                  <Icon icon="mdi:check-circle" /> Activate
                </MenuItem>
              )}
            </Menu>
          </>
        );
      },
    },
  ];
  
  

  return (
    <>
      <Grid>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">
                  {'List of Services'}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid container spacing={2} alignItems="center" justifyContent="flex-end">
                  <Grid item xs={12} sm={7} md={4} lg={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            placeholder="Search by service"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                                fetchUsers(page, pageSize, searchKeyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                height: "40px",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                      fetchUsers(
                                        page,
                                        pageSize,
                                        searchKeyword
                                      );
                                    }}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={7} md={4.8} lg={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="name"
                        control={control}
                        rules={{ required: 'Service name is required' }} 
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            size='small'
                            InputLabelProps={{ shrink: true }}
                            placeholder={'Enter service name to add'}
                            error={Boolean(errors.name)}
                            helperText={errors.name?.message}
                            aria-describedby="Section1-name"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm="auto" md="auto" lg="auto">
                    <Button variant="contained" onClick={handleSubmit(submit)}>
                      Add Service
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"md"}>
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 15, md: 21 },
              }}
              textAlign={"center"}
            >
              Edit Service Details
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseDialog}
                  sx={{
                    p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: '#66BB6A',
                      transition: 'background 0.5s ease, transform 0.5s ease',                       
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1.125rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
              <ServiceDataView
                data={servicesDataDetails}
                expanded={expanded}
                onCancel={handleCloseDialog}
                fetchUsers={fetchUsers}
              />
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "center",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleCloseDialog}
              >
                Close
              </Button>             
            </DialogActions>
          </Dialog>
          <Divider />
          <CardContent>
            <div style={{ height: 380, width: "100%", overflow: "auto" }}>    
              <DataGrid
                rows={userList}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={40}
                headerHeight={40} 
              />
            </div>
          </CardContent>
          <Divider />
          <DeleteServiceDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />

<UpdateServiceDialog
            open={openUpdateDialog}
            onClose={handleCloseUpdateDialog}
            data={currentRow}
          />
        </Card>
      </Grid>
      <Divider/>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default Services;
