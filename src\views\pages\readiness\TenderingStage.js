// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";
import { FormControl, Radio, RadioGroup } from "@mui/material";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
const TenderingStage = ({
  setNextActive,
  posts,
  handleTenderingDocumentsChange,
  handlePreQualificationOfDevelopersChange,
  handleScrutinizationOfTenderDocumentsChange,
  handleAppointmentOfDevelopersChange,
  handleSigningOfDaDmChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  const [value, setValue] = useState("");

  // ** Hook
  const theme = useTheme();
  const {
    settings: { direction },
  } = useSettings();
  const [tenderingDocs, setTenderingDocs] = useState(
    defaultData?.hasTenderingDocuments
  );
  const [preQualification, setPreQualification] = useState(
    defaultData?.hasPreQualificationOfDevelopers
  );
  const [scrutinization, setScrutinization] = useState(
    defaultData?.hasScrutinizationOfTenderDocuments
  );
  const [appointment, setAppointment] = useState(
    defaultData?.hasAppointmentOfDevelopers
  );
  const [signingDA, setSigningDA] = useState(defaultData?.hasSigningOfDaOrDm);
  const [tendering, setTendering] = useState([]);

  const handleChange = (event) => {
    const { name, value } = event.target;

    switch (name) {
      case "tendering-docs":
        setTenderingDocs(value);
        handleTenderingDocumentsChange(value);
        break;
      case "pre-qualification":
        setPreQualification(value);
        handlePreQualificationOfDevelopersChange(value);
        break;
      case "scrutinization":
        setScrutinization(value);
        handleScrutinizationOfTenderDocumentsChange(value);
        break;
      case "appointment":
        setAppointment(value);
        handleAppointmentOfDevelopersChange(value);
        break;
      case "signing-da":
        setSigningDA(value);
        handleSigningOfDaDmChange(value);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setTendering(data);
    }
  }, [posts]);

  useEffect(() => {
    if (
      tenderingDocs &&
      preQualification &&
      scrutinization &&
      appointment &&
      signingDA && name && email && contactNumber && societyName
    ) {
      setNextActive(true);
    }else {
      setNextActive(false);
    }
  }, [tenderingDocs, preQualification, scrutinization, appointment, signingDA,name,email,contactNumber,societyName]);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 2,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}>
                Have the following processes are completed ?
              </Typography>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mt: 3, width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              a. Tendering Documents
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={tenderingDocs}
                  name="tendering-docs"
                  onChange={handleChange}
                  aria-label="tendering-docs"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='tendering-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          Yes
                        </Typography>
                      }
                      checked={tenderingDocs === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='tendering-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          No
                        </Typography>
                      }
                      checked={tenderingDocs === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              b. Pre Qualification of Developers
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={preQualification}
                  name="pre-qualification"
                  onChange={handleChange}
                  aria-label="pre-qualification"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='qualification-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={preQualification === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio  id='qualification-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={preQualification === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              c. Scrutinization of Tender Documents
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={scrutinization}
                  name="scrutinization"
                  onChange={handleChange}
                  aria-label="scrutinization"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='scrutinization-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={scrutinization === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='scrutinization-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={scrutinization === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              d. Appointment of Developers
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={appointment}
                  name="appointment"
                  onChange={handleChange}
                  aria-label="appointment"
                >
                  <Box display="flex">
                    <FormControlLabel 
                      value="Yes"
                      control={<Radio id='appointment-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={appointment === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='appointment-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          No
                        </Typography>
                      }
                      checked={appointment === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              e. Signing of DA/DM
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={signingDA}
                  name="signing-da"
                  onChange={handleChange}
                  aria-label="signing-da"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio  id='signing-da-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                          Yes
                        </Typography>
                      }
                      checked={signingDA === "Yes"}
                    />
                    <FormControlLabel  
                      value="No"
                      control={<Radio id='signing-da-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important',transform: 'scale(0.8)' }}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold",fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          No
                        </Typography>
                      }
                      checked={signingDA === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          {tendering.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(2)} !important`,
                  mb: `${theme.spacing(2)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {tendering.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={tendering} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default TenderingStage;
