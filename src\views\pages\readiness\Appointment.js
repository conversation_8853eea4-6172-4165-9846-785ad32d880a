// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";
import { FormControl, Radio, RadioGroup } from "@mui/material";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
const Appointment = ({
  setNextActive,
  posts,
  handleHaveYouAppointedChange,
  contactNumber,
  handleHaveYouAppointedAdvocateChange,
  handleHaveYouAppointedCaChange,
  handleHaveYouAppointedPmcChange,
  email,
  societyName,
  name,
  defaultData,
}) => {
  // ** State
  const [haveYouAppointed, setHaveYouAppointed] = useState(
    defaultData?.haveAppointedAnyArchitectOrPmc
  );

  const [haveYouAppointedAdvocate, setHaveYouAppointedAdvocate] = useState(
    defaultData?.haveAppointedAnyAdvocate
  );

  const [haveYouAppointedCa, setHaveYouAppointedCa] = useState(
    defaultData?.haveAppointedAnyCa
  );

  const [haveYouAppointedPmc, setHaveYouAppointedPmc] = useState(
    defaultData?.haveAppointedAnyPmc
  );
  const {
    settings: { direction },
  } = useSettings();

  const [appointment, setAppointment] = useState([]);

  const handleChange = (event) => {
    const { name, value } = event.target;

    switch (name) {
      case "Architect":
        setHaveYouAppointed(value);
        handleHaveYouAppointedChange(value);
        break;
      case "Advocate":
        setHaveYouAppointedAdvocate(value);
        handleHaveYouAppointedAdvocateChange(value);
        break;
      case "Ca":
        setHaveYouAppointedCa(value);
        handleHaveYouAppointedCaChange(value);
        break;
      case "Pmc":
        setHaveYouAppointedPmc(value);
        handleHaveYouAppointedPmcChange(value);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setAppointment(data);

    }
  }, [posts]);

  useEffect(() => {
    if (
      haveYouAppointed &&
      email &&
      contactNumber &&
      name &&
      societyName &&
      haveYouAppointedAdvocate &&
      haveYouAppointedCa &&
      haveYouAppointedPmc
    ) {
      setNextActive(true);
    } else {
      setNextActive(false);
    }
  }, [
    haveYouAppointed,
    email,
    contactNumber,
    name,
    societyName,
    haveYouAppointedAdvocate,
    haveYouAppointedCa,
    haveYouAppointedPmc,
  ]);

  const theme = useTheme();

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 5,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h5"
                sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}
              >
                Have you appointed?
              </Typography>
            </Box>

            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{ mt: {xs:'0 !important' ,lg:2}, width: { md: "65%" } }}
            >
              <Typography
                variant="h6"
                sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
              >
                a. Architect
              </Typography>
              <Box display="flex" alignItems="center">
                <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                  <RadioGroup
                    value={haveYouAppointed}
                    name="Architect"
                    onChange={handleChange}
                    aria-label="simple-radio"
                  >
                    <Box display="flex">
                      <FormControlLabel
                        value="Yes"
                        control={<Radio id='architect-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                            Yes
                          </Typography>
                        }
                        checked={haveYouAppointed === "Yes"}
                      />
                      <FormControlLabel
                        value="No"
                        control={<Radio id='architect-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            No
                          </Typography>
                        }
                        checked={haveYouAppointed === "No"}
                      />
                    </Box>
                  </RadioGroup>
                </FormControl>
              </Box>
            </Box>

            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{  mt: {xs:'0 !important' ,lg:2}, width: { md: "65%" } }}
            >
              <Typography
                variant="h6"
                sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
              >
                b. PMC
              </Typography>
              <Box display="flex" alignItems="center">
                <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                  <RadioGroup
                    value={haveYouAppointedPmc}
                    name="Pmc"
                    onChange={handleChange}
                    aria-label="simple-radio"
                  >
                    <Box display="flex">
                      <FormControlLabel 
                        value="Yes"
                        control={<Radio id='Pmc-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            Yes
                          </Typography>
                        }
                        checked={haveYouAppointedPmc === "Yes"}
                      />
                      <FormControlLabel
                        value="No"
                        control={<Radio id='Pmc-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            No
                          </Typography>
                        }
                        checked={haveYouAppointedPmc === "No"}
                      />
                    </Box>
                  </RadioGroup>
                </FormControl>
              </Box>
            </Box>

            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{ mt: {xs:'0 !important' ,lg:2}, width: { md: "65%" } }}
            >
              <Typography
                variant="h6"
                sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'} }}
              >
                c. Advocate
              </Typography>
              <Box display="flex" alignItems="center">
                <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                  <RadioGroup
                    value={haveYouAppointedAdvocate}
                    name="Advocate"
                    onChange={handleChange}
                    aria-label="simple-radio"
                  >
                    <Box display="flex">
                      <FormControlLabel
                        value="Yes"
                        control={<Radio id='advocate-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            Yes
                          </Typography>
                        }
                        checked={haveYouAppointedAdvocate === "Yes"}
                      />
                      <FormControlLabel
                        value="No"
                        control={<Radio  id='advocate-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            No
                          </Typography>
                        }
                        checked={haveYouAppointedAdvocate === "No"}
                      />
                    </Box>
                  </RadioGroup>
                </FormControl>
              </Box>
            </Box>

            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{ mt: {xs:'0 !important' ,lg:2}, width: { md: "65%" } }}
            >
              <Typography
                variant="h6"
                sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'} }}
              >
                d. CA
              </Typography>
              <Box display="flex" alignItems="center">
                <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                  <RadioGroup
                    value={haveYouAppointedCa}
                    name="Ca"
                    onChange={handleChange}
                    aria-label="simple-radio"
                  >
                    <Box display="flex">
                      <FormControlLabel 
                        value="Yes"
                        control={<Radio id='ca-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            Yes
                          </Typography>
                        }
                        checked={haveYouAppointedCa === "Yes"}
                      />
                      <FormControlLabel 
                        value="No"
                        control={<Radio id='ca-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                        label={
                          <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                            No
                          </Typography>
                        }
                        checked={haveYouAppointedCa === "No"}
                      />
                    </Box>
                  </RadioGroup>
                </FormControl>
              </Box>
            </Box>
          </Box>
          {/* <Divider sx={{ mt: `${theme.spacing(12.25)} !important`, mb: `${theme.spacing(3.25)} !important`, }} /> */}
          <Box>
            {(!name || !societyName || !contactNumber) && (
              <Typography
                variant="body1"
                sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
              >
                Please fill out contact details to move forward.
              </Typography>
            )}
            {appointment.length > 0 && (
              <Typography
                variant="body1"
                sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
              >
                <Divider
                  sx={{
                    mt: `${theme.spacing(20)} !important`,
                    mb: `${theme.spacing(3)} !important`,
                  }}
                />
                Review below articles for more info
              </Typography>
            )}
          </Box>
        </Grid>
      </Grid>
      {appointment.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={appointment} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default Appointment;
