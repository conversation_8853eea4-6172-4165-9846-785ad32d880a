import React, { useState, useContext } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { useMediaQuery, useTheme } from "@mui/material";
import {
  Button,
  Divider,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
  TableContainer,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";

const ProjectEdit = ({ data, onCancel, serviceId, userData }) => {
  const {
    uploadProject,
    allCategories,
    deleteProject,
    allSubCategories,
    user,
    fetchUserProjects,
  } = useContext(AuthContext);

  const [disableButton, setDisableButton] = useState(true);
  const [projects, setProjects] = useState([]);
  const [projectToDelete, setProjectToDelete] = useState(null);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectFields, setProjectFields] = useState(null);
  const [projectErrors, setProjectErrors] = useState({});
  const [fieldChanged, setFieldChanged] = useState(false);


  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  
  const addEntry = () => {
    setDisableButton(true);
    setProjects([...projects, { id: Date.now(), name: "", file: null }]);
  };


  const removeEntry = (projectId) => {
    console.log('Deleting project with ID:', projectId);
    setProjects(projects.filter((project) => project.id !== projectId));
  };

  const handleFileChange = (projectId, file) => {
    if (file && file.type === "image/png") {
      setDisableButton(false);
      setProjects(
        projects.map((project) => {
          if (project.id === projectId) {
            return { ...project, file };
          }
          return project;
        })
      );
    }
  };






  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isExtraSmallScreen = useMediaQuery("(max-width:360px)");

  const handleViewIconClick = (projectFilePath) => {
    setSelectedProject(projectFilePath);
  };

  const handleDialogClose = () => {
    setSelectedProject(null);
  };


  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };
  const userUniqueId =
  userData && userData.id !== undefined ? userData.id : user.id;

  const documentDetails = {
    userId: userUniqueId,
    serviceNameId: serviceId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("projectsList"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  
  const onSubmit = async () => {
    for (const project of projects) {
      const formData = new FormData();
      formData.append("documentDetails", JSON.stringify(documentDetails));
      formData.append("projectName", project.name);
      formData.append("file", project.file);
      try {
        const userUniqueId =
        userData && userData.id !== undefined ? userData.id : user.id;
        await uploadProject(formData, userUniqueId);
        fetchUserProjects(userUniqueId);
      } catch (error) {
        console.error("Failure", error);
      }
    }
    onCancel();
  };


  const noProjectsAvailable =
    projects.length === 0 &&
    (!data || !data.metadata || data.metadata.length === 0);

    async function handleDelete() {
      await deleteProject(
        projectFields?.imageUrl,
        projectFields?.projectName,
        userUniqueId,
        serviceId
      );
    await fetchUserProjects(userUniqueId);
    setProjectToDelete(null);
    setConfirmDeleteDialogOpen(false);
  }
  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid
            container
            justifyContent={
              noProjectsAvailable? { lg: "space-between", md: "space-between", sm: "space-between" } : "flex-end"
            }
            flexDirection={noProjectsAvailable  ? { xs: "column", lg: "row", md: "row", sm: "row" } : ""}
            alignItems="center"
            sx={{
              mt: { xs: 2, lg: 2 },
              mb: { xs: 2, lg: 2 },
            }}
          >
            {noProjectsAvailable && (
              <Typography
                style={{
                  textAlign: "center",
                  flex: 1,
                  marginLeft: isSmallScreen ? "" : "4rem",
                }}
              >
                Click on ADD to add Projects
              </Typography>
            )}
            <Button
              onClick={addEntry}
              color="primary"
              variant="contained"
              sx={{
                mb: { xs: 2, lg: 0 },
                mt: { xs: 2, lg: 2 },
                          alignSelf: isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",

              }}
            >
              Add
            </Button>
          </Grid>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                {projects.length > 0 && (
                  <TableRow
                    style={{ backgroundColor: "#f2f7f2" }}
                  >
                    <TableCell sx={{ padding: "5px" }}>Project Name</TableCell>
                    <TableCell sx={{ padding: "5px" }}>Image</TableCell>
                    <TableCell sx={{ padding: "5px" }}>Delete</TableCell>
                  </TableRow>
                )}
              </TableHead>
              <TableBody>
                {projects.map((project, index) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <Controller
                        name={`projects[${index}].name`}
                        control={control}
                        defaultValue={project.name}
                        rules={{ required: "Project Name is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Project Name"
                            variant="outlined"
                            fullWidth
                            error={Boolean(errors?.projects?.[index]?.name)}
                            helperText={
                              errors?.projects?.[index]?.name?.message || ""
                            }
                            onChange={(e) => {
                              field.onChange(e);
                              setProjects((prevProjects) =>
                                prevProjects.map((prevProject) =>
                                  prevProject.id === project.id
                                    ? { ...prevProject, name: e.target.value }
                                    : prevProject
                                )
                              );
                            }}
                            sx={{ width: isSmallScreen ? "150px" : "100%" }}
                          />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        component="label"
                        htmlFor={`file-input-${project.id}`}
                      >
                        Choose Image
                        <input
                          id={`file-input-${project.id}`}
                          type="file"
                          accept=".png"
                          style={{ display: "none" }}
                          onChange={(e) =>
                            handleFileChange(project.id, e.target.files[0])
                          }
                        />
                      </Button>
                      <br />
                      <span>{project.file ? project.file.name : ""}</span>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => removeEntry(project.id)}
                        color="error"
                      >
                        <Icon icon="iconamoon:trash" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {data?.metadata?.length > 0 && (
                  <TableRow>
                    <TableCell colSpan={3}>
                      <Divider />
                      <Table>
                        <TableHead>
                          <TableRow
                            style={{
                              backgroundColor: "#f2f7f2",
                            }}
                          >
                            <TableCell sx={{ padding: "5px" }}>Name</TableCell>
                            <TableCell sx={{ padding: "5px" }}>
                              Image
                            </TableCell>
                            <TableCell sx={{ padding: "5px" }}>
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {data?.metadata?.map((project, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Typography
                                  className="data-field"
                                  marginLeft={"12px"}
                                >
                                  {project?.projectName}
                                </Typography>
                              </TableCell>

                              <TableCell>
                                <Typography className="data-field">
                                  {project?.documentDTO?.location &&
                                    project.documentDTO.location
                                      .split("/")
                                      .pop()}
                                </Typography>
                              </TableCell>

                              <TableCell>
                                <IconButton
                                  onClick={() => {
                                    setProjectFields({
                                      imageUrl: project?.documentDTO?.location,
                                      projectName: project?.projectName,
                                    });
                                    setConfirmDeleteDialogOpen(true);
                                  }}
                                  color="error"
                                >
                                  <Icon icon="iconamoon:trash" />
                                </IconButton>

                                <IconButton
                                  onClick={() =>
                                    handleViewIconClick(
                                      project?.documentDTO?.location
                                    )
                                  }
                                  color="error"
                                  disabled={selectedProject}
                                >
                                  <Icon icon="iconamoon:eye" />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>

                      <ViewDialogByLocation
                        location={selectedProject}
                        setSelectedLocation={setSelectedProject}
                        open={Boolean(selectedProject)}
                        onClose={handleDialogClose}
                      />
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <DeleteConfirmationDialog
              open={confirmDeleteDialogOpen}
              onClose={() => setConfirmDeleteDialogOpen(false)}
              onConfirm={handleDelete}
            />
          </TableContainer>
          <Grid
            container
            justifyContent="center"
            sx={{
              mt: { xs: 4, lg: 4 },
              mb: { xs: 2, lg: 4 },
            }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit"
              variant="contained"
              color="primary"
              disabled={disableButton}
              
            >
              Submit
            </Button>
          </Grid>
        </form>
      </Grid>
    </Grid>
  );
};

export default ProjectEdit;
