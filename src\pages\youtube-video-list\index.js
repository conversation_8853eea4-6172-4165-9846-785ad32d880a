import React from 'react';
import PlayYoutubeVideo from './PlayYoutubeVideo';

const YoutubeVideosList = () => {
  const videoUrls = [
    'https://youtu.be/UTiXQcrLlv4',
    'https://youtu.be/DoTkzj3wiHE',
    'https://youtu.be/iZUeDkjfPps',
    'https://youtu.be/JC6NHSIi4ws',
    'https://youtu.be/avz06PDqDbM',
    'https://youtu.be/NgBoMJy386M',
    'https://youtu.be/bMf0IyzyKt4'
  ];

  return (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
      {videoUrls.map((url, index) => (
        <PlayYoutubeVideo key={index} videoUrl={url} />
      ))}
    </div>
  );
};

export default YoutubeVideosList;
