import React, { useContext, useEffect, useState } from "react";
import axios from "axios";
import { AuthContext } from "src/context/AuthContext";
import StatisticsTabs from "./StatisticsTabs";
import { Grid } from "@mui/material";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
function StatisticsParent(userDataAllProfile, expanded) {
  const {
    user,
    basicProfileGetData,
    getBasicProfileData,
    micrositeBasicData,
    micrositeGetEndpoint,
    fetchStatistics,
    statisticsData,
  } = useContext(AuthContext);


  useEffect(() => {
    micrositeGetEndpoint();
  }, []);

  const [listValues, setListValues] = useState(null);
  const [serviceId, setServiceId] = useState([]);
  const [data, setData] = useState([]);

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, []);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  const paramsData = { isStatistics: true };

  const fetchAll = async (serviceId, data) => {
    const url = `${getUrl(
      authConfig.getAllServiceProfiles
    )}/${serviceId}/statistics/questions`;
    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "get",
        url: url,
        headers: headers,
      });

      if (response.data) {
        setData(response.data);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };
  const userUniqueId =
    userDataAllProfile.userDataAllProfile?.id &&
    userDataAllProfile.userDataAllProfile?.id !== undefined
      ? userDataAllProfile.userDataAllProfile?.id
      : user?.id;

  useEffect(() => {
    getBasicProfileData(userUniqueId);
  }, []);

  useEffect(() => {
    fetchStatistics(userUniqueId);
  }, []);

  const handleTabChange = (serviceId) => {
    fetchAll(serviceId);
    setServiceId(serviceId);
  };

  return (
    <Grid>
      <StatisticsTabs
        data={data}
        userDataAllProfile={userDataAllProfile.userDataAllProfile}
        statisticsData={statisticsData}
        serviceId={serviceId}
        onTabChange={handleTabChange}
        tabContents={serviceNames}
        expanded={expanded}
        getData={micrositeBasicData}
      />
    </Grid>
  );
}

export default StatisticsParent;
