import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";

const quickLinks = () => {
  const { can } = useRBAC();
  const { user, quickLinksData } = useContext(AuthContext);
  const [navArray, setNavArray] = useState([]);


  const processChildren = (children) => {
    // Ensure children is an array before calling filter
    return (children || [])
      .filter(child => child.accessCode === null || can(child.accessCode))
      .map(child => ({
        title: child.title,
        path: child.path,
        icon: child.icon,
        // Ensure child.children is an array before recursion
        children: child.children ? processChildren(child.children) : null
      }));
  };

  useEffect(() => {
    // Ensure quickLinksData?.quickLinksDataSetDTO?.quickLinkData is an array before calling filter
    const updatedNavArray = (quickLinksData?.quickLinksDataSetDTO?.quickLinkData || [])
      .filter(item => item.accessCode === null || can(item.accessCode))
      .map(item => {
        return {
          title: item.title,
          path: item.path,
          icon: item.icon,
          // Ensure item.children is an array before processing
          children: item.children ? processChildren(item.children) : null
        };
      });

    setNavArray(updatedNavArray);
  }, [user, quickLinksData, can]);

  return navArray;
};

export default quickLinks;