import React from 'react';
import {
  Box,
  LinearProgress,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
} from '@mui/icons-material';
import {
  validatePasswordStrength,
  getPasswordStrengthColor,
  getPasswordStrengthPercentage,
} from '../../utils/validation';

/**
 * PasswordStrengthIndicator component displays password strength and requirements
 * @param {Object} props - Component props
 * @param {string} props.password - Password to validate
 * @param {boolean} props.showRequirements - Whether to show detailed requirements
 * @param {Object} props.sx - Additional styling
 */
const PasswordStrengthIndicator = ({ 
  password = '', 
  showRequirements = true,
  sx = {} 
}) => {
  const validation = validatePasswordStrength(password);
  const { score, strength, feedback } = validation;
  
  const strengthColor = getPasswordStrengthColor(strength);
  const strengthPercentage = getPasswordStrengthPercentage(score);

  // Password requirements with their validation status
  const requirements = [
    {
      text: 'At least 8 characters',
      met: password.length >= 8,
    },
    {
      text: 'One uppercase letter (A-Z)',
      met: /[A-Z]/.test(password),
    },
    {
      text: 'One lowercase letter (a-z)',
      met: /[a-z]/.test(password),
    },
    {
      text: 'One number (0-9)',
      met: /\d/.test(password),
    },
    {
      text: 'One special character (!@#$%^&*)',
      met: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    },
  ];

  // Don't show anything if no password is entered
  if (!password) {
    return null;
  }

  return (
    <Box sx={{ mt: 1, ...sx }}>
      {/* Password Strength Bar */}
      <Box sx={{ mb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
          <Typography variant="caption" sx={{ mr: 1 }}>
            Password strength:
          </Typography>
          <Chip
            label={strength.charAt(0).toUpperCase() + strength.slice(1)}
            size="small"
            sx={{
              backgroundColor: strengthColor,
              color: 'white',
              fontWeight: 'bold',
              fontSize: '0.7rem',
            }}
          />
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={strengthPercentage}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: '#e0e0e0',
            '& .MuiLinearProgress-bar': {
              backgroundColor: strengthColor,
              borderRadius: 3,
            },
          }}
        />
      </Box>

      {/* Password Requirements */}
      {showRequirements && (
        <Box>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Password must contain:
          </Typography>
          
          <List dense sx={{ py: 0 }}>
            {requirements.map((requirement, index) => (
              <ListItem key={index} sx={{ py: 0.25, px: 0 }}>
                <ListItemIcon sx={{ minWidth: 24 }}>
                  {requirement.met ? (
                    <CheckCircleIcon 
                      sx={{ 
                        fontSize: 16, 
                        color: 'success.main' 
                      }} 
                    />
                  ) : (
                    <RadioButtonUncheckedIcon 
                      sx={{ 
                        fontSize: 16, 
                        color: 'text.disabled' 
                      }} 
                    />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={requirement.text}
                  primaryTypographyProps={{
                    variant: 'caption',
                    sx: {
                      color: requirement.met ? 'success.main' : 'text.secondary',
                      textDecoration: requirement.met ? 'line-through' : 'none',
                    },
                  }}
                />
              </ListItem>
            ))}
          </List>

          {/* Additional Feedback */}
          {feedback.length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" color="error.main">
                Missing: {feedback.join(', ')}
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default PasswordStrengthIndicator;
