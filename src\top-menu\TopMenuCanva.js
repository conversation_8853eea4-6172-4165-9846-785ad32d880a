import React, { useState } from "react";
import {
  Drawer,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableHead,
  Paper,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Tooltip,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";
import { useRouter } from "next/router";
import topmenu from "src/top-menu";

function TopMenuCanva() {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const dataFromBackend = topmenu();

  // Function to recursively map menu items and their children
  const mapMenuItems = (items) => {
    return items.map((item, index) => (
      <TableRow key={index}>
        <TableCell
          onClick={() => handleCellClick(item.path)}
          style={{
            cursor: "pointer",
            borderBottom: "none",
          }}
          sx={
            item.path
              ? {
                  "&:hover": {
                    background: "#f2f7f2",
                  },
                }
              : {}
          }
        >
          {item.title && (
            <Typography
              variant="body1"
              component="span"
              style={{
                cursor: "pointer",
                fontWeight: 100,
                fontSize: "0.8rem",
              }}
            >
              {item.title}
            </Typography>
          )}
        </TableCell>
      </TableRow>
    ));
  };

  const handleDrawerOpen = () => setIsOpen(true);
  const handleDrawerClose = () => setIsOpen(false);

  const handleCellClick = (url) => {
    router.push(url);
  };

  return (
    <>
      <Tooltip title="Mobile Settings">
        <CustomAvatar
          skin="light"
          variant="rounded"
          sx={{
            cursor: "pointer",
            zIndex: 1200,
            mr:{xs:1,lg:2}
          }}
          onClick={handleDrawerOpen}
        >
          <Icon icon="ci:hamburger-md" />
        </CustomAvatar>
      </Tooltip>
      {isOpen && (
        <Drawer anchor="left" open={isOpen} onClose={handleDrawerClose}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <Typography variant="subtitle1">Menu Items</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {dataFromBackend.map((item, colIndex) => (
                  <TableRow key={colIndex}>
                    <TableCell>
                      <Accordion
                        sx={{
                          width: "250px",
                        }}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls={`${item.title}-content`}
                          id={`${item.title}-header`}
                        >
                          <Typography variant="body1">
                            {item.title}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <TableContainer component={Paper}>
                            <Table>
                              <TableBody>
                                {item.children && mapMenuItems(item.children)}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </AccordionDetails>
                      </Accordion>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Drawer>
      )}
    </>
  );
}

export default TopMenuCanva;
