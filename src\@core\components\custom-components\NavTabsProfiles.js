// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'
import Icon from 'src/@core/components/icon'

const NavTabsProfiles = props => {
  // ** State
  const [value, setValue] = useState('1')

  // ** Props
  const { tabContent1, tabContent2, tabContent3, tabContent4 ,tabContent5,tabContent6,tabContent7,tabContent8,tabContent9,tabContent10} = props

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="forced scroll tabs example"
      >
        <Tab
          value="1"
          label="Overview"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:users' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="2"
          label="Quotations"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:file-text' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="3"
          label="Work orders"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:file-check' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="4"
          label="Activity timelines"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:calendar' />}
          onClick={(e) => e.preventDefault()}
        />
         <Tab
          value="5"
          label="Invoices"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:file-dollar' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="6"
          label="Security"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:lock' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="7"
          label="Billing & Plans"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:currency-dollar' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="8"
          label="Notifications"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:bell' />}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="9"
          label="Connections"
          sx={{ textTransform: 'none' ,color:'#384551'}} 
          icon={<Icon fontSize='1.125rem' icon='tabler:link' />}
          onClick={(e) => e.preventDefault()}
        />
      </TabList>
      <TabPanel value="1" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent1}</Typography>
      </TabPanel>
      <TabPanel value="2" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent2}</Typography>
      </TabPanel>
      <TabPanel value="3" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent3}</Typography>
      </TabPanel>
      <TabPanel value="4" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent4}</Typography>
      </TabPanel>
      <TabPanel value="5" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent5}</Typography>
      </TabPanel>
      <TabPanel value="6" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent6}</Typography>
      </TabPanel>
      <TabPanel value="7" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent7}</Typography>
      </TabPanel>
      <TabPanel value="8" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent8}</Typography>
      </TabPanel>
      <TabPanel value="9" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent9}</Typography>
      </TabPanel>
    </TabContext>
  );
}

export default NavTabsProfiles;
