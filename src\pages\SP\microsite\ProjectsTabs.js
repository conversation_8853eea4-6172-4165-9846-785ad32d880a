import { useState, useEffect } from "react";
import Tab from "@mui/material/Tab";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import { Card, Grid } from "@mui/material";
import CustomSlider from "./CustomSlider";
import authConfig from 'src/configs/auth';
import axios from "axios";

const ProjectsTabs = (props) => {
  const [value, setValue] = useState("1");
  const [initialTabClicked, setInitialTabClicked] = useState(false);
  const { tabContents, data, onTabChange } = props;

  const handleServiceChange = (_event, newValue) => {
    setValue(newValue);
    const selectedService = tabContents.find((tab) => tab.id === newValue);
    if (selectedService) {
      onTabChange(selectedService.id);
    }
  };

  // this use effect is used to set the service id initially with out click any tab
  useEffect(() => {
    if (!initialTabClicked && tabContents && tabContents.length > 0) {
      setValue(tabContents[0].id);
      setInitialTabClicked(true);
      const selectedService = tabContents[0];
      if (selectedService) {
        onTabChange(selectedService.id);
      }
    }
  }, [initialTabClicked, tabContents, onTabChange]);

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        aria-label="forced scroll tabs example"
      >
        {tabContents?.map((tabContent) => (
          <Tab
            key={tabContent.id}
            value={tabContent.id}
            component="a"
            label={tabContent.name}
            onClick={(e) => handleServiceChange(e, tabContent.id)}
          />
        ))}
      </TabList>
      {tabContents?.map((tabContent) => (
        <TabPanel
          key={tabContent.id}
          value={tabContent.id}
          sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}
        >
          <CustomSlider
            data={data} slidesToShow={2} />
        </TabPanel>
      ))}
    </TabContext>
  );
};

export default ProjectsTabs;
