import { useState, useEffect, useContext, Fragment } from "react";
import {
  Box,
  Grid,
  Button,
  Typo<PERSON>,
  Divider,
  IconButton,
  Card,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Slider,
  FormHelperText,
  DialogContentText,
  TableCell,
  TableHead,
  FormControlLabel,
  Autocomplete,
  Tooltip,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import CustomAvatar from "src/@core/components/mui/avatar";
import CloseIcon from "@mui/icons-material/Close";

import Icon from "src/@core/components/icon";

import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import "react-datepicker/dist/react-datepicker.css";
import authConfig from "src/configs/auth";
import axios from "axios";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import MUITableCell from "../SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CommentsDialog from "./CommentsDialog";
import { DataGrid } from "@mui/x-data-grid";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import NameTextField from "src/@core/components/custom-components/NameTextField";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const marks = Array.from({ length: 20 }, (_, i) => ({
  value: (i + 1) * 5,
  label: `${(i + 1) * 5}`,
}));

function valuetext(value) {
  return `${value}`;
}

const rupees = [
  {
    value: "HUNDRED",
    key: "hundred",
  },
  {
    value: "THOUSAND",
    key: "thousand",
  },
  {
    value: "LAKHS",
    key: "lakhs",
  },
  {
    value: "CRORES",
    key: "crores",
  },
];

const EditRequisition = ({
  role,
  open,
  onClose,
  listOfSubCategories,
  formattedData,
  setSpecifications,
  formData,
  fetchRequisitions,
}) => {
  const theme = useTheme();

  const auth = useAuth();
  const { getAllListValuesByListNameId, listValues } = useContext(AuthContext);
  const {
    register,
    handleSubmit,
    control,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [budgetValue, setBudgetValue] = useState([]);
  const [budget, setBudget] = useState(formData?.requisitionData?.units);
  const [serviceId, setServiceId] = useState(
    formData?.requisitionData?.serviceType
  );

  const [subServices, setSubServices] = useState(
    formData?.requisitionData?.subServices
  );

  const [conversation, setConversation] = useState({});
  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleChange = (event, newValue) => {
    setBudgetValue(newValue);
  };

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [convList, setConvList] = useState([]);

  const [allServicesList, setAllServicesList] = useState([]);
  const [subServicesList, setSubServicesList] = useState(
    formData?.requisitionData?.subServices
  );

  const [dataView, setDataView] = useState({});

  const [selectedOptions, setSelectedOptions] = useState([]);

  useEffect(() => {
    setSelectedOptions([]);
  }, [serviceId]);

  function reverseTransformData(transformedData) {
    const originalData = {};
    transformedData?.forEach((item) => {
      // Check if the listValues array has more than one item.
      // If so, map through the array and extract the listValueId, forming a new array.
      // If not, just extract the single listValueId.
      if (item.listValues.length > 0) {
        originalData[item.listNameId] = item.listValues.map(
          (valueItem) => valueItem.listValueId
        );
      } else {
        originalData[item.listNameId] = item.otherValue;
      }
    });

    return originalData;
  }

  const [requisitionDialog, setRequisitionDialog] = useState(false);

  useEffect(() => {
    const reverse = reverseTransformData(
      formData?.requisitionData?.specifications?.listNames
    );
    setSelectedOptions(reverse);
  }, [formData, serviceId]);

  const [service, setService] = useState("");
  const [designation, setDesignation] = useState("");

  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);

          const matchingSociety = metadataArray.find(
            (society) => society?.userId === formData?.userId
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingSociety) {
            setDataView({
              ...dataView, // This keeps the existing properties
              ...matchingSociety, // This adds/updates properties from newValue
            });
            const loc = matchingSociety?.designation
              ? listOfSubCategories.find(
                  (item) => item.value === matchingSociety?.designation
                )?.key
              : null;

            setDesignation(loc);
          }
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
    setServiceId(formData?.requisitionData?.serviceType);
    setSubServices(formData?.requisitionData?.subServices);
    setValue("referenceType", formData?.referenceType);
    setValue("houzerSocietyTeamMember", formData?.teamMember);
    setValue("referralName", formData?.referralName);
    setValue("assignedTo", formData?.assignedTo);
    setValue("status", formData?.status);
    setValue("dataSentDate", formData?.dataSentDate);
    setValue("societyRemarks", formData?.requisitionData?.societyRemarks);
    setConvList(formData?.conversationData);
    setValue(
      "requirementDeadLine",
      formData?.requisitionData?.requirementDeadLine
    );
    setValue("priority", formData?.requisitionData?.priority);
    setBudget(formData?.requisitionData?.units);
    function stringToArray(rangeString) {
      // Split the string by the hyphen and trim any extra spaces
      let parts = rangeString?.split("-").map((part) => part.trim());

      // Convert the split parts to numbers
      let numArray = parts?.map(Number);

      // Return the resulting array
      return numArray;
    }

    let resultArray = stringToArray(formData?.requisitionData?.budget);

    let units = formData?.requisitionData?.units;
    let zeroesToTrim;

    if (units === "HUNDRED") {
      zeroesToTrim = 2;
    } else if (units === "THOUSAND") {
      zeroesToTrim = 3;
    } else if (units === "LAKHS") {
      zeroesToTrim = 5;
    } else if (units === "CRORES") {
      zeroesToTrim = 7;
    }

    let trimmedArray = resultArray?.map(
      (num) => num / Math.pow(10, zeroesToTrim)
    );

    setBudgetValue(trimmedArray);

    const serv = formData?.requisitionData?.serviceType
      ? listValues.find(
          (item) => item.id === formData?.requisitionData?.serviceType
        )?.name
      : null;

    setService(serv);
  }, [formData]);

  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleCancel = () => {
    onClose();
  };
  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleSubServicesSuccess = (data) => {
    setSubServicesList(data?.listValues);
  };

  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };

  const [priorityData, setPriorityData] = useState(null);

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const [subServiceId, setSubServiceId] = useState("");

  useEffect(() => {
    if (subServiceId) {
      getAllListValuesByListNameId(
        subServiceId,
        handleSubServicesSuccess,
        handleError
      );
    }
  }, [subServiceId]);

  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [showSubService, setShowSubService] = useState(false);

  const [userList, setUserList] = useState([]);
  useEffect(() => {
    if (serviceId) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      fetchAll(serviceId);
    }
  }, [serviceId]);

  useEffect(() => {
    userList.forEach((category) => {
      const formDataItem = formattedData?.find(
        (item) => item.id === category.id
      );
      if (formDataItem) {
        if (
          category.component === "Multi Select Dropdown" ||
          category.component === "Single Select Dropdown"
        ) {
          setValue(
            `selectedOptions.${category.name}`,
            formDataItem.values.map((val) => val.id) || []
          );
        } else {
          setValue(
            `selectedOptions.${category.name}`,
            formDataItem.otherValue || ""
          );
        }
      }
    });
  }, [formattedData, userList, setValue]);

  const handleSelectChange = (event) => {
    const value = event.target.value;
    setServiceId(value);
    const name = value
      ? listValues?.find((item) => item?.id === value)?.name
      : null;
    if (name === "Any other") {
      setIsOtherSelected(true);
    } else {
      setShowSubService(true);
    }
  };

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Requisition details updated Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to update requisition details. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  function transformData(originalData) {
    const transformedData = [];
    for (const listNameId in originalData) {
      let listValues = [];
      let otherValue = null;

      if (Array.isArray(originalData[listNameId])) {
        listValues = originalData[listNameId].map((listValueId) => ({
          listValueId,
        }));
      } else {
        otherValue = originalData[listNameId];
      }

      transformedData.push({
        listNameId,
        otherValue,
        listValues,
      });
    }

    return transformedData;
  }

  const handleSpecificationChange = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: event.target.value,
    });
  };

  const handleChangeSelect = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: [event.target.value],
    });
  };

  async function submit(data) {
    if (budget === "HUNDRED") {
      budgetValue[0] = `${budgetValue[0]}00`;
      budgetValue[1] = `${budgetValue[1]}00`;
    } else if (budget === "THOUSAND") {
      budgetValue[0] = `${budgetValue[0]}000`;
      budgetValue[1] = `${budgetValue[1]}000`;
    } else if (budget === "LAKHS") {
      budgetValue[0] = `${budgetValue[0]}00000`;
      budgetValue[1] = `${budgetValue[1]}00000`;
    } else if (budget === "CRORES") {
      budgetValue[0] = `${budgetValue[0]}0000000`;
      budgetValue[1] = `${budgetValue[1]}0000000`;
    }

    const transformedData = transformData(selectedOptions);

    const fields = {
      id: formData?.id,
      userId: formData?.userId,
      isActive: formData?.isActive,
      requisitionData: {
        serviceType: serviceId,
        subServices: subServices,
        anyOtherServices: data?.anyOtherServices,
        priority: data?.priority,
        specifications: {
          listNames: transformedData,
        },
        units: budget,
        budget: `${budgetValue[0]} - ${budgetValue[1]}`,
        requirementDeadLine: data?.requirementDeadLine,
        societyRemarks: data?.societyRemarks,
      },
      conversationData: convList,
      teamMember: data?.houzerSocietyTeamMember,
      referralName: data?.referralName,
      referenceType: data?.referenceType,
      assignedTo: data?.assignedTo,
      status: data?.status,
      dataSentDate: data?.dataSentDate,
    };

    try {
      const response = await auth.patchRequisition(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    onClose();
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    onClose();
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Edit Service Requisition
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          {!role && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Society Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Society name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.location}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Zone:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.zone}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Address:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.societyAddress}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>
                          Society contact name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.chairmanName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Designation:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {designation}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Contact Number:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.mobileNumber}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Email Id:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {dataView?.loginEmail}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Requisition Details
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid container item xs={12} sm={4} spacing={2}>
                <Grid item>
                  <Typography className="data-field">Service:</Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {service}
                  </Typography>
                </Grid>
              </Grid>

              {isOtherSelected && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="anyOtherService"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Other Service"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Other Service"
                          error={Boolean(errors.anyOtherService)}
                          helperText={errors.anyOtherService?.message}
                          aria-describedby="validation-basic-anyOtherService"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLine"
                    control={control}
                    defaultValue={
                      formData?.requisitionData?.requirementDeadLine
                    }
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requirement DeadLine"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLine"
                        value={field.value}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={2}>
                <FormControl fullWidth>
                  <InputLabel id="priorityId"> Priority</InputLabel>
                  <Controller
                    name="priority"
                    control={control}
                    defaultValue={formData?.requisitionData?.priority}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="priorityId-label"
                        label="Priority"
                        id="priorityId"
                        size="small"
                      >
                        {priorityData?.map((status) => (
                          <MenuItem key={status.id} value={status.id}>
                            {status.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box display="flex" alignItems="center">
                  <Typography id="range-slider" gutterBottom>
                    Budget in
                  </Typography>
                  <Grid item xs={4} sx={{ marginLeft: "6px" }}>
                    <SelectProject
                      register={register}
                      id={"budget"}
                      label={"units"}
                      nameArray={rupees}
                      value={budget}
                      defaultValue={budget}
                      onChange={(e) => setBudget(e.target.value)}
                      error={Boolean(errors.budget)}
                      aria-describedby="validation-budget"
                    />
                  </Grid>
                </Box>
                <Slider
                  value={budgetValue}
                  onChange={handleChange}
                  valueLabelDisplay="auto"
                  aria-labelledby="range-slider"
                  getAriaValueText={valuetext}
                  step={5}
                  marks={marks}
                  min={5}
                  max={100}
                />
              </Grid>
              <Grid item xs={12}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      {userList.length > 0 && (
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell>Select Options</TableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {userList.map((category) => (
                        <TableRow key={category.id}>
                          <TableCell>{category.name}</TableCell>

                          <TableCell>
                            {(() => {
                              const formDataItem = formattedData?.find(
                                (item) => item.id === category.id
                              );
                              switch (category.component) {
                                case "Radio buttons":
                                  return (
                                    <FormControl component="fieldset">
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.values[0]?.id || ""
                                            : ""
                                        } // Default value for the radio group (none selected)
                                        render={({ field }) => (
                                          <RadioGroup
                                            {...field}
                                            aria-label="option"
                                            size="small"
                                            value={field.value}
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                category.id,
                                                event
                                              );
                                              field.onChange(event); // This updates the value in react-hook-form
                                            }}
                                            row
                                          >
                                            {category.values.map((value) => (
                                              <FormControlLabel
                                                key={value.id} // Using value.id as the key
                                                value={value.id} // Using value.id as the value
                                                control={<Radio />} // Radio button control
                                                label={value.name} // Displaying the name property as the label
                                              />
                                            ))}
                                          </RadioGroup>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Multi Select Dropdown":
                                  return (
                                    <FormControl style={{ width: "500px" }}>
                                      <InputLabel
                                        id={category.name}
                                        style={{ zIndex: 0 }}
                                      >
                                        Select From {category.name}
                                      </InputLabel>
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.values.map(
                                                (val) => val.id
                                              )
                                            : []
                                        }
                                        render={({ field }) => (
                                          <Select
                                            multiple
                                            labelId={category.name}
                                            size="small"
                                            label={`Select from ${category.name}`}
                                            value={field.value || []}
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                category.id,
                                                event
                                              );
                                              field.onChange(
                                                event.target.value
                                              ); // This updates the value in react-hook-form
                                            }}
                                            renderValue={(selected) => (
                                              <span>
                                                {selected
                                                  .map(
                                                    (selectedValue) =>
                                                      category.values.find(
                                                        (value) =>
                                                          value.id ===
                                                          selectedValue
                                                      )?.name
                                                  )
                                                  .join(", ")}
                                              </span>
                                            )}
                                          >
                                            {category.values.map((value) => (
                                              <MenuItem
                                                key={value.id}
                                                value={value.id}
                                              >
                                                {value.name}
                                              </MenuItem>
                                            ))}
                                          </Select>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Single Select Dropdown":
                                  return (
                                    <FormControl style={{ width: "500px" }}>
                                      <InputLabel
                                        id={category.name}
                                        style={{ zIndex: 0 }}
                                      >
                                        Select From {category.name}
                                      </InputLabel>
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.values[0]?.id || ""
                                            : ""
                                        }
                                        render={({ field }) => (
                                          <Select
                                            labelId={category.name}
                                            label={`Select from ${category.name}`}
                                            size="small"
                                            value={field.value || ""}
                                            onChange={(event) => {
                                              handleChangeSelect(
                                                category.id,
                                                event
                                              );
                                              field.onChange(event); // This updates the value in react-hook-form
                                            }}
                                          >
                                            {category.values.map((value) => (
                                              <MenuItem
                                                key={value.id}
                                                value={value.id}
                                              >
                                                {value.name}
                                              </MenuItem>
                                            ))}
                                          </Select>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Switch":
                                  return (
                                    <FormControl
                                      key={category.name}
                                      component="fieldset"
                                    >
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.values[0]
                                            : false
                                        } // Default value for the switch
                                        render={({ field }) => (
                                          <FormControlLabel
                                            control={
                                              <Switch
                                                {...field}
                                                checked={field.value}
                                                size="small"
                                                onChange={(event) => {
                                                  handleSpecificationChange(
                                                    category.id,
                                                    event
                                                  );
                                                  field.onChange(event); // This updates the value in react-hook-form
                                                }}
                                                name={category.name}
                                                inputProps={{
                                                  "aria-label": category.name,
                                                }}
                                              />
                                            }
                                            label="Yes"
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Number Text Field":
                                  return (
                                    <FormControl
                                      key={category.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.otherValue
                                            : ""
                                        }
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={category.name}
                                            size="small"
                                            type="number"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                category.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": category.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Text Area":
                                  return (
                                    <FormControl
                                      key={category.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        // defaultValue={formDataItem ? formDataItem?.otherValue : ''}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={category.name}
                                            size="small"
                                            rows={3}
                                            multiline
                                            type="number"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                category.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": category.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                default:
                                  return (
                                    <FormControl
                                      key={category.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${category.name}`}
                                        control={control}
                                        defaultValue={
                                          formDataItem
                                            ? formDataItem.otherValue
                                            : ""
                                        }
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={category.name}
                                            size="small"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                category.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": category.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  ); // for handling default behavior
                              }
                            })()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="societyRemarks"
                    control={control}
                    rules={{ required: "societyRemarks is required" }}
                    defaultValue={formData?.requisitionData?.societyRemarks}
                    render={({ field }) => (
                      <TextField
                        rows={3}
                        multiline
                        {...field}
                        label="Society Remarks"
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 1000 }}
                        error={Boolean(errors.societyRemarks)}
                        aria-describedby="societyRemarks"
                      />
                    )}
                  />
                  {errors.societyRemarks && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="societyRemarks"
                    >
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Card>

          {!role && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Status and Assignment Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <Grid container spacing={5} style={{ padding: "16px" }}>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="referenceType"
                      control={control}
                      // defaultValue={formData?.referenceType}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Reference Type"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter your Reference Type"
                          error={Boolean(errors.reference)}
                          helperText={errors.reference?.message}
                          aria-describedby="validation-basic-reference"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.houzerSocietyTeamMember)}
                  >
                    <Controller
                      name="houzerSocietyTeamMember"
                      control={control}
                      rules={{
                        required: "Houzer Society Team Member is required",
                      }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Houzer Society Team Member"
                              error={Boolean(errors.houzerSocietyTeamMember)}
                              helperText={
                                errors.houzerSocietyTeamMember
                                  ? errors.houzerSocietyTeamMember.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="referralName"
                      control={control}
                      // defaultValue={formData?.referralName}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Team Reference "
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Team Reference"
                          error={Boolean(errors.teamReference)}
                          helperText={errors.teamReference?.message}
                          aria-describedby="validation-basic-teamReference"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                    <Controller
                      name="assignedTo"
                      control={control}
                      rules={{ required: "Assigned To is required" }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Assigned To"
                              error={Boolean(errors.assignedTo)}
                              helperText={
                                errors.assignedTo
                                  ? errors.assignedTo.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth error={Boolean(errors.status)}>
                    <Controller
                      name="status"
                      control={control}
                      rules={{ required: "Status is required" }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            statusData?.find(
                              (status) => status.id === field.value
                            ) || null
                          }
                          options={statusData || []}
                          getOptionLabel={(option) => option.listValue || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Status"
                              error={Boolean(errors.status)}
                              helperText={
                                errors.status ? errors.status.message : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="dataSentDate"
                      control={control}
                      // defaultValue={formData?.dataSentDate}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Data Sent Date"
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="dataSentDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Comments Section
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid container sx={{ padding: "10px" }} justifyContent="flex-end">
              {role ? (
                <CommentsDialog
                  setConvList={setConvList}
                  list={formData?.conversationData || []}
                  role={role}
                />
              ) : (
                <CommentsDialog
                  setConvList={setConvList}
                  list={formData?.conversationData || []}
                />
              )}

              {convList?.length > 0 && (
                <TableContainer>
                  <Table sx={{ ml: 4 }}>
                    <TableHead>
                      {role ? (
                        <TableRow>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      ) : (
                        <TableRow>
                          <MUITableCell>Follow-up Date</MUITableCell>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>Follow Up Action</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {role
                        ? convList?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))
                        : convList?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.followUpDate}</MUITableCell>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>{row.followUpAction}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Card>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Comments Details
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
               <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
          </DialogTitle>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Follow Up Date
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpDate}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}

                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>Comments</Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.comments}</Typography>
                  </MUITableCell>
                </TableRow>
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        FollowUp Action
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpAction}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default EditRequisition;
