import React, { useState, useEffect, useContext } from "react";
import Typography from "@mui/material/Typography";
import {
  Box,
  Button,
  Icon,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableRow,
} from "@mui/material";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { useForm } from "react-hook-form";
import FormHelperText from "@mui/material/FormHelperText";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import MUITableCell from "src/pages/SP/MUITableCell";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Controller } from "react-hook-form";
import TextField from "@mui/material/TextField";
import { useTheme } from "@emotion/react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import { AuthContext } from "src/context/AuthContext";
const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServiceInfo = ({ data, expanded, employeeData }) => {

  const theme = useTheme();


  const { listValues ,basicProfileDataAllProfiles} = useContext(AuthContext);

  const [assignedToName, setAssignedToName] = useState("");


  const leadStatusName = (basicProfileDataAllProfiles?.leadStatus)
  ? listValues?.find((item) => item.id === (basicProfileDataAllProfiles?.leadStatus))?.name
  : null;


  const leadPriorityName = (basicProfileDataAllProfiles?.leadPriority)
  ? listValues?.find((item) => item.id === (basicProfileDataAllProfiles?.leadPriority))?.name
  : null;


useEffect(() => {
  if (employeeData && employeeData?.length > 0) {
    const assignedName = (data?.assignedTo)
        ? employeeData.find(item => item.id === (data?.assignedTo))?.name
        : null;

    setAssignedToName(assignedName);
  }
}, []);

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"SP Info"}
        body={
          <>
            <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
              <Table>
                <TableBody
                  sx={{
                    "& .MuiTableCell-root": {
                      p: `${theme.spacing(1.35, 1.125)} !important`,
                    },
                  }}
                >
                 <TableRow>
                 <MUITableCell>
                      <Typography>SP Name</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {data?.firstName +
                          (data?.lastName ? " " + data?.lastName : "")}
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>Email</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {data?.email}
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>Mobile Number</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {data?.basicProfile?.mobileNumber||data?.basicProfileData?.mobileNumber}
                      </Typography>
                    </MUITableCell>

                 </TableRow>
                 <TableRow>
                 <MUITableCell>
                      <Typography>Lead Status</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {leadStatusName}
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>Lead Priority</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {leadPriorityName}
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>Assigned To</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        {assignedToName}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                  <MUITableCell>
                      <Typography>Smart Summary</Typography>
                      <Typography
                        className="data-field"
                        style={{ paddingTop: "5px" }}
                      >
                        
                        {(basicProfileDataAllProfiles?.remarks)}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                 
                </TableBody>
              </Table>
            </TableContainer>
          </>
        }
        expanded={expanded || true}
      />

    </>
  );
};
export default ServiceInfo;
