// ** MUI Imports

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

import CustomAvatar from "src/@core/components/mui/avatar";
import {
  Box,
  Tooltip,
  TableBody,
  TableContainer,
  TableRow,
  Table,
  Dialog,
  TableCell,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Typography,
} from "@mui/material";
import { useTheme } from "@emotion/react";
import CloseIcon from "@mui/icons-material/Close";
import { DataGrid } from "@mui/x-data-grid";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServiceConversationDetails = ({
  expanded,
  conversationList,
  rowCount,
}) => {
  const theme = useTheme();

  const auth = useAuth();
  const [currentRow, setCurrentRow] = useState(null);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const totalGridHeight = pageSize * 52 + 80;
  const [searchKeyword, setSearchKeyword] = useState("");

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const { listValues, setConversation, conversationData } =
    useContext(AuthContext);
  
// For typeOfConversation
const typeOfConversationName = (conversationData?.typeOfConversation)
    ? listValues?.find((item) => item.id === conversationData?.typeOfConversation)?.name
    : null;


// For outcomeOfConversation
const outcomeOfConversationName = (conversationData?.outcomeOfConversation)
    ? listValues?.find((item) => item.id === conversationData?.outcomeOfConversation)?.name
    : null;



// For target
const targetName = (conversationData?.target)
    ? listValues?.find((item) => item.id === conversationData?.target)?.name
    : null;

// For shallRemind
const shallRemindName = (conversationData?.shallRemind)
    ? listValues?.find((item) => item.id === conversationData?.shallRemind)?.name
    : null;


  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const columns = [
    {
      field: "typeOfConversation",
      minWidth: 115,
      headerName: "Type",
      flex: 0.16,
      renderCell: (params) => {
        const conversationType = listValues.find(
          (item) => item.id === params.value
        );
        return (
          <span>{conversationType ? conversationType.name : "Unknown"}</span>
        );
      },
    },
    {
      field: "conversationDate",
      minWidth: 95,
      headerName: "Date",
      flex: 0.13,
    },

    {
      flex: 0.13,
      minWidth: 160,
      field: "nextConversationDate",
      headerName: "Follow-up Date",
    },
    {
      flex: 0.13,
      minWidth: 120,
      field: "nextConversationTime",
      headerName: "Next Time",
    },
    {
      flex: 0.15,
      minWidth: 115,
      field: "outcomeOfConversation",
      headerName: "OutCome",
      renderCell: (params) => {
        const outcome = listValues.find((item) => item.id === params.value);
        return <span>{outcome ? outcome.name : "Unknown"}</span>;
      },
    },
    {
      flex: 0.15,
      minWidth: 115,
      field: "target",
      headerName: "Target",
      renderCell: (params) => {
        const target = listValues.find((item) => item.id === params.value);
        return <span>{target ? target?.name : ""}</span>;
      },
    },
    {
      flex: 0.15,
      minWidth: 115,
      field: "shallRemind",
      headerName: "Shall Remind",
      renderCell: (params) => {
        const shallRemind = listValues.find((item) => item.id === params.value);
        return <span>{shallRemind ? shallRemind?.name : ""}</span>;
      },
    },
    {
      flex: 0.077,
      field: "More Info",
      headerName: "More Info",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleMoreInfoClick = () => {
          const row = params.row;
          setConversation(row);
          setOpenMoreInfoDialog(true);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="More Info">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 28,
                  height: 28,
                  cursor: "pointer",
                }}
                onClick={handleMoreInfoClick}
              >
                <Icon icon="tabler:info-circle" fontSize="2.2rem" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"SP Conversation Details"}
        body={
          <>
            <Box style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={conversationList || []}
                columns={columns}
                autoHeight
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38} 
              />
            </Box>
          </>
        }
        expanded={expanded || true}
      />

      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Complete SP Conversation Details
        </DialogTitle>
        <DialogActions>
          <Box sx={{ position: "absolute", top: "13px", right: "40px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogActions>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      Type
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{typeOfConversationName}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                     Date 
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{conversationData?.conversationDate}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      OutCome
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{outcomeOfConversationName}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                     Next Date
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{conversationData?.nextConversationDate}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                     Next Time
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{conversationData?.nextConversationTime}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      Target
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{targetName}</Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      Shall Remind
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{shallRemindName}</Typography>
                  </MUITableCell>
                </TableRow>
                
                
               
                
                
                
                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                    Comments
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography >{conversationData?.remarks}</Typography>
                  </MUITableCell>
                </TableRow>
               

              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "center",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ServiceConversationDetails;
