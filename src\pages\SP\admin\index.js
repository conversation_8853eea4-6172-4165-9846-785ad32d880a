// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'

// ** Custom Components Imports
import PageHeader from 'src/@core/components/page-header'



// ** Config

// ** Styled Component
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { TableCell } from '@mui/material'
import styled from '@emotion/styled'





const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: '0 !important',
  paddingRight: '0 !important',
  '&:not(:last-child)': {
    paddingRight: `${theme.spacing(2)} !important`
  }
}))

const field = {
  fontWeight: 400
}

const SuperAdmin = () => {


  return (
    <>
     <style>
        {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
      </style>
   
        <DatePickerWrapper>
          <Grid container spacing={6} className='match-height'>
            <PageHeader
              title={<Typography variant='h5'>Super Admin Profile</Typography>}
              subtitle={<Typography variant='body2'></Typography>}
            />
          
          </Grid>
        </DatePickerWrapper>
       
    </>
  )
}

export default SuperAdmin

