
import Slider from 'react-slick'

import { useState } from 'react'

import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Box, Card, CardContent, Typography } from '@mui/material'

const InsightsSlider = ({ videoIds }) => {
  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  // const [isPlaying, setIsPlaying] = useState(false);

  // const videoId = new URL('https://www.youtube.com/watch?v=KG1gCIR67y4').searchParams.get('v');
  // const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=${isPlaying ? '1' : '0'}`;

  // const handleClick = () => {
  //   setIsPlaying(true);
  // };

  // const sliderSettings = {
  //   infinite: true,
  //   dots: true,
  //   speed: 1500,
  //   slidesToShow: hidden ? 1 : 2,
  //   slidesToScroll: 1,
  //   autoplay: true,
  //   autoplaySpeed: 4500,
  // };

  // return (
  //   <>
  //     <Slider {...sliderSettings}>
  //       <div>
  //       <CardContent onClick={handleClick} sx={{
  //         p: theme => `${theme.spacing(0)} !important`,
  //         position: 'relative', width: '100%', height: 350,
  //       }}>

  //         <iframe style={{ borderRadius: '8px' }}
  //           title="YouTube Video"
  //           width="100%"
  //           height="100%"
  //           src={embedUrl}
  //           allowFullScreen
  //         />
  //         </CardContent>
  //       </div>
  //       <div>
  //         <CardContent onClick={handleClick} sx={{
  //           p: theme => `${theme.spacing(0)} !important`,
  //           position: 'relative', width: '100%', height: 350,
  //         }}>

  //           <iframe style={{ borderRadius: '8px' }}
  //             title="YouTube Video"
  //             width="100%"
  //             height="100%"
  //             src={embedUrl}
  //             allowFullScreen
  //           />
  //         </CardContent>
  //       </div>
  //       <div>
  //         <CardContent onClick={handleClick} sx={{
  //           p: theme => `${theme.spacing(0)} !important`,
  //           position: 'relative', width: '100%', height: 350,
  //         }}>

  //           <iframe style={{ borderRadius: '8px' }}
  //             title="YouTube Video"
  //             width="100%"
  //             height="100%"
  //             src={embedUrl}
  //             allowFullScreen
  //           />
  //         </CardContent>
  //       </div>

  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)

  const embedUrl = videoId =>
    `https://www.youtube.com/embed/${videoId}?autoplay=${currentVideoIndex === videoIds.indexOf(videoId)}`

  const handleClick = index => {
    setCurrentVideoIndex(index)
  }

  const sliderSettings = {
    infinite: true,
    dots: true,
    speed: 1500,
    slidesToShow: videoIds.length > 1 ? (hidden ? 1 : 2) : 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 4500
  }

  return (
    <>
      <Slider {...sliderSettings}>
        {videoIds?.map((videoId, index) => (
          <div key={index}>
            <CardContent
              onClick={() => handleClick(index)}
              sx={{
                p: theme => `${theme.spacing(0)} !important`,
                position: 'relative',
                width: '100%',
                height: 350
              }}
            >
              <iframe
                style={{ borderRadius: '14px', outline: 'none', border: 'none' }}
                title={`YouTube Video ${index + 1}`}
                width='100%'
                height='100%'
                src={embedUrl(videoId)}
                allowFullScreen
              />
            </CardContent>
          </div>
        ))}
      </Slider>
      <style jsx global>
        {`
          .slick-slide {
            padding: 0 10px;
          }
          .slick-slide img {
            border-radius: 6px;
            height: 220px;
            width: 100%;
          }
          .slide-item iframe {
            border-radius: 6px;
            outline: none; // Remove outline on the slides
          }
          .slick-prev,
          .slick-next {
            width: 22px;
            height: 22px;
          }
          .slick-prev:before,
          .slick-next:before {
            font-size: 30px !important;
            color: black;
          }
          .slick-dots {
            display: none !important;
          }
          .slick-dots li {
            margin: 10px;
            width: 17px;
            height: 17px;
          }
          .slick-dots li button:before {
            font-size: 13px;
          }
          .slick-dots li.slick-active button:before {
            color: #000;
          }

          @media only screen and (max-width: 600px) {
            .slick-prev,
            .slick-next {
              display: none !important;
            }
            .slick-slide {
              padding: 0 4px;
            }
          }
        `}
      </style>
    </>
  )
}

export default InsightsSlider
