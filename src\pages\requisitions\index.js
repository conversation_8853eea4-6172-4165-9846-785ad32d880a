import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";

import NavTabsRequisitions from "src/@core/components/custom-components/NavTabsRequisitions";
import RequisitionDetails from "./RequisitionDetails";

const Requisitions = () => {
  return (
    <Card>
      <NavTabsRequisitions
        tabContent1={
          <>
            <RequisitionDetails/>
          </>
        }
        tabContent2={
          <>
             <RequisitionDetails/>
          </>
        }
        tabContent3={
            <>
               <RequisitionDetails/>
            </>
        }
        tabContent4={
            <>
               <RequisitionDetails/>
            </>
        }
        tabContent5={
            <>
               <RequisitionDetails/>
            </>
        }
      />
    </Card>
  );
};

export default Requisitions;
