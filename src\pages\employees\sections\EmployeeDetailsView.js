// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";


// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import EmployeeDetailsEdit from "./EmployeeDetailsEdit";
import { AuthContext } from "src/context/AuthContext";



const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const EmployeeDetailsView = ({ data, expanded, fetchUsers,employeesOptions,departmentOptions }) => {

  const { listValues } = useContext(AuthContext);
  // ** Hook
  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const locationName = data?.employeeMetaData?.employeeData?.workLocation
  ? listValues.find((item) => item.id === data?.employeeMetaData?.employeeData?.workLocation)?.name
  : null;

  const designation = data?.designation
  ? listValues.find((item) => item.id === data?.designation)?.name
  : null;

  const reporting = data?.reportingTo
  ? employeesOptions.find((item) => item.value === data?.reportingTo)?.key
  : null;

  const department = data?.department
  ? departmentOptions.find((item) => item.value === data?.department)?.key
  : null;
  // data?.designation

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Employee Details"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {" "}
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Employee Id:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.systemCode}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Is Active:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.status}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>First Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.firstName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Last Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.lastName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Designation:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {designation}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Reporting To:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {reporting}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Department:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {department}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Work Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {locationName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Email:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.email}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Mobile Number:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.mobileNumber}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Street1:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.street1}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Street2:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.street2}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>PinCode:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.pinCode}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>City:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.city}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>State:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.state}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Country:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.employeeMetaData?.employeeData?.address?.country}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <EmployeeDetailsEdit  onCancel={editClick} formData={data} fetchUsers={fetchUsers} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default EmployeeDetailsView;