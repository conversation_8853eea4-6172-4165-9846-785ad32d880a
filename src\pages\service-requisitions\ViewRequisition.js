// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import axios from "axios";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import { DataGrid } from "@mui/x-data-grid";
import CloseIcon from "@mui/icons-material/Close";
import EditRequisition from "./EditRequisition";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ViewRequisition = ({
  open,
  onClose,
  role,
  data,
  fetchRequisitions,
  employeesData,
}) => {
  // ** Hook
  const theme = useTheme();

  const [dataView, setDataView] = useState({});

  const [designation, setDesignation] = useState("");

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);

  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);
          const matchingSociety = metadataArray.find(
            (society) => society?.userId === data?.userId
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingSociety) {
            setDataView({
              ...dataView, // This keeps the existing properties
              ...matchingSociety, // This adds/updates properties from newValue
            });

            const loc = matchingSociety?.designation
              ? listOfSubCategories.find(
                  (item) => item.value === matchingSociety?.designation
                )?.key
              : null;

            setDesignation(loc);
          }
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
  }, [data]);

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdown) +
        "?selectionType=SOCIETY_SUB_CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        if (
          res.data.data.length > 0 &&
          res.data.data[0].metaData &&
          res.data.data[0].metaData.subRoleTypes
        ) {
          setSubCategories(res.data.data[0].metaData.subRoleTypes);
        }
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (!!subCategories) {
      let data = [];
      subCategories.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListOfSubCategories(data);
    }
  }, [subCategories]);

  const { user, listValues } = useContext(AuthContext);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [conversation, setConversation] = useState({});

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const serviceType = data?.requisitionData?.serviceType
    ? listValues?.find((item) => item.id === data?.requisitionData?.serviceType)
        ?.name
    : null;

  const priorityName = data?.requisitionData?.priority
    ? listValues?.find((item) => item.id === data?.requisitionData?.priority)
        ?.name
    : null;

  const getNamesFromIds = (ids, listValues) => {
    return ids?.map((id) => {
      const foundItem = listValues?.find((item) => item?.id === id);
      return foundItem ? foundItem?.name : null;
    });
  };

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  useEffect(() => {
    if (data?.requisitionData?.serviceType) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = data?.requisitionData?.serviceType;
      fetchAll(serviceId);
    }
  }, [data]);

  useEffect(() => {
    if (data?.requisitionData?.specifications?.listNames.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.requisitionData?.specifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [data, userList]);

  // Using the function to get names
  const names = getNamesFromIds(data?.requisitionData?.subServices, listValues);

  const subServices = names?.filter((name) => name !== null).join(", ");

  const assignedName = data?.assignedTo
    ? employeesData?.find((item) => item.id === data?.assignedTo)?.name
    : null;

  const status = data?.status
    ? listValues?.find((item) => item.id === data?.status)?.name
    : null;

  const teamMember = data?.teamMember
    ? employeesData?.find((item) => item.id === data?.teamMember)?.name
    : null;

  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Requisition Details
          <Box sx={{ position: "absolute", top: "6px", right: "30px" }}>
            <Tooltip title="Edit">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 30 }}
                onClick={handleOpenEditDialog}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </Box>
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            {!role && (
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Society Details
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <TableContainer
                  sx={{ padding: "4px 6px" }}
                  className="tableBody"
                >
                  <Table>
                    <TableBody
                      sx={{
                        "& .MuiTableCell-root": {
                          p: `${theme.spacing(1.35, 1.125)} !important`,
                        },
                      }}
                    >
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Society name:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.name}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Location:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.location}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Zone:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.zone}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Address:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.societyAddress}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>
                            Society contact name:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.chairmanName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Designation:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {designation}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Contact Number:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.mobileNumber}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Email Id:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {dataView?.loginEmail}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            )}

            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Requisition Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Service Type:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {serviceType}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    {data?.requisitionData?.subServices?.length > 0 && (
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Sub Services:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {subServices}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}
                    {data?.requisitionData?.anyOtherServices && (
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Other Services:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {data?.requisitionData?.anyOtherServices}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Priority:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {priorityName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Budget:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.requisitionData?.budget}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>
                          Requirement Dead Line
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.requisitionData?.requirementDeadLine}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Society Remarks:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.requisitionData?.societyRemarks}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    {specifications
                      ?.slice()
                      ?.sort((a, b) => a.listSequence - b.listSequence)
                      ?.map((category) => (
                        category.values.length > 0 ||
                        category.otherValue ? (
                        <TableRow key={category.name}>
                          <MUITableCell>
                            <Typography style={field}>
                              {category.name}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            {category?.values?.length > 0 ? (
                              category?.values?.map((value, index) => (
                                <Typography key={index} className="data-field">
                                  {value.name}
                                </Typography>
                              ))
                            ) : category?.otherValue && (
                              <Typography className="data-field">
                                {category.otherValue}
                              </Typography>
                            )}
                          </MUITableCell>
                        </TableRow>
                        ):null
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
            {!role && (
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Status and Assignment Details
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <TableContainer
                  sx={{ padding: "4px 6px" }}
                  className="tableBody"
                >
                  <Table>
                    <TableBody
                      sx={{
                        "& .MuiTableCell-root": {
                          p: `${theme.spacing(1.35, 1.125)} !important`,
                        },
                      }}
                    >
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Reference Type:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {data?.referenceType}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>
                            Houzer Society Team Member:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {teamMember}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Referral Name:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {data?.referralName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Assigned To:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {assignedName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Status</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {status}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Data Sent Date:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {data?.dataSentDate}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            )}

            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Comments Section
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              {data?.conversationData.length > 0 ? (
                <TableContainer>
                  <Table sx={{ ml: 4 }}>
                    <TableHead>
                      {role ? (
                        <TableRow>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      ) : (
                        <TableRow>
                          <MUITableCell>Follow-up Date</MUITableCell>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>Follow Up Action</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {role
                        ? data?.conversationData?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))
                        : data?.conversationData?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.followUpDate}</MUITableCell>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>{row.followUpAction}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      height: "50px", // Adjust height as needed
                    }}
                  >
                    <Typography variant="body1">
                      No conversation happened yet
                    </Typography>
                  </Box>
                </>
              )}
            </Card>
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Comments Details
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
               <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
          </DialogTitle>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Follow Up Date
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpDate}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}

                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>Comments</Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.comments}</Typography>
                  </MUITableCell>
                </TableRow>
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        FollowUp Action
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpAction}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {role ? (
        <EditRequisition
          role={role}
          open={openEditDialog}
          formData={data}
          listOfSubCategories={listOfSubCategories}
          formattedData={specifications}
          setSpecifications={setSpecifications}
          employeesData={employeesData}
          onClose={handleCloseEditDialog}
          fetchRequisitions={fetchRequisitions}
        />
      ) : (
        <EditRequisition
          open={openEditDialog}
          formData={data}
          listOfSubCategories={listOfSubCategories}
          formattedData={specifications}
          setSpecifications={setSpecifications}
          employeesData={employeesData}
          onClose={handleCloseEditDialog}
          fetchRequisitions={fetchRequisitions}
        />
      )}
    </>
  );
};
export default ViewRequisition;
