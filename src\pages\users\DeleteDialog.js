import React, { useContext } from "react";
import {
  Dialog,
  DialogActions,
  Button,
  Box,
  DialogContent,
  DialogContentText,
} from "@mui/material";

const DeleteDialog = ({ open, onClose, handleYesButtonClick }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      PaperProps={{
        sx: {
          p: (theme) => `${theme.spacing(2.5)} !important`,
          backgroundColor: (theme) => theme.palette.primary.background,
        },
      }}
    >
      <Box
        sx={{
          width: "100%",
          borderRadius: 1,
          textAlign: "center",
          border: (theme) => `1px solid ${theme.palette.divider}`,
          borderColor: "primary.main",
        }}
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description" color="primary.main">
            Are you sure, you want to delete row?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            variant="contained"
            onClick={handleYesButtonClick}
            sx={{ margin: "auto", width: 100 }}
          >
            Yes
          </Button>
          <Button
            variant="contained"
            onClick={onClose}
            sx={{ margin: "auto", width: 100 }}
          >
            No
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default DeleteDialog;
