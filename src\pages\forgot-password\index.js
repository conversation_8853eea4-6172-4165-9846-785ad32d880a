import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Alert,
  InputAdornment,
  Snackbar,
} from '@mui/material';
import {
  Email as EmailIcon,
  ArrowBack as ArrowBackIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { validationRules } from '../../utils/validation';
import LoadingSpinner from '../../components/common/LoadingSpinner';

/**
 * Forgot Password page component
 */
const ForgotPasswordPage = () => {
  const router = useRouter();
  const { forgotPassword, isAuthenticated, loading, error, clearError } = useAuth();
  
  const [forgotPasswordError, setForgotPasswordError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showSnackbar, setShowSnackbar] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      email: '',
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, loading, router]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setForgotPasswordError('');
  }, [clearError]);

  /**
   * Handle form submission
   * @param {Object} data - Form data
   */
  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setForgotPasswordError('');
    setSuccessMessage('');
    clearError();

    try {
      const result = await forgotPassword(data.email);
      
      if (result.success) {
        setSuccessMessage(result.message);
        setShowSnackbar(true);
        reset(); // Clear the form
        
        // Redirect to login page after a delay
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } else {
        setForgotPasswordError(result.error);
      }
    } catch (err) {
      setForgotPasswordError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle back to login
   */
  const handleBackToLogin = () => {
    router.push('/login');
  };

  /**
   * Handle snackbar close
   */
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setShowSnackbar(false);
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner loading={true} message="Checking authentication..." overlay />;
  }

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
          }}
        >
          {/* Header */}
          <Typography component="h1" variant="h4" gutterBottom>
            Forgot Password?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
            No worries! Enter your email address and we'll send you a link to reset your password.
          </Typography>

          {/* Success Alert */}
          {successMessage && (
            <Alert severity="success" sx={{ mb: 3, width: '100%' }}>
              {successMessage}
            </Alert>
          )}

          {/* Error Alert */}
          {(error || forgotPasswordError) && (
            <Alert severity="error" sx={{ mb: 3, width: '100%' }}>
              {error || forgotPasswordError}
            </Alert>
          )}

          {/* Forgot Password Form */}
          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ width: '100%' }}>
            {/* Email Field */}
            <TextField
              {...register('email', validationRules.email)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Email Address"
              autoComplete="email"
              autoFocus
              error={!!errors.email}
              helperText={errors.email?.message || 'Enter the email address associated with your account'}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isSubmitting}
              startIcon={<SendIcon />}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isSubmitting ? 'Sending Reset Link...' : 'Send Reset Link'}
            </Button>

            {/* Back to Login Button */}
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToLogin}
              sx={{ mb: 2, py: 1.5 }}
            >
              Back to Login
            </Button>

            {/* Additional Help */}
            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Still having trouble?
              </Typography>
              <Link
                href="mailto:<EMAIL>"
                variant="body2"
                sx={{ textDecoration: 'none' }}
              >
                Contact Support
              </Link>
            </Box>
          </Box>

          {/* Instructions */}
          <Box sx={{ mt: 4, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1, width: '100%' }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              <strong>What happens next?</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary" component="div">
              <ul style={{ margin: 0, paddingLeft: '1.2rem' }}>
                <li>We'll send a password reset link to your email</li>
                <li>Click the link in the email to reset your password</li>
                <li>Create a new password and sign in to your account</li>
              </ul>
            </Typography>
            <Typography variant="caption" color="text.disabled" sx={{ mt: 1, display: 'block' }}>
              The reset link will expire in 24 hours for security reasons.
            </Typography>
          </Box>
        </Paper>
      </Box>

      {/* Success Snackbar */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="success" sx={{ width: '100%' }}>
          Password reset link sent successfully! Check your email.
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ForgotPasswordPage;
