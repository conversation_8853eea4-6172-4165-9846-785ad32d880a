// ** Next Import

// ** <PERSON><PERSON> Components
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import { styled } from '@mui/material/styles'
import { useEffect } from 'react'

// ** Layout Import

// ** Demo Imports
import FooterIllustrations from 'src/views/pages/misc/FooterIllustrations'


import { useRBAC } from "src/pages/permission/RBACContext"

import { useRouter } from "next/router"

// ** Styled Components
const BoxWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.down('md')]: {
    width: '90vw'
  }
}))


const transactions = () => {
  
  const { can,rbacRoles } = useRBAC();
  const router = useRouter();


  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0){
      if (!can('transaction_READ')) {
        router.push("/401");
      }
    }  
  }, [rbacRoles]);


  return (
    <>
      {can('transaction_READ') && 
        <Box className='content-center'>
        <Box sx={{ p: 5, display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
          <BoxWrapper>
          <Typography sx={{ mb: 6, color: 'text.secondary',fontSize: '2.5rem' }}>
              Work in progress in transactions
            </Typography>
          </BoxWrapper>
         
        </Box>
        <FooterIllustrations />
      </Box>
      }
    </>
    
  );
}


export default transactions;
