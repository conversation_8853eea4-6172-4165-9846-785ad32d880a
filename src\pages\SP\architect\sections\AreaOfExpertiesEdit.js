import { useState, useContext } from "react";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Box, Checkbox, Dialog, DialogActions, DialogContentText, DialogTitle, Divider, TextField } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormControlLabel, Switch, Typography } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";

const schema = yup.object().shape({
  healthCare: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
  townShip: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
  commercial: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
  hospitality: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
  education: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
  leisure: yup
    .number()
    .min(0, "Minimum value should be 0")
    .max(100, "Maximum value should be 100")
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
    other: yup
    .number()
    .typeError('Other must be a number')
    .min(0, 'Minimum value should be 0')
    .max(100, 'Maximum value should be 100')
    .nullable(true) 
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
    
});



const AreaOfExpertiseEdit = ({  formData,onCancel  }) => {

  const auth = useAuth();
 
 
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const handleError = () => {

    const message = `
    <div>
    <h3>
    Only 6 fields can be selected. Please adjust your selection.
    </h3>
    </div>`
    setDialogMessage(message);
    setDialogOpen(true);
    };

  const handleFailure = () => {

    const message = `
    <div>
    <h3>
    Total weightage exceeds 100! Please adjust the values
    </h3>
    </div>`
    setDialogMessage(message);
    setDialogOpen(true);
    };

    const handleDialogClose = () => {
      setDialogOpen(false);
    };


    const { control, handleSubmit, register, formState: { errors }, reset } = useForm({
      defaultValues: {
        healthCare: formData?.areaOfExpertiseDTO?.healthCare !== null ? formData?.areaOfExpertiseDTO?.healthCare : 0,
        townShip: formData?.areaOfExpertiseDTO?.townShip !== null ? formData?.areaOfExpertiseDTO?.townShip : 0,
        commercial: formData?.areaOfExpertiseDTO?.commercial !== null ? formData?.areaOfExpertiseDTO?.commercial : 0,
        hospitality: formData?.areaOfExpertiseDTO?.hospitality !== null ? formData?.areaOfExpertiseDTO?.hospitality : 0,
        education: formData?.areaOfExpertiseDTO?.education !== null ? formData?.areaOfExpertiseDTO?.education : 0,
        leisure: formData?.areaOfExpertiseDTO?.leisure !== null ? formData?.areaOfExpertiseDTO?.leisure : 0,
        other: formData?.areaOfExpertiseDTO?.other !== null ? formData?.areaOfExpertiseDTO?.other : 0,
      },
      
      resolver: yupResolver(schema),
    });



    async function onSubmit(formData) {

      // Calculate total weightage
      const totalWeightage =
        formData.healthCare +
        formData.townShip +
        formData.commercial +
        formData.hospitality +
        formData.education +
        formData.leisure;
    
      // Check if total weightage exceeds 100
      if (totalWeightage > 100) {
        console.error("Total weightage exceeds 100! Please adjust the values.");
        handleFailure();
        return;
      }

      const selectedFields = Object.values(formData).filter(value => value > 0);
      if (selectedFields.length > 6) {
        console.error("Only 6 fields can be selected. Please adjust your selection.");
        handleError();
        return;
      }
    
      console.log("weightage", totalWeightage);
    
      
      const fields = {
        areaOfExpertiseDTO: {
          healthCare: formData.healthCare,
          townShip: formData.townShip,
          commercial: formData.commercial,
          hospitality: formData.hospitality,
          education: formData.education,
          leisure: formData.leisure,
          other: formData.other,
        },
      };
      const response = await auth.updateEntityServices(fields);
      console.log("submitted data", fields);
    
      if (response) {
    
        reset({
          healthCare: response.areaOfExpertiseDTO.healthCare,
          townShip: response.areaOfExpertiseDTO.townShip,
          commercial: response.areaOfExpertiseDTO.commercial,
          hospitality: response.areaOfExpertiseDTO.hospitality,
          education: response.areaOfExpertiseDTO.education,
          leisure: response.areaOfExpertiseDTO.leisure,
          other: response.areaOfExpertiseDTO.other,
        });
        setter(response);
      }
      onCancel();
    }
    

  return (
    <>
      <Grid container spacing={4} sx={{ display: "flex", alignItems: "center" }}>

        <Grid item sx={{ textAlign: 'center' }} xs={12} sm={4}>
          <FormControl fullWidth sx={{ paddingTop: '20px' }}>
            <Typography sx={{ fontWeight: 600 }}>Sectors:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
  <FormControl fullWidth sx={{ paddingTop: '20px', textAlign: 'center', marginLeft: '250px' }}>
    <Typography sx={{ fontWeight: 600 }}>Weightage:</Typography>
  </FormControl>
</Grid>

       
        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
          <Typography >HealthCare</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="healthCare"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.healthCare}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="HealthCare"
                  type="number"
                  inputProps={{ min: 0, max: 100 }}
                  onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.healthCare)}
                  helperText={errors.healthCare?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>
       
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>TownShip</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="townShip"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.townShip}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="TownShip"
                  inputProps={{ min: 0, max: 100 }}
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.townShip)}
                  helperText={errors.townShip?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>
          
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>Commercial</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="commercial"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.commercial}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Commercial"
                  type="number"
                  inputProps={{ min: 0, max: 100 }}
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.commercial)}
                  helperText={errors.commercial?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>

        
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>Hospitality</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="hospitality"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.hospitality}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Hospitality"
                  inputProps={{ min: 0, max: 100 }}
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.hospitality)}
                  helperText={errors.hospitality?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>
                    
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>Education</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="education"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.education}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Education"
                  type="number"
                  inputProps={{ min: 0, max: 100 }}
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.education)}
                  helperText={errors.education?.message}
                  aria-describedby="validation-basic-textarea"
              
                />
              )}
            />
          </FormControl>
        </Grid>

        
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>Leisure</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="leisure"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.leisure}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Leisure"
                  type="number"
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.leisure)}
                  helperText={errors.leisure?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth sx={{ paddingLeft: '20px' }}>
            <Typography>Other</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="other"
              control={control}
              defaultValue={formData?.areaOfExpertiseDTO?.other}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Other"
                  type="number"
                  inputProps={{ min: 0, max: 100 }}
                   onChange={(e) => {
                    let newValue = e.target.value.trim() === "" ? 0 : parseFloat(e.target.value);
                    if (newValue < 0) {
                      newValue = 0; 
                    } 
                    field.onChange(isNaN(newValue) ? '' : newValue); 
                  }}
                  value={field.value || ''}
                  error={Boolean(errors.other)}
                  helperText={errors.other?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
    <Divider sx={{ my: (theme) => `${theme.spacing(0)} !important` }} />
  </Grid>
        <Dialog
          open={dialogOpen}
          onClose={() => handleDialogClose(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogTitle  color="primary.main" fontWeight={"bold"}>{}</DialogTitle>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
              sx={{ padding: '20px',marginTop:"-43px" }}
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            
            </DialogContentText>
            <DialogActions sx={{ justifyContent: 'center',marginTop:"-15px" }}>
            <Button
                onClick={() => handleDialogClose(true)}
                color="primary"
                autoFocus
              >
                okay
              </Button>
             
            </DialogActions>
          </Box>
        </Dialog>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              onClick={() => onCancel()}
              variant="outlined"
              color="primary"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={handleSubmit(onSubmit)}
              variant="contained"
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
              }

export default AreaOfExpertiseEdit;
