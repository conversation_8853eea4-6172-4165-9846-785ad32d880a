// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Styled Component
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import CompanyDetails from "./sections/SocietyDetails";
import LandDetails from "./sections/LandDetails";
import FsiDetails from "./sections/FsiDetails";
import Requirements from "./sections/Requirements";
import ContactsReferences from "./sections/ContactsReferences";
import OtherDetails from "./sections/OtherDetails";
import SocietyDetails from "./sections/SocietyDetails";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import { useRouter } from "next/router";
import { Box } from "@mui/material";
import AssignmentAndStatus from "./sections/AssignmentAndStatus";
import { useRBAC } from "src/pages/permission/RBACContext";

const RegistrationForm = () => {
  const { can } = useRBAC();
  const { entityData, getSocietyProfile, user } = useContext(AuthContext);

  console.log("Entity data in society", entityData);
  console.log("user data from auth context", user);
  const [entityCategory, setEntityCategory] = useState("");
  const router = useRouter();

  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    getSocietyProfile();
    console.log("use effect -Society");
  }, []);

  const handleToggle = (value) => {
    setExpanded(value);
  };

  // const hasCreatePermission = user.permissions.includes(
  //   "societyProfile_CREATE"
  // );
  // useEffect(() => {
  //   if (!hasCreatePermission) {
  //     router.push("/401");
  //   }
  // }, []);

  // if (hasCreatePermission) {
  if(can('chsProfile_READ')){
    return (
      <div>
        <>
          <style>
            {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
      `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={2} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={12} sx={{ position: "relative" }}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Society Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                    <Box sx={{ position: "absolute", top: 1, right: 0 }}>
                      <CloseExpandIcons
                        expanded={expanded}
                        onToggle={handleToggle}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <SocietyDetails
                  data={entityData}
                  expanded={expanded}
                ></SocietyDetails>
              </Grid>
              <Grid item xs={12}>
                <LandDetails
                  data={entityData}
                  expanded={expanded}
                ></LandDetails>
              </Grid>
              <Grid item xs={12}>
                <FsiDetails data={entityData} expanded={expanded}></FsiDetails>
              </Grid>
              <Grid item xs={12}>
                <Requirements
                  data={entityData}
                  expanded={expanded}
                ></Requirements>
              </Grid>
              <Grid item xs={12}>
                <ContactsReferences
                  data={entityData}
                  expanded={expanded}
                ></ContactsReferences>
              </Grid>
              <Grid item xs={12}>
                <OtherDetails
                  data={entityData}
                  expanded={expanded}
                ></OtherDetails>
              </Grid>
              <Grid item xs={12}>
                <AssignmentAndStatus data={entityData} expanded={expanded} />
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );}
   
  else {
    return null;
  }
};

export default RegistrationForm;
