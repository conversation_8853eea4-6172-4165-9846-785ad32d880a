// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";
import { FormControl, Radio, RadioGroup, TextField ,useMediaQuery } from "@mui/material";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const ValidManagingCommittee = ({
  posts,
  setNextActive,
  handleIsExistingManagingCommitteeChange,
  handleTenureInYearsChange, handleValidTillDateChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  const [isExistingManagingCommittee, setIsExistingManagingCommittee] =
    useState(defaultData?.isExistingManagingCommitteeProvisionsAct);

    const [tenureYears, setTenureYears] = useState(defaultData?.tenureYears);
    const [errors, setErrors] = useState({ tenureYears: "", validTillDate: "" });
    const [validTillDate, setValidTillDate] = useState(null);
  const [validManagingCommittee, setValidManagingCommittee] = useState([]);


  useEffect(() => {
    if (defaultData && defaultData.validTillDate) {
      const parsedDate = new Date(defaultData?.validTillDate);
      setValidTillDate(parsedDate);
    }
  }, [defaultData]);

  const handleChange = (event) => {
    const value = event.target.value;
    setIsExistingManagingCommittee(value);
    handleIsExistingManagingCommitteeChange(value);
  };

  
  const handleDateChange = (date) => {
    setValidTillDate(date);
    handleValidTillDateChange(date.toISOString())
    setErrors((prevErrors) => ({
      ...prevErrors,
      validTillDate: "",
    }));
  };

  const handleTotalNumberChange = (event) => {
    const tenureYears = event.target.value.trim();


    if (/^\d*$/.test(tenureYears) && !/^0\d+$/.test(tenureYears)) {
      setTenureYears(tenureYears);
      handleTenureInYearsChange(tenureYears);
      if (tenureYears < 1 || tenureYears > 20) {
        setErrors({
          tenureYears: {
            type: 'range',
            message: 'Tenure in No. of Years should be between 1 and 20',
          },
        });
      } else {
        setErrors({}); // Clear the error if the entered value is valid
      }
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        tenureYears: {
          type: 'invalid',
          message: 'Enter a valid Tenure in No. of Years',
        },
      }));
    }
  };


  const {
    settings: { direction },
  } = useSettings();

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setValidManagingCommittee(data);
    }
  }, [posts]);

  useEffect(() => {
    if (isExistingManagingCommittee && email &&contactNumber &&name&&societyName && tenureYears && validTillDate) {
      setNextActive(true);
    }else {
      setNextActive(false);
    }
  }, [isExistingManagingCommittee,name,email,contactNumber,societyName,tenureYears,validTillDate]);

  const theme = useTheme();
  const isXs = useMediaQuery((theme) => theme.breakpoints.down('xs'));

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 4,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h5"
                sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}
              >
                Is the existing managing committee in accordance with the
                provisions of this Act and rules and bye-laws. Is their terms
                are valid and not expired ?
              </Typography>
            </Box>

            <Box>
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={isExistingManagingCommittee}
                  aria-label="simple-radio"
                >
                  <FormControlLabel
                    control={
                      <Radio
                        id='validManagingCommittee-yes'
                        value="Yes"
                        checked={isExistingManagingCommittee === "Yes"}
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
  
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold"  , fontSize:'1rem !important' }}>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel 
                    control={
                      <Radio
                        id='validManagingCommittee-no'
                        value="No"
                        checked={isExistingManagingCommittee === "No"}
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
  
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:'1rem !important'  }}>
                        No
                      </Typography>
                    }
                    checked={isExistingManagingCommittee === "No"}
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          {/* <Divider sx={{ mt: `${theme.spacing(12.25)} !important`, mb: `${theme.spacing(3.25)} !important`, }} /> */}
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            type='number'
            size='small'
            value={tenureYears}
            label='Tenure in No. of Years'
            InputLabelProps={{
              sx: {
                fontSize: '0.9rem', // Adjust the font size as needed
              },
            }}
            onChange={handleTotalNumberChange}
            helperText={errors.tenureYears?.message}
            error={Boolean(errors.tenureYears)}
            placeholder='Enter Tenure in No. of Years'
            onKeyPress={(event) => {
              if (event.target.value.length >= 2) {
                event.preventDefault();
              }
            }}
          />
          {errors.tenureYears?.type === 'required' && (
            <FormHelperText sx={{ color: 'error.main' }} id='validation-tenureYears'>
              Tenure in No. of Years is required
            </FormHelperText>
          )}
        </Grid>
        <Grid item xs={12} sm={3}>
          <DatePicker
            selected={validTillDate}
            onChange={handleDateChange}
            popperPlacement={isXs ? 'top !important' : 'bottom'} // Adjust placement based on screen size
            customInput={
              <TextField
                label="Valid till Date(D/M/Y)"
                InputLabelProps={{
                  sx: {
                    fontSize: '0.9rem', // Adjust the font size as needed
                  },
                }}
                fullWidth
                size="small"
                margin="dense"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
                sx={{ mb: 0, mt: 0 }}
                error={Boolean(errors.validTillDate)}
                helperText={errors.validTillDate?.message}
              />
            }
            dateFormat="dd/MM/yyyy"
          />
          {errors.validTillDate?.type === 'required' && (
            <FormHelperText sx={{ color: 'error.main' }} id='validation-validTillDate'>
              Valid till date is required
            </FormHelperText>
          )}
        </Grid>

      </Grid>

      {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          <Box>
            {validManagingCommittee.length > 0 && (
              <Typography
                variant="body1"
                sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
              >
                <Divider
                  sx={{
                    mt: `${theme.spacing(3)} !important`,
                    mb: `${theme.spacing(3)} !important`,
                  }}
                />
                Review below articles for more info
              </Typography>
            )}
          </Box>

      {validManagingCommittee.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={validManagingCommittee} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default ValidManagingCommittee;
