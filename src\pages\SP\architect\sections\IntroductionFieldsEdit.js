// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  TextField,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";

const IntroductionFieldsEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [architecture, setArchitecture] = useState(formData?.architecture);
  const [urbanDesign, setUrbanDesign] = useState(formData?.urbanDesign);
  const [interiors, setInteriors] = useState(formData?.interiors);
  const [masterPlanning, setMasterPlanning] = useState(
    formData?.masterPlanning
  );
  const [structuralRepairs, setStructuralRepairs] = useState(
    formData?.structuralRepairs
  );
  const [landscapeIntegration, setLandscapeIntegration] = useState(
    formData?.landscapeIntegration
  );
  const [otherText, setOtherText] = useState(formData?.otherText);
  const [others, setOthers] = useState(formData?.others);
  const [allIntroductionFields, setAllIntroductionFields] = useState(
    formData?.allIntroductionFields
  );
  const [checkboxChanged, setCheckboxChanged] = useState(false);
  const [fieldChanged, setFieldChanged] = useState(false);

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    if (
      architecture !== formData?.architecture ||
      urbanDesign !== formData?.urbanDesign ||
      interiors !== formData?.interiors ||
      masterPlanning !== formData?.masterPlanning ||
      structuralRepairs !== formData?.structuralRepairs ||
      landscapeIntegration !== formData?.landscapeIntegration ||
      others !== formData?.others ||
      otherText !== formData?.otherText
    ) {
      setCheckboxChanged(true);
    } else {
      setCheckboxChanged(false);
    }
    const introductionYoutubeUrl = getValues("introductionYoutubeUrl");

    if (introductionYoutubeUrl !== formData?.introductionYoutubeUrl) {
      setFieldChanged(true);
    } else {
      setFieldChanged(false);
    }
  }, [
    architecture,
    urbanDesign,
    interiors,
    masterPlanning,
    structuralRepairs,
    landscapeIntegration,
    others,
    otherText,
    getValues,
    formData,
  ]);
  useEffect(() => {
    if (
      architecture &&
      urbanDesign &&
      interiors &&
      masterPlanning &&
      structuralRepairs &&
      landscapeIntegration &&
      others
    ) {
      setValue("allIntroductionFields", true);
      setAllIntroductionFields(true);
    } else {
      setValue("allIntroductionFields", false);
      setAllIntroductionFields(false);
    }
  }, [
    architecture,
    urbanDesign,
    interiors,
    masterPlanning,
    structuralRepairs,
    landscapeIntegration,
    others,
  ]);

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    console.log("trim", trimmedData);

    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" IntroductionFields Details failed");
    });
    onCancel();
  }

  const handleCheckboxChange = (e) => {
    const isChecked = e.target.checked;
    setOthers(isChecked);
  };

  const handleTextChange = (e) => {
    const textValue = e.target.value;
    setOtherText(textValue);
  };

  return (
    <>
      <Box sx={{ pt: 1.5 }}>
        <Grid item xs={12}>
          <FormControl>
            <Controller
              name="allIntroductionFields"
              control={control}
              defaultValue={
                formData?.allIntroductionFields ? allIntroductionFields : false
              }
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value}
                      style={{ transform: "scale(1)" }}
                      onChange={(e) => {
                        field.onChange(e);

                        if (e.target.checked) {
                          setValue("allIntroductionFields", true);
                          setAllIntroductionFields(true);

                          setValue("architecture", true);
                          setArchitecture(true);
                          setValue("urbanDesign", true);
                          setUrbanDesign(true);
                          setValue("interiors", true);
                          setInteriors(true);
                          setValue("masterPlanning", true);
                          setMasterPlanning(true);
                          setValue("structuralRepairs", true);
                          setStructuralRepairs(true);
                          setValue("landscapeIntegration", true);
                          setLandscapeIntegration(true);
                        } else {
                          setValue("allIntroductionFields", false);
                          setAllIntroductionFields(false);

                          setValue("architecture", false);
                          setArchitecture(false);
                          setValue("urbanDesign", false);
                          setUrbanDesign(false);
                          setValue("interiors", false);
                          setInteriors(false);
                          setValue("masterPlanning", false);
                          setMasterPlanning(false);
                          setValue("structuralRepairs", false);
                          setStructuralRepairs(false);
                          setValue("landscapeIntegration", false);
                          setLandscapeIntegration(false);
                        }
                      }}
                    />
                  }
                  label={<span>ALL</span>}
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="architecture"
                control={control}
                defaultValue={formData?.architecture ? architecture : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("architecture", true);
                            setArchitecture(true);
                          } else {
                            setValue("architecture", false);
                            setArchitecture(false);
                          }
                        }}
                      />
                    }
                    label={<span>Architecture</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="urbanDesign"
                control={control}
                defaultValue={formData?.urbanDesign ? urbanDesign : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("urbanDesign", true);
                            setUrbanDesign(true);
                          } else {
                            setValue("urbanDesign", false);
                            setUrbanDesign(false);
                          }
                        }}
                      />
                    }
                    label={<span>Urban Design</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="interiors"
                control={control}
                defaultValue={formData?.interiors ? interiors : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("interiors", true);
                            setInteriors(true);
                          } else {
                            setValue("interiors", false);
                            setInteriors(false);
                          }
                        }}
                      />
                    }
                    label={<span>Interiors</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="masterPlanning"
                control={control}
                defaultValue={formData?.masterPlanning ? masterPlanning : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("masterPlanning", true);
                            setMasterPlanning(true);
                          } else {
                            setValue("masterPlanning", false);
                            setMasterPlanning(false);
                          }
                        }}
                      />
                    }
                    label={<span>Matser Planning</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="structuralRepairs"
                control={control}
                defaultValue={
                  formData?.structuralRepairs ? structuralRepairs : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("structuralRepairs", true);
                            setStructuralRepairs(true);
                          } else {
                            setValue("structuralRepairs", false);
                            setStructuralRepairs(false);
                          }
                        }}
                      />
                    }
                    label={<span>Structural Repairs</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="landscapeIntegration"
                control={control}
                defaultValue={
                  formData?.landscapeIntegration ? landscapeIntegration : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("landscapeIntegration", true);
                            setLandscapeIntegration(true);
                          } else {
                            setValue("landscapeIntegration", false);
                            setLandscapeIntegration(false);
                          }
                        }}
                      />
                    }
                    label={<span>Landscape Integration</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="others"
                control={control}
                defaultValue={formData?.others ? others : false}
                render={({ field }) => (
                  <>
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...field}
                          checked={field.value}
                          style={{ transform: "scale(1)" }}
                          onChange={(e) => {
                            field.onChange(e);
                            setValue("others", e.target.checked);
                            setOthers(e.target.checked);
                          }}
                        />
                      }
                      label="Others"
                    />
                    {field.value && (
                      <TextField
                        name="otherText"
                        label="Others"
                        defaultValue={formData?.otherText}
                        variant="outlined"
                        style={{ width: "600px" }}
                        {...register("otherText", {
                          required: field.value
                            ? "This field is required"
                            : false,
                        })}
                        error={Boolean(errors.otherText)}
                        helperText={errors.otherText?.message}
                      />
                    )}
                  </>
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} marginTop={3}>
            <FormControl>
              <Controller
                name="introductionYoutubeUrl"
                control={control}
                rules={{
                  required: "This field is required",
                  pattern: {
                    value:
                      /^(https?\:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+/,
                    message:
                      "Enter a valid YouTube URL (e.g., https://www.youtube.com/watch?v=VIDEO_ID)",
                  },
                }}
                defaultValue={formData?.introductionYoutubeUrl}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Youtube Url"
                    variant="outlined"
                    style={{ width: "600px" }}
                    error={Boolean(errors.introductionYoutubeUrl)}
                    helperText={errors.introductionYoutubeUrl?.message}
                    aria-describedby="validation-youtube-url"
                    onChange={(e) => {
                      field.onChange(e);
                      setFieldChanged(true);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} marginTop={5}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={!checkboxChanged && !fieldChanged}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default IntroductionFieldsEdit;
