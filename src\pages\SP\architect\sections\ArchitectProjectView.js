// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ArchitectProjectEdit from "./ArchitectProjectEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ArchitectProjectView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  console.log("Architect Project List",data);

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Project"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Image</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {data?.projectsList?.map((project, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography
                            className="data-field"
                            marginLeft={"12px"}
                          >
                            {project?.projectName}
                          </Typography>
                        </TableCell>

                        <TableCell>
                          <Typography className="data-field">
                            {project?.filePath &&
                              project.filePath.split("/").pop()}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <ArchitectProjectEdit data={data} onCancel={editClick3} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default ArchitectProjectView;
