// ** React Imports
import { useState, forwardRef } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Checkbox from '@mui/material/Checkbox'
import MenuItem from '@mui/material/MenuItem'
import TextField from '@mui/material/TextField'
import FormGroup from '@mui/material/FormGroup'
import FormLabel from '@mui/material/FormLabel'
import InputLabel from '@mui/material/InputLabel'
import FormControl from '@mui/material/FormControl'
import OutlinedInput from '@mui/material/OutlinedInput'
import FormControlLabel from '@mui/material/FormControlLabel'
import Select from '@mui/material/Select'

import { useForm, Controller } from 'react-hook-form'

import { useTheme } from "@emotion/react";

// ** Custom Components Imports
import CustomChip from 'src/@core/components/mui/chip'

// ** Third Party Imports
import format from 'date-fns/format'
import DatePicker from 'react-datepicker'
import { Divider, FormHelperText } from '@mui/material'
import SelectCategory from 'src/@core/components/custom-components/SelectCategory'

const offeredItemsArray = [
  'Apple iPhone 12 Pro Max (256GB)',
  'Apple iPhone 12 Pro (512GB)',
  'Apple iPhone 12 Mini (256GB)',
  'Apple iPhone 11 Pro Max (256GB)',
  'Apple iPhone 11 (64GB)',
  'OnePlus Nord CE 56 (128GB)'
]

const CustomInput = forwardRef((props, ref) => {
  const startDate = props.start !== null ? format(props.start, 'MM/dd/yyyy') : ''
  const endDate = props.end !== null ? ` - ${format(props.end, 'MM/dd/yyyy')}` : null
  const value = `${startDate}${endDate !== null ? endDate : ''}`

  return <TextField fullWidth inputRef={ref} label={props.label || ''} {...props} value={value} />
})

const PlotDetails = () => {
  // ** State

  const [ward,setWard] = useState("")
  const [zone, setZone] = useState("")
  const [subZone,setSubZone] = useState("")
  const [division,setDivision] = useState("")
  const [village,setVillage] = useState("")

  const theme = useTheme()


  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    formState: { errors }
  } = useForm()

  return (
    <Grid container spacing={5}>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}  sx={{ marginTop: 5 }}>
        <Grid item xs={3} sm={3} sx={{ marginTop: 5 }}>
            CTS No
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='ctsNumber'
              control={control}

              //defaultValue={formData?.ctsNumber}
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='text'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.ctsNumber)}
                  helperText={errors.ctsNumber?.message}
                  aria-describedby='validation-ctsNumber'
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }}>        
            Ward
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ marginTop: 2 }}>
           <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"ward"}
              name="ward"
              nameArray={[]}
              defaultValue={""}
              value={ward}
              onChange={(e) => {
                setWard(e.target.value);
              }}
              error={Boolean(errors.ward)}
              aria-describedby="validation-ward"
            />
            {errors.ward && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-ward"
              >
                Please Choose Your Ward
              </FormHelperText>
            )}
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={3} sm={3} sx={{ marginTop: 5 }}>
            Zone
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"zone"}
              name="zone"
              nameArray={[]}
              defaultValue={""}
              value={zone}
              onChange={(e) => {
                setZone(e.target.value);
              }}
              error={Boolean(errors.zone)}
              aria-describedby="validation-zone"
            />
            {errors.zone && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-zone"
              >
                Please select zone
              </FormHelperText>
            )}
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }}>
            Division
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ marginTop: 2 }}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"division"}
              name="division"
              nameArray={[]}
              defaultValue={""}
              value={division}
              onChange={(e) => {
                setDivision(e.target.value);
              }}
              error={Boolean(errors.division)}
              aria-describedby="validation-division"
            />
            {errors.division && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-division"
              >
                Please select division
              </FormHelperText>
            )}
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={3} sm={3} sx={{ marginTop: 5 }}>
            Sub Zone
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"subZone"}
              name="subZone"
              nameArray={[]}
              defaultValue={""}
              value={subZone}
              onChange={(e) => {
                setSubZone(e.target.value);
              }}
              error={Boolean(errors.subZone)}
              aria-describedby="validation-subZone"
            />
            {errors.subZone && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-subZone"
              >
                Please select subZone
              </FormHelperText>
            )}
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }}>
            Village
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ marginTop: 2 }}>
        <SelectCategory
              clearErrors={clearErrors}
              register={register}
              id={"village"}
              name="village"
              nameArray={[]}
              defaultValue={""}
              value={village}
              onChange={(e) => {
                setVillage(e.target.value);
              }}
              error={Boolean(errors.village)}
              aria-describedby="validation-village"
            />
            {errors.village && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-village"
              >
                Please select village
              </FormHelperText>
            )}
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={3} sm={3} sx={{ marginTop: 5 }}>
            Road Width (mtrs)
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='roadWidth'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.roadWidth)}
                  helperText={errors.roadWidth?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }}>
            RR rate Land '22-'23
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name='rrRate'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.rrRate)}
                  helperText={errors.rrRate?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={3} sm={3} sx={{ marginTop: 5 }}>
            Height Restrictions NOCAS (Mtrs) ASML
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='heightNocas'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.heightNocas)}
                  helperText={errors.heightNocas?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }}>
            Permissions
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name='permissions'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='text'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.permissions)}
                  helperText={errors.permissions?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>

      <Divider sx={{ mt: `${theme.spacing(4)} !important`, mb: `${theme.spacing(3.25)} !important` }} />

      <Grid container item xs={12} sm={12} sx={{ marginTop: 15 }} >
        <Grid item xs={6} sm={6}>
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
          
            Sq.Mtrs
          
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
            Sq.Ft
         
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
         
            % Used
         
        </Grid>
      </Grid>


      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={6} sm={6} sx={{ marginTop: 5 }}>
         
            PR Card Plot Area
         
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='prCardPlotAreaSqmt'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.prCardPlotArea)}
                  helperText={errors.prCardPlotArea?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
          
            XXXXXX
         
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
       
            XXXXXX
         
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={6} sm={6} sx={{ marginTop: 5 }}>         
            Road Set Back
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='roadSetBackSqmt'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.roadSetBack)}
                  helperText={errors.roadSetBack?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
         
            XXXXXX
        
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
          
            XXXXXX
          
        </Grid>
      </Grid>


      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={6} sm={6} sx={{ marginTop: 5 }}>
        
            Physical Survey Plot Area
        
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='physicalSurveyPlotAreaSqmt'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.physicalSurveyPlotArea)}
                  helperText={errors.physicalSurveyPlotArea?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
        
            XXXXXX
          
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
          
            XXXXXX
          
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={6} sm={6} sx={{ marginTop: 5 }}>
          
            Net Plot for Calculations
         
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2}  sx={{ marginTop: 5 }}>
                XXXXXX  
        </Grid>
        <Grid item xs={2} sm={2}  sx={{ marginTop: 5 }} alignItems="center">
                XXXXXX  
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
                XXXXXX
        </Grid>
      </Grid>

      {/* ss  */}


      <Divider sx={{ mt: `${theme.spacing(4)} !important`, mb: `${theme.spacing(3.25)} !important` }} />

      <Grid container item xs={12} sm={12}  sx={{ marginTop: 15 }}>
        <Grid item xs={4} sm={4}>
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
          
            Members
        
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
          
            Sq.Mtrs
         
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
         
            Sq.Ft
         
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
         
            % Used
        
        </Grid>
      </Grid>


      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
         
            Members in Residential No./Used CA
        
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='membersResidentialCA'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.membersResidentialCA)}
                  helperText={errors.membersResidentialCA?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='residentialCASqmt'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.ResidentialCASqmt)}
                  helperText={errors.ResidentialCASqmt?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
         
            XXXXXX
       
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
         
            XXXXXX
       
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
          
            Members in Commercial No./Used CA
      
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='membersCommercialCA'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.membersCommercialCA)}
                  helperText={errors.membersCommercialCA?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name='commercialCASqmt'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <TextField
                  variant='standard'
                  {...field}
                  type='number'
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.commercialCASqmt)}
                  helperText={errors.commercialCASqmt?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
         
            XXXXXX
       
        </Grid>
        <Grid item xs={2} sm={2} sx={{ marginTop: 5 }} alignItems="center">
          
            XXXXXX
     
        </Grid>
      </Grid>


      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
          
            Total Used CA Area
      
        </Grid>
        <Grid item flexGrow={1} xs={2} sm={2}>
                XXXXXX
        </Grid>
        <Grid item xs={2} sm={2}  alignItems="center">
                XXXXXX    
        </Grid>
        <Grid item xs={2} sm={2}  alignItems="center">
                XXXXXX
        </Grid>
        <Grid item xs={2} sm={2} alignItems="center">
                XXXXXX
        </Grid>
      </Grid>


      <Divider sx={{ mt: `${theme.spacing(4)} !important`, mb: `${theme.spacing(3.25)} !important` }} />



    </Grid>
  )
}

export default PlotDetails
