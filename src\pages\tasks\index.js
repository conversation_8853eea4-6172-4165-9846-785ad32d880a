import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";

import PageHeader from "src/@core/components/page-header";
import NavTabsTasks from "src/@core/components/custom-components/NavTabsTasks";
import AssignedTasks from "./AssignedTasks";
import { useRBAC } from "src/pages/permission/RBACContext";


const Tasks = () => {

  const { can } = useRBAC();
  
  if(can('tasks_READ')){
  return (
    <Card>
      <NavTabsTasks
        tabContent1={
          <>
            <AssignedTasks assignedToMe={true} createdBy={false}/>
          </>
        }
        tabContent2={
          <>
            <AssignedTasks assignedToMe={false} createdBy={true} />
          </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default Tasks;
