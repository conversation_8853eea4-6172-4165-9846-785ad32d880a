// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Tab from "@mui/material/Tab";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import StatisticsPageView from "./StatisticsPageView";

const StatisticsTabs = (props) => {
  // ** State
  const [value, setValue] = useState("1");

  // ** Props
  const {
    tabContents,
    data,
    onTabChange,
    expanded,
    getData,
    userDataAllProfile,
    serviceId,
    statisticsData,
  } = props;



  useEffect(() => {
    if (tabContents && tabContents.length > 0) {
      setValue(tabContents[0].id.toString());
      onTabChange(tabContents[0].id);
    }
  }, [tabContents]);
  const handleServiceChange = (_event, newValue) => {
    setValue(newValue);
    const selectedService = tabContents.find((tab) => tab.id === newValue);
    if (selectedService) {
      onTabChange(selectedService.id);
    }
  };

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleServiceChange}
        aria-label="forced scroll tabs example"
      >
        {tabContents?.map((tabContent) => (
          <Tab
            key={tabContent.id}
            value={tabContent.id.toString()}
            component="a"
            label={tabContent.name}
            onClick={(e) => e.preventDefault()}
          />
        ))}
      </TabList>
      {tabContents?.map((tabContent) => (
        <TabPanel
          key={tabContent.id}
          value={tabContent.id.toString()}
          sx={{
            pb: 1,
            pt: 3,
            px: { xs: "0", md: "0.5rem" },
            mt: "10px",
            overflowY: "auto",
          }}
        >
          <StatisticsPageView
            data={data}
            statisticsData={statisticsData}
            serviceId={serviceId}
            userDataAllProfile={userDataAllProfile}
            expanded={expanded}
            getData={getData}
          />
        </TabPanel>
      ))}
    </TabContext>
  );
};

export default StatisticsTabs;
