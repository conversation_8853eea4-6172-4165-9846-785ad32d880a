import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Typography,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  DialogContentText,
  FormControl,
  TextField, FormHelperText,
  InputAdornment,
  Menu,
  MenuItem,
  ListItemIcon
} from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { useAuth } from "src/hooks/useAuth";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import IconButton from "@mui/material/IconButton";
import { useForm } from "react-hook-form";
import MUITableCell from "../SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CustomChip from "src/@core/components/mui/chip";
import SearchIcon from "@mui/icons-material/Search";

import { Controller } from "react-hook-form";
import FSIDeleteDialog from "./FSIDeleteDialog";
import EditDialog from "./EditDialog";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import { useRBAC } from "src/pages/permission/RBACContext";
import FSIUpdateDialog from "./FSIUpdateDialog";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const Fsi = () => {
  const auth = useAuth();

  const theme = useTheme();

  const { user, listValues, fsiData, setFsiData, fsiDataDetails,entityData } =
    useContext(AuthContext);


  const [openDialog, setOpenDialog] = useState(false);

  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogUpdate, setOpenDialogUpdate] = useState(false);

  const [openUpdateDialog,setOpenUpdateDialog] = useState(false);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [userList, setUserList] = useState([]);
  
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [editForm, setEditForm] = useState(false);

  const [condition,setCondition] = useState(true)

  const [currentRow, setCurrentRow] = useState();
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [calculateLoad, setCalculateLoad] = useState(false);
  const [saveLoad, setSaveLoad] = useState(false);

  const [calculationDialog,setCalculationDialog] = useState(false);
  const [calculatedData,setCalculatedData] = useState();

  const [authority,setAuthority] = useState("")
  const [roadWidth,setRoadWidth] = useState("")
  const [grossPlotArea,setGrossPlotArea] = useState("")

  const {
    register,
    handleSubmit,
    clearErrors,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm();

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      field: "societyName",
      headerName: "Society Name",
      flex: 1,
      minWidth: 150  
    },
    {
      field: "location",
      headerName: "Location",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "type",
      headerName: "Type",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "roadWidth",
      headerName: "Road Width",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "zonalFSI",
      headerName: "Zonal FSI",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "premiumFSI",
      headerName: "Premium FSI",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "tdr",
      headerName: "TDR",
      flex: 1,
      minWidth: 80 
    },
    {
      field: "fungibleFSI",
      headerName: "FSI",
      flex: 1,
      minWidth: 100 
    },
    {
      field: "buildupArea",
      headerName: "Built-up Area",
      flex: 1,
      minWidth: 100, 
      valueGetter: (params) => {
        const roundedValue = Number(params.value).toFixed(2);
        return roundedValue;
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 90,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = useState(null);
    
        const handleMenuClick = (event) => {
          setAnchorEl(event.currentTarget);
        };
    
        const handleMenuClose = () => {
          setAnchorEl(null);
        };
    
        const onClickViewProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setViewDialogOpen(true);
          setFsiData({
            ...fsiData,
            id: row.id,
          });
          handleMenuClose(); // Close the menu after clicking
        };
        const onClickToggleStatus = () => {
          const row = params.row;
          setCurrentRow(row);
          if (row.isActive) {
            setOpenDeleteDialog(true);
          } else {
            setOpenDialogUpdate(true);
          }
          handleMenuClose();
        };
    
        return (
          <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
            <IconButton onClick={handleMenuClick}>
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={onClickViewProfile}>
                <Icon icon="iconamoon:edit" style={{ marginRight: 8 }} />
                Edit
              </MenuItem>
              <MenuItem onClick={onClickToggleStatus}>
                  <ListItemIcon>
                    <Icon icon={params.row.isActive ? "iconamoon:trash" : "tabler:circle-check"} />
                  </ListItemIcon>
                  {params.row.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
            </Menu>
          </div>
        );
      },
    }
    
    
  ];


  const fetchAllUsers = async (currentPage, currentPageSize, searchKeyword) => {
    
    const url = getUrl(authConfig.fsiCalculatorGetALL);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response?.data?.fsiCalculatorResponse);
        setRowCount(response?.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
      fetchAllUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const router = useRouter();

  const [dataView, setDataView] = useState({});

  const handleSocietyChange = (newValue) => {
    setSocietySelected(true)
    setSelectedSociety(newValue);
    setDataView({
      ...dataView, // This keeps the existing properties
      ...newValue, // This adds/updates properties from newValue
    });

    setZone(newValue?.zone)
    const matchingLocation = listValues?.find(location => location?.name === newValue?.location);

    // Set the matching location as the defaultValue for the SelectCategory
    if (matchingLocation) {
      setLocate(matchingLocation?.id)
      setLocationId(matchingLocation);
    }

    const matchingWard = listOfWards?.find(ward => ward?.name === newValue?.ward);

    // Set the matching location as the defaultValue for the SelectCategory
    if (matchingWard) {
      setSelectedWard(matchingWard);
    } else {
      setSelectedWard(null);
    }
    
    const matchingType = listOfTypes?.find(type => type?.name === newValue?.type);

    // Set the matching location as the defaultValue for the SelectCategory
    if (matchingType) {
      setSelectedType(matchingType);
    } else {
      setSelectedType(null);
    }
  };

  useEffect(() => {
    reset({ ...dataView });
  }, [dataView, reset]);


  const [locate,setLocate] = useState(null)
  const [locationId, setLocationId] = useState({});
  const [zone, setZone] = useState("");

  const [listOfLocations, setListOfLocations] = useState([]);
  const [locationsOptions, setLocationsOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=LOCATIONS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfLocations(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);
  
  useEffect(() => {
    if (!!listOfLocations) {
      let data = [];
      listOfLocations.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setLocationsOptions(data);
    }
  }, [listOfLocations]);


  const [wardNo,setWardNo] = useState({})
  const [selectedWard, setSelectedWard] = useState({});
  const [listOfWards, setListOfWards] = useState([]);

  
  const wardOptions = listOfWards.map((ward) => ({
    value: ward,
    key: ward?.name,
  }));

  const [typeNo,setTypeNo] = useState({})
  const [selectedType, setSelectedType] = useState({});
  const [listOfTypes, setListOfTypes] = useState([]);

  
  const typeOptions = listOfTypes.map((type) => ({
    value: type,
    key: type?.name,
  }));

  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data.masterDataResponse);

          const matchingWard = res.data?.masterDataResponse.find(ward => ward?.name === entityData?.ward);

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingWard) {
            setWardNo(matchingWard);
          } else {
            setWardNo(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();

    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);

          const matchingType = res.data?.masterDataResponse.find(type => type?.name === entityData?.type);

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingType) {
            setTypeNo(matchingType);
          } else {
            setTypeNo(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();
  }, [entityData]);


  const handleSuccess = () => {
    const message = `
      <div> 
        <h3>FSI Calculated Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    if(calculationDialog){
      setOpenDialogContent(false);
    }else{
      setOpenDialogContent(true);
    }
    
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Calculate FSI. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };


  const handleUpdateProfile = () => {
    const message = `
      <div> 
        <h3> Do you want to update the profile data with this.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenUpdateDialog(true);
  };

  async function Calculation(data) {
    setCondition(false)
    setCalculateLoad(true);
    setCalculationDialog(true);
    let fields;
    if (!editForm) {
      
      // Create Mode
     
        fields = {
          userId: dataView?.userId,
          societyName: dataView?.name,
          location: dataView?.location,
          ward: selectedWard?.name,
          type: selectedType?.name,
          zone:dataView?.zone,
          type:dataView?.type,
          saveInTable:false,
          updateInProfile:false,
          authority: dataView?.authority,
          roadWidth: dataView?.roadWidth,
          grossPlotArea: dataView?.grossPlotArea,
        };
      
           
    } else {
      // Update Mode
        fields = {
          userId: dataView?.userId,
          societyName: dataView?.name,
          location: locationId?.name,
          ward: selectedWard?.name,
          zone:zone,
          type: selectedType?.name,
          saveInTable:false,
          updateInProfile:false,
          authority: data?.authority,
          roadWidth: data?.roadWidth,
          grossPlotArea: data?.grossPlotArea,
        };
         
    }


    try {
      const response = await auth.postCalculatedFSI(
        fields,
        handleFailure,
        handleSuccess
      );
      setCalculatedData(response)
      setCalculateLoad(false);
    } catch (error) {
      console.error("FSI Rule Creation failed:", error);
      setCalculateLoad(false);
      handleFailure();
    }
    setCondition(true);
     
  }

  async function SaveCalculation() {
    setOpenUpdateDialog(false);
    setCondition(false)
    setSaveLoad(true);
    let fields;
        fields = {
          userId: dataView?.userId,
          societyName: dataView?.name,
          location: locationId?.name,
          zone: zone,
          ward: selectedWard?.name,
          type: selectedType?.name,
          saveInTable:true,
          updateInProfile:true,
          authority: authority ? authority : dataView?.authority,
          roadWidth: roadWidth ? roadWidth : dataView?.roadWidth,
          grossPlotArea: grossPlotArea ? grossPlotArea : dataView?.grossPlotArea,
        };
      
      try {
        const response = await auth.postCalculatedFSI(
          fields,
          handleFailure,
          handleSuccess
        );
        setSaveLoad(false);
      } catch (error) {
        console.error("FSI Rule Creation failed:", error);
        setSaveLoad(false);
        handleFailure();
      }
      fetchAllUsers(page, pageSize, searchKeyword);  
      setOpenDialog(false);
      setEditForm(false);
      setSocietySelected(false);
      setCalculationDialog(false);
      setCondition(true);
      setSelectedSociety("");
      setLocationId({});
      setLocate(null)
      setSelectedWard({});
      setDataView({});
      reset();     
      
  }

  async function SaveCalculate() {
    setOpenUpdateDialog(false);
    setCondition(false)
    setSaveLoad(true);
    let fields;
        fields = {
          userId: dataView?.userId,
          societyName: dataView?.name,
          location: locationId?.name,
          zone: zone,
          ward: selectedWard?.name,
          type: selectedType?.name,
          saveInTable:true,
          updateInProfile:false,
          authority: authority ? authority : dataView?.authority,
          roadWidth: roadWidth ? roadWidth : dataView?.roadWidth,
          grossPlotArea: grossPlotArea ? grossPlotArea : dataView?.grossPlotArea,
        };
      
      try {
        const response = await auth.postCalculatedFSI(
          fields,
          handleFailure,
          handleSuccess
        );
        setSaveLoad(false);
      } catch (error) {
        console.error("FSI Rule Creation failed:", error);
        setSaveLoad(false);
        handleFailure();
      }
      fetchAllUsers(page, pageSize, searchKeyword);  
      setOpenDialog(false);
      setEditForm(false);
      setSocietySelected(false);
      setCalculationDialog(false);
      setCondition(true);
      setSelectedSociety("");
      setLocationId({});
      setLocate(null)
      setSelectedWard({});
      setDataView({});
      reset();       
  }
  const { can } = useRBAC();
  const [societySelected,setSocietySelected] = useState(false)

  const [selectedSociety, setSelectedSociety] = useState({});
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [mainObjectsOfSocieties, setMainObjectsOfSocieties] = useState([]);

  const societyOptions = listOfSocieties
  .filter(society => society?.name) 
  .map(society => ({
    value: society,
    key: society?.name,
  }));

  useEffect(() => {
      const fetchSocieties = async () => {
        axios({
          method: "get",
          url:
            getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
          headers: getAuthorizationHeaders(),
        })
          .then((res) => {

            const metadataArray = res.data?.data?.map((item) => item?.metaData);
            setMainObjectsOfSocieties(res.data?.data);

            setListOfSocieties(metadataArray);
          })
          .catch((err) => console.log("error", err));
      };
      fetchSocieties();
    
  }, [openDialog]);

  

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleUpdateDialog = () => {
    setOpenUpdateDialog(false);
  };



  const handleCalculateClick = () => {
    setOpenDialog(true);
  };


  const handleCloseDialog = () => {
    setSelectedSociety("");
    setLocationId({});
    setLocate(null)
    setSelectedWard({});
    setSelectedType({})
    setDataView("");
    setEditForm(false);
    setSocietySelected(false);
    reset();
    setCalculationDialog(false);
    setOpenDialog(false);
    // fetchAllUsers();
    // reset();
  };

  const handleCloseViewDialog = () => {
    setViewDialogOpen(false);
    reset();
  };

  const handleEditClick = () => {
    setEditForm(true); 
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchAllUsers(page, pageSize, searchKeyword);
  };

  const handleCloseDialogUpdate = () => {
    setOpenDialogUpdate(false);
    fetchAllUsers(page, pageSize, searchKeyword);    
  }


  const handleLocationChange = (newValue) => {
    const matchedLocation = listValues.find((item) => item.id === newValue);

    // If a matching object is found, setLocationId with the entire object
    if (matchedLocation) {
      setLocationId(matchedLocation);
    } else {
      console.error("No matching location found in listValues");
    }
    setLocate(newValue)
    const selectedLocation = listOfLocations.find(
      (location) => location.id === newValue
    );
    if (selectedLocation) {
      const zone = selectedLocation.zoneId
        ? listValues.find((item) => item.id === selectedLocation.zoneId)?.name
        : null;
      setZone(zone);
    }
  };

  const [placeholderText, setPlaceholderText] = useState('Search by society name');
  const phrases = ["society name", "location", "road width", "FSI", "buildup area"];
  let phraseIndex = 0;

  useEffect(() => {
    const intervalId = setInterval(() => {
      phraseIndex = (phraseIndex + 1) % phrases.length;
      setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
    }, 1000); 

    return () => clearInterval(intervalId);
  }, []);
if(can('calculatedFSI_READ')){

  return (
    <Grid>
      <Card>
       
        <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4.5} md={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6">List of Calculated FSI's</Typography>
                </Grid>

                <Grid item xs={12} sm={7.5} md={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm={7} md={6} lg={5} >                  
                        <FormControl fullWidth>
                          <Controller
                            name="mainSearch"
                            control={control}
                            render={({ field: { onChange } }) => (
                              <TextField
                                id="mainSearch"
                                placeholder={placeholderText}
                                value={keyword}
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  setKeyword(e.target.value);
                                  setSearchKeyword(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    setSearchKeyword(e.target.value);
                                    fetchAllUsers(page, pageSize, searchKeyword);
                                  }
                                }}
                                sx={{
                                  "& .MuiInputBase-root": {
                                    height: "40px",
                                  },
                                  "& .MuiInputBase-input::placeholder": {
                                    animation: 'scrollPlaceholder 2s infinite',
                                  }
                                }}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="start">
                                      <SearchIcon
                                        sx={{
                                          cursor: "pointer",
                                          marginRight: "-17px",
                                        }}
                                        onClick={() => {
                                          setSearchKeyword(keyword);
                                          fetchAllUsers(page, pageSize, searchKeyword)}}
                                      />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        </FormControl>
                    </Grid>
                      <Grid item xs={12} sm="auto">
                        <Button variant="contained" onClick={handleCalculateClick}>
                        Calculate New FSI
                        </Button>
                      </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>


        <Dialog
          fullWidth
          scroll="paper"
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth={"md"}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start" ,
              fontSize: { xs: 19, md: 20 },
            }}
          >
            FSI Calculator
            <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color:"common.white", 
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            {editForm ? (
                <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <SelectCategory
                    register={register}
                    id="society"
                    label="Select Society"
                    name="society"
                    nameArray={societyOptions}
                    value={selectedSociety}
                    defaultValue={selectedSociety}
                    onChange={(e) => handleSocietyChange(e.target.value)}
                    clearErrors={clearErrors}
                  />
                  {errors.society && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-society"
                    >
                      {errors.society?.message}
                    </FormHelperText>
                  )}
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="authority"
                      control={control}
                      rules={{ required: { value: true, message: "Authority is required" },                       
                      }}
                      defaultValue={dataView?.authority}
                      value={dataView?.authority}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("authority", dataView?.authority);
                        }, [dataView?.authority, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value.replace(/\s/g, ''); 
                          if (/^[a-zA-Z]*$/.test(inputValue)) { 
                            field.onChange(inputValue);
                            setAuthority(inputValue)
                          }
                        };

                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Authority"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your authority"
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="validation-basic-authority"
                            inputProps={{ maxLength: 50 }}
                            onChange={handleInputChange}
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
            <SelectCategory
              register={register}
              clearErrors={clearErrors}
              id={"location-select"}
              label={" Location "}
              name="location-select"
              nameArray={locationsOptions}
              defaultValue={locate}
              value={locate}
              onChange={(event) => handleLocationChange(event.target.value)}
              aria-describedby="location-select"
            />
          </Grid>
          <Grid container item xs={12} sm={6} spacing={2}>
            <Grid item>
              <Typography className="data-field">Zone:</Typography>
            </Grid>
            <Grid item>
              <Typography style={{ fontWeight: "bold" }}>{zone}</Typography>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6}>
                  <SelectCategory
                    register={register}
                    id="type"
                    label="Select Type"
                    name="type"
                    nameArray={typeOptions}
                    value={selectedType}
                    defaultValue={selectedType}
                    onChange={(e) => {
                      setSelectedType(e.target.value);
                    }}
                    clearErrors={clearErrors}
                  />
                  {errors.type && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-type"
                    >
                      {errors.type?.message}
                    </FormHelperText>
                  )}

                </Grid>

                
                  <Grid item xs={12} sm={6}>
                  <SelectCategory
                    register={register}
                    id="ward"
                    label="Choose Your Ward"
                    name="ward"
                    nameArray={wardOptions}
                    value={selectedWard}
                    defaultValue={selectedWard}
                    onChange={(e) => {
                      setSelectedWard(e.target.value);
                    }}
                    clearErrors={clearErrors}
                  />
                  {errors.ward && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-ward"
                    >
                      {errors.ward?.message}
                    </FormHelperText>
                  )}
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="roadWidth"
                      control={control}
                      rules={{
                        required: { value: true, message: "Road width is required" },
                        validate: value => parseFloat(value) > 0 || "Road width should be greater than 0"
                      }}
                      defaultValue={dataView?.roadWidth}
                      value={dataView?.roadWidth}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("roadWidth", dataView?.roadWidth);
                        }, [dataView?.roadWidth, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value;
                          if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) { 
                            field.onChange(inputValue); 
                            setRoadWidth(inputValue)
                          }
                        };


                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Road Width"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter road width"
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            inputProps={{ maxLength: 10 }}
                            aria-describedby="validation-basic-roadWidth"
                            onChange={handleInputChange}
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>

                {/* Plot Area Field */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="grossPlotArea"
                      control={control}
                      rules={{ 
                        required: { value: true, message: "plot area is required" },
                        validate: value => parseFloat(value) > 0 || "plot area should be greater than 0"
                      }}
                      defaultValue={dataView?.grossPlotArea}
                      value={dataView?.grossPlotArea}
                      render={({ field, fieldState, formState }) => {
                        useEffect(() => {
                          setValue("grossPlotArea", dataView?.grossPlotArea);
                        }, [dataView?.grossPlotArea, setValue]);

                        const handleInputChange = (e) => {
                          const inputValue = e.target.value;
                          if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) { 
                            field.onChange(inputValue); 
                            setGrossPlotArea(inputValue)
                          }
                        };

                        return (
                          <TextField
                            {...field}
                            size='small'
                            label="Net Plot Area in mts"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter Net Plot Area"
                            inputProps={{ maxLength: 10 }}
                            error={Boolean(fieldState.error)}
                            helperText={fieldState.error?.message}
                            aria-describedby="validation-basic-grossPlotArea"
                            onChange={handleInputChange} 
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>
              </Grid>               
            ) : (
              <>
              <Grid container spacing={2}>
              <Grid item xs={12} style={{ display: "flex",justifyContent:"right" , marginTop: "-20px"}}>
                   <Tooltip title="Edit">
                    <CustomAvatar
                      skin="light"
                      variant="rounded"
                      sx={{ mr: 5, width: 34, height: 34,  cursor: condition ? "pointer" : "not-allowed" }}
                      onClick={condition ? handleEditClick : undefined}
                    >
                      <Icon icon="iconamoon:edit" />
                    </CustomAvatar>
                  </Tooltip>
                </Grid>
                <Grid item xs={12} sm={6}>
                            <SelectCategory
                              register={register}
                              id="society"
                              label="Select Society"
                              name="society"
                              nameArray={societyOptions}
                              value={selectedSociety}
                              // defaultValue={selectedSociety}
                              onChange={(e) =>
                                handleSocietyChange(e.target.value)
                              }
                              clearErrors={clearErrors}
                            />
                            {errors.society && (
                              <FormHelperText
                                sx={{ color: "error.main" }}
                                id="validation-society"
                              >
                                {errors.society?.message}
                              </FormHelperText>
                            )}
                          </Grid>
              </Grid>         
                          {societySelected &&
                          
                          <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >  
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Authority:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.authority}
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography style={field}>Type:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.type}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Location:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.location}
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography style={field}>Zone:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.zone}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
        
                              <TableRow>
                               
                                <MUITableCell>
                                  <Typography style={field}>Ward:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.ward}
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography style={field}>RoadWidth:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.roadWidth}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow> 
                                <MUITableCell>
                                  <Typography style={field}>Plot area:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {dataView?.grossPlotArea}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>                             
                            </TableBody>
                          </Table>
                        </TableContainer>
                      }               
              </>           
            )}
            {calculationDialog && (
              <Grid  container spacing={2}>
                <Grid item xs={12} sm={12}  style={{fontWeight: 'bold', marginTop: '15px', marginBottom: '2px'}}>Calculated FSI Details: </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography>The maximum FSI under 33(7) / 33(7)B for your Society/ Plot is :
                       </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{fontWeight: 'bold'}}>{calculatedData?.fungibleFSI}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography> Built Up Area available on your Plot as per FSI (sq mts) :  </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{fontWeight: 'bold'}}>{calculatedData?.buildupArea}</Typography>
                  </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
         
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={editForm ? handleUpdateProfile : handleSubmit(SaveCalculation)}
                disabled={(!societySelected)}
              >
                {saveLoad ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Save"
              )}
              </Button>
            

            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(Calculation)}
              disabled={(!societySelected)}
            >
              {calculateLoad ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Calculate FSI"
              )}
            </Button>
          </DialogActions>
        </Dialog>

        <Divider />
       
       
          <CardContent>
            <div style={{ height: 480, width: "100%" }}>  
             <DataGrid
              rows={userList}
              columns={columns}
              checkboxSelection
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={40}
              headerHeight={40} 
            />
             </div>
             </CardContent>

        <EditDialog
          open={viewDialogOpen}
          onClose={handleCloseViewDialog}
          formData={fsiDataDetails}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
          fetchAllUsers={fetchAllUsers}
        />
      </Card>
      <FSIDeleteDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
        fetchUsers={fetchAllUsers}
        page={page}
        pageSize={pageSize}
        searchKeyword={searchKeyword}
      />

<FSIUpdateDialog
        open={openDialogUpdate}
        onClose={handleCloseDialogUpdate}
        data={currentRow}
        fetchUsers={fetchAllUsers}
        page={page}
        pageSize={pageSize}
        searchKeyword={searchKeyword}
      />

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openUpdateDialog}
        onClose={handleUpdateDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
          <Button
              variant="contained"
              onClick={SaveCalculation}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={SaveCalculate}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </Grid>
  );}
  else{
    return null;
  }
};
export default Fsi;
