import { <PERSON><PERSON>, <PERSON>, <PERSON>rid, Typography } from "@mui/material";

import NavTabsRequisitions from "src/@core/components/custom-components/NavTabsRequisitions";
import QuotationDetails from "./QuotationDetails";
import { useRBAC } from "src/pages/permission/RBACContext";


const Quotations = () => {
  const { can } = useRBAC();
  if(can('appointments_READ')){
  return (
    <Card>
      <NavTabsRequisitions
        tabContent1={
          <>
            <QuotationDetails/>
          </>
        }
        tabContent2={
          <>
            <QuotationDetails/>
          </>
        }
        tabContent3={
            <>
              <QuotationDetails/>
            </>
        }
        tabContent4={
            <>
              <QuotationDetails/>
            </>
        }
        tabContent5={
            <>
              <QuotationDetails/>
            </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default Quotations;
