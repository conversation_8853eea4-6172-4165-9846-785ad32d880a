import React, { useContext, useEffect, useState, Suspense } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Typography, IconButton } from "@mui/material";
import { Close as CloseIcon, CropSquareOutlined, MinimizeSharp } from "@mui/icons-material";
import { AuthContext } from "src/context/AuthContext";
import mammoth from "mammoth";
import { Buffer } from 'buffer';

const ViewSnapshotByLocation = ({ location, setSelectedLocation }) => {
  const { getFileByLocation } = useContext(AuthContext);
  const [fileContent, setFileContent] = useState(null);
  const [fileType, setFileType] = useState(null);
  const [isLarge, setIsLarge] = useState(false);
  const [docxContent, setDocxContent] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getFileByLocation(location?.location);
        const fileData = response?.data?.data;
        const fileName = location?.location.split("/").pop();
        const fileExtension = fileName.split(".").pop().toLowerCase();

        let inferredFileType;
        switch (fileExtension) {
          case 'pdf':
            inferredFileType = 'application/pdf';
            break;
          case 'docx':
            inferredFileType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            break;
          case 'jpg':
          case 'jpeg':
          case 'png':
            inferredFileType = `image/${fileExtension}`;
            break;
          default:
            inferredFileType = 'unknown';
        }

        setFileType(inferredFileType);
        if (inferredFileType === 'application/pdf') {
          const pdfUrl = `data:application/pdf;base64,${fileData}`;
          setFileContent(pdfUrl);
        } else if (inferredFileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          const arrayBuffer = Buffer.from(fileData, 'base64').buffer;
          const { value } = await mammoth.convertToHtml({ arrayBuffer });
          setDocxContent(value);
        } else {
          setFileContent(fileData);
        }
      } catch (error) {
        console.error("Error fetching file content:", error);
      }
    };

    if (location?.location) {
      fetchData();
    }
  }, [getFileByLocation, location]);

  const renderFileContent = () => {
    if (fileType) {
      if (fileType.startsWith('image/')) {
        const imageUrl = `data:${fileType};base64,${fileContent}`;
        return <img src={imageUrl} alt="file" style={{ width: "100%", height: "100%", objectFit: "contain" }} />;
      } else if (fileType === 'application/pdf') {
        return (
          <iframe
            src={fileContent}
            style={{ width: "100%", height: "100%" }}
            title="PDF Viewer"
          />
        );
      } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        return (
          <div
            dangerouslySetInnerHTML={{ __html: docxContent }}
            style={{ width: "100%", height: "100%", overflowY: "auto" }}
          />
        );
      } else {
        return <Typography>Unsupported file type: {fileType}</Typography>;
      }
    }
    return <Typography>File type not yet determined.</Typography>;
  };

  const onClose = () => {
    setFileContent(null);
    setSelectedLocation(null);
  };

  const toggleSize = () => {
    setIsLarge((prevIsLarge) => !prevIsLarge);
  };

  return (
    <Dialog
      open={Boolean(location)}
      onClose={onClose}
      maxWidth={isLarge ? "xl" : "md"}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography>
          {location?.location && location?.location?.split("/").pop()}
        </Typography>

        <div
          style={{
            position: "absolute",
            top: 0,
            right: 0,
            margin: "8px",
          }}
        >
          <IconButton
            edge="end"
            color="inherit"
            style={{ margin: "8px" }}
            aria-label="toggle-size"
            onClick={toggleSize}
          >
            {isLarge ? <MinimizeSharp /> : <CropSquareOutlined />}
          </IconButton>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            style={{ margin: "8px" }}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </div>
      </DialogTitle>
      <DialogContent
        sx={{
          width: isLarge ? "800px" : "500px",
          height: isLarge ? "500px" : "300px",
          position: "relative",
        }}
      >
        {renderFileContent()}
      </DialogContent>
    </Dialog>
  );
};

export default ViewSnapshotByLocation;
