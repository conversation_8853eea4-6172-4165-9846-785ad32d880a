import React, { useEffect, useState } from 'react';
import { Card, CardActionArea, CardContent, CardMedia, Dialog, DialogTitle, Typography, CircularProgress, DialogContent, IconButton } from '@mui/material';
import ReactPlayer from 'react-player/youtube';
import CloseIcon from '@mui/icons-material/Close';

const PlayYoutubeVideo = ({ videoUrl }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [videoInfo, setVideoInfo] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`https://www.youtube.com/oembed?url=${videoUrl}&format=json`);
        const data = await response.json();
        const videoId = videoUrl.replace('https://youtu.be/', '');
        setVideoInfo({
          title: data.title,
          thumbnail: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
        });
        setLoading(false);
      } catch (error) {
        console.error('Error fetching video data:', error);
      }
    };

    fetchData();
  }, [videoUrl]);

  const handleCardClick = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  return (
    <div>
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <CircularProgress />
        </div>
      ) : (
        <Card sx={{ maxWidth: 250 }} onClick={handleCardClick}>
          <CardActionArea>
            <CardMedia component="img" height="150" image={videoInfo.thumbnail || ''} alt={videoInfo.title || ''} />
            <CardContent>
              <Typography gutterBottom variant="h7" component="div">
                {videoInfo.title || 'Loading...'}
              </Typography>
            </CardContent>
          </CardActionArea>
        </Card>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Typography variant="h5">{videoInfo.title}</Typography>
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ height: '80vh' }}>
          <ReactPlayer url={videoUrl} playing controls width="100%" height="100%" style={{ position: 'relative', padding: '0' }} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PlayYoutubeVideo;
