// ** MUI Imports
import Typography from "@mui/material/Typography";

// ** Custom Components Imports
// ** Demo Components Imports

import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Tooltip,
} from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { useContext, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useTheme } from "@emotion/react";
import SpecificationEdit from "./SpecificationEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ViewSpecification = ({ open, onClose, data, fetchSpecifications }) => {
  const theme = useTheme();

  const { listValues, listNames } = useContext(AuthContext);

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
    onClose();
  };
  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleClose = () => {
    onClose();
  };

  const serviceType = data?.listValuesId
    ? listValues?.find((item) => item.id === data?.listValuesId)?.name
    : null;

  const sectionName = data?.sectionId
    ? listValues?.find((item) => item.id === data?.sectionId)?.name
    : null;

  const componentName = data?.uiComponentsId
    ? listValues?.find((item) => item.id === data?.uiComponentsId)?.name
    : null;

  const specificationName = data?.listNamesId
    ? listNames?.find((item) => item.id === data?.listNamesId)?.name
    : null;

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md" scroll="paper">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Specification Details
          <Box sx={{ position: "absolute", top: "6px", right: "30px" }}>
            <Tooltip title="Edit">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 30 }}
                onClick={handleOpenEditDialog}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </Box>
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Service Type :</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {serviceType}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>
                          Specification Label :
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {specificationName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Section name :</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {sectionName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>UI Component :</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {componentName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <SpecificationEdit
          open={openEditDialog}
          formData={data}
          onClose={handleCloseEditDialog}
          fetchSpecifications={fetchSpecifications}
        />
    </>
  );
};

export default ViewSpecification;
