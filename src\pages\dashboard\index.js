// ** MUI Imports
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

// ** Custom Components Imports
import { useContext, useState } from "react";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import NavTabsConversations from "src/@core/components/custom-components/NavTabsConversations";
import NavTabsTaskDetails from "src/@core/components/custom-components/NavTabsTaskDetails";
import BasicLineChart from "src/@core/components/statistics/BasicLineChart";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";
import QuotationDetails from "../quotations/QuotationDetails";
import RequisitionDetails from "../requisitions/RequisitionDetails";
import WorkOrderDetails from "../work-orders/WorkOrderDetails";
import ConversationDetails from "./ConversationDetails";
import ProfilesDetails from "./ProfilesDetails";
import ServiceProviders from "./ServiceProviders";
import DashedLineChart from "src/@core/components/statistics/DashedLineChart";
import StackedAreaChart from "src/@core/components/statistics/StackedAreaChart";
import StackedBarGraph from "src/@core/components/statistics/StackedBarGraph";
import BasicColumnChart from "src/@core/components/statistics/BasicColumnChart";
import GroupedStackColumnChart from "src/@core/components/statistics/GroupedStackColumnChart";
import GroupedStackedBarGraph from "src/@core/components/statistics/GroupedStackedBarGraph";
import ComboChart from "src/@core/components/statistics/ComboChart";
import RangeAreaChart from "src/@core/components/statistics/RangeAreaChart";
import DumbBellChart from "src/@core/components/statistics/DumbBellChart";
import FunnelChart from "src/@core/components/statistics/FunnelChart";
import CandleStickChart from "src/@core/components/statistics/CandleStickChart";
import PieChart from "src/@core/components/statistics/PieChart";
import DonutChart from "src/@core/components/statistics/DonutChart";
import RadarChart from "src/@core/components/statistics/RadarChart";
import PolarAreaChart from "src/@core/components/statistics/PolarAreaChart";
import RadialBarChart from "src/@core/components/statistics/RadialBarChart";
import StrokedGaugeCircleChart from "src/@core/components/statistics/StrokedGaugeCircleChart";
import BubbleChart from "src/@core/components/statistics/BubbleChart";
import ScatterChart from "src/@core/components/statistics/ScatterChart";
import HeatMapChart from "src/@core/components/statistics/HeatMapChart";
import TreeMapChart from "src/@core/components/statistics/TreeMapChart";
import SlopeChart from "src/@core/components/statistics/SlopeChart";
import SparkLinesChart from "src/@core/components/statistics/SparkLinesChart";

const Home = () => {
  const { can } = useRBAC();
  const { user } = useContext(AuthContext);
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  if (can("dashboard_READ")) {
    return (
      <Grid container spacing={6}>
        <Box sx={{ width: "100%", mt: 5, ml: 5 }}>
          <Tabs value={tabIndex} onChange={handleTabChange} aria-label="tabs">
            <Tab
              label="Actionable"
              sx={{ textTransform: "capitalize" }} // Ensures label is in title case
            />
            <Tab
              label="Statistics"
              sx={{ textTransform: "capitalize" }} // Ensures label is in title case
            />
          </Tabs>
          <Box sx={{ paddingTop: "0.4rem !important" }}>
            {tabIndex === 0 && (
              <Grid container spacing={4}>
                {can("tasks_READ") && (
                  <Grid item xs={12}>
                    <AccordionBasic
                      id={"task-details"}
                      ariaControls={"task-details"}
                      sx={{ border: "2px solid #f2f7f2 !important" }}
                      heading={"Task Details"}
                      body={<NavTabsTaskDetails />}
                    />
                  </Grid>
                )}
                {can("conversations_READ") && (
                  <Grid item xs={12}>
                    <AccordionBasic
                      id={"conversation-details"}
                      ariaControls={"conversation-details"}
                      heading={"Conversation Details"}
                      body={
                        <NavTabsConversations
                          tabContent1={<ServiceProviders />}
                          tabContent2={
                            <ConversationDetails
                              conversationType={"PENDING_TODAY"}
                            />
                          }
                          tabContent3={
                            <ConversationDetails
                              conversationType={"PENDING_ONE_TO_SEVEN_DAYS"}
                            />
                          }
                          tabContent4={
                            <ConversationDetails
                              conversationType={
                                "PENDING_GREATER_THAN_SEVEN_DAYS"
                              }
                            />
                          }
                          tabContent5={
                            <ConversationDetails
                              conversationType={"UPCOMING_SEVEN_DAYS"}
                            />
                          }
                        />
                      }
                    />
                  </Grid>
                )}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"societies"}
                    ariaControls={"societies-details"}
                    heading={"Societies CHS"}
                    body={<ProfilesDetails />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"serviceProviders"}
                    ariaControls={"service-providers"}
                    heading={"Service Providers"}
                    body={<ProfilesDetails />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"serviceRequests"}
                    ariaControls={"service-requests"}
                    heading={"Service Requests"}
                    body={<RequisitionDetails />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"quotations"}
                    ariaControls={"quotation-details"}
                    heading={"Quotations"}
                    body={<QuotationDetails />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"workOrders"}
                    ariaControls={"work-orders"}
                    heading={"Work Orders"}
                    body={<WorkOrderDetails />}
                  />
                </Grid>
              </Grid>
            )}
            {tabIndex === 1 && (
              <Grid container spacing={4}>
                {/* Line Charts Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"lineCharts"}
                    ariaControls={"line-charts"}
                    heading={"Line Charts"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={6}>
                          <BasicLineChart />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <DashedLineChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Bar Charts Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"barCharts"}
                    ariaControls={"bar-charts"}
                    heading={"Bar Charts"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={6}>
                          <StackedBarGraph />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <GroupedStackedBarGraph />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Column Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"columnChart"}
                    ariaControls={"column-chart"}
                    heading={"Column Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={6}>
                          <BasicColumnChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Area Charts Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"areaCharts"}
                    ariaControls={"area-charts"}
                    heading={"Area Charts"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={6}>
                          <StackedAreaChart />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <RangeAreaChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Mixed Charts Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"comboCharts"}
                    ariaControls={"combo-charts"}
                    heading={"Mixed Charts"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12}>
                          <ComboChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Pie Donut Charts Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"pieDonutCharts"}
                    ariaControls={"pie-donut-charts"}
                    heading={"Pie/Donut Charts"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={4}>
                          <PieChart />
                        </Grid>
                        <Grid item xs={12} sm={5}>
                          <DonutChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Candlestick Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"candlestickChart"}
                    ariaControls={"candlestick-chart"}
                    heading={"Candlestick Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12}>
                          <CandleStickChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Funnel Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"funnelChart"}
                    ariaControls={"funnel-chart"}
                    heading={"Funnel Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={6}>
                          <FunnelChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Tree Map Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"treeMapChart"}
                    ariaControls={"tree-map-chart"}
                    heading={"Tree Map Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={4}>
                          <TreeMapChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Heat Map Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"heatMapChart"}
                    ariaControls={"heat-map-chart"}
                    heading={"Heat Map Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={8}>
                          <HeatMapChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Scatter Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"scatterChart"}
                    ariaControls={"scatter-chart"}
                    heading={"Scatter Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={4}>
                          <ScatterChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Bubble Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"bubbleChart"}
                    ariaControls={"bubble-chart"}
                    heading={"Bubble Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={4}>
                          <BubbleChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Radial Bar Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"radialBarChart"}
                    ariaControls={"radial-bar-chart"}
                    heading={"Radial bar/Circle Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={4}>
                          <RadialBarChart />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <StrokedGaugeCircleChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Polar Area Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"polarAreaChart"}
                    ariaControls={"polar-area-chart"}
                    heading={"Polar Area Chart"}
                    body={
                      <Grid container spacing={12}>
                        <Grid item xs={12} sm={4}>
                          <PolarAreaChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Radar Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"radarChart"}
                    ariaControls={"radar-chart"}
                    heading={"Radar Chart"}
                    body={
                      <Grid container spacing={6}>
                        <Grid item xs={12} sm={4}>
                          <RadarChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Slope Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"slopeChart"}
                    ariaControls={"slope-chart"}
                    heading={"Slope Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={12}>
                          <SlopeChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Dumbbell Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"dumbbellChart"}
                    ariaControls={"dumbbell-chart"}
                    heading={"DumbBell Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12}>
                          <DumbBellChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>

                {/* Spark Lines Chart Accordion */}
                <Grid item xs={12}>
                  <AccordionBasic
                    id={"sparkLinesChart"}
                    ariaControls={"spark-lines-chart"}
                    heading={"Spark Lines Chart"}
                    body={
                      <Grid container spacing={10}>
                        <Grid item xs={12} sm={8}>
                          <SparkLinesChart />
                        </Grid>
                      </Grid>
                    }
                  />
                </Grid>
              </Grid>
            )}
          </Box>
        </Box>
      </Grid>
    );
  }
  return null;
};

export default Home;
