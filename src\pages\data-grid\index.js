import { Box, Card, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";

function DataGridComponent() {
  const rowsPerPageOptions = [5, 10, 15, 20, 25, 50, 100];
  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);

  const columns = [
    {
      field: "firstName",
      headerName: "First Name",
      flex: 1,
    },
    {
      field: "lastName",
      headerName: "Last Name",
      flex: 1,
    },
    {
      field: "email",
      headerName: "Email Id",
      flex: 1.5,
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 1,
    },
    {
      field: "entityCategory",
      headerName: "Category",
      flex: 1,
    },
    {
      field: "createdBy",
      headerName: "Created By",
      flex: 1,
    },
    {
      field: "createdOn",
      headerName: "Created On",
      flex: 1,
    },
  ];

  const fetchUsers = async (currentPage, currentPageSize) => {
    setLoading(true);

    const url = getUrl(authConfig.getAllMembersEndpoint);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: "",
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        console.log("response", response.data);
        setUserList(response.data.memberListDTOResponses || []);
        setRowCount(response.data.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(`Fetching data for page: ${page}, pageSize: ${pageSize}`);
    fetchUsers(page, pageSize);
  }, [page, pageSize]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else{
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <Box style={{ height: 500, width: "100%" }}>
      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="100%"
        >
          <CircularProgress />
        </Box>
      ) : (
        <DataGrid
          rows={userList}
          columns={columns}
          autoHeight
          checkboxSelection
          pagination
          pageSize={pageSize}
          page={page - 1}
          rowsPerPageOptions={rowsPerPageOptions}
          rowCount={rowCount}
          paginationMode="server"
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </Box>
  );
}

export default DataGridComponent;
