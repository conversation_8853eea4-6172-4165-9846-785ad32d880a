import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Backdrop,
} from '@mui/material';

/**
 * LoadingSpinner component for displaying loading states
 * @param {Object} props - Component props
 * @param {boolean} props.loading - Whether to show loading spinner
 * @param {string} props.message - Loading message to display
 * @param {boolean} props.overlay - Whether to show as overlay
 * @param {string} props.size - Size of the spinner ('small', 'medium', 'large')
 * @param {Object} props.sx - Additional styling
 */
const LoadingSpinner = ({
  loading = false,
  message = 'Loading...',
  overlay = false,
  size = 'medium',
  sx = {},
}) => {
  if (!loading) return null;

  const getSpinnerSize = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  const spinnerContent = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        ...sx,
      }}
    >
      <CircularProgress size={getSpinnerSize()} />
      {message && (
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      )}
    </Box>
  );

  if (overlay) {
    return (
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        }}
        open={loading}
      >
        {spinnerContent}
      </Backdrop>
    );
  }

  return spinnerContent;
};

export default LoadingSpinner;
