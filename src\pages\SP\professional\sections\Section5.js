// ** React Imports
import { forwardRef, useState } from 'react'

// ** MUI Imports
import Card from '@mui/material/Card'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import Typography from '@mui/material/Typography'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'
import { FormControlLabel, Radio, RadioGroup } from '@mui/material'

// ** Icon Imports

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false
}

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})


const Section5 = ({ onCancel, formData}) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();
  const [value, setValue] = useState('');
  const handleChange = (event) => {
    const value = event.target.checked ? 'Yes' : 'No';
    setValue(event.target.value);
  };


  async function submit(data) {

    const response = await auth.updateEntity(data,() => {
      console.error("Structural Details failed");
    });
    onCancel();
  }

  return (
    <Card>
      
      <CardContent>
        
          <Grid container spacing={6}>

          <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='location'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  label='Location'
                  onChange={onChange}
                  placeholder='Enter your location'
                  error={Boolean(errors.name)}
                  aria-describedby='Section5-location'


                />
              )}
            />
            {errors.name && (
              <FormHelperText sx={{ color: 'error.main' }} id='Section5-location'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name='minimumPlotSize'
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.grossPlotArea}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      onChange={onChange}
                      type='number'
                      label='Minimum Plot Size(Sq Mt.)'
                      error={Boolean(errors.minimumPlotSize)}
                      aria-describedby='validation-basic-minimum-plot-size'
                    />
                  )}
                />
                {errors.minimumPlotSize && (
                  <FormHelperText sx={{ color: 'error.main' }} id='validation-basic-minimum-plot-size'>
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name='maximumPlotSize'
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.grossPlotArea}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      onChange={onChange}
                      type='number'
                      label='Maximum Plot Size(Sq Mt.)'
                      error={Boolean(errors.maximumPlotSize)}
                      aria-describedby='validation-basic-maximum-plot-size'
                    />
                  )}
                />
                {errors.maximumPlotSize && (
                  <FormHelperText sx={{ color: 'error.main' }} id='validation-basic-maximum-plot-size'>
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name='minSize'
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.grossPlotArea}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      onChange={onChange}
                      type='number'
                      label='Min Size(In Cr.)'
                      error={Boolean(errors.minSize)}
                      aria-describedby='validation-basic-min-size'
                    />
                  )}
                />
                {errors.minSize && (
                  <FormHelperText sx={{ color: 'error.main' }} id='validation-basic-min-size'>
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name='maxSize'
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.grossPlotArea}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      onChange={onChange}
                      type='number'
                      label='Max Size(In Cr.)'
                      error={Boolean(errors.maxSize)}
                      aria-describedby='validation-basic-max-size'
                    />
                  )}
                />
                {errors.maxSize && (
                  <FormHelperText sx={{ color: 'error.main' }} id='validation-basic-max-size'>
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
            <Box sx={{ backgroundColor: '#F8F7FA', display: 'flex', flexDirection: 'column', p: { xs: 2, lg: 4 } }}>
            <Box sx={{ mb: '1rem' }}>
              <Typography variant='body1' sx={{ mb: 0, mr: { xs: 2, md: 16 }}}>
                Are you looking for employ agency for Sole Selling
              </Typography>
            </Box>

            <Box>
              <FormControl sx={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                <RadioGroup value={value} name='simple-radio' onChange={handleChange} aria-label='simple-radio'>
                  <FormControlLabel
                    value='yes'
                    control={<Radio />}
                    label={
                      <Typography variant='body1'>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel value='no' control={<Radio />} label={
                    <Typography variant='body1'>
                      No
                    </Typography>
                  } />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
      
            </Grid>
            <Grid item xs={12}>
            <Box sx={{ backgroundColor: '#F8F7FA', display: 'flex', flexDirection: 'column', p: { xs: 2, lg: 4 } }}>
            <Box sx={{ mb: '1rem' }}>
              <Typography variant='body1' sx={{ mb: 0, mr: { xs: 2, md: 16 }}}>
                Are you looking to raise Institutional Finance
              </Typography>
            </Box>

            <Box>
              <FormControl sx={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                <RadioGroup value={value} name='simple-radio' onChange={handleChange} aria-label='simple-radio'>
                  <FormControlLabel
                    value='yes'
                    control={<Radio />}
                    label={
                      <Typography variant='body1'>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel value='no' control={<Radio />} label={
                    <Typography variant='body1'>
                      No
                    </Typography>
                  } />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
     
            </Grid>
            

            <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name='remarks'
                control={control}
                rules={{ required: true }}
                defaultValue={formData?.address}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label='Remarks'
                    error={Boolean(errors.address)}
                    aria-describedby='broker-validation-basic-remarks'
                  />
                )}
              />
              {errors.remarks && (
                <FormHelperText sx={{ color: 'error.main' }} id='broker-validation-basic-remarks'>
                  This field is required
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
         
            
        

            <Grid item xs={12}>
          <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
          </Grid>
        
      </CardContent>
    </Card>
  )
}

export default Section5
