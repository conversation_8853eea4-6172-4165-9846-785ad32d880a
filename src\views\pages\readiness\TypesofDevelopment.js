// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";

import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import { FormControl, Radio, RadioGroup } from "@mui/material";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";
const TypesofDevelopment = ({
  setNextActive,
  posts,
  handleAwareOfRedevelopChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  const theme = useTheme();
  const {
    settings: { direction },
  } = useSettings();

  const [value, setValue] = useState(
    defaultData?.hasAwareOfTypesOfRedevelopment_ChallengesBenefits
  );
  const [typesOfRedevelopment, setTypesOfRedevelopment] = useState([]);

  const handleChange = (event) => {
    const value = event.target.value;
    setValue(event.target.value);
    handleAwareOfRedevelopChange(value);
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setTypesOfRedevelopment(data);
    }
  }, [posts]);

  useEffect(() => {
    if (value && email && contactNumber && name&& societyName) {
      setNextActive(true);
    } else {
      setNextActive(false);
    }
  }, [value,name,email,contactNumber,societyName]);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 4,
              padding:{xs:'0.6rem'}
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}>
                Are you aware of the types of Redevelopment? <br />
                The challenges and benefits of each type?
              </Typography>
            </Box>
            <Box>
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={value}
                  name="simple-radio"
                  aria-label="simple-radio"
                >
                  <FormControlLabel 
                    control={
                      <Radio
                        id='typesOfDevelopment-yes'
                        value="Yes"
                        checked={value === "Yes"}
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:'1rem !important'  }}>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel 
                    control={
                      <Radio
                        id='typesOfDevelopment-no'
                        value="No"
                        checked={value === "No"}
                        onChange={handleChange}
                        sx={{padding:'6px !important', 
                          transform: 'scale(0.8)'}}
                      />
                    }
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:'1rem !important'  }}>
                        No
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          {/* <Divider sx={{ mt: `${theme.spacing(12.25)} !important`, mb: `${theme.spacing(3)} !important`, }} /> */}
          {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          {typesOfRedevelopment.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(12)} !important`,
                  mb: `${theme.spacing(3)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {typesOfRedevelopment.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={typesOfRedevelopment} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default TypesofDevelopment;
