import { useState } from 'react'
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import { Table, TableBody, TableContainer, TableRow } from '@mui/material'
// ** MUI Imports
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Section2 from 'src/pages/SP/structural/sections/Section2'


// ** Third Party Imports

// ** Icon Imports

import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const TeamDetailsStructural = ({data,expanded}) =>{
    const theme = useTheme()
    const { can } = useRBAC();
    const [state2, setState2] = useState('view')

    const viewClick2 = () => {
        setState2('edit')
    }

    const editClick2 = () => {
        setState2('view')
    }
    // const [structuralTeamDetails, setStructuralTeamDetails] = useState({
    //     teamSize: "",
    //     awards: "",
    //     briefProfile: "",
    //     areaofOperation: "",
    //     yearsOfExperience: ""
    // });
    return(
        <>
         {/* {can('structuralEngineer_teamStructural_READ') && */}
             <AccordionBasic
                id={'panel-header-1'}
                ariaControls={'panel-content-1'}
                heading={'Team'}
                body={
                  <>
                    {state2 === 'view' && (
                      
                        
                          <TableContainer
                            sx={{ padding: "4px 6px" }}
                            className='tableBody'
                            // onClick={can('structuralEngineer_teamStructural_UPDATE') ? viewClick2 : null}
                            onClick={ viewClick2}
                          >
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Team Size:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.teamSize}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Years of Experience:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.yearsOfExperience}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Area of operation:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >
                                      {data?.areaofOperation === "OTHER" ? data?.otherArea : data?.areaofOperation}
                                    </Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Awards:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.awards}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Brief Profile in your words:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.briefProfile}</Typography>
                                  </MUITableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {state2 === 'edit' && <Section2 formData={data} onCancel={editClick2} />}
                  </>
                }
                expanded={expanded}
              />
          {/* } */}
        </>
    )
}
export default TeamDetailsStructural;