// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

import { useTheme } from "@emotion/react";

// ** Demo
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

import NavTabs from "src/@core/components/custom-components/NavTabs";
import Section2 from "./Section2";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Dialog,
  DialogActions,
  DialogContentText,
  DialogTitle,
  Box,
  Button,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import Section3 from "./Section3";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const ServicesCA = ({ data, expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state1, setState1] = useState("view");

  const viewClick1 = () => {
    setState1("edit");
  };

  const editClick1 = () => {
    setState1("view");
  };

  const [state2, setState2] = useState("view");

  const viewClick2 = () => {
    setState2("edit");
  };

  const editClick2 = () => {
    setState2("view");
  };

  const [developerSocietyServices, setServices] = useState({
    projectRegistration: {
      isSupported: false,
      price: "",
    },
    quarterlyCompliance: {
      isSupported: false,
      price: "",
    },
    changeOfDeveloper: {
      isSupported: false,
      price: "",
    },
    litigation: {
      isSupported: false,
      price: "",
    },
    titleReport: {
      isSupported: false,
      price: "",
    },
    caCertificate: {
      isSupported: false,
      price: "",
    },
    draftAllotmentLetterAndAgreementForSale: {
      isSupported: false,
      price: "",
    },
    staffTrainingRelatedToRERA: {
      isSupported: false,
      price: "",
    },
    engineerCertificate: {
      isSupported: false,
      price: "",
    },
  });

  const [homeBuyerServices, setServices1] = useState({
    homeBuyerLitigation: {
      isSupported: false,
      price: "",
    },
    homeBuyerDueDiligence: {
      isSupported: false,
      price: "",
    },
  });

  const [currentTab, setCurrentTab] = useState("1");
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingTab, setPendingTab] = useState(null);

  const message = `
  <div> 
    <h3> You have unsaved changes. Are you sure you want to switch tabs ?</h3>
  </div>
`;

  const handleTabChange = (event, newValue) => {
    console.log("unsaved state", unsavedChanges);
    if (unsavedChanges) {
      setPendingTab(newValue);
      setDialogOpen(true);
    } else {
      setCurrentTab(newValue);
      setDialogOpen(false);
    }
  };

  const handleDialogClose = (confirm) => {
    if (confirm) {
      setCurrentTab(pendingTab);
      setUnsavedChanges(false);
    }
    setDialogOpen(false);
  };

  return (
    <>
       {/* {can('ca_services_READ') &&  */}
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={" panel-header-2"}
        heading={"Developer/ Society Services"}
        body={
          <NavTabs
            tabContent={
              <>
                {state1 === "view" && (
                  <TableContainer
                    sx={{ paddingLeft: 2, paddingRight: 5, paddingBottom: 5 }}
                    className="tableBody"
                   // onClick={can('ca_services_UPDATE') ? viewClick1 : null}
                    onClick={ viewClick}
                  >
                    <Table>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            py: `${theme.spacing(1.5)} !important`,
                          },
                        }}
                      >
                        <TableRow>
                          <MUITableCell>
                            <Typography sx={{ fontWeight: 600 }}>
                              Service Name:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography sx={{ fontWeight: 600 }}>
                              (Yes/No):{" "}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Project Registration:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices
                                ?.projectRegistration?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Quarterly Compliance:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices
                                ?.quarterlyCompliance?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Change of Developer:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices?.changeOfDeveloper
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Litigation:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices?.litigation
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Title Report:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices?.titleReport
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              CA Certificate:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices?.caCertificate
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Draft Allotment Letter & Agreement for Sale::
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices
                                ?.draftAllotmentLetterAndAgreementForSale
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Staff Training Related to RERA:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices
                                ?.staffTrainingRelatedToRERA?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Engineer Certificate:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.developerSocietyServices
                                ?.engineerCertificate?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
                {state1 === "edit" &&
                currentTab === "1" &&
                  (data?.developerSocietyServices != null ? (
                    <Section2
                      setter={setServices}
                      defaultData={data.developerSocietyServices}
                      onCancel={editClick1}
                      setUnsavedChanges={setUnsavedChanges}
                    />
                  ) : (
                    <Section2
                      setter={setServices}
                      defaultData={developerSocietyServices}
                      onCancel={editClick1}
                      setUnsavedChanges={setUnsavedChanges}
                    />
                  ))}
                {/* {state1 === 'edit' && <Section2 setter= {setServices} defaultData={services} onCancel={editClick1} />} */}
              </>
            }
            tabContent2={
              <>
                {state2 === "view" && (
                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                    onClick={viewClick2}
                  >
                    <Table>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        <TableRow>
                          <MUITableCell>
                            <Typography sx={{ fontWeight: 600 }}>
                              Service Name:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography sx={{ fontWeight: 600 }}>
                              (Yes/No):{" "}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Home buyer Litigation:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.homeBuyerServices?.homeBuyerLitigation
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {
                                data?.homeBuyerServices?.homeBuyerLitigation
                                  ?.price
                              }
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Home buyer Due Diligence
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {data?.homeBuyerServices?.homeBuyerDueDiligence
                                ?.isSupported
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography
                              fontSize={"16px"}
                              color={"#7367F0"}
                              fontWeight={"bold"}
                            >
                              {
                                data?.homeBuyerServices?.homeBuyerDueDiligence
                                  ?.price
                              }
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
                {state2 === "edit" &&
                  currentTab === "2" &&
                  (data?.homeBuyerServices != null ? (
                    <Section3
                      setter={setServices1}
                      defaultData={data.homeBuyerServices}
                      onCancel={editClick2}
                      setUnsavedChanges={setUnsavedChanges}
                    />
                  ) : (
                    <Section3
                      setter={setServices1}
                      defaultData={homeBuyerServices}
                      onCancel={editClick2}
                      setUnsavedChanges={setUnsavedChanges}
                    />
                  ))}
                {/* {state2 === 'edit' && <Section3 setter= {setServices1} defaultData={services1} onCancel={editClick2} />} */}
              </>
            }
            currentTab={currentTab}
            handleTabChange={handleTabChange}
          />
        }
        expanded={expanded}
      />
      {/* } */}
       <Dialog
          open={dialogOpen}
          onClose={() => handleDialogClose(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogTitle  color="primary.main" fontWeight={"bold"}>{"Unsaved Changes"}</DialogTitle>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: message }} />
            
            </DialogContentText>
            <DialogActions>
            <Button
                onClick={() => handleDialogClose(true)}
                color="primary"
                autoFocus
              >
                Yes
              </Button>
              <Button onClick={() => handleDialogClose(false)} color="primary">
                No
              </Button>
             
            </DialogActions>
          </Box>
        </Dialog>
    </>
  );
};
export default ServicesCA;
