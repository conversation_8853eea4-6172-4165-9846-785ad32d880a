import React, { useState } from "react";
import MUITableCell from "src/pages/SP/MUITableCell";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  TextField,
  Box,
  IconButton,
  InputAdornment,
  FormControl,
} from "@mui/material";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import Remarks from "src/@core/components/custom-components/Remarks";
import { useTheme } from "@emotion/react";
import CloseIcon from "@mui/icons-material/Close";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import SearchIcon from "@mui/icons-material/Search";

function QuoteRequestDialog({ data, open, onClose, onSubmit }) {
  const [selectionModel, setSelectionModel] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);

  const columns = [
    { field: "id", headerName: "ID", flex: 0.5, minWidth: 50 },
    { field: "name", headerName: "Name", flex: 2, minWidth: 120 },
    {
      field: "companyName",
      headerName: "Company Name",
      minWidth: 160,
      flex: 3,
    },
    { field: "email", headerName: "Email", minWidth: 180, flex: 3 },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      minWidth: 120,
      flex: 2,
    },
  ];

  const dummyData = [
    {
      id: 1,
      name: "John Doe",
      companyName: "Doe Enterprises",
      email: "<EMAIL>",
      mobileNumber: "************",
    },
    {
      id: 2,
      name: "Jane Smith",
      companyName: "Smith Innovations",
      email: "<EMAIL>",
      mobileNumber: "************",
    },
    {
      id: 3,
      name: "Alice Johnson",
      companyName: "Johnson Tech",
      email: "<EMAIL>",
      mobileNumber: "************",
    },
  ];

  const { reset, control } = useForm();
  const handleRowSelection = (newSelection) => {
    setSelectionModel(newSelection.selectionModel);
  };
  const theme = useTheme();
  const [state2, setState2] = useState("view");
  const [remarks, setRemarks] = useState("");

  const viewClick2 = () => {
    setState2("edit");
  };

  const field = {
    fontWeight: 500,
    fontSize: "12.75px",
  };

  const handleCloseDialog = () => {
    setSelectionModel([]);
    setRemarks("");
    onClose();
  };

  // Handle save action
  const handleSave = () => {
    if (selectionModel.length > 0) {
      const selectedItems = selectionModel.map((id) =>
        dummyData.find((d) => d.id === parseInt(id))
      );
      onSubmit(selectedItems);
      reset();
    } else {
      console.log("No items selected or invalid selection model");
    }
    handleCloseDialog();
  };

  return (
    <Dialog open={open} onClose={handleCloseDialog} maxWidth="md" fullWidth>
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },
          fontSize: { xs: 19, md: 20 },
          margin: "2px 2px 2px 2px",
        }}
        textAlign={"center"}
      >
        Request Preliminary Quote
        <Box
          sx={{
            position: "absolute",
            top: "4px",
            right: "14px",
          }}
        >
          <IconButton
            size="small"
            onClick={handleCloseDialog}
            sx={{
              p: "0.2rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          position: "relative",
          pt: (theme) => `${theme.spacing(8)} !important`,
          pb: (theme) => `${theme.spacing(5)} !important`,
          px: (theme) => [`${theme.spacing(8)} !important`],
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12}>
            <Grid
              container
              spacing={2}
              alignItems="center"
              justifyContent="flex-end"
            >
              <Grid item xs={12} sm="auto">
                <FormControl>
                  <Controller
                    name="mainSearch"
                    control={control}
                    render={({ field: { onChange } }) => (
                      <TextField
                        id="mainSearch"
                        placeholder="Search"
                        // value={keyword}
                        onChange={(e) => {
                          onChange(e.target.value);
                          // setKeyword(e.target.value);
                          // setSearchKeyword(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            // setSearchKeyword(keyword);
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={() => {
                                  // setSearchKeyword(keyword);
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <div style={{ height: 300, width: "100%" }}>
              <DataGrid
                rows={dummyData}
                columns={columns}
                pageSize={5}
                checkboxSelection
                onSelectionModelChange={(newSelectionModel) => {
                  setSelectionModel(newSelectionModel);
                }}
                selectionModel={selectionModel}
              />
            </div>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Remarks"
              fullWidth
              multiline
              rows={4}
              value={remarks}
              onChange={(e) => {
                setRemarks(e.target.value), reset();
              }}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <Grid item xs={12}></Grid>
      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
        }}
      >
        <Button onClick={handleCloseDialog}>Cancel</Button>

        <Button onClick={handleSave} variant="contained">
          Send
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default QuoteRequestDialog;
