import React, { useContext, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Divider,
  Box,
  Tooltip,
  Menu,
  MenuItem,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import ServiceProfilesDialog from "./ServiceTypesEdit";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import CustomChip from "src/@core/components/mui/chip";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";
import DeleteSpecificationDialog from "./DeleteSpecificationDialog";
import ActivateSpecificationDialog from "./ActivateSpecificationDialog";
import ViewSpecification from "./SpecificationView";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const ServiceTypes = () => {
  const [openDialog, setOpenDialog] = useState(false);

  const { listValues, listNames,specificationData,setSpecificationData,specificationDataDetails } = useContext(AuthContext);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [viewProfileDialogOpen,setViewProfileDialogOpen] = useState(false);

  const [specificationsList, setSpecificationsList] = useState([]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      field: "listValuesId",
      headerName: "Service Type",
      flex: 0.11,
      minWidth: 120,
      renderCell: (params) => {
        const listValue = listValues?.find((item) => item.id === params.value);
        return <span>{listValue ? listValue.name : "Unknown"}</span>;
      },
    },
    {
      field: "listNamesId",
      headerName: "Label Name",
      flex: 0.11,
      minWidth: 120,
      renderCell: (params) => {
        const listName = listNames?.find((item) => item.id === params.value);
        return <span>{listName ? listName.name : "Unknown"}</span>;
      },
    },
    {
      field: "sectionId",
      headerName: "Section Name",
      flex: 0.11,
      minWidth: 120,
      renderCell: (params) => {
        const listValue = listValues?.find((item) => item.id === params.value);
        return <span>{listValue ? listValue.name : "Unknown"}</span>;
      },
    },

    {
      field: "isActive",
      headerName: "Status",
      flex: 0.11,
      minWidth: 120,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "uiComponentsId",
      headerName: "UI Components",
      flex: 0.11,
      minWidth: 120,
      renderCell: (params) => {
        const listValue = listValues?.find((item) => item.id === params.value);
        return <span>{listValue ? listValue.name : "Unknown"}</span>;
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setSpecificationData({
            ...specificationData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setViewProfileDialogOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => setOpenDialog(false);

  const fetchSpecifications = async (
    currentPage,
    currentPageSize,
    searchKeyword
  ) => {
    const url = getUrl(authConfig.getAllSpecifications);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setSpecificationsList(response.data?.serviceProfiles || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchSpecifications(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchSpecifications(page, pageSize);
  };
  
  const handleCloseViewDialog = () => {
    setViewProfileDialogOpen(false);
    fetchSpecifications(page, pageSize);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchSpecifications(page, pageSize);
  };

  return (
    <>
      <Grid>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">{"Specifications"}</Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid item xs={12} sm="auto">
                    <Button variant="contained" onClick={handleOpenDialog}>
                      Add Specifications
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <CardContent>
            <div style={{ height: 430, width: "100%" }}>
              <DataGrid
                columns={columns}
                rows={specificationsList} // Empty array for rows
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            </div>
          </CardContent>
        </Card>
      </Grid>

      <ViewSpecification
        open={viewProfileDialogOpen}
        onClose={handleCloseViewDialog}
        data={specificationDataDetails}
        fetchSpecifications={fetchSpecifications}
      />

      <DeleteSpecificationDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
      />

      <ActivateSpecificationDialog
        open={openActivateDialog}
        onClose={handleCloseActivateDialog}
        data={currentRow}
      />
      <ServiceProfilesDialog open={openDialog} onClose={handleCloseDialog} />
    </>
  );
};

export default ServiceTypes;
