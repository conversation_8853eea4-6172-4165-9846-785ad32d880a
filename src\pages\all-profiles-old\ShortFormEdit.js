// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Typography } from "@mui/material";
import { Box } from "@mui/system";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";

import { ShortFormDataValidations } from "./ShortFormDataValidations";
import { useContext } from "react";

const ShortFormEdit = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();

  const { shortFormData } =
  useContext(AuthContext);

  const fields = [
    "firstName",
    "lastName",
    "mobileNumber"
  ];
  
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(ShortFormDataValidations(fields)),
    mode: "onChange"
  });


  async function submit(data) {

    console.log("Submitted Data to update", data);

    const fields = {
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email:formData.email,
      entityCategory:formData.entityCategory,
      entityType:formData.entityType,
      entityId:formData.entityId
      };
   
    const response = await auth.updateShortForm(fields, () => {
      console.error(" Basic Details failed");
    });

    console.log("responseUpdateinSection1", response);
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
       
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="firstName"
              control={control}
              defaultValue={formData?.firstName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label=" First Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your firstName"
                  error={Boolean(errors.firstName)}
                  helperText={errors.firstName?.message}
                  aria-describedby="Section1-firstName"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="lastName"
              control={control}
              defaultValue={formData?.lastName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Last Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your firstName"
                  error={Boolean(errors.lastName)}
                  helperText={errors.lastName?.message}
                  aria-describedby="Section1-lastName"
                />
              )}
            />
          </FormControl>
        </Grid>


        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Mobile Number"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.contactNumber)}
                  placeholder="Enter 10 digit Mobile Number"
                  helperText={errors.contactNumber?.message}
                  aria-describedby="Section1-contactNumber"
                />
              )}
            />
          </FormControl>
        </Grid>


        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Typography className="data-field">Email:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.email}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Typography className="data-field">Entity Category:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.entityCategory}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Typography className="data-field">Entity Type:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.entityType}
            </Typography>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit" 
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ShortFormEdit;
