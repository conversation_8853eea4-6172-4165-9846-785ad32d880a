import {
  Button,
  Box,
  IconButton,
  Grid,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  TextField,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import Icon from "src/@core/components/icon";
import { useTheme } from "@emotion/react";

const SiteVisitTimings = ({ open, onClose }) => {
  const theme = useTheme();

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm();

  const handleCloseDialog = () => {
    onClose();
    setValue("contactPersonName", "");
    setValue("contactNumber", "");
    setValue("siteVisitDate", "");
    setValue("startTime", "");
    setValue("endTime", "");
  };

  const submit = (data) => {
    console.log("Data in submit function", data);
    handleCloseDialog()
  };

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          Site Visit Timings
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid
            container
            alignItems="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          >
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="contactPersonName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Contact Person Name"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter Contact Person Name"
                      error={Boolean(errors.contactPersonName)}
                      helperText={errors.contactPersonName?.message}
                      aria-describedby="validation-basic-contactPersonName"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="contactNumber"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      type="tel"
                      label="Contact Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      inputProps={{
                        maxLength: 10,
                      }}
                      error={Boolean(errors.contactNumber)}
                      helperText={errors.contactNumber?.message}
                      aria-describedby="validation-basic-contactNumber"
                      onKeyDown={(e) => {
                        if (
                          !/[0-9]/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="siteVisitDate"
                  control={control}
                  rules={{ required: "Site Visiting Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Site Visiting Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      aria-describedby="siteVisitDate"
                      value={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="startTime"
                  control={control}
                  rules={{ required: "Start Time is required" }} 
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Start Time"
                      type="time"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.startTime)}
                      helperText={errors.startTime?.message}
                      aria-describedby="startTime"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <Controller
                  name="endTime"
                  control={control}
                  rules={{ required: "End Time is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="End Time"
                      type="time"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.endTime)}
                      helperText={errors.endTime?.message}
                      aria-describedby="endTime"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>

          <Button onClick={handleSubmit(submit)} variant="contained">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SiteVisitTimings;
