import React from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Button, DialogContentText } from "@mui/material";
import Box from "@mui/material/Box";

import { getAuthorizationHeaders } from "src/helpers/utils";

const DeleteSpecificationDialog = ({ open, onClose, data }) => {

  async function onDelete(data) {
    try {
      await axios.delete(
        authConfig.baseURL + authConfig.getAllServiceProfiles + "/" + data.id,
        {
          headers: getAuthorizationHeaders(),
        }
      );
    } catch (error) {
      console.error("Delete operation failed", error);
    }
    onClose();
    
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              Are you sure, you want to delete row?
            </DialogContentText>
          </DialogContent>
          <DialogActions> 
            <Button
              variant="contained"
              onClick={() => onDelete(data)}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};


export default DeleteSpecificationDialog;