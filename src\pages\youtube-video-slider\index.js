import React from 'react';
import KeenSliderStyle from './KeenSliderStyle'
import SwiperPosts from './SwiperPosts';
import { useSettings } from 'src/@core/hooks/useSettings'

const YoutubeVideosSlider = () => {

  const {
    settings: { direction }
  } = useSettings()


  const videoUrls = [
    'https://youtu.be/UTiXQcrLlv4',
    'https://youtu.be/DoTkzj3wiHE',
    'https://youtu.be/iZUeDkjfPps',
    'https://youtu.be/JC6NHSIi4ws',
    'https://youtu.be/avz06PDqDbM',
    'https://youtu.be/NgBoMJy386M',
    'https://youtu.be/bMf0IyzyKt4'
  ];

  return (
    
<KeenSliderStyle>

<SwiperPosts direction={direction} videoUrls={videoUrls} />

</KeenSliderStyle>
  );
};

export default YoutubeVideosSlider;
