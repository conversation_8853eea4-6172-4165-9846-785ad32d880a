import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Checkbox,
  FormControlLabel,
  Divider,
  Alert,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Google as GoogleIcon,
  Facebook as FacebookIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { validationRules } from '../../utils/validation';
import PasswordStrengthIndicator from '../../components/forms/PasswordStrengthIndicator';
import LoadingSpinner from '../../components/common/LoadingSpinner';

/**
 * Registration page component
 */
const RegisterPage = () => {
  const router = useRouter();
  const { register: registerUser, socialLogin, isAuthenticated, loading, error, clearError } = useAuth();
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [registerError, setRegisterError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    getValues,
  } = useForm({
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      termsAccepted: false,
    },
  });

  const watchedPassword = watch('password');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, loading, router]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setRegisterError('');
  }, [clearError]);

  /**
   * Handle form submission
   * @param {Object} data - Form data
   */
  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setRegisterError('');
    setSuccessMessage('');
    clearError();

    try {
      const { confirmPassword, termsAccepted, ...userData } = data;
      
      const result = await registerUser(userData);
      
      if (result.success) {
        setSuccessMessage('Registration successful! Redirecting to dashboard...');
        
        // Redirect after a short delay
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setRegisterError(result.error);
      }
    } catch (err) {
      setRegisterError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle social registration
   * @param {string} provider - Social provider (google, facebook)
   */
  const handleSocialRegister = async (provider) => {
    setRegisterError('');
    setSuccessMessage('');
    clearError();

    try {
      // Mock social login for demo
      const mockToken = `mock-${provider}-token-${Date.now()}`;
      
      const result = await socialLogin({
        provider,
        token: mockToken,
      });

      if (result.success) {
        setSuccessMessage('Registration successful! Redirecting to dashboard...');
        
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setRegisterError(result.error);
      }
    } catch (err) {
      setRegisterError(`${provider} registration failed. Please try again.`);
    }
  };

  /**
   * Toggle password visibility
   */
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  /**
   * Toggle confirm password visibility
   */
  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner loading={true} message="Checking authentication..." overlay />;
  }

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
          }}
        >
          {/* Header */}
          <Typography component="h1" variant="h4" gutterBottom>
            Create Account
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Join us today and get started
          </Typography>

          {/* Success Alert */}
          {successMessage && (
            <Alert severity="success" sx={{ mb: 3, width: '100%' }}>
              {successMessage}
            </Alert>
          )}

          {/* Error Alert */}
          {(error || registerError) && (
            <Alert severity="error" sx={{ mb: 3, width: '100%' }}>
              {error || registerError}
            </Alert>
          )}

          {/* Registration Form */}
          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ width: '100%' }}>
            {/* Full Name Field */}
            <TextField
              {...register('fullName', validationRules.fullName)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Full Name"
              autoComplete="name"
              autoFocus
              error={!!errors.fullName}
              helperText={errors.fullName?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />

            {/* Email Field */}
            <TextField
              {...register('email', validationRules.email)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Email Address"
              autoComplete="email"
              error={!!errors.email}
              helperText={errors.email?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />

            {/* Password Field */}
            <TextField
              {...register('password', validationRules.password)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              error={!!errors.password}
              helperText={errors.password?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Password Strength Indicator */}
            <PasswordStrengthIndicator 
              password={watchedPassword} 
              showRequirements={true}
            />

            {/* Confirm Password Field */}
            <TextField
              {...register('confirmPassword', validationRules.confirmPassword(watchedPassword))}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              autoComplete="new-password"
              error={!!errors.confirmPassword}
              helperText={errors.confirmPassword?.message}
              sx={{ mt: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle confirm password visibility"
                      onClick={handleToggleConfirmPasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Terms and Conditions Checkbox */}
            <FormControlLabel
              control={
                <Checkbox
                  {...register('termsAccepted', validationRules.termsAccepted)}
                  color="primary"
                  size="small"
                />
              }
              label={
                <Typography variant="body2">
                  I agree to the{' '}
                  <Link href="/terms" target="_blank" sx={{ textDecoration: 'none' }}>
                    Terms and Conditions
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" target="_blank" sx={{ textDecoration: 'none' }}>
                    Privacy Policy
                  </Link>
                </Typography>
              }
              sx={{ mt: 2, mb: 2, alignItems: 'flex-start' }}
            />
            {errors.termsAccepted && (
              <Typography variant="caption" color="error" sx={{ mt: -1, mb: 2, display: 'block' }}>
                {errors.termsAccepted.message}
              </Typography>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isSubmitting}
              sx={{ mt: 2, mb: 2, py: 1.5 }}
            >
              {isSubmitting ? 'Creating Account...' : 'Create Account'}
            </Button>

            {/* Login Link */}
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Link
                href="/login"
                variant="body2"
                sx={{ textDecoration: 'none' }}
              >
                Already have an account? Sign in
              </Link>
            </Box>

            {/* Divider */}
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                Or register with
              </Typography>
            </Divider>

            {/* Social Registration Buttons */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<GoogleIcon />}
                onClick={() => handleSocialRegister('google')}
                sx={{ py: 1.5 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FacebookIcon />}
                onClick={() => handleSocialRegister('facebook')}
                sx={{ py: 1.5 }}
              >
                Facebook
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default RegisterPage;
