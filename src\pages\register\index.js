
// ** React Imports
import { useState, useRef } from "react";
import { useEffect, useContext } from "react";

// ** Next Imports
import Link from "next/link";


// ** MUI Components
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import Box from "@mui/material/Box";
import CustomChip from "src/@core/components/mui/chip";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  CircularProgress,
  Grid,
  Divider,
  Card,
} from "@mui/material";
import FormControl from "@mui/material/FormControl";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import { AuthContext } from "src/context/AuthContext";
// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

// ** Hooks
import { useAuth } from "src/hooks/useAuth";
import useBgColor from "src/@core/hooks/useBgColor";
import { useSettings } from "src/@core/hooks/useSettings";
import { Tab, Tabs } from "@mui/material";

import axios from "axios";

import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";


// ** Configs

// ** Config
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Demo Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";

const CustomTab = styled(Tab)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    fontSize: "0.8rem",
  },
  [theme.breakpoints.down("md")]: {
    fontSize: "0.7rem",
  },
  [theme.breakpoints.up("md")]: {
    fontSize: "0.7rem",
  },
  [theme.breakpoints.down("sm")]: {
    fontSize: "0.56rem",
  },
}));

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ width: "100%" }}>
          {" "}
          {/* Ensure Box takes full width */}
          <Typography component="div">{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

// ** Styled Components
const LoginIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 350,
  margin: theme.spacing(5),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 350,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 350,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "40%",
  },
}));

const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: { xs: "0.7rem", lg: "0.9rem" },
  textDecoration: "none",
  color: theme.palette.primary.main,
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));

const emailValidationSchema = yup.object().shape({
  email: yup.string().email("Invalid email").required("Email is required"),
  firstName: yup.string().required("First Name is required"),
  lastName: yup.string().required("Last Name is required"),
});

const passwordValidationSchema = yup.object().shape({
  password: yup.string().required("Password is required").min(8),
});

const defaultValues = {
  firstName: "",
  lastName:"",
  email: "",
  mobileNumber: "",
  password:""
};

const CustomTabDesktop = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      // width: "40%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
    "& button:nth-child(2)": {
      // width: "60%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));

const CustomizedTabStyle = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      width: "50%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));



//SignUpPage

const SignUpPage = () => {

  const [value, setValue] = useState(0);
  const router = useRouter();
  const isLargeScreen = useMediaQuery("(min-width:600px)");
  const isMobileView = useMediaQuery((theme) => theme.breakpoints.down("xs"));
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [password, setPassword] = useState("");
  const [overrideExistingLogins, setOverrideExistingLogins] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [isFailed, setIsFailed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingSuccess, setLoadingSuccess] = useState(false);
  const [showPopup, setShowPopup] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [mobileVerified, setMobileVerified] = useState(false);
  const [otpOptions,setOtpOptions] = useState(false)
  const [otp,setOtp] = useState("")
  const [countdown,setCountdown] = useState(0)
  const [loadingValidate,setLoadingValidate] = useState(false)
  const [okayButtonClose, setOkayButtonClose] = useState(false);
  const { role } = router.query; // Extract role from query parameter
  const [selectedRole, setSelectedRole] = useState('');
  const [circular,setCircular] = useState(false)

  const currentValidationSchema =
  value === 0 ? emailValidationSchema : mobileValidationSchema;

  const {
    control,
    setError,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues,
    resolver: yupResolver(currentValidationSchema),
  });

    // ** Hooks
    const auth = useAuth();
    const theme = useTheme();
    const bgColors = useBgColor();
    const { settings } = useSettings();
    const hidden = useMediaQuery(theme.breakpoints.down("md"));
  
    const url = authConfig.googleAuthUrl;

    const googleUrl = `${url}?role=${selectedRole}`; 
  
    const facebookUrl = authConfig.facebookAuthUrl;
  
    const { fetchProfile } = useContext(AuthContext);
  
    // ** Vars
    const { skin } = settings;

  

  const CustomTabsComponent = isMobileView ? CustomTabMobile : CustomTabDesktop;

  useEffect(() => {
    Promise.resolve().then(() => {
      if (
        window.localStorage?.getItem("userData") &&
        window.localStorage?.getItem("accessToken")
      ) {
        window.location.href = "/dashboard";
      }
    });
  }, [router.route]);

  useEffect(() => {
    // localStorage.removeItem(authConfig.storageUserKeyName) //userData
    // localStorage.removeItem(authConfig.storageTokenKeyName) //accessToken
    localStorage.removeItem("refreshToken");
  }, []);

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };




  useEffect(() => {
    // If the role exists in the query parameter, set it in the state
    if (role) {
      setSelectedRole(role);
      console.log("role",role)
    }
  }, [role]);



  async function verifyOtp() {  
    setLoadingValidate(true);
    console.log("Validating the OTP.");

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint3) + "?isMember=true",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        const returnedResponse = response.data?.passwordResetLink;
        const params = new URLSearchParams(returnedResponse.split("?")[1]);
        console.log("params from URLSearchParams: ", params);
        const extractedResetCode = params.get("resetCode");
        localStorage.setItem("resetCode", extractedResetCode);
        console.log("extractedResetCode", extractedResetCode);
        console.log("OTP verified successfully", response);
        const message = `
          <div>
            <h3>
              Email Verified. </br>Please set your password now. 
            </h3>
            <h3>${email}</h3>
          </div>
        `;

        setDialogMessage(message);
        setOtpOptions(false);
        setEmailVerified(true);
        setOkayButtonClose(false);
        setLoadingValidate(false);
        // setLoadingSuccess(true);
        setLoadingValidate(false);
        // setSubmitSuccess(true);

        // setTimeout(() => {
        //   const url = `/reset-password/?resetCode=${extractedResetCode}&emailId=${email}&userId=${userId}`;
        //   router.push(url);
        // }, 5000);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
          <div>
          <h3>
          Failed to verify OTP. Please try again.
          </h3>
          </div>
        `;
        setDialogMessage(message);
        setLoadingValidate(false);
      });
  }




  const isapicalling = useRef(false);
  async function handleEmailVerification () {
    if (isapicalling.current) {
      // API call is already in progress, return early
      return;
    }
  
    isapicalling.current = true;
    const data = getValues(); 
    

    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password
    if (value === 0) {
      fields = {
        email: data?.email,
        firstName: data?.firstName,
        lastName:data?.lastName
      };
      await emailValidationSchema.validate(fields);
    }
    // If the mobile number tab is active, populate fields with mobileNumber and password
    else if (value === 1) {
      fields = {
        mobileNumber: data?.mobileNumber,
        password: data?.password,
      };
    }
   
    setLoading(true)
    const ipAddress = await fetchIpAddress();
   
    data.ipAddress = ipAddress;
    data.role=selectedRole;
    console.log("# SUBMITTED DATA - SIGNUP V3",data);
    // Implement the OTP verification logic here
    await auth.signupV3(data,handleFailure,handleSuccess)
    setEmail(data.email);
    isapicalling.current = false;
  };

  const handleLoginData = async (email, password) => {
    try {
      const ipAddress = await fetchIpAddress();
      auth.login(
        {
          email: email,
          password: password,
          overrideExistingLogins: true,
          ipAddress,
        },
        (errorCallback) => {
          console.log(errorCallback);
        },
        (successCallback) => {
          setCircular(true)
          const message = `
          <div>
          <h3>
          Registration Successful.Redirecting to dashboard.
          </h3>
          </div>
        `;
        setDialogMessage(message);
        setOpenDialog(true);
          
          console.log("Login successful!");
        }
      );
    } catch (error) {
      console.log("Login failed:", error);
    }
  };

  async function handleSignUp(){
    const data = getValues(); 
    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password
  
      fields = {
        password:data?.password
      };
await passwordValidationSchema.validate(fields);

      
    let resetCode = localStorage.getItem("resetCode");
    auth.resetPassword(
      { resetCode:resetCode,emailId: data?.email, password: data?.password },
      (errMessage) => {
        handleSignUpFailure(errMessage);
      },
      ()=>{
        handleLoginData(data?.email, data?.password);
      }
    );
  }

  const handleMobileVerification = () => {
    // Implement the OTP verification logic here
    setMobileVerified(true);
  };

  const handleFailure = () => {
    setLoading(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Failed to register. Please try again later</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setOkayButtonClose(true)
  };

  const handleSuccess = (data) => {
    setLoading(false);
    if(data?.isVerified){
      const message = ` 
    <div>
      <h3>User already exists with this email .Please try to login.Redirecting to login page in 5 seconds</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setTimeout(()=>{
      router.push("/login");
    },5000)
    }
    else{
      const message = ` 
    <div>
      <h3>Registration Success. OTP has been sent to your email for verification .Please check</h3>
    </div>
    `;
    setLoading(false);
    setDialogMessage(message);
    setOtpOptions(true);
    setCountdown(30);
    setOpenDialog(true);
    }
  };

  const handleSignUpFailure = () => {
    setLoading(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Failed to register. Please try again later</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    // setOkayButtonClose(true)
  };

  const handleSignUpSuccess = () => {
    setLoading(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Sign Up Successful</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setOkayButtonClose(true)
    
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  const StyledCard = styled(Card)(({ theme }) => ({
    marginTop: "3.5%",
    marginBottom: "3.5%",
    marginRight: "10%",
    marginLeft: "10%",
    width: "100%",
  
    [theme.breakpoints.up("md")]: {
      marginRight: "5%",
      marginLeft: "0.5%",
      borderRadius: "40px",
    },
    [theme.breakpoints.down("lg")]: {
      marginRight: "5%",
      marginLeft: "5%",
     
    },
    [theme.breakpoints.up("lg")]: {
      marginRight: "10%",
      marginLeft: "10%",
     
    },
    overflow: "hidden",
  }));


  

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };


  
  const SocialLoginBlock = ({ imageUrl, text, url }) => {
    const [isHovered, setIsHovered] = useState(false);

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    // Append action to URL

    return (
      <Grid item>
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          style={{
            border: isHovered ? "1px solid #aaa" : "1px solid #ccc",
            padding: "5px",
            borderRadius: "5px",
            textAlign: "center",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "10px",
            cursor: "pointer",
            backgroundColor: isHovered ? "#f2f7f2" : "transparent",
          }}
        >
          <Box
            component="img"
            sx={{
              height: {
                xs: "1.3rem",
                lg: "1.5rem",
              },
            }}
            src={imageUrl}
            alt={text}
          />
          <LinkStyled
            href={url}
            sx={{ color: "black", fontSize: { xs: "0.75rem", lg: "0.8rem" } }}
          >
            {text}
          </LinkStyled>
        </div>
      </Grid>
    );
  };

  const SocialLogin = () => {
    return (
      <Grid container spacing={2} justifyContent="center">
      <Grid item xs={12} >
        <SocialLoginBlock
          imageUrl="/images/google-logo.png"
          text="Continue with Google"
          url={googleUrl}
        />
      </Grid>
      {/* <Grid item xs={6} style={{ paddingLeft: 4 }}>
        <SocialLoginBlock
          imageUrl="/images/fb-logo.png"
          text="Facebook"
          url={facebookUrl}
        />
      </Grid> */}
    </Grid>
    );
  };

  
  useEffect(()=>{
    if(countdown > 0){
      const timerId = setTimeout(() => setCountdown(countdown-1),1000);
      return () => clearTimeout(timerId)
    }
  },[countdown])

  const onSubmit = async (data) => {
    console.log("S_D", data, value);
    console.log("E EMail", errors);
    console.log("E Mobil", errors.mobileNumber);

    let processedData = { ...data };

    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password
    if (value === 0) {
      fields = {
        email: data?.email,
        password: data?.password,
      };
    }
    // If the mobile number tab is active, populate fields with mobileNumber and password
    else if (value === 1) {
      fields = {
        mobileNumber: data?.mobileNumber,
        password: data?.password,
      };
    }

    console.log("F F", fields);

    const { email, password } = data;
    //await schema.validate(data);

    const ipAddress = await fetchIpAddress();
   
  };


  const imageSource =
    skin === "bordered"
      ? "auth-v2-login-illustration-bordered"
      : "auth-v2-login-illustration";
  if (
    !(
      window.localStorage?.getItem("userData") &&
      window.localStorage?.getItem("accessToken")
    )
  ) {
    return (
      <Box className="content-right" sx={{ backgroundColor: "#f2f7f2" }}>
        <Dialog
        open={openDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          {otpOptions && (
            <Grid container spacing={5}>
              <Grid container justifyContent="center">
                <TextField
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  placeholder="OTP"
                  value={otp}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value) && value.length <= 6) {
                      setOtp(value);
                    }
                  }}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors?.otp)}
                  helperText={errors?.otp?.message}
                  sx={{
                    borderRadius: "5px",
                    background: "white",
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={12}>
                <Grid container justifyContent="center">
                  <Button
                    variant="contained"
                    disabled={!otp || Boolean(errors?.otp)}
                    onClick={verifyOtp}
                    sx={{
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loadingValidate ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "VALIDATE OTP"
                    )}
                  </Button>
                  <Button
                    variant={countdown > 0 ? "outlined" : "contained"}
                    disabled={countdown > 0}
                    onClick={handleEmailVerification}
                    sx={{
                      marginLeft: "7px",
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loading ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "RESEND OTP"
                    )}
                  </Button>
                </Grid>
                {countdown > 0 && (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "primary.main",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}
          {okayButtonClose && (
            <DialogActions>
              <Button
                onClick={handleClose}
                style={{ margin: "10px auto", width: 100 }}
              >
                 Okay
              </Button>
            </DialogActions>
          )}
          {emailVerified && (
            <Button  onClick={handleClose} style={{ margin: "10px auto", width: 100 }}>
              {circular ? <CircularProgress color="inherit" size={24} /> : "Okay"}
            </Button>
          )}
        </Box>
      </Dialog>
        {!hidden ? (
          <Box
            sx={{
              flex: 1,
              display: "flex",
              position: "relative",
              alignItems: "center",
              borderRadius: "20px",
              justifyContent: "center",
              backgroundColor: "customColors.bodyBg",
              margin: (theme) => theme.spacing(11),
            }}
          >
            <LoginIllustration
              alt="login-illustration"
              src={`/images/pages/login.webp`}
            />
            <FooterIllustrationsV2 />
          </Box>
        ) : null}

        <StyledCard>
          <RightWrapper>
            <Box
              sx={{
                p: [6, 10],
                height: "80%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Box sx={{ width: "100%", maxWidth: 400 }}>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "0.6rem",
                  }}
                >
                  <a href={authConfig.guestURL + "home"}>
                    <img
                      width="40"
                      height="40"
                      alt=""
                      src="/images/logo.webp"
                      className=""
                    />
                  </a>
                  <Typography
                    sx={{
                      mb: 1,
                      ml: 0,
                      fontWeight: 500,
                      fontSize: { xs: "1rem", lg: "1.2rem" },
                      lineHeight: 1.385,
                    }}
                  >
                    {`Sign up`}
                  </Typography>
                </div>

                {/* <Typography
                  sx={{
                    color: "text.secondary",
                    ml: { xs: 9, lg: 9 },
                    mb: { xs: 4, lg: 4 },
                    fontSize: { xs: "0.6rem", lg: "0.8rem" },
                  }}
                >
                  Please select using
                </Typography> */}

                <form
                  noValidate
                  autoComplete="off"
                  onSubmit={handleSubmit(onSubmit)}
                >
                  <Box sx={{ width: "100%", maxWidth: 400 }}>
                    <SocialLogin />
                  </Box>

                  <Divider sx={{ mt: 4, fontSize: { xs: "0.65rem", lg: "0.85rem" } }}>or</Divider>


                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "column" }, // Stack on mobile, row on larger screens
                      alignItems: "center",
                      justifyContent: "space-between",
                      mb: 2,
                      mt: 2,
                    }}
                  >
                    {/* <Typography
                      sx={{
                        whiteSpace: "nowrap",
                        mb: { xs: 1, md: 1 }, // Add margin bottom on mobile to separate from tabs
                        fontSize: { xs: "0.65rem", lg: "0.85rem" },
                      }}
                    >
                      Choose verification: email/mobile.
                    </Typography> */}
                   

                    <CustomizedTabStyle
                      value={value}
                      onChange={handleTabChange}
                      aria-label="email or mobile number tabs"
                    >
                      <CustomTab label="Email" {...a11yProps(0)} />
                      <CustomTab label="Mobile Number" {...a11yProps(1)} />
                    </CustomizedTabStyle>
                  </Box>

                  <TabPanel value={value} index={0}>
  <Grid container spacing={2} direction="column" sx={{minHeight:'27vh'}}>
    {/* First Row */}
    <Grid item xs={12}>
      <FormControl fullWidth sx={{ width: "100%"}}>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Controller
              name="firstName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="First Name*"
                  fullWidth
                  sx={{
                    "& .MuiInputBase-input::placeholder": {
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputBase-input": {
                      padding: "8px",
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        padding: "6px",
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      fontSize: "0.9rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                  }}
                  size="small"
                  onChange={(e) => {
                    const newValue = e.target.value.replace(/\s/g, " ");
                    field.onChange(newValue);
                    // setFormData((prevData) => ({ ...prevData, firstName: newValue }));
                  }}
                  id="auth-login-v2-first-name"
                  error={Boolean(errors.firstName)}
                  helperText={errors.firstName?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <Controller
              name="lastName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Last Name*"
                  fullWidth
                  sx={{
                    "& .MuiInputBase-input::placeholder": {
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputBase-input": {
                      padding: "8px",
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        padding: "6px",
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      fontSize: "0.9rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                  }}
                  size="small"
                  onChange={(e) => {
                    const newValue = e.target.value.replace(/\s/g, " ");
                    field.onChange(newValue);
                    // setFormData((prevData) => ({ ...prevData, lastName: newValue }));
                  }}
                  id="auth-login-v2-last-name"
                  error={Boolean(errors.lastName)}
                  helperText={errors.lastName?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
          </Grid>
        </Grid>
      </FormControl>
    </Grid>

    {/* Second Row */}
    <Grid item xs={12}>
      <FormControl sx={{  width: "100%" }}>
        <Box display="flex" alignItems="center" width="100%">
          <Controller
            name="email"
            control={control}
            render={({field}) =>(
              <TextField
              {...field}
              label="Email*"
            size="small"
            InputLabelProps={{ shrink: true }}
            helperText={errors.email?.message}
            error={Boolean(errors.email)}
            placeholder="<EMAIL>"
            fullWidth
            disabled={emailVerified}
            sx={{
              "& .MuiInputBase-input::placeholder": {
                fontSize: "1rem",
                "@media (max-width:600px)": {
                  fontSize: "0.75rem",
                },
              },
              "& .MuiInputBase-input": {
                padding: "8px",
                fontSize: "1rem",
                "@media (max-width:600px)": {
                  padding: "6px",
                  fontSize: "0.75rem",
                },
              },
              "& .MuiInputLabel-root": {
                fontSize: "0.9rem",
                "@media (max-width:600px)": {
                  fontSize: "0.75rem",
                },
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {emailVerified ? (
                    <CustomChip
                      rounded={true}
                      skin="light"
                      size="small"
                      label={<span style={{ fontSize: "inherit" }}>Verified</span>}
                      color="success"
                      sx={{
                        textTransform: "capitalize",
                        mr: { xs: 0, lg: 0 },
                        fontSize: { xs: "0.7rem", lg: "0.8rem" },
                      }}
                    />
                  ) : (
                    <Box
                    onClick={!emailVerified ? handleSubmit(handleEmailVerification) : undefined}
                      sx={{
                        border: "1px solid #f2f7f2",
                        borderRadius: "4px",
                        padding: "0 5px",
                        cursor: "pointer",
                        color: "blue",
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: { xs: "0.7rem", lg: "0.8rem" },
                          color: "blue",
                        }}
                      >
                        Verify
                      </Typography>
                    </Box>
                  )}
                </InputAdornment>
              ),
            }}
          />
            )}
          />
          
        </Box>
      </FormControl>
    </Grid>

    {/* Third Row */}
    <Grid item xs={12}>
      <FormControl fullWidth sx={{ width: "100%" }}>
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Password*"
              sx={{
                "& .MuiInputBase-input::placeholder": {
                  fontSize: "1rem",
                  "@media (max-width:600px)": {
                    fontSize: "0.75rem",
                  },
                },
                "@media (max-width:600px)": {
                  maxHeight: "0.2",
                },
                "& .MuiInputBase-input": {
                  padding: "8px",
                  fontSize: "1rem",
                  "@media (max-width:600px)": {
                    padding: "6px",
                    fontSize: "0.75rem",
                  },
                },
                "& .MuiInputLabel-root": {
                  fontSize: "0.9rem",
                  "@media (max-width:600px)": {
                    fontSize: "0.75rem",
                  },
                },
              }}
              size="small"
              onChange={(e) => {
                const newValue = e.target.value.replace(/\s/g, "");
                field.onChange(newValue);
              }}
              id="auth-login-v2-password"
              error={Boolean(errors.password)}
              fullWidth
              helperText={errors.password?.message}
              InputLabelProps={{ shrink: true }}
              type={showPassword ? "text" : "password"}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      edge="end"
                      onMouseDown={(e) => e.preventDefault()}
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={!field.value}
                    >
                      <Icon
                        icon={showPassword ? "tabler:eye" : "tabler:eye-off"}
                        fontSize={{ xs: 5, lg: 20 }}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
            </FormControl>
    </Grid>
  </Grid>
</TabPanel>

<TabPanel value={value} >
  <Grid container spacing={2} direction="column" sx={{minHeight:'27vh'}}>
    {/* First Row */}
    <Grid item xs={12}>
    <FormControl sx={{ width: "100%" }}>
                      <Box display="flex" alignItems="center" width="100%">
                        <TextField
                          label=""
                          size="small"
                          disabled
                          value="+91"
                          sx={{
                            marginRight: 1,
                            maxWidth: "40px",
                            "& .MuiInputBase-input": {
                              padding: "8px",
                              fontSize: "1rem",
                              "@media (max-width:600px)": {
                                padding: "6px",
                                fontSize: "0.75rem",
                                maxWidth: "30px",
                              },
                            },
                          }}
                        />
                        <TextField
                          label="Mobile Number*"
                          size="small"
                          InputLabelProps={{ shrink: true }}
                          helperText={errors.mobileNumber?.message}
                          error={Boolean(errors.mobileNumber)}
                          placeholder="999 999 99 99"
                          fullWidth
                          disabled={mobileVerified} // Disable field based on emailVerified state
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              fontSize: "1rem",
                              "@media (max-width:780px)": {
                                fontSize: "0.75rem",
                              },
                            },
                            "& .MuiInputBase-input": {
                              padding: "8px",
                              fontSize: "0.9rem",
                              "@media (max-width:600px)": {
                                padding: "6px",
                                fontSize: "0.75rem",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              fontSize: "0.9rem",
                              "@media (max-width:600px)": {
                                fontSize: "0.75rem",
                              },
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                {mobileVerified ? (
                                  <CustomChip
                                    rounded={true}
                                    skin="light"
                                    size="small"
                                    label={
                                      <span style={{ fontSize: "inherit" }}>
                                        Verified
                                      </span>
                                    }
                                    color="success"
                                    sx={{
                                      textTransform: "capitalize",
                                      fontSize: { xs: "0.7rem", lg: "0.8rem" }, // Responsive font size
                                    }}
                                  />
                                ) : (
                                  <Box
                                    onClick={
                                      !mobileVerified
                                        ? handleMobileVerification
                                        : undefined
                                    }
                                    sx={{
                                      border: "1px solid #f2f7f2", // Grey border color
                                      borderRadius: "4px",
                                      padding: "0 5px",
                                      cursor: "pointer",
                                      color: "blue",
                                    }}
                                  >
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontSize: {
                                          xs: "0.7rem",
                                          lg: "0.8rem",
                                        },
                                        color: "blue",
                                      }}
                                    >
                                      Verify
                                    </Typography>
                                  </Box>
                                )}
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Box>
                    </FormControl>
    </Grid>

    {/* Second Row */}
    <Grid item xs={12}>
      {/* Content for the second row */}
    </Grid>

    {/* Third Row */}
    <Grid item xs={12}>
      {/* Content for the third row */}
    </Grid>
  </Grid>
</TabPanel>

                 
                 

                 
                  <Box
                    sx={{
                      mb: 1.75,
                      display: "flex",
                      flexWrap: "wrap",
                      alignItems: "start",
                      justifyContent: "flex-start",
                    }}
                  >
                    {/* Ensure no leading spaces or new lines before 'By signing up' */}
                    <Typography
                      sx={{
                        fontSize: { xs: "0.6rem", lg: "0.7rem" },
                        margin: 0,
                        padding: 0,
                      }}
                    >
                      By signing up you agree to the{" "}
                      <Link
                        href="https://houzer.co.in/terms-conditions/"
                        passHref
                      >
                        <Typography
                          component="a"
                          sx={{
                            textDecoration: "none",
                            fontSize: { xs: "0.6rem", lg: "0.7rem" },
                            cursor: "pointer",
                            fontWeight: 600,
                            color: "primary.main", // Apply primary color
                            margin: 0, // Ensure no default margin
                            padding: 0, // Ensure no default padding
                          }}
                        >
                          T & C
                        </Typography>
                      </Link>{" "}
                      and{" "}
                      <Link
                        href="https://houzer.co.in/privacy-policy/"
                        passHref
                      >
                        <Typography
                          component="a"
                          sx={{
                            textDecoration: "none !important",
                            fontSize: { xs: "0.6rem", lg: "0.7rem" },
                            cursor: "pointer",
                            fontWeight: 600,
                            color: "primary.main", // Apply primary color
                            margin: 0, // Ensure no default margin
                            padding: 0, // Ensure no default padding
                          }}
                        >
                          Privacy Policy
                        </Typography>
                      </Link>
                    </Typography>
                  </Box>

                  <Button
                    fullWidth
                    size={isLargeScreen ? "medium" : "small"}
                    onClick={handleSubmit(handleSignUp) }
                    disabled={!emailVerified}
                    
                    variant="contained"
                    sx={{
                      width: "100%",
                      mb: { xs: 2, lg: 2 },
                      fontSize: { xs: "0.7rem", lg: "0.8rem" },
                    }}
                  >
                    Sign Up
                  </Button>

                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      justifyContent: "center", // Align items to the start of the box
                      alignItems: "center", // Vertically center the items in the box
                    }}
                  >
                    <Typography
                      sx={{
                        color: "text.secondary",
                        mr: 2,
                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      Already registered?
                    </Typography>
                    <Typography
                      sx={{
                        color: "text.secondary",

                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      <LinkStyled
                        href="/login"
                        sx={{
                          fontWeight: 500,
                          fontSize: { xs: "0.7rem", lg: "0.9rem" },
                        }}
                      >
                        Login
                      </LinkStyled>
                    </Typography>
                  </Box>
                </form>
              </Box>
            </Box>
          </RightWrapper>
        </StyledCard>
      </Box>
    );
  }
};
SignUpPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
SignUpPage.guestGuard = true;

export default SignUpPage;
