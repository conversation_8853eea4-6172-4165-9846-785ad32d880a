// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";


// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import LocationEdit from "./LocationEdit";



const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const LocationView = ({ data, expanded, fetchUsers }) => {
  // ** Hook
  const theme = useTheme();

  const { listValues } = useContext(AuthContext)

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const zone = (data?.zoneId)
  ? listValues?.find((item) => item.id === (data?.zoneId))?.name
  : null;

  const location = (data?.locationId)
  ? listValues?.find((item) => item.id === (data?.locationId))?.name
  : null;

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Location Data Details"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {" "}
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}> Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {location}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}> Zone:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {zone}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Active:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.isActive? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <LocationEdit onCancel={editClick} formData={data} fetchUsers={fetchUsers} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default LocationView;
