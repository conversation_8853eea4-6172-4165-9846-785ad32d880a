// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Avatar from "@mui/material/Avatar";
import Stepper from "@mui/material/Stepper";
import { styled } from "@mui/material/styles";
import StepLabel from "@mui/material/StepLabel";
import Typography from "@mui/material/Typography";
import FormControl from "@mui/material/FormControl";
import MuiStep from "@mui/material/Step";
import { useForm, Controller } from "react-hook-form";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import DialogContent from "@mui/material/DialogContent";
import {
  DialogActions,
  CircularProgress,
  DialogContentText,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

import { useRouter } from "next/router";

// ** Hooks
import { useAuth } from "src/hooks/useAuth";

// ** Axios
import axios from "axios";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Custom Components Imports
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Step Components

// ** Util Import
import { hexToRGBA } from "src/@core/utils/hex-to-rgba";

// ** Styled Components
import StepperWrapper from "src/@core/styles/mui/stepper";
import StepPersonalInfo from "./StepPersonalInfo";
import StepAccountDetails from "./StepAccountDetails";
import { Alert, AlertTitle, Button, Dialog } from "@mui/material";
import validationSchemaSignUp from "./validationSchemaSignUp";
import { yupResolver } from "@hookform/resolvers/yup";
import authConfig from "src/configs/auth";
import { TrendingUpRounded } from "@mui/icons-material";

const steps = [
  {
    title: "Account",
    icon: "tabler:smart-home",
    subtitle: "Account Details",
  },
  {
    title: "Personal",
    icon: "tabler:users",
    subtitle: "Enter Information",
  },
];

const Step = styled(MuiStep)(({ theme }) => ({
  padding: 0,
  "& .MuiStepLabel-iconContainer": {
    display: "none",
  },
  "& .step-subtitle": {
    color: `${theme.palette.text.disabled} !important`,
  },
  "& + svg": {
    color: theme.palette.text.disabled,
  },
  "&.Mui-completed .step-title": {
    color: theme.palette.text.disabled,
  },
  "&.Mui-completed + svg": {
    color: theme.palette.primary.main,
  },
  "& .MuiStepLabel-label": {
    cursor: "pointer",
  },
  [theme.breakpoints.down("md")]: {
    "&:not(:last-child)": {
      marginBottom: theme.spacing(4),
    },
    "& + svg": {
      display: "none",
    },
  },
  [theme.breakpoints.up("md")]: {
    marginLeft: theme.spacing(4),
    marginRight: theme.spacing(4),
    "&:first-of-type": {
      marginLeft: 0,
    },
    "&:last-of-type": {
      marginRight: 0,
    },
  },
}));

const RegisterTwoSteps = () => {
  // ** Hooks
  const auth = useAuth();

  const router = useRouter();

  const {
    register,
    handleSubmit,
    reset,
    control,
    watch,
    formState: { errors },
  } = useForm({ resolver: yupResolver(validationSchemaSignUp) });
  const watchFields = watch(); // Watch all fields

  const [submitSuccess, setSubmitSuccess] = useState(false);

  useEffect(() => {
    return () => {
      if (submitSuccess) {
        reset();
      }
    };
  }, [submitSuccess, reset]);

  // ** States
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingValidate, setLoadingValidate] = useState(false);
  const [loadingSuccess, setLoadingSuccess] = useState(false);
  const [disableSubmitButton, setDisableSubmitButton] = useState(false);

  const getUrl = (endpoint) => {
    return authConfig.baseURL + endpoint;
  };
  // Handle Stepper
  const handleNext = (type) => {
    console.log("From handleNext: ", type);
    setActiveStep(activeStep + 1);
  };

  const handlePrev = () => {
    if (activeStep !== 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const [openDialog, setOpenDialog] = useState(false);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [otp, setOTP] = useState("");
  const [otpOptions, setOTPOptions] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [okayButtonClose, setOkayButtonClose] = useState(false);
  const [email, setEmail] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [userId, setUserId] = useState(true);

  const handleErrorCloseDialog = () => {
    setOpenDialog(false);
  };

  async function verifyOtp() {
    setLoadingValidate(true);
    console.log("Validating the OTP.");

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint) + "?isMember=true",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        const returnedResponse = response.data?.passwordResetLink;
        const params = new URLSearchParams(returnedResponse.split("?")[1]);
        console.log("params from URLSearchParams: ", params);
        const extractedResetCode = params.get("resetCode");
        console.log("extractedResetCode", extractedResetCode);
        console.log("OTP verified successfully", response);
        const message = `
          <div>
            <h3>
              Email Verified. Please wait to Create a new password. 
            </h3>
            <h3>${email}</h3>
          </div>
        `;

        setDialogMessage(message);
        setOTPOptions(false);
        setIsEmailVerified(true);
        setOkayButtonClose(false);

        setLoadingSuccess(true);
        setLoadingValidate(false);
        setSubmitSuccess(true);

        setTimeout(() => {
          const url = `/reset-password/?resetCode=${extractedResetCode}&emailId=${email}&userId=${userId}`;
          router.push(url);
        }, 5000);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
          <div>
          <h3>
          Failed to verify OTP. Please try again.
          </h3>
          </div>
        `;
        setLoadingValidate(false);
        setDialogMessage(message);
      });
  }

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <StepAccountDetails
            watchFields={watchFields}
            handleNext={handleNext}
            register={register}
          />
        );
      case 1:
        return (
          <StepPersonalInfo
            handlePrev={handlePrev}
            submit={submit}
            handleSubmit={handleSubmit}
            errors={errors}
            disableSubmitButton={disableSubmitButton}
            control={control}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  const renderContent = () => {
    return getStepContent(activeStep);
  };

  const handleSuccess = (data) => {
    setLoading(false);
    if (data?.isVerified) {
      const message = `
    <div>
        <h3>
          User Already exist with this email. Please try to login. Redirecting to login page in 5 seconds.
        </h3>
       </div>
       `;
      setDialogMessage(message);
      setOpenDialog(true);
      setTimeout(() => {
        router.push("/login");
      }, 5000);
    } else {
      const message = `
      <div>
          <h3>
          Registration Success. OTP has been sent to your email for verification. Please check.
          </h3>
         </div>
         `;
      setDialogMessage(message);
      setOpenDialog(true);
      setOTPOptions(true);
      setCountdown(30);
      setLoading(false);
    }
  };

  const handleFailure = () => {
    const message = ` 
    <div>
      <h3>Failed to register. Please try again later.</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setOkayButtonClose(true);
    setLoading(false);
    setDisableSubmitButton(false);
  };

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  async function submit(data) {
    const ipAddress = await fetchIpAddress();
    setDisableSubmitButton(true);

    data.ipAddress = ipAddress;
    setLoading(true);
    await validationSchemaSignUp.validate(data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    await auth.signupV2(trimmedData, handleFailure, handleSuccess);
    setEmail(trimmedData.email);

    console.log("Submitted Data: ", data);
  }

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  return (
    <>
      <Dialog
        open={openDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          {otpOptions && (
            <Grid container spacing={5}>
              <Grid container justifyContent="center">
                <TextField
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  placeholder="OTP"
                  value={otp}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value) && value.length <= 6) {
                      setOTP(value);
                    }
                  }}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors?.otp)}
                  helperText={errors?.otp?.message}
                  sx={{
                    borderRadius: "5px",
                    background: (theme) => theme.palette.text.contrast,
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={12}>
                <Grid container justifyContent="center">
                  <Button
                    variant="contained"
                    disabled={!otp || Boolean(errors?.otp)}
                    onClick={verifyOtp}
                    sx={{
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loadingValidate ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "VALIDATE OTP"
                    )}
                  </Button>
                  <Button
                    variant={countdown > 0 ? "outlined" : "contained"}
                    disabled={countdown > 0}
                    onClick={handleSubmit(submit)}
                    sx={{
                      marginLeft: "7px",
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loading ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "RESEND OTP"
                    )}
                  </Button>
                </Grid>
                {countdown > 0 && (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "primary.main",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}
          {okayButtonClose && (
            <DialogActions>
              <Button
                onClick={handleErrorCloseDialog}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          )}
          {isEmailVerified && (
            <Button style={{ margin: "10px auto", width: 100 }}>
              {loadingSuccess ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "ok"
              )}
            </Button>
          )}
        </Box>
      </Dialog>

      <StepperWrapper sx={{ mb: 10, mt: { lg: 0, xs: 12 } }}>
        <Stepper
          activeStep={activeStep}
          sx={{ justifyContent: "space-between" }}
          connector={<Icon fontSize="1.5rem" icon="tabler:chevron-right" />}
        >
          {steps.map((step, index) => {
            const RenderAvatar = activeStep >= index ? CustomAvatar : Avatar;

            return (
              <Step key={index} sx={{ cursor: "default" }}>
                <StepLabel sx={{ cursor: "default" }}>
                  <div className="step-label">
                    <RenderAvatar
                      variant="rounded"
                      {...(activeStep >= index && { skin: "light" })}
                      {...(activeStep === index && { skin: "filled" })}
                      {...(activeStep >= index && { color: "primary" })}
                      sx={{
                        cursor: "default",
                        mr: 4,
                        ...(activeStep === index && {
                          boxShadow: (theme) => theme.shadows[3],
                        }),
                        ...(activeStep > index && {
                          color: (theme) =>
                            hexToRGBA(theme.palette.primary.main, 0.4),
                        }),
                      }}
                    >
                      <Icon
                        fontSize="1.5rem"
                        icon={step.icon}
                        sx={{ cursor: "default !important" }}
                      />
                    </RenderAvatar>
                    <div>
                      <Typography
                        className="step-title"
                        sx={{ cursor: "default !important" }}
                      >
                        {step.title}
                      </Typography>
                      <Typography
                        className="step-subtitle"
                        sx={{ cursor: "default !important" }}
                      >
                        {step.subtitle}
                      </Typography>
                    </div>
                  </div>
                </StepLabel>
              </Step>
            );
          })}
        </Stepper>
      </StepperWrapper>
      {renderContent()}
    </>
  );
};

export default RegisterTwoSteps;
