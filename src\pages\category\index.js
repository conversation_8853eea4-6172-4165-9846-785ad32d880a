import { useState, useEffect, useContext } from "react";
import { DataGrid } from "@mui/x-data-grid";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import {
  Box,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableBody,
  InputAdornment,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import { FormControlLabel } from "@mui/material";
import Checkbox from "@mui/material/Checkbox";
import { yupResolver } from "@hookform/resolvers/yup";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { Card, CardContent, Divider, Tooltip } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "../SP/MUITableCell";
import ViewDialog from "./viewDialog";
import DeleteDialog from "./deleteDialog";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { RoleTypesValidation } from "./roleTypesValidation";
import CustomChip from 'src/@core/components/mui/chip'
import { useRBAC } from "src/pages/permission/RBACContext";
import { useRouter } from "next/router";
import { format } from "date-fns";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import UpdateDialog from "./updateDialog";


const userStatusObj = {
  true: 'Active',
  false: 'InActive'
}
const RoleTypes = () => {
  const [formData, setFormData] = useState({
    subRoleTypes: [],
  });

  const { can,rbacRoles } = useRBAC();
  
  const router = useRouter();

  const [userList, setUserList] = useState([]);
  const [expanded, setExpanded] = useState(true);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);

  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");

  const [showButton, setShowButton] = useState(false);
  const [openList, setOpenList] = useState(false);
  const [subRoleType, setSubRoleType] = useState("");
  const [subRoleTypes, setSubRoleTypes] = useState([]);
  const [items, setItems] = useState([]);
  const [openEditDialog, setOpenEditDialog] = useState(false);
const [isActive, setIsActive] = useState(false);

  const [currentRow, setCurrentRow] = useState(null);

  const [id, setId] = useState(null);
  const [editIndex, setEditIndex] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);
  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);
  const { createRole } = useContext(AuthContext);

  const [isChecked, setIsChecked] = useState(false);
  

  const {
    reset,
    setValue,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(RoleTypesValidation([])),
    defaultValues: {
      roleType: "",
      subRoleType: "",
    },
    mode: "onChange",
  });

  async function submit(data) {
    const subRoleTypeObjects = subRoleTypes.map((subRole) => ({
      name: subRole,
      isActive: true,
    }));

    const formData = {
      roleType: data.roleType,
      hasSubTypes: data.hasSubTypes,
      isActive: isActive,
      metadata: {
        subRoleTypes: subRoleTypeObjects,
      },
    };

    console.log("formData", formData);
    const response = await createRole(formData, () => {});
    setOpenDialog(false);
    reset();
    setSubRoleTypes([]);
    fetchRoleTypes(page, pageSize);
    console.log("response1234444", response);
  }

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleClose = () => {
    setOpenEditDialog(false);
    fetchRoleTypes(page, pageSize);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditIndex(null);
    setId(null);
  };

  const fetchRoleTypes = async (
    currentPage,
    currentPageSize,
    searchKeyword
  ) => {
    const url = getUrl(authConfig.getAllRoleTypesEndpoint);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        console.log("response", response.data);
        setUserList(response.data?.roleTypes || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    console.log(`Fetching data for page: ${page}, pageSize: ${pageSize}`);
    fetchRoleTypes(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };


  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || 'Unknown';
  }

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0){
      if (!can('userCategory_READ')) {
        router.push("/401");
      }
    }  
  }, [rbacRoles]);

  const columns = [
    { field: "roleType", headerName: "Category", flex: 1.5, minWidth: 150 },
    {
      field: "subCategories",
      headerName: "Sub-categories",
      flex: 1.6,
      minWidth: 150,
      renderCell: ({ row }) => {
        const subRoleTypes = row.metadata.subRoleTypes;
        if (subRoleTypes && subRoleTypes.length > 0) {
          return subRoleTypes.map(subType => subType.name).join(",");
        } else {
          return "NA";
        }
      }
    },
    { field: "createdOn", headerName: "Created on", flex: 1.5, minWidth: 130,
      renderCell: ({ row }) => {
        return format(new Date(row.createdOn), 'dd-MM-yyyy');
      }
    },
    {
      field: "updatedOn",
      headerName: "Updated on",
      flex: 1.5,
      minWidth: 130,
      renderCell: ({ row }) => {
        return format(new Date(row.updatedOn), 'dd-MM-yyyy');
      }
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1.2,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin='light'
            size='small'
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: 'capitalize' }}
          />
        )
      }
    },
    (can('userCategory_UPDATE') || can('userCategory_DELETE')) && 
    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 100,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = useState(null);
  
        const handleClick = (event) => {
          setAnchorEl(event.currentTarget);
        };
  
        const handleClose = () => {
          setAnchorEl(null);
        };
  
        const onClickEditProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenEditDialog(true);
          setIsActive(row.isActive);
          handleClose();
        };
  
        const onClickToggleStatus = () => {
          const row = params.row;
          setCurrentRow(row);
          if (row.isActive) {
            setOpenDeleteDialog(true);
          } else {
            setOpenUpdateDialog(true);
          }
          handleClose();
        };
  
        return (
          <>
            <IconButton
              aria-label="more"
              aria-controls="long-menu"
              aria-haspopup="true"
              onClick={handleClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              {can('userCategory_UPDATE') && (
                <MenuItem onClick={onClickEditProfile}>
                  <ListItemIcon>
                    <Icon icon="iconamoon:edit" />
                  </ListItemIcon>
                  Edit
                </MenuItem>
              )}
              {can('userCategory_DELETE') && (
                <MenuItem onClick={onClickToggleStatus}>
                  <ListItemIcon>
                    <Icon icon={params.row.isActive ? "iconamoon:trash" : "tabler:circle-check"} />
                  </ListItemIcon>
                  {params.row.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </>
        );
      },
    },
  ];
  

  const handleSubCheckboxChange = (fieldName, checked) => {
    setValue(fieldName, checked);
    if (checked) {
      setOpenList(true);
    } else {
      setOpenList(false);
    }
  };

  const handleAddItem = () => {
    if (subRoleType.trim() !== "") {
      const lowerCaseSubRoleType = subRoleType.toLowerCase(); // Convert to lowercase
      if (
        !subRoleTypes.some(
          (type) => type.toLowerCase() === lowerCaseSubRoleType
        )
      ) {
        setSubRoleTypes([...subRoleTypes, subRoleType]); // Add to the list
      }
      setSubRoleType("");
    }
  };

  const handleDeleteItem = (indexToDelete) => {
    setSubRoleTypes(subRoleTypes.filter((_, index) => index !== indexToDelete));
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchRoleTypes(page, pageSize);
  };
  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchRoleTypes(page, pageSize);
  };

  return (
    <>
    {can('userCategory_READ') && 
      <Grid>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">
                  Categories / Departments
                </Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid container spacing={2} alignItems="center" justifyContent="flex-end">
                  <Grid item xs={12} sm="auto">
                    <FormControl>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            placeholder="Search"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                height: "40px",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={setSearchKeyword(keyword)}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  {can('userCategory_READ') && 
                  <Grid item xs={12} sm="auto" >
                    <Button
                      sx={{ width: "120px" }}
                      variant="contained"
                      onClick={handleOpenDialog}
                    >
                      Add New
                    </Button>
                  </Grid>
                  }
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />

          <DeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />
          <UpdateDialog
              open={openUpdateDialog}
              onClose={handleCloseUpdateDialog}
              data={currentRow}
            />
          

          <Dialog
            fullWidth
            maxWidth="lg"
            scroll="paper"
            open={openEditDialog}
            onClose={handleClose}
          >
            <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "start"},
              fontSize: { xs: 19, md: 20  },
            }}
            textAlign={"center"}
            >
              Edit Category
              <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                <IconButton
                  size="small"
                  onClick={handleClose}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                  backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(6)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
                "@media (max-width:900px)": {
                  p: (theme) => `${theme.spacing(4, 4)} !important`,
                },
              }}
            >
              <ViewDialog data={currentRow} expanded={expanded} />
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleClose}
              >
                Close
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            fullWidth
            maxWidth="md"
            scroll="paper"
            open={openDialog}
            onClose={handleCloseDialog}
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: { xs: "start"},
                fontSize: { xs: 19, md: 20  },
              }}
              textAlign={"center"}
            >
              Add New Category
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseDialog}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color: "text.primary",
                    backgroundColor: "action.selected",
                    "&:hover": {
                      backgroundColor: (theme) =>
                        `rgba(${theme.palette.customColors.main}, 0.16)`,
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                p: (theme) => `${theme.spacing(10, 8)} !important`,
                "@media (max-width:900px)": {
                  p: (theme) => `${theme.spacing(8, 6)} !important`,
                },
              }}
            >
              <Grid container spacing={4} alignItems={"center"}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="roleType"
                      control={control}
                      render={({ field }) => (
                        <NameTextField
                          id="roleType"
                          label="Category"
                          placeholder="Enter Category Name"
                          InputLabelProps={{ shrink: true }}
                          inputProps={{ maxLength: 30 }}
                          error={Boolean(errors.roleType)}
                          helperText={errors.roleType?.message}
                          {...field}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={5}>
                  <FormControl fullWidth>
                    <Controller
                      name="hasSubTypes"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={field.value}
                              onChange={(e) =>
                                handleSubCheckboxChange(
                                  "hasSubTypes",
                                  e.target.checked
                                )
                              }
                            />
                          }
                          label="Have sub-categories?"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                {openList && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <Controller
                          name="subCategoryType"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Sub Category Type"
                              value={subRoleType}
                              onChange={(e) => setSubRoleType(e.target.value)}
                              placeholder="Enter Sub Category Type"
                              aria-describedby="Section-name"
                              inputProps={{ maxLength: 30 }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Button
                        variant="contained"
                        onClick={handleAddItem}
                        style={{
                          width: "50px",
                          height: "40px",
                        }}
                      >
                        Add
                      </Button>
                    </Grid>
                  </>
                )}
                <Grid item xs={12}>
                  <TableContainer
                    component={Card}
                    sx={{
                      pt: { xs: 2, md: 3 },
                      px: { xs: 2, md: 3 },
                      width: { xs: "100%", md: "50%" },
                    }}
                  >
                    <Table size="small">
                      <TableHead sx={{ whiteSpace: "nowrap" }}>
                        <TableRow>
                          <MUITableCell
                            sx={{ fontWeight: "bold", noWrap: "nowrap" }}
                          >
                            Sub-Category
                          </MUITableCell>
                          <MUITableCell
                            style={{ textAlign: "right" }}
                            sx={{ fontWeight: "bold" }}
                          >
                            Actions
                          </MUITableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {subRoleTypes?.map((item, index) => (
                          <TableRow key={index}>
                            <MUITableCell>{item}</MUITableCell>
                            <MUITableCell style={{ textAlign: "right" }}>
                              <IconButton
                                sx={{ p: 0, width: 26, height: 26 }}
                                size="small"
                                color="error"
                                onClick={() => handleDeleteItem(index)}
                              >
                                <Icon icon="tabler:trash" />
                              </IconButton>
                            </MUITableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleCloseDialog}
              >
                Cancel
              </Button>
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>
          <CardContent>
            <div style={{ height: 400, width: "100%" }}>
              <DataGrid
                rows={userList}
                columns={columns}
                autoHeight
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38} 
              />
            </div>
          </CardContent>
        </Card>
      </Grid>
    }
    </>
  );
 
};

export default RoleTypes;
