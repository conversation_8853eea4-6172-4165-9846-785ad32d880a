// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import Section1 from "./Section1";
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const CompanyDetailsAdvocate= ({ data,expanded }) => {
  // ** Hook
  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Company Details"}
        body={
          <>
            {state === "view" && (
              
                  <TableContainer
                     sx={{ padding:'4px 6px' }}
                    className="tableBody"
                    onClick={viewClick}
                  >
                    <Table>
                    <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Name:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.name}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Company:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.companyName}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Address:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.address}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Contact Number:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.mobileNumber}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Email:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.email}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Website Url:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field" >
                              {data?.websiteUrl}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        
                        
                      </TableBody>
                    </Table>
                  </TableContainer>
               
            )}
            {state === "edit" && (
              <Section1 formData={data} onCancel={editClick} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default CompanyDetailsAdvocate;
