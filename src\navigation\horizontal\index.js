import { useContext, useEffect, useState } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import { useRBAC } from 'src/pages/permission/RBACContext';

const Navigation = () => {
  const { can } = useRBAC();
  const { user, leftMenuData } = useContext(AuthContext);
  const [navArray, setNavArray] = useState([]);

  useEffect(() => {
    const profilePaths = {
      ARCHITECT: '/profile/architect',
      SOCIETY: '/profile/society',
      STRUCTURAL_ENGINEER: '/profile/structural',
      PMC: '/profile/pmc',
      BROKER: '/profile/broker',
      CHARTERED_ACCOUNTANT: '/profile/charteredAccountant',
      LEGAL: '/profile/legal',
      SUPER_ADMIN: '/profile/admin',
    };

    const processMenuItems = (items, isRoot = false) => items
      .filter(item => item.accessCode === null || can(item.accessCode))

      .map(item => {
        const path = item.path;

        return {
          title: item.title,
          path:  path,
          icon: item.icon,
          children: item.children ? processMenuItems(item.children) : null,
        };
      });


    if (user && leftMenuData?.leftMenuDataSetDTO?.leftMenuData) {
      const updatedNavArray = processMenuItems(leftMenuData.leftMenuDataSetDTO.leftMenuData, true);
      setNavArray(updatedNavArray);
    }
  }, [user, leftMenuData, can]);

  return navArray;
};

export default Navigation;
