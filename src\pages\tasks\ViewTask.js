// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";

// ** Styled Component
import { Box, Button, Card, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, Table, TableBody, TableContainer, TableRow, Tooltip } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import { DataGrid } from "@mui/x-data-grid";
import RemarksTable from "./RemarksTable";
import EditTask from "./EditTask";



const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ViewTask = ({ open,onClose,data,  fetchUsers,employeesData }) => {

  // ** Hook
  const theme = useTheme();

  const [expanded, setExpanded] = useState(true);

  const handleToggle = (value) => {
    setExpanded(value);
  };
  const {user,listValues} = useContext(AuthContext)

  const [state, setState] = useState("view");

  const [openEditDialog,setOpenEditDialog] = useState(false)

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const [assignedToName, setAssignedToName] = useState("");


  const statusName = (data?.additionalDetails?.statusId)
  ? listValues?.find((item) => item.id === (data?.additionalDetails?.statusId))?.name
  : null;


  const priorityName = (data?.additionalDetails?.priorityId)
  ? listValues?.find((item) => item.id === (data?.additionalDetails?.priorityId))?.name
  : null;


  
    const assignedName = (data?.assignedTo)
        ? employeesData?.find(item => item.id === (data?.assignedTo))?.name
        : null;
    



  return (
    <>
     <Dialog open={open} onClose={onClose} fullScreen>
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 19, md: 20 },
              }}
              textAlign={"center"}
            >
                Task Details
                <Box sx={{ position: "absolute", top: "0px", right: "78px" ,}}>
                <CloseExpandIcons
                        expanded={expanded}
                        onToggle={handleToggle}
                      />
                </Box>
                
              <Box sx={{ position: "absolute", top: "6px", right: "30px" }}>
              <Tooltip title="Edit">
                    <CustomAvatar
                      skin="light"
                      variant="rounded"
                      sx={{ mr: 5, width: 34, height: 30 }}
                      onClick={handleOpenEditDialog}
                    >
                      <Icon icon="iconamoon:edit" />
                    </CustomAvatar>
                  </Tooltip>
              </Box>
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>           
                <IconButton
                  size="small"
                  onClick={onClose}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                    backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: 
                    '#66BB6A',
                     transition: 'background 0.5s ease, transform 0.5s ease',                       
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
              <>
                <AccordionBasic
                    id={"panel-header-1"}
                    ariaControls={"panel-content-1"}
                    heading={"Task Details"}
                    body={
                        <>
                            <Grid item xs={12}>
                            <TableContainer
                                sx={{ padding: "4px 6px" }}
                                className="tableBody"
                            >
                                <Table>
                                <TableBody
                                    sx={{
                                    "& .MuiTableCell-root": {
                                        p: `${theme.spacing(1.35, 1.125)} !important`,
                                    },
                                    }}
                                >
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Title:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.title}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Description:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.description}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Status:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {statusName}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Priority:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {priorityName}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>
                                        AssignedTo
                                        </Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {assignedName}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Created by:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {user?.firstName} {user?.lastName}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Created Date:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.createdOn ? data.createdOn.split('T')[0] : ''}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                </TableBody>
                                </Table>
                            </TableContainer>
                            </Grid>           
                        </>
            
                    }
                    expanded={expanded} 
                />
                <AccordionBasic
                    id={"panel-header-1"}
                    ariaControls={"panel-content-1"}
                    heading={"Estimated Details"}
                    body={
                        <>
                            <Grid item xs={12}>
                            <TableContainer
                                sx={{ padding: "4px 6px" }}
                                className="tableBody"
                            >
                                <Table>
                                <TableBody
                                    sx={{
                                    "& .MuiTableCell-root": {
                                        p: `${theme.spacing(1.35, 1.125)} !important`,
                                    },
                                    }}
                                >
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Target Start Date:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.targetStartDate}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Target End Date:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.targetEndDate}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Actual Start Date:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.actualStartDate}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Actual End Date:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.actualEndDate}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>

                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>
                                        Estimated Time
                                        </Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.estimatedTime}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Actual End Time:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.actualEndTime}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                </TableBody>
                                </Table>
                            </TableContainer>
                            </Grid>           
                        </>
            
                    }
                    expanded={expanded} 
                />
                <AccordionBasic
                    id={"panel-header-1"}
                    ariaControls={"panel-content-1"}
                    heading={"Tags and attachments"}
                    body={
                        <>
                            <Grid item xs={12}>
                            <TableContainer
                                sx={{ padding: "4px 6px" }}
                                className="tableBody"
                            >
                                <Table>
                                <TableBody
                                    sx={{
                                    "& .MuiTableCell-root": {
                                        p: `${theme.spacing(1.35, 1.125)} !important`,
                                    },
                                    }}
                                >
                                    <TableRow>
                                    <MUITableCell>
                                        <Typography style={field}>Tags:</Typography>
                                    </MUITableCell>
                                    <MUITableCell>
                                        <Typography className="data-field">
                                        {data?.additionalDetails?.tags}
                                        </Typography>
                                    </MUITableCell>
                                    </TableRow>
                                </TableBody>
                                </Table>
                            </TableContainer>
                            </Grid>           
                        </>
            
                    }
                    expanded={expanded} 
                />
                <AccordionBasic
                    id={"panel-header-1"}
                    ariaControls={"panel-content-1"}
                    heading={"Remarks History"}
                    body={
                        <>
                            <RemarksTable remarksList={data?.additionalDetails?.remarksList} employeesData={employeesData}/>      
                        </>
            
                    }
                    expanded={expanded} 
                />
              </>
              
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={onClose}
              >
                Close
              </Button>
            </DialogActions>
          </Dialog>
          <EditTask
                  open={openEditDialog}
                  formData={data}
                  employeesData={employeesData}
                  onCancel={handleCloseEditDialog}
                  fetchUsers={fetchUsers}
                />
    </>
  );
};
export default ViewTask;
