import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import {
  <PERSON>ton,
    Card,
    CardContent, Dialog, DialogActions, DialogContent, DialogTitle, Divider, FormControl, Grid, IconButton, InputAdornment, TextField, Tooltip,
    Typography
} from "@mui/material";
import Icon from "src/@core/components/icon";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Controller, useForm } from "react-hook-form";

import { Box } from "@mui/system";
import SearchIcon from "@mui/icons-material/Search";

import { AuthContext } from "src/context/AuthContext";
import Index from "../conversations";


const CallConversations = () => {
  const { can, rbacRoles } = useRBAC();
  const { user,listValues } = useContext(AuthContext);
  
 const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [employeeData, setEmployeesData] = useState([]);

  // Define columns
  const columns = [
    {
      field: "name",
      minWidth: 120, 
      headerName: "Name",
      flex: 0.18, // Adjust the flex as needed
      valueGetter: (params) => {
        const { firstName, lastName } = params.row;
        return `${firstName} ${lastName}`;
      },
    },
    { field: "email", minWidth: 115, headerName: "Email", flex: 0.2 },
    { field: "mobileNumber", minWidth: 125, headerName: "Mobile No", flex: 0.14,valueGetter: (params) => params.row.basicProfile.mobileNumber, },
    {
      flex: 0.15,
      minWidth: 120,
      field: "assignedTo",
      headerName: "Assigned To",
      renderCell: (params) => {
        const assignedTo = employeeData.find(item => item.id === params.row.basicProfile.assignedTo);
        return <span>{assignedTo ? assignedTo.name : ""}</span>;
      }
    },
    {
      flex: 0.13,
      minWidth: 120,
      field: "leadStatus",
      headerName: "Lead Status",
      renderCell: (params) => {
        const leadStatus = listValues?.find(item => item.id === params.row.basicProfile.leadStatus);
        return <span>{leadStatus ? leadStatus.name : ""}</span>;
      }
    },
    {
      flex: 0.25,
      minWidth: 115,
      field: "remarks",
      headerName: "Remarks",
      valueGetter: (params) => params.row.basicProfile.remarks,
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
        //   openEditDialog(params.row);
        openConversationDialog(params.row);
          const row = params.row;
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow,setCurrentRow] = useState("")
  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);

  // Define getRowId function to extract userId as the row id
  const getRowId = (row) => row.userId;

  const openConversationDialog = (row) => {
    setConversationDialogOpen(true);
    setCurrentRow(row);
  };

  const closeConversationDialog = () => {
    setConversationDialogOpen(false);
    fetchUsers();
  };

 

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

    const url = getUrl(authConfig.getAllConversations);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      serviceProvidersLastSevenDays : false
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
        
      });

      if (response.data) {
        setUserList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };
if(can('conversations_READ')){
  return (
    <>
        <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >

          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "row", // Change to row for horizontal alignment
              alignItems: "center",
              justifyContent: "space-between", // Changed to evenly distribute space
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
           <div style={{ flex: 1, textAlign: "center" }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                sx={{ fontSize: "18px" }}
              >
                Conversation Details
              </Typography>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <CloseExpandIcons
                expanded={expanded}
                onToggle={handleToggle}
                sx={{ mt: 4 }}
              />
              <IconButton
                size="small"
                onClick={closeConversationDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mt: 3,
                  mb: 3,
                  mr: 2,
                  color:"common.white", 
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </div>
          </DialogTitle>
          
          <DialogContent>
            {employeeData && 
            <>
              <Index currentRow={currentRow} setCurrentRow={setCurrentRow} closeConversationDialog={close} employeeData={employeeData}/>
            </>
            }
            
          </DialogContent>
        </Dialog>
      <Card>

            <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={12}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                 <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6">List of Conversations</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm={8} md={6} lg={5}>
                      <FormControl fullWidth>
                        <Controller
                          name="mainSearch"
                          control={control}
                          // defaultValue={name}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search by name and email"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setSearchKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                  fetchUsers(page, pageSize, searchKeyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                        fetchUsers(
                                          page,
                                          pageSize,
                                          searchKeyword
                                        );
                                      }}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>   
                    <Grid item xs={12} sm="auto" md="auto" lg="auto">
                        <Button variant="contained"  sx={{ textTransform: 'none'}} >
                        + Task
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm="auto" md="auto" lg="auto">
                        <Button variant="contained"  sx={{ textTransform: 'none'}} >
                        + Event
                        </Button>
                      </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />

        <CardContent>
          <Box style={{ height: "100%", width: "100%" }}>
            <DataGrid
              rows={userList || []}
              columns={columns}
              autoHeight
              checkboxSelection
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={userList.length > 0 ? rowsPerPageOptions : []}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              getRowId={getRowId} // Specify getRowId function
              rowHeight={38}
              headerHeight={38} 
            />
          </Box>
        </CardContent>
      </Card>
    </>
  );}
  else{
    return null;
  }
};

export default CallConversations;
