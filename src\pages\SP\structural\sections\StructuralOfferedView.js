// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "../../MUITableCell";
import StructuralServicesOffered from "./StructuralServicesOffered";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServicesOfferedView = ({ data, expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
     {/* {can('structuralEngineer_structuralServices_READ') && */}
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Structural Services Offered"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                //onClick={can('structuralEngineer_structuralServices_UPDATE') ? viewClick3 : null}
                onClick={ viewClick3}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography
                          sx={{  fontWeight: 600 }}
                        >
                          Service Name
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          (Yes/No):
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow >
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }} >
                        RCC Design, Planning & Inspection
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.rccDesignPlanning
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Structural Audit
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}></Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Verification of load conditions
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.loadConditionVerification ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Evaluation of the structural system of the building
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.buildingStructuralSystemEvaluation
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Detection of structural defects, damages, distress,
                          deformation or deterioration
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.structuralDefectsDetection ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Plan and alignment check
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.planAndAlignmentCheck ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Assessing maintenance and exposure to aggressive
                          environment
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.maintenanceAssessment ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Other structural surveys and checks
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.otherStructuralSurveys ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Fire safety check
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.fireSafetyCheck ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Electrical safety check
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.electricalSafetyCheck ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Structural Repairs
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.structuralRepairs ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell >
                        <Typography sx={{ fontWeight: 600 }}>Major Repairs</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.majorRepairs ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <StructuralServicesOffered
                formData={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
      {/* } */}
    </>
  );
};
export default ServicesOfferedView;
