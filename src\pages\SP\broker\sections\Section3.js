// ** React Imports

import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Box } from "@mui/system";
import { Divider } from "@mui/material";

// ** Third Party Imports
import { useForm, Controller } from "react-hook-form";
import { FormControlLabel, Switch, Typography} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const Section3 = ({ onCancel, setter, defaultData }) => {
  const { updateEntityServices } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  const [formData, setFormData] = useState(defaultData);

  const [
    top10MicroMarketsWorkingSupported,
    setTop10MicroMarketsWorkingSupported,
  ] = useState(defaultData.top10MicroMarketsWorking.isSupported);

  const [residentialSupported, setResidentialSupported] = useState(
    defaultData.residential.isSupported
  );

  const [commercialSupported, setCommercialSupported] = useState(
    defaultData.commercial.isSupported
  );

  const [retailSupported, setRetailSupported] = useState(
    defaultData.retail.isSupported
  );

  const [investorSupported, setInvestorSupported] = useState(
    defaultData.investor.isSupported
  );

  const [
    top10MicroMarketsWorkingPriceSupported,
    setTop10MicroMarketsWorkingPriceSupported,
  ] = useState(defaultData.top10MicroMarketsWorking.price);

  const [residentialPriceSupported, setResidentialPriceSupported] = useState(
    defaultData.residential.price
  );
 
  const [commercialPriceSupported, setCommercialPriceSupported] = useState(
    defaultData.commercial.price
  );
 
  const [retailPriceSupported, setRetailPriceSupported] = useState(
    defaultData.retail.price
  );
 
  const [investorPriceSupported, setInvestorPriceSupported] = useState(
    defaultData.investor.price
  );

  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (event) => {
    const { checked } = event.target;

    setTop10MicroMarketsWorkingSupported(checked);
    setResidentialSupported(checked);
    setCommercialSupported(checked);
    setRetailSupported(checked);
    setInvestorSupported(checked);

    setSelectAll(checked);

    setFormData(prev => ({
        ...prev,
        societyCoordinationThroughoutProject: { ...prev.societyCoordinationThroughoutProject, isSupported: checked },
        top10MicroMarketsWorking: { ...prev.top10MicroMarketsWorking, isSupported: checked },
        residential: { ...prev.residential, isSupported: checked },
        commercial: { ...prev.commercial, isSupported: checked },
        retail: { ...prev.retail, isSupported: checked },
        investor: { ...prev.investor, isSupported: checked }
    }));

};

  useEffect(() => {
    if (
        top10MicroMarketsWorkingSupported &&
        residentialSupported &&
        commercialSupported &&
        retailSupported &&
        investorSupported
    ) {
        setSelectAll(true);
    } else {
        setSelectAll(false);
    }
}, [
    top10MicroMarketsWorkingSupported,
    residentialSupported,
    commercialSupported,
    retailSupported,
    investorSupported
]);


  const handleOnChange = (event) => {
    const { name, checked } = event.target;
    switch (name) {
      case "top10MicroMarketsWorking":
        setFormData({
          ...formData,
          top10MicroMarketsWorking: {
            ...formData.top10MicroMarketsWorking,
            isSupported: checked,
          },
        });
        break;
      case "residential":
        setFormData({
          ...formData,
          residential: { ...formData.residential, isSupported: checked },
        });
        break;
      case "commercial":
        setFormData({
          ...formData,
          commercial: { ...formData.commercial, isSupported: checked },
        });
        break;
      case "retail":
        setFormData({
          ...formData,
          retail: { ...formData.retail, isSupported: checked },
        });
        break;
      case "investor":
        setFormData({
          ...formData,
          investor: { ...formData.investor, isSupported: checked },
        });
        break;
    }
  };

  const handlePrices = (event) => {
    const { name, value } = event.target;

    switch (name) {
      case "marketPrice":
        setFormData({
          ...formData,
          top10MicroMarketsWorking: {
            ...formData.top10MicroMarketsWorking,
            price: value,
          },
        });
        break;
      case "residentialPrice":
        setFormData({
          ...formData,
          residential: { ...formData.residential, price: value },
        });
        break;
      case "commercialPrice":
        setFormData({
          ...formData,
          commercial: { ...formData.commercial, price: value },
        });
        break;
      case "retailPrice":
        setFormData({
          ...formData,
          retail: { ...formData.retail, price: value },
        });
        break;
      case "investorPrice":
        setFormData({
          ...formData,
          investor: { ...formData.investor, price: value },
        });
        break;
    }
  };

  async function onSubmit() {
    const response = await updateEntityServices({ services: formData }, () => {
      console.error("serviceDetails failed");
    });

    if (response) {
      setFormData(response);
      setter(response);
    }
    onCancel();
  }

  return (
    <Box sx={{ pt: 2 }}>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>

        <Grid item xs={4} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Top 10 Micro-markets working:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="top10MicroMarketsWorking"
            control={control}
            defaultValue={formData.top10MicroMarketsWorking.isSupported}
            render={() => (
              // <FormControlLabel control={<Switch {...field} onChange={handleOnChange}/>} />
              <FormControlLabel
                control={
                  <Switch
                    checked={top10MicroMarketsWorkingSupported}
                    onChange={(event) => {
                      setTop10MicroMarketsWorkingSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="top10MicroMarketsWorking"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Residential:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="residential"
            control={control}
            defaultValue={formData.residential.isSupported}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={residentialSupported}
                    onChange={(event) => {
                      setResidentialSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="residential"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Commercial:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="commercial"
            control={control}
            defaultValue={formData.commercial.isSupported}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    checked={commercialSupported}
                    onChange={(event) => {
                      setCommercialSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="commercial"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Retail:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="retail"
            control={control}
            defaultValue={formData.retail.isSupported}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={retailSupported}
                    onChange={(event) => {
                      setRetailSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="retail"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Investor:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="investor"
            control={control}
            defaultValue={formData.investor.isSupported}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    checked={investorSupported}
                    onChange={(event) => {
                      setInvestorSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="investor"
                  />
                }
              />
            )}
          />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={onSubmit}
              variant="contained"
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section3;
