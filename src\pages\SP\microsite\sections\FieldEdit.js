// ** React Imports
import { useEffect, useState, useContext } from "react";
import { useMediaQuery, useTheme } from "@mui/material";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import { Box, Button, FormControl, TextField } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";

import { useAuth } from "src/hooks/useAuth";

const FieldsEdit = ({ onCancel, formData, userData }) => {
  const auth = useAuth();
  const { user } = useContext(AuthContext);
  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  const [fieldChanged, setFieldChanged] = useState(null);
  useEffect(() => {
    const youtubeUrl = getValues("youtubeUrl");
    const briefProfile = getValues("briefProfile");
    if (youtubeUrl !== formData?.youtubeUrl) {
      setFieldChanged(true);
    } else {
      setFieldChanged(false);
    }
    if (briefProfile !== formData?.briefProfile) {
      setFieldChanged(true);
    } else {
      setFieldChanged(false);
    }
  }, [getValues, formData]);

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );

    try {
      const userUniqueId =
        userData && userData.id !== undefined ? userData.id : user.id;
      const response = await auth.micrositeLevel1Patch(
        trimmedData,
        userUniqueId
      );
      onCancel();
    } catch (error) {
      console.error("Error while submitting introduction fields:", error);
    }
  }
  const theme = useTheme(); // Access current theme
  const isExtraSmallScreen = useMediaQuery("(max-width:284px)");

  return (
    <>
<Box sx={{ pt: 1.5 }}>
  <Grid container spacing={5}>
  {(userData && userData.id !== undefined) &&
    <Grid item xs={12} lg={7} marginTop={3}>
      <FormControl fullWidth>
        <Controller
          name="youtubeUrl"
          control={control}
          rules={{
            pattern: {
              value: /^https:\/\/www\.youtube\.com\/watch\?v=[\w-]+$/,
              message: "Enter a valid YouTube URL (e.g., https://www.youtube.com/watch?v=VIDEO_ID)",
            },
          }}
          defaultValue={formData?.youtubeUrl}
          render={({ field }) => (
            <TextField
              {...field}
              label="YouTube URL"
              variant="outlined"
              size="small"
              fullWidth
              placeholder="Copy & Paste valid URL(https://www.youtube.com/watch?v=VIDEO_ID)"
              error={Boolean(errors.youtubeUrl)}
              helperText={errors.youtubeUrl?.message}
              aria-describedby="validation-youtube-url"
              onChange={(e) => {
                field.onChange(e);
                setFieldChanged(true);
              }}
            />
          )}
        />
      </FormControl>
    </Grid>
}
    <Grid item xs={12} lg={7} marginTop={3}>
      <FormControl fullWidth>
        <Controller
          name="briefProfile"
          control={control}
          defaultValue={formData?.briefProfile}
          render={({ field }) => (
            <TextField
              {...field}
              label="Brief Profile"
              variant="outlined"
              size="small"
              fullWidth
              multiline
              rows={6}
              error={Boolean(errors.briefProfile)}
              helperText={errors.briefProfile?.message}
              aria-describedby="validation-briefProfile"
              onChange={(e) => {
                field.onChange(e);
                setFieldChanged(true);
              }}
            />
          )}
        />
      </FormControl>
    </Grid>

    <Grid item xs={12} marginTop={1}>
      <center>
        <Button
          size="medium"
          sx={{ mr: 3 }}
          variant="outlined"
          color="primary"
          onClick={() => onCancel()}
        >
          Cancel
        </Button>
        <Button
          size="medium"
          type="button"
          variant="contained"
          sx={{marginTop : isExtraSmallScreen ? '1rem' : ''}}
          disabled={!fieldChanged}
          onClick={handleSubmit(submit)}
        >
          Save
        </Button>
      </center>
    </Grid>
  </Grid>
</Box>
    </>
  );
};

export default FieldsEdit;
