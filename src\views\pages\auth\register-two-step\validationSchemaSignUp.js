import * as yup from 'yup';


const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

const validationSchemaSignUp = yup.object().shape({
    

  firstName: yup.string()
  .required('Name is required')
  .test(
    'no-double-space',
    'Double space is not accepted',
    (value) => !value || !/\s\s/.test(value)
  )
    .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, 'Invalid Name')
    .max(50, 'Name must not exceed 50 characters'),
    

    email: yup.string().required('Email is required').matches(emailRegex, 'Invalid email format'),
  
  
});
export default validationSchemaSignUp;