import React, { useState, useEffect } from 'react';
import { Box, Popper } from '@mui/material';
import NotificationIcon from "src/@core/components/custom-components/NotificationIcon";
import NotificationDropdown from "src/@core/components/custom-components/NotificationDropdown";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { styled } from '@mui/material/styles';

const IconContainer = styled(Box)(({ theme }) => ({
  height: '100%', // Ensure the container fills the height of its parent
  width: '100%', // Ensure the container fills the width of its parent
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '&:hover, &:active': {
    // Adjust these styles as needed for the hover and active states
    backgroundColor: theme.palette.action.hover,
    // For example, to make the icon itself scale, you might adjust its font size or transform scale here.
    '& .icon': { // Assuming .icon can be targeted within <NotificationIcon>
      transform: 'scale(1.1)', // Example: scale up the icon
    },
  },
}));

const Notification = () => {
  const [notifications, setNotifications] = useState([]);

  const fetchNotifications = () => {
    axios({
      method: "post",
      url: getUrl(authConfig.notificationEndpoint) + "/all",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setNotifications(res.data?.listOfNotifications);
      })
      .catch((err) => console.log("Notifications error", err));
  };
  
  useEffect(() => {
    fetchNotifications();
  }, []);


  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const [openDrawer,setOpenDrawer] = useState(false);

  const [notificationContent,setNotificationContent] = useState("")

  const [createdOnDate,setCreatedOnDate] = useState("")

  const unreadNotifications = notifications.filter(n => !n.isRead).length;

  const handleToggle = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDrawerClose = () => {
    setOpenDrawer(false);
  };

  
  const markAsRead = (id,content,createdOn) => {
    // setOpenDrawer(true)
    // setNotificationContent(content)
    // setCreatedOnDate(createdOn)
    const notificationToMarkAsRead = notifications.find((notification) => notification.id === id);
  
    if (notificationToMarkAsRead && !notificationToMarkAsRead.isRead) {
      axios({
        method: "patch",
        url: getUrl(authConfig.notificationEndpoint) + `/${id}`,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          if (res.status === 200) {
            
            
            setNotifications((prevNotifications) =>
              prevNotifications.map((notification) =>
                notification.id === id ? { ...notification, isRead: true } : notification
              )
            );
          }
        })
        .catch((err) => console.log("Mark as Read error", err));
    } else {
      console.log("Notification is already marked as read ");
    }
  };
       

  const deleteNotification = (id) => {
    axios({
      method: "DELETE",
      url: getUrl(authConfig.notificationEndpoint) + `/${id}`,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        
        setNotifications((prevNotifications) =>
          prevNotifications.filter((notification) => notification.id !== id)
        );
        fetchNotifications();
      })
      .catch((err) => console.log("Delete Notification error", err));
  };

  return (
    <>
      <header>
      <IconContainer>
      <NotificationIcon unreadCount={unreadNotifications} onClick={handleToggle} />
    </IconContainer>
      </header>

      <Popper open={open} anchorEl={anchorEl} placement="bottom-start">
        <NotificationDropdown
          open={open}
          openDrawer={openDrawer}
          notifications={notifications}     
          setNotifications={setNotifications}
          fetchNotifications={fetchNotifications}
          notificationContent={notificationContent}
          createdOnDate={createdOnDate}
          markAsRead={markAsRead}
          deleteNotification={deleteNotification}
          closeDropdown={handleClose}
          handleDrawerClose={handleDrawerClose}
        />
      </Popper>
    </>
  );
};

export default Notification;
