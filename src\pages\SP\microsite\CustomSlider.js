

import Slider from 'react-slick';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import useMediaQuery from '@mui/material/useMediaQuery';
import { Box, Card, Typography } from '@mui/material';

const getFileType = (fileName) => {
  return fileName?.split('.').pop().toLowerCase();
};

const CustomSlider = ({ slides, data, projectNames, height, slidesToShow }) => {
  const hidden = useMediaQuery((theme) => theme.breakpoints.down('md'));

  // let slidesToShow = 2;

  if (data?.length < 2) {

    slidesToShow = data?.length;
  }

  const sliderSettings = {

    infinite: true,
    dots: true,
    speed: 1300,
    slidesToShow: hidden ? 1 : slidesToShow,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3500,
  };

  const defaultImage = '/images/houzer/login.png'; // Update this path to your default image

  return (
    <>
      {data && data.length > 0 ? (
        <Slider {...sliderSettings}>
          {data.map((item, index) => {
            const fileType = item.fileName ? getFileType(item.fileName) : getFileType(item.filePath);
            const backgroundImageUrl = `url(data:image/${fileType};base64,${item.fileData})`;

            return (
              <div key={index}>
                <Card
                  sx={{
                    border: 'none',
                    display: 'flex',
                    alignItems: 'self-end',
                    justifyContent: 'center',
                    p: 2,
                    height: "385px",
                    width: '100%',
                    backgroundImage: backgroundImageUrl,
                    backgroundPosition: 'top',
                    backgroundSize: 'cover',
                  }}
                >
                  {/* <Typography
                    variant="h4"
                    color="primary.dark" align='center'
                    sx={{
                      borderRadius: 1,
                      minWidth: 200,
                      padding: (6, 3, 6, 3),
                      background: 'rgba(0 , 0, 0, 0.6)',
                    }}
                  >
                    {item.fileName}
                  </Typography> */}
                </Card>
              </div>
            );
          })}
        </Slider>
      ) : (
        <Card
          sx={{
            border: 'none',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end', // Align items at the bottom
            alignItems: 'center',
            p: 2,
            height: "385px",
            width: '100%',
            backgroundImage: `url(${defaultImage})`,
            backgroundPosition: 'center',
            backgroundSize: 'cover',
          }}
        >
          {/* <Typography
            variant="body2"
            color="primary.dark" align='center'
            sx={{
              borderRadius: 1,
              minWidth: 200,
              padding: (6, 3, 6, 3),
              background: 'rgba(0 , 0, 0, 0.6)',
            }}
          >
            No Image Available
          </Typography> */}
        </Card>
      )}
    </>
  );
};

export default CustomSlider;
