/**
 * Validation utility functions and rules for form validation
 */

/**
 * Email validation regex pattern
 */
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

/**
 * Password validation regex patterns
 */
const PASSWORD_PATTERNS = {
  minLength: /.{8,}/,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /\d/,
  hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/,
};

/**
 * Validation rules for react-hook-form
 */
export const validationRules = {
  email: {
    required: 'Email is required',
    pattern: {
      value: EMAIL_REGEX,
      message: 'Please enter a valid email address',
    },
  },

  password: {
    required: 'Password is required',
    minLength: {
      value: 8,
      message: 'Password must be at least 8 characters long',
    },
    validate: {
      hasUppercase: (value) =>
        PASSWORD_PATTERNS.hasUppercase.test(value) ||
        'Password must contain at least one uppercase letter',
      hasLowercase: (value) =>
        PASSWORD_PATTERNS.hasLowercase.test(value) ||
        'Password must contain at least one lowercase letter',
      hasNumber: (value) =>
        PASSWORD_PATTERNS.hasNumber.test(value) ||
        'Password must contain at least one number',
    },
  },

  confirmPassword: (password) => ({
    required: 'Please confirm your password',
    validate: (value) =>
      value === password || 'Passwords do not match',
  }),

  fullName: {
    required: 'Full name is required',
    minLength: {
      value: 2,
      message: 'Full name must be at least 2 characters long',
    },
    maxLength: {
      value: 50,
      message: 'Full name must not exceed 50 characters',
    },
    pattern: {
      value: /^[a-zA-Z\s]+$/,
      message: 'Full name can only contain letters and spaces',
    },
  },

  termsAccepted: {
    required: 'You must accept the terms and conditions',
  },
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
export const isValidEmail = (email) => {
  return EMAIL_REGEX.test(email);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with strength score and feedback
 */
export const validatePasswordStrength = (password) => {
  if (!password) {
    return {
      score: 0,
      strength: 'none',
      feedback: [],
      isValid: false,
    };
  }

  const feedback = [];
  let score = 0;

  // Check minimum length
  if (PASSWORD_PATTERNS.minLength.test(password)) {
    score += 1;
  } else {
    feedback.push('At least 8 characters');
  }

  // Check for uppercase letter
  if (PASSWORD_PATTERNS.hasUppercase.test(password)) {
    score += 1;
  } else {
    feedback.push('One uppercase letter');
  }

  // Check for lowercase letter
  if (PASSWORD_PATTERNS.hasLowercase.test(password)) {
    score += 1;
  } else {
    feedback.push('One lowercase letter');
  }

  // Check for number
  if (PASSWORD_PATTERNS.hasNumber.test(password)) {
    score += 1;
  } else {
    feedback.push('One number');
  }

  // Check for special character (bonus point)
  if (PASSWORD_PATTERNS.hasSpecialChar.test(password)) {
    score += 1;
  }

  // Determine strength level
  let strength = 'weak';
  if (score >= 4) {
    strength = 'strong';
  } else if (score >= 3) {
    strength = 'medium';
  }

  return {
    score,
    strength,
    feedback,
    isValid: score >= 4, // Require at least 4 criteria
  };
};

/**
 * Get password strength color for UI
 * @param {string} strength - Password strength level
 * @returns {string} Color value
 */
export const getPasswordStrengthColor = (strength) => {
  switch (strength) {
    case 'strong':
      return '#4caf50'; // Green
    case 'medium':
      return '#ff9800'; // Orange
    case 'weak':
      return '#f44336'; // Red
    default:
      return '#e0e0e0'; // Gray
  }
};

/**
 * Get password strength percentage for progress bar
 * @param {number} score - Password strength score
 * @returns {number} Percentage value (0-100)
 */
export const getPasswordStrengthPercentage = (score) => {
  return Math.min((score / 4) * 100, 100);
};

/**
 * Validate full name
 * @param {string} fullName - Full name to validate
 * @returns {Object} Validation result
 */
export const validateFullName = (fullName) => {
  if (!fullName || fullName.trim().length === 0) {
    return {
      isValid: false,
      message: 'Full name is required',
    };
  }

  if (fullName.trim().length < 2) {
    return {
      isValid: false,
      message: 'Full name must be at least 2 characters long',
    };
  }

  if (fullName.length > 50) {
    return {
      isValid: false,
      message: 'Full name must not exceed 50 characters',
    };
  }

  if (!/^[a-zA-Z\s]+$/.test(fullName)) {
    return {
      isValid: false,
      message: 'Full name can only contain letters and spaces',
    };
  }

  return {
    isValid: true,
    message: '',
  };
};

/**
 * Sanitize input to prevent XSS attacks
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Format validation error messages for display
 * @param {Object} errors - Form errors object
 * @returns {Array} Array of formatted error messages
 */
export const formatValidationErrors = (errors) => {
  if (!errors || typeof errors !== 'object') return [];
  
  return Object.keys(errors).map(field => ({
    field,
    message: errors[field]?.message || 'Invalid input',
  }));
};

/**
 * Check if form has any validation errors
 * @param {Object} errors - Form errors object
 * @returns {boolean} True if form has errors
 */
export const hasValidationErrors = (errors) => {
  return errors && Object.keys(errors).length > 0;
};

/**
 * Custom validation for terms and conditions
 * @param {boolean} accepted - Whether terms are accepted
 * @returns {boolean|string} True if valid, error message if invalid
 */
export const validateTermsAcceptance = (accepted) => {
  return accepted || 'You must accept the terms and conditions';
};

/**
 * Validate phone number (optional field)
 * @param {string} phoneNumber - Phone number to validate
 * @returns {Object} Validation result
 */
export const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) {
    return { isValid: true, message: '' }; // Optional field
  }

  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  
  if (!phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
    return {
      isValid: false,
      message: 'Please enter a valid phone number',
    };
  }

  return {
    isValid: true,
    message: '',
  };
};

/**
 * Real-time validation helper for form fields
 * @param {string} fieldName - Name of the field being validated
 * @param {any} value - Value to validate
 * @param {Object} additionalData - Additional data for validation (e.g., password for confirm password)
 * @returns {Object} Validation result
 */
export const validateField = (fieldName, value, additionalData = {}) => {
  switch (fieldName) {
    case 'email':
      return {
        isValid: isValidEmail(value),
        message: isValidEmail(value) ? '' : 'Please enter a valid email address',
      };

    case 'password':
      return validatePasswordStrength(value);

    case 'confirmPassword':
      return {
        isValid: value === additionalData.password,
        message: value === additionalData.password ? '' : 'Passwords do not match',
      };

    case 'fullName':
      return validateFullName(value);

    case 'phoneNumber':
      return validatePhoneNumber(value);

    case 'termsAccepted':
      return {
        isValid: !!value,
        message: value ? '' : 'You must accept the terms and conditions',
      };

    default:
      return {
        isValid: true,
        message: '',
      };
  }
};
