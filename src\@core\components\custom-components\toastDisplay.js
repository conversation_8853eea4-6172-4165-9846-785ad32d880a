import React, { useState } from 'react';
import { IconButton } from '@mui/material';
import toast, { Toaster } from "react-hot-toast";
// import 'react-toastify/dist/ReactToastify.css';

const navigateToGoogleMaps = () => {
  const url = 'https://maps.google.com/?authuser=0';
  window.open(url, '_blank');
};



const GoogleMapsIconButton = () => {
  const [toastDisplay, setToastDisplay] = useState(null);

  const handleIconHover = () => {
    if (toastDisplay !== null) {
      toast.dismiss(toastDisplay);
    }

    setToastDisplay(
      toast(
        "Click on the icon to navigate to Google Maps.\n1. Search the location.\n2. Click on 'Share'.\n3. Copy the URL.\n4. Paste it in the field.",
        {
          autoClose: false,
        }
      )
    );
  };

  return (
    <>
      <IconButton sx={{ cursor: 'pointer' }} onClick={navigateToGoogleMaps} onMouseOver={handleIconHover} edge="end">
        <img src="/images/google-maps-logo.webp" alt="Google Maps icon" style={{ width: 24, height: 24 }} />
      </IconButton>
      <Toaster position="top-right" />
      </>
  );
};

export default GoogleMapsIconButton;
