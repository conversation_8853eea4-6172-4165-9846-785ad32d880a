// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow
} from "@mui/material";
import ServicesOfferedEdit from "src/pages/SP/architect/sections/ServicesOfferedEdit";
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServicesOfferedView = ({ data, expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
     {/* {can('architect_servicesOffered_READ') && */}
        <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Architectural Services Offered"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
               // onClick={can('architect_servicesOffered_UPDATE') ? viewClick3 : null}
                onClick={viewClick3}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Service Name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          (Yes/No):
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Design Consultancy
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                         
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    
                     
                   
                    <TableRow>
                      <MUITableCell>
                      <Typography sx={{ marginLeft: "15px" }}>
                          Concept planning, design & feasibility
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.conceptPlanning ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ marginLeft: "15px" }}>
                          Architecture design & execution support
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.architectureDesign ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Liaisoning Consultancy
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                         
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                      <Typography sx={{ marginLeft: "15px" }}>
                      For smooth execution and completion of a project by coordinating between the sanctioning authorities and principal architect and other stake holders
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.forSmoothExecution ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {state3 === "edit" && (
              <ServicesOfferedEdit formData={data} onCancel={editClick3} />
            )}
          </>
        }
        expanded={expanded}
      />
     {/* } */}
      
    </>
  );
};
export default ServicesOfferedView;
