import React, { useState } from 'react';
import Box from "@mui/material/Box";
import CustomAutocomplete from './CustomAutoComplete';
import CustomTextField from './CustomTextField';

const SelectAutoComplete = (props) => {
  const {
    id,
    label,
    nameArray,
    value,
    onChange,
  } = props;

  const [selectedValue, setSelectedValue] = useState(value || null);
  const [isDropdownOpen, setDropdownOpen] = useState(false); // Add state for dropdown open/close

  const customStyles = {
    menu: (provided, state) => ({
      ...provided,
      zIndex: 2,
    }),
  };

  // Handle option selection
  const handleOptionSelect = (event, option) => {
    const selectedValue = option ? option.value : null;
    setSelectedValue(selectedValue);
    console.log("Selected value from dropdown",selectedValue)
    onChange({ target: { value: selectedValue } });
    setDropdownOpen(false); // Close the dropdown after selection
  };

  const handleDropdownOpen = () => {
    setDropdownOpen(true); // Open the dropdown when clicking the input
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false); // Close the dropdown when clicking outside
  };

  return (
    <Box sx={{ position: 'relative', zIndex: 1 }}>
      <CustomAutocomplete
        autoHighlight
        id={id}
        options={nameArray}
        getOptionLabel={option => option.key || ''}
        value={nameArray.find(option => option.value === selectedValue) || null}
        onChange={handleOptionSelect}
        onOpen={handleDropdownOpen} // Open the dropdown on input click
        onClose={handleDropdownClose} // Close the dropdown when clicking outside
        open={isDropdownOpen} // Pass the open state
        renderOption={(props, option) => (
          <Box
            component='li'
            {...props}
            onClick={(event) => handleOptionSelect(event, option)}
          >
            {option && option.key}
          </Box>
        )}
        renderInput={params => (
          <CustomTextField
            {...params}
            size='small'
            placeholder={`${label}`}
            sx={{ borderRadius: 1 }}
            inputProps={{
              ...params.inputProps,
            }}
          />
        )}
      />
    </Box>
  );
};

export default SelectAutoComplete;
