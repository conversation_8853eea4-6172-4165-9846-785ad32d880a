// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import axios from "axios";

import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";

// ** Hooks
// ** Icon Imports
import { Dialog, DialogActions, DialogContent, DialogTitle, FormControlLabel, IconButton, Switch } from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useState,useEffect } from "react";

import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";


const RuleDataEdit = ({ open,onCancel, formData,fetchUsers }) => {


  const [isActive, setIsActive] = useState(formData?.isActive); 

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  //Hooks
  const auth = useAuth();
  
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  async function submit(data) {  
    
    let basicZonalFSI = parseFloat(data.basicZonalFSI);
    let premiumFSI = parseFloat(data.premiumFSI);
    let tdr = parseFloat(data.tdr);

    let max = basicZonalFSI + premiumFSI + tdr;
    let maxFSI = parseFloat(max.toFixed(4)).toString();
    let FungibleFSI = max * 1.35;
    FungibleFSI = parseFloat(FungibleFSI.toFixed(4));
    let maxFungibleFSI = FungibleFSI.toString();
    const fields = {
      id:formData.id,
      location:selectedLocation?.name,
      ward:selectedWard?.name,
      type:selectedType?.name,
      city:selectedCityType?.name,
      minRoadWidth: data.minRoadWidth,
      maxRoadWidth: data.maxRoadWidth,
      basicZonalFSI: data.basicZonalFSI,
      premiumFSI: data.premiumFSI,
      tdr: data.tdr,
      maxFSI: maxFSI,
      fungibleFSI : maxFungibleFSI,
      isActive:isActive,
    };


    const response = await auth.patchFSIRule(fields, () => {
      console.error(" FSI Rule Data Details failed");
    });

    const currentPage = 1;  
    const currentPageSize = 10;

    fetchUsers(currentPage, currentPageSize);
   
    onCancel();
  }

  const [selectedWard, setSelectedWard] = useState({
    name: formData?.ward || "",
    id: formData?.id || "",
  });
  const [listOfWards, setListOfWards] = useState([]);

  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();
  }, []);

  const handleWardChange = (newValue) => {
    setSelectedWard(newValue);
  };

  const [selectedLocation, setSelectedLocation] = useState({
    name: formData?.location || "",
    id: formData?.id || "",
  });
  const [listOfLocations, setListOfLocations] = useState([]);

  useEffect(() => {
    const fetchLocations = async () => {
      const data = {
        masterDataType: "LOCATION_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfLocations(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchLocations();
  }, []);

  const handleLocationChange = (newValue) => {
    setSelectedLocation(newValue);
  };

  const [selectedType, setSelectedType] = useState({
    name: formData?.type || "",
    id: formData?.id || "",
  });
  const [listOfTypes, setListOfTypes] = useState([]);

  useEffect(() => {
    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();
  }, []);

  const handleTypeChange = (newValue) => {
    setSelectedType(newValue);
  };

  const [selectedCityType, setSelectedCityType] = useState({
    name: formData?.city || "",
    id: formData?.id || "",
  });
  const [listOfCityTypes, setListOfCityTypes] = useState([]);

  useEffect(() => {
    const fetchCityTypes = async () => {
      const data = {
        masterDataType: "CITY_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfCityTypes(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchCityTypes();
  }, []);

  const handleCityTypeChange = (newValue) => {
    setSelectedCityType(newValue);
  };
  


  return (
    <>
    <Dialog open={open} onClose={onCancel} fullWidth maxWidth="lg" scroll="paper">
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 19, md: 20 },
              }}
              textAlign={"center"}
            >
              Edit FSI Rule
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={onCancel}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >

        <Grid container spacing={5}>
        
        
        <Grid item xs={12} sm={6}>
                    <CustomAutocomplete
                      autoHighlight
                      id="autocomplete-ward-select"
                      label="Ward"
                      options={listOfWards}
                      getOptionLabel={(option) => option.name || ""}
                      value={selectedWard}
                      getOptionSelected={(option, value) =>
                        option.id === value.id
                      }
                      defaultValue={{
                        name: formData?.ward || "",
                        id: formData?.id || "",
                      }}
                      onChange={(event, newValue) => handleWardChange(newValue)}
                      renderInput={(params) => (
                        <CustomTextField {...params} placeholder="Choose Your Ward" />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <CustomAutocomplete
                      autoHighlight
                      id="autocomplete-location-select"
                      label="Location"
                      options={listOfLocations}
                      getOptionLabel={(option) => option.name || ""}
                      value={selectedLocation}
                      getOptionSelected={(option, value) =>
                        option.id === value.id
                      }
                      defaultValue={{
                        name: formData?.location || "",
                        id: formData?.id || "",
                      }}
                      onChange={(event, newValue) =>
                        handleLocationChange(newValue)
                      }
                      renderInput={(params) => (
                        <CustomTextField
                          {...params}
                          placeholder="Select Location"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <CustomAutocomplete
                      autoHighlight
                      id="autocomplete-type-select"
                      label="Type"
                      options={listOfTypes}
                      getOptionLabel={(option) => option.name || ""}
                      value={selectedType}
                      getOptionSelected={(option, value) =>
                        option.id === value.id
                      }
                      defaultValue={{
                        name: formData?.type || "",
                        id: formData?.id || "",
                      }}
                      onChange={(event, newValue) => handleTypeChange(newValue)}
                      renderInput={(params) => (
                        <CustomTextField {...params} placeholder="Select Type" />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <CustomAutocomplete
                      autoHighlight
                      id="autocomplete-cityType-select"
                      label="City Type"
                      options={listOfCityTypes}
                      getOptionLabel={(option) => option.name || ""}
                      value={selectedCityType}
                      getOptionSelected={(option, value) =>
                        option.id === value.id
                      }
                      defaultValue={{
                        name: formData?.city || "",
                        id: formData?.id || "",
                      }}
                      onChange={(event, newValue) =>
                        handleCityTypeChange(newValue)
                      }
                      renderInput={(params) => (
                        <CustomTextField
                          {...params}
                          placeholder="Select City Type"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="minRoadWidth"
                        control={control}
                        rules={{
                          required: true,
                        }}
                        defaultValue={formData?.minRoadWidth}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="number"
                            size="small"
                            value={value}
                            label="Min Road Width"
                            InputLabelProps={{ shrink: true }}
                            onChange={onChange}
                            error={Boolean(errors.minRoadWidth)}
                            placeholder="Enter min road width"
                            aria-describedby="validation-minRoadWidth"
                          />
                        )}
                      />
                      {errors.minRoadWidth?.type === "required" && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-minRoadWidth"
                        >
                          Minimum road width is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="maxRoadWidth"
                        control={control}
                        rules={{
                          required: true,
                        }}
                        
                        defaultValue={formData?.maxRoadWidth}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="number"
                            size="small"
                            value={value}
                            label="Max Road Width"
                            InputLabelProps={{ shrink: true }}
                            onChange={onChange}
                            error={Boolean(errors.maxRoadWidth)}
                            placeholder="Enter max road width"
                            aria-describedby="validation-maxRoadWidth"
                          />
                        )}
                      />
                      {errors.maxRoadWidth?.type === "required" && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-maxRoadWidth"
                        >
                          Maximum road width is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="basicZonalFSI"
                        control={control}
                        rules={{
                          required: true,
                        }}
                        defaultValue={formData?.basicZonalFSI}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="number"
                            size="small"
                            value={value}
                            label="Basic/Zonal FSI"
                            InputLabelProps={{ shrink: true }}
                            onChange={onChange}
                            error={Boolean(errors.basicZonalFSI)}
                            placeholder="Enter basic/zonal FSI"
                            aria-describedby="validation-basicZonalFSI"
                          />
                        )}
                      />
                      {errors.basicZonalFSI?.type === "required" && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-basicZonalFSI"
                        >
                          Basic/Zonal FSI is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="premiumFSI"
                        control={control}
                        rules={{
                          required: true,
                        }}
                        defaultValue={formData?.premiumFSI}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="number"
                            size="small"
                            value={value}
                            label="Premium FSI"
                            InputLabelProps={{ shrink: true }}
                            onChange={onChange}
                            error={Boolean(errors.premiumFSI)}
                            placeholder="Enter premium FSI"
                            aria-describedby="validation-premiumFSI"
                          />
                        )}
                      />
                      {errors.premiumFSI?.type === "required" && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-premiumFSI"
                        >
                          Premium FSI is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="tdr"
                        control={control}
                        rules={{
                          required: true,
                        }}
                        defaultValue={formData?.tdr}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="number"
                            size="small"
                            value={value}
                            label="TDR"
                            InputLabelProps={{ shrink: true }}
                            onChange={onChange}
                            error={Boolean(errors.tdr)}
                            placeholder="Enter tdr"
                            aria-describedby="validation-tdr"
                          />
                        )}
                      />
                      {errors.tdr?.type === "required" && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-tdr"
                        >
                          TDR is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
  
          <Grid item xs={12} sm={12} sx={{ mb: 1.5 }}>
        <Controller
          name="isActive" 
          control={control}
          render={() => (
            <FormControlLabel
              control={
                <Switch
                  checked={isActive} 
                  onChange={handleOnChange}
                  name="isActive" 
                />
              }
              label="Is Active"
            />
          )}
        />
      </Grid>
  
        </Grid>
              
              
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            > 
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="submit" 
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>

        
    
    </>
    
     
 
  );
};

export default RuleDataEdit;
