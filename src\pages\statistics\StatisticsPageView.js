import Typography from "@mui/material/Typography";
import { useState, useContext, useEffect } from "react";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useTheme } from "@emotion/react";
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "src/pages/SP/MUITableCell";
import StatisticsPage from "./StatisticsPage";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const StatisticsPageView = ({
  data,
  expanded,
  getData,
  userDataAllProfile,
  serviceId,
  statisticsData,
}) => {
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };


  const [finalUser, setFinalUser] = useState({});
  useEffect(() => {
    if (statisticsData && statisticsData.length > 0) {
      // Find the user whose serviceNameId matches the serviceId
      const userWithMatchingService = statisticsData.find(
        (user) => user.serviceNameId === serviceId
      );
      if (userWithMatchingService) {
        // If a user is found, set the user data in the state variable
        setFinalUser(userWithMatchingService);
      }
    }
  }, [statisticsData, serviceId]);

  const getListName = (listNameId) => {
    const foundItem = data.find((item) => item.id === listNameId);
    return foundItem ? foundItem.name : "";
  };

  const otherValue = (listNameId) => {
    const foundItem = finalUser?.metadata?.listNames?.find(
      (item) => item.listNameId === listNameId
    );
    return foundItem ? foundItem.otherValue : "";
  };

  return (
    <>
    {data?.length > 0 ? (
      <AccordionBasic
      id={"panel-header-1"}
      ariaControls={"panel-content-1"}
      heading={"Statistics"}
      body={
        <>
          {state3 === "view" && (
            <TableContainer
              sx={{ padding: "4px 6px" }}
              className="tableBody"
              onClick={viewClick3}
            >
              <Table>
                <TableBody
                  sx={{
                    "& .MuiTableCell-root": {
                      p: `${theme.spacing(1.35, 1.125)} !important`,
                    },
                  }}
                >
                  {data?.map((statistic, index) => (
                   statistic?.isActive && (
                   <TableRow key={index}>
                      <MUITableCell>
                        <Typography   sx={{ fontWeight: 400, whiteSpace:'nowrap' }}>
                          {getListName(statistic.id)}:{" "}
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {otherValue(statistic.id)}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                   )
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {state3 === "edit" && (
            <StatisticsPage
              data={data}
              finalUser={finalUser}
              serviceId={serviceId}
              userDataAllProfile={userDataAllProfile}
              onCancel={editClick3}
              getData={getData}
            />
          )}
        </>
      }
      expanded={expanded}
    />
    ):(
      <Typography>Statistics - No Questions Found</Typography>
    )}
      
    </>
  );
};

export default StatisticsPageView;
