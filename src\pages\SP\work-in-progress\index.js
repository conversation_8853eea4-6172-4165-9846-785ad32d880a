import { Box, Typography } from '@mui/material'
import React from 'react'
import { styled } from '@mui/material/styles'
import FooterIllustrations from 'src/views/pages/misc/FooterIllustrations'

const BoxWrapper = styled(Box)(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
      width: '90vw'
    }
  }))
  

function WorkInProgress() {
  return (
    <div>
       <Box className='content-center'>
        <Box sx={{ p: 5, display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
          <BoxWrapper>
          <Typography sx={{ mb: 6, color: 'text.secondary',fontSize: '2.5rem' }}>
              Work in progress
            </Typography>
          </BoxWrapper>
         
        </Box>
        <FooterIllustrations />
      </Box>
    </div>
  )
}

export default WorkInProgress
