import React, { useState } from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  DialogContentText,
  CircularProgress,
  Box,
  Snackbar,
  Alert,
} from "@mui/material";

import { getAuthorizationHeaders } from "src/helpers/utils";

const UpdateServiceDialog = ({ open, onClose, data }) => {
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  async function onUpdate(data) {
    setLoading(true);
    try {
      await axios.patch(
        authConfig.baseURL + authConfig.listValueUpdate + "/" + data.id,
        {
          headers: getAuthorizationHeaders(),
        }
      );
      setSuccessMessage(`Service ${data.name} activated successfully.`);
    } catch (error) {
      console.error("Active operation failed", error);
    } finally {
      setLoading(false);
      onClose();
    }
  }

  const handleCloseSnackbar = () => {
    setSuccessMessage("");
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              Are you sure you want to Activate {data?.name} ?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => onUpdate(data)}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : "Yes"}
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={!!successMessage}
        onClose={handleCloseSnackbar}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              {successMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseSnackbar}
              sx={{ margin: "auto", width: 100 }}
              autoFocus
            >
              OK
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default UpdateServiceDialog;
