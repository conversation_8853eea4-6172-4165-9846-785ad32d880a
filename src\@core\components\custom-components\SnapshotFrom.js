import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
} from "@mui/material";
import {
  Close as CloseIcon,
  CropSquareOutlined,
  MinimizeSharp,
} from "@mui/icons-material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import mammoth from "mammoth";

function SnapshotInput({ selectedFiles, setSelectedFiles }) {
  const [fileError, setFileError] = useState("");
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const [docxContent, setDocxContent] = useState("");
  const [pdfUrl, setPdfUrl] = useState(null);
  const [expanded, setExpanded] = useState(false);
  const fileInputRef = useRef(null);

  useEffect(() => {
    const preventDefaultDragDrop = (event) => {
      event.preventDefault();
    };
    document.addEventListener("dragover", preventDefaultDragDrop);
    document.addEventListener("drop", preventDefaultDragDrop);
    return () => {
      document.removeEventListener("dragover", preventDefaultDragDrop);
      document.removeEventListener("drop", preventDefaultDragDrop);
    };
  }, []);

  const removeFile = (index) => {
    setSelectedFiles((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const isValidFileType = (file) => {
    const validTypes = [
      ".pdf",
      ".doc",
      ".docx",
      ".jpg",
      ".jpeg",
      ".gif",
      ".bmp",
      ".png",
    ];
    const fileType = "." + file.name.split(".").pop();
    return validTypes.includes(fileType);
  };

  const isValidFileSize = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5 MB in bytes
    return file.size <= maxSize;
  };

  const validateFileName = (fileName) => {
    return fileName.length <= 100;
  };

  const handleFileChange = (event) => {
    const fileInput = event.target;
    const files = Array.from(fileInput.files);
    fileInput.value = "";

    let fileErrors = [];
    const validFiles = [];

    files.forEach((file) => {
      let fileError = "";

      if (!isValidFileType(file)) {
        fileError = "Invalid file type.";
      } else if (!isValidFileSize(file)) {
        fileError = "File size exceeds the 5 MB limit.";
      } else if (!validateFileName(file.name)) {
        fileError = "File name length exceeds the 100 characters limit.";
      }

      if (fileError) {
        fileErrors.push({ fileName: file.name, error: fileError });
      } else {
        validFiles.push(file);
      }
    });

    if (validFiles.length > 0) {
      setSelectedFiles((prevFiles) => [...prevFiles, ...validFiles]);
    }

    if (fileErrors.length > 0) {
      setFileError(fileErrors.map((error) => `${error.fileName}: ${error.error}`).join("\n"));
    } else {
      setFileError("");
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    files.forEach((file) => {
      if (isValidFileType(file) && isValidFileSize(file) && validateFileName(file.name)) {
        setSelectedFiles((prevFiles) => [...prevFiles, file]);
        setFileError("");
      } else {
        setFileError("Invalid file type, size, or name.");
      }
    });
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const openDialog = async (index) => {
    setSelectedFileIndex(index);
    const file = selectedFiles[index];

    if (file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.convertToHtml({ arrayBuffer });
        setDocxContent(result.value);
      } catch (error) {
        console.error("Error converting DOCX to HTML:", error);
      }
    }

    if (file.type === "application/pdf") {
      try {
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
      } catch (error) {
        console.error("Error creating object URL for PDF:", error);
      }
    }
  };

  const closeDialog = () => {
    setSelectedFileIndex(null);
    setDocxContent("");
    if (pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
  };

  const handleExpand = () => {
    setExpanded(!expanded);
  };

  const handleClick = () => {
    fileInputRef.current.click();
  };

  return (
    <Grid container>
      <Grid item xs={12}>
        <FormControl error={!!fileError}>
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "20px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              backgroundColor: "#f2f7f2",
              cursor: "pointer",
              width:400,
            }}
            onClick={handleClick}
          >
            <input
              type="file"
              onChange={handleFileChange}
              style={{ display: "none" }}
              ref={fileInputRef}
              multiple
              accept=".pdf, .doc, .docx, .jpg, .jpeg, .gif, .bmp, .png"
            />
            <CloudUploadIcon style={{ fontSize: 50 }} />
            <p>Drop the files here (max 5 MB)</p>
            <Button
              style={{
                backgroundColor: "#eee",
                border: "none",
                padding: "5px 10px",
                cursor: "pointer",
              }}
            >
              Upload Files
            </Button>
          </div>
          {fileError && <FormHelperText>{fileError}</FormHelperText>}
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        {selectedFiles && selectedFiles.length > 0 && (
          <div>
            <p>Selected Files:</p>
            <ul>
              {selectedFiles.map((file, index) => (
                <li
                  key={index}
                  style={{
                    marginBottom: "5px",
                    wordWrap: 'break-word !important',
                    whiteSpace: 'pre-wrap !important',
                    maxWidth: "100%",
                  }}
                >
                  {file.name}
                  <div style={{ display: "flex", gap: "10px" }}>
                    <Button onClick={() => removeFile(index)}>Remove</Button>
                    <Button onClick={() => openDialog(index)}>View</Button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </Grid>
      <Dialog
        open={selectedFileIndex !== null}
        onClose={closeDialog}
        maxWidth={expanded ? "xl" : undefined}
        maxHeight={expanded ? "xl" : undefined}
        fullWidth={expanded}
        fullScreen={expanded}
      >
        <DialogTitle style={{ display: "flex", justifyContent: "space-between" }}>
          <span style={{ wordBreak: "break-word", whiteSpace: "pre-wrap", maxWidth: "75%" }}>
            {selectedFileIndex !== null && selectedFiles[selectedFileIndex].name}
          </span>
          <div>
            <IconButton edge="end" color="inherit" onClick={handleExpand} aria-label="expand">
              {expanded ? <MinimizeSharp style={{ fontSize: 20 }} /> : <CropSquareOutlined style={{ fontSize: 20 }} />}
            </IconButton>
            <IconButton edge="end" color="inherit" onClick={closeDialog} aria-label="close">
              <CloseIcon style={{ fontSize: 20 }} />
            </IconButton>
          </div>
        </DialogTitle>
        <DialogContent style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
          {selectedFileIndex !== null && selectedFiles[selectedFileIndex].type.startsWith("image/") && (
            <img
              src={URL.createObjectURL(selectedFiles[selectedFileIndex])}
              alt={selectedFiles[selectedFileIndex].name}
              style={{ maxWidth: "100%", maxHeight: "80vh" }}
            />
          )}
          {selectedFileIndex !== null && selectedFiles[selectedFileIndex].type === "application/pdf" && pdfUrl && (
            <iframe
              src={pdfUrl}
              style={{ width: "100%", height: "80vh" }}
              title={selectedFiles[selectedFileIndex].name}
            />
          )}
          {selectedFileIndex !== null && selectedFiles[selectedFileIndex].type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" && (
            <div dangerouslySetInnerHTML={{ __html: docxContent }} style={{ maxWidth: "100%", maxHeight: "80vh", overflowY: "auto" }} />
          )}
          {selectedFileIndex !== null && !["application/pdf", "image/", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(selectedFiles[selectedFileIndex].type) && (
            <p>Preview not available for this file type.</p>
          )}
        </DialogContent>
      </Dialog>
    </Grid>
  );
}

export default SnapshotInput;
