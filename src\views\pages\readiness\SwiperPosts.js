// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Badge from '@mui/material/Badge'

// ** Third Party Components
import clsx from 'clsx'
import { useKeenSlider } from 'keen-slider/react'
import { useMediaQuery } from '@mui/material'

import Icon from 'src/@core/components/icon'

const SwiperPosts = props => {
  const { direction, post } = props
  const [loaded, setLoaded] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  const hidden = useMediaQuery(theme => theme.breakpoints.down('sm'))

  const [sliderRef, instanceRef] = useKeenSlider(
    {
      loop: true,
      slides: {
        perView: hidden ? 1 : window.innerWidth >= 1400 ? 4 : 3,
        spacing: 16,
      },
      rtl: direction === "rtl",

      //controls
      slideChanged(slider) {
        setCurrentSlide(slider.track.details.rel);
      },
      created() {
        setLoaded(true);
      },
    }

    // Auto Switch
    // [
    //     slider => {
    //         let mouseOver = false
    //         let timeout

    //         const clearNextTimeout = () => {
    //             clearTimeout(timeout)
    //         }

    //         const nextTimeout = () => {
    //             clearTimeout(timeout);
    //             if (post === null || '') return;
    //             if (mouseOver) return;
    //             timeout = setTimeout(() => {
    //                 if (post !== null) {
    //                     slider.next();
    //                 }
    //             }, 2000);
    //         };

    //         slider.on('created', () => {
    //             slider.container.addEventListener('mouseover', () => {
    //                 mouseOver = true
    //                 clearNextTimeout()
    //             })
    //             slider.container.addEventListener('mouseout', () => {
    //                 mouseOver = false
    //                 nextTimeout()
    //             })
    //             nextTimeout()
    //         })
    //         slider.on('dragStarted', clearNextTimeout)
    //         slider.on('animationEnded', nextTimeout)
    //         slider.on('updated', nextTimeout)
    //     }
    // ]
  );

  return (
    <>
      <Box className='navigation-wrapper'>

        <Box ref={sliderRef} className='keen-slider' >
          {post && post.map((slide, index) => (
            <Box className='keen-slider__slide' key={index} >
              {slide}
            </Box>
          ))}
        </Box>

        {/* Control Arrow Icons */}
        {loaded && instanceRef.current && instanceRef.current.track && instanceRef.current.track.details && instanceRef.current.track.details.slides && (
          <>
            <Icon
              icon='tabler:chevron-left'
              className={clsx('arrow arrow-left', {
                'arrow-disabled': currentSlide === 0
              })}
              onClick={e => e.stopPropagation() || instanceRef.current.prev()}
            />

            <Icon
              icon='tabler:chevron-right'
              className={clsx('arrow arrow-right', {
                'arrow-disabled': currentSlide === (instanceRef.current.track.details.slides.length - 1)
              })}
              onClick={e => e.stopPropagation() || instanceRef.current.next()}
            />
          </>
        )}
      </Box>

      {/* Pagination swiper-dots */}
      {loaded && instanceRef.current && instanceRef.current.track && instanceRef.current.track.details &&  instanceRef.current.track.details.slides && (
        <Box className='swiper-dots' sx={{ mt: 8 }}>
          {[...Array(instanceRef.current.track.details.slides.length).keys()].map(idx => {
            return (
              <Badge
                key={idx}
                variant='dot'
                component='div'
                className={clsx({
                  active: currentSlide === idx
                })}
                onClick={() => {
                  instanceRef.current?.moveToIdx(idx)
                }}
              ></Badge>
            )
          })}
        </Box>
      )}
    </>
  )
}



export default SwiperPosts
