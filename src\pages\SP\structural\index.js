// ** MUI Imports

import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Styled Component

import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import Remarks from "src/@core/components/custom-components/Remarks";
import ServicesView from "src/@core/components/custom-components/ServicesView";
import PageHeader from "src/@core/components/page-header";
import CompanyDetailsStructural from "src/pages/SP/structural/sections/CompanyDetailsStructural";
import ProjectDetailsStructural from "src/pages/SP/structural/sections/ProjectDetailsSructural";
import TeamDetailsStructural from "src/pages/SP/structural/sections/TeamDetailsStructural";
import StructuralServices from "./sections/StructuralServices";
import StructuralOfferedView from "src/pages/SP/structural/sections/StructuralOfferedView";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import { useRouter } from "next/router";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const Structural = () => {

  const { can } = useRBAC();
  const theme = useTheme();
  const router = useRouter();
  const { entityData, getEntityProfile } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");

  const [expanded, setExpanded] = useState(true);

  
  useEffect(() => {
    getEntityProfile();
    console.log("use effect -Structural")
  },[])

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "STRUCTURAL_ENGINEER") {
      router.push("/401");
    }
  }, []);

  const [state2, setState2] = useState("view");

  const viewClick2 = () => {
    setState2("edit");
  };

  const editClick2 = () => {
    setState2("view");
  };

  if (entityCategory === "STRUCTURAL_ENGINEER") {
    return (
      <div>
        <>
          <style>
            {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
          </style>

          <DatePickerWrapper>
            <Grid container spacing={2} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Structural Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <CompanyDetailsStructural
                  data={entityData}
                  expanded={expanded}
                ></CompanyDetailsStructural>
              </Grid>

              <Grid item xs={12}>
                <TeamDetailsStructural
                  data={entityData}
                  expanded={expanded}
                ></TeamDetailsStructural>
              </Grid>

              <Grid item xs={12}>
                <ProjectDetailsStructural
                  data={entityData}
                  expanded={expanded}
                ></ProjectDetailsStructural>
              </Grid>
              <Grid item xs={12}>
                <StructuralOfferedView
                  data={entityData}
                  expanded={expanded}
                ></StructuralOfferedView>
              </Grid>

              <Grid item xs={12}>             
                <ServicesView
                  data={entityData}
                  expanded={expanded}
                  readPermission={'structuralEngineer_services_READ'}
                  permission={'structuralEngineer_services_UPDATE'}
                ></ServicesView>             
              </Grid>
              <Grid item xs={12}>
                <StructuralServices
                  data={entityData}
                  expanded={expanded}
                ></StructuralServices>
              </Grid>
              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'structuralEngineer_otherServices_READ'}
                  permission={"structuralEngineer_otherServices_UPDATE"}
                ></OtherServices>
              </Grid>
              <Grid item xs={12}>
                <Remarks data={entityData} expanded={expanded} readPermission={'structuralEngineer_remarks_READ'} permission={'structuralEngineer_remarks_UPDATE'}></Remarks>           
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default Structural;
