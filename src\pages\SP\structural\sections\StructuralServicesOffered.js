// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  Typography,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";

const StructuralServicesOffered = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [structuralAudit, setStructuralAudit] = useState(
    formData?.structuralAudit
  );
  const [loadConditionVerification, setLoadConditionVerification] = useState(
    formData?.loadConditionVerification
  );
  const [
    buildingStructuralSystemEvaluation,
    setBuildingStructuralSystemEvaluation,
  ] = useState(formData?.buildingStructuralSystemEvaluation);
  const [structuralDefectsDetection, setStructuralDefectsDetection] = useState(
    formData?.structuralDefectsDetection
  );
  const [planAndAlignmentCheck, setPlanAndAlignmentCheck] = useState(
    formData?.planAndAlignmentCheck
  );
  const [maintenanceAssessment, setMaintenanceAssessment] = useState(
    formData?.maintenanceAssessment
  );
  const [otherStructuralSurveys, setOtherStructuralSurveys] = useState(
    formData?.otherStructuralSurveys
  );
  const [fireSafetyCheck, setFireSafetyCheck] = useState(
    formData?.fireSafetyCheck
  );
  const [electricalSafetyCheck, setElectricalSafetyCheck] = useState(
    formData?.electricalSafetyCheck
  );
  const [structuralRepairs, setStructuralRepairs] = useState(
    formData?.structuralRepairs
  );
  const [majorRepairs, setMajorRepairs] = useState(formData?.majorRepairs);

  const [rccDesignPlanning, setRccDesignPlanning] = useState(
    formData?.rccDesignPlanning
  );

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    // Check if all the states are true using the && operator
    const allTrue =
      loadConditionVerification &&
      buildingStructuralSystemEvaluation &&
      structuralDefectsDetection &&
      planAndAlignmentCheck &&
      maintenanceAssessment &&
      otherStructuralSurveys &&
      fireSafetyCheck &&
      electricalSafetyCheck;

    // Set structuralAudit based on the result
    if (allTrue) {
      setStructuralAudit(true);
      setValue("structuralAudit", true);
    } else {
      setStructuralAudit(false);
      setValue("structuralAudit", false);
    }
  }, [
    loadConditionVerification,
    buildingStructuralSystemEvaluation,
    structuralDefectsDetection,
    planAndAlignmentCheck,
    maintenanceAssessment,
    otherStructuralSurveys,
    fireSafetyCheck,
    electricalSafetyCheck,
  ]);

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="rccDesignPlanning"
                control={control}
                defaultValue={
                  formData?.rccDesignPlanning ? rccDesignPlanning : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("rccDesignPlanning", true);
                            setRccDesignPlanning(true);
                          } else {
                            setValue("rccDesignPlanning", false);
                            setRccDesignPlanning(false);
                          }
                        }}
                      />
                    }
                    label={
                      <span style={{ fontSize: "1.32rem", fontWeight: "bold" }}>
                        RCC Design, Planning & Inspection
                      </span>
                    }
                  />
                )}
              />
            </FormControl>
          
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="structuralAudit"
                control={control}
                defaultValue={
                  formData?.structuralAudit ? structuralAudit : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("structuralAudit", true);
                            setStructuralAudit(true);
                            setValue("loadConditionVerification", true);
                            setLoadConditionVerification(true);
                            setValue(
                              "buildingStructuralSystemEvaluation",
                              true
                            );
                            setBuildingStructuralSystemEvaluation(true);
                            setValue("structuralDefectsDetection", true);
                            setStructuralDefectsDetection(true);
                            setValue("planAndAlignmentCheck", true);
                            setPlanAndAlignmentCheck(true);
                            setValue("maintenanceAssessment", true);
                            setMaintenanceAssessment(true);
                            setValue("otherStructuralSurveys", true);
                            setOtherStructuralSurveys(true);
                            setValue("fireSafetyCheck", true);
                            setFireSafetyCheck(true);
                            setValue("electricalSafetyCheck", true);
                            setElectricalSafetyCheck(true);
                          } else {
                            setValue("structuralAudit", false);
                            setStructuralAudit(false);
                            setValue("loadConditionVerification", false);
                            setLoadConditionVerification(false);
                            setValue(
                              "buildingStructuralSystemEvaluation",
                              false
                            );
                            setBuildingStructuralSystemEvaluation(false);
                            setValue("structuralDefectsDetection", false);
                            setStructuralDefectsDetection(false);
                            setValue("planAndAlignmentCheck", false);
                            setPlanAndAlignmentCheck(false);
                            setValue("maintenanceAssessment", false);
                            setMaintenanceAssessment(false);
                            setValue("otherStructuralSurveys", false);
                            setOtherStructuralSurveys(false);
                            setValue("fireSafetyCheck", false);
                            setFireSafetyCheck(false);
                            setValue("electricalSafetyCheck", false);
                            setElectricalSafetyCheck(false);
                          }
                        }}
                      />
                    }
                    label={
                      <span style={{ fontSize: "1.32rem", fontWeight: "bold" }}>
                        Structural Audit
                      </span>
                    }
                  />
                )}
              />
            </FormControl>

            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="loadConditionVerification"
                    control={control}
                    defaultValue={
                      formData?.loadConditionVerification
                        ? loadConditionVerification
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.checked) {
                                setValue("loadConditionVerification", true);
                                setLoadConditionVerification(true);
                              } else {
                                setValue("loadConditionVerification", false);
                                setLoadConditionVerification(false);
                              }
                            }}
                          />
                        }
                        label={
                          <span
                            style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                          >
                            Verification of load conditions
                          </span>
                        }
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>

            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="buildingStructuralSystemEvaluation"
                    control={control}
                    defaultValue={
                      formData?.buildingStructuralSystemEvaluation
                        ? buildingStructuralSystemEvaluation
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.checked) {
                                setValue(
                                  "buildingStructuralSystemEvaluation",
                                  true
                                );
                                setBuildingStructuralSystemEvaluation(true);
                              } else {
                                setValue(
                                  "buildingStructuralSystemEvaluation",
                                  false
                                );
                                setBuildingStructuralSystemEvaluation(false);
                              }
                            }}
                          />
                        }
                        label={
                          <span
                            style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                          >
                            Evaluation of the structural system of the building
                          </span>
                        }
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>

            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="structuralDefectsDetection"
                    control={control}
                    defaultValue={
                      formData?.structuralDefectsDetection
                        ? structuralDefectsDetection
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.checked) {
                                setValue("structuralDefectsDetection", true);
                                setStructuralDefectsDetection(true);
                              } else {
                                setValue("structuralDefectsDetection", false);
                                setStructuralDefectsDetection(false);
                              }
                            }}
                          />
                        }
                        label={
                          <span
                            style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                          >
                            Detection of structural defects, damages, distress, deformation or
deterioration
                          </span>
                        }
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>

            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="planAndAlignmentCheck"
                    control={control}
                    defaultValue={
                      formData?.planAndAlignmentCheck
                        ? planAndAlignmentCheck
                        : false
                    }
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.checked) {
                                setValue("planAndAlignmentCheck", true);
                                setPlanAndAlignmentCheck(true);
                              } else {
                                setValue("planAndAlignmentCheck", false);
                                setPlanAndAlignmentCheck(false);
                              }
                            }}
                          />
                        }
                        label={
                          <span
                            style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                          >
                           Plan and alignment check
                          </span>
                        }
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="maintenanceAssessment"
                      control={control}
                      defaultValue={
                        formData?.maintenanceAssessment
                          ? maintenanceAssessment
                          : false
                      }
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={field.value}
                              style={{ transform: "scale(1)" }}
                              onChange={(e) => {
                                field.onChange(e);
                                if (e.target.checked) {
                                  setValue("maintenanceAssessment", true);
                                  setMaintenanceAssessment(true);
                                } else {
                                  setValue("maintenanceAssessment", false);
                                  setMaintenanceAssessment(false);
                                }
                              }}
                            />
                          }
                          label={
                            <span
                              style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                            >
                              Assessing maintenance and exposure to aggressive environment
                            </span>
                          }
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="otherStructuralSurveys"
                      control={control}
                      defaultValue={
                        formData?.otherStructuralSurveys
                          ? otherStructuralSurveys
                          : false
                      }
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={field.value}
                              style={{ transform: "scale(1)" }}
                              onChange={(e) => {
                                field.onChange(e);
                                if (e.target.checked) {
                                  setValue("otherStructuralSurveys", true);
                                  setOtherStructuralSurveys(true);
                                } else {
                                  setValue("otherStructuralSurveys", false);
                                  setOtherStructuralSurveys(false);
                                }
                              }}
                            />
                          }
                          label={
                            <span
                              style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                            >
                              Other structural surveys and checks
                            </span>
                          }
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="fireSafetyCheck"
                      control={control}
                      defaultValue={
                        formData?.fireSafetyCheck ? fireSafetyCheck : false
                      }
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={field.value}
                              style={{ transform: "scale(1)" }}
                              onChange={(e) => {
                                field.onChange(e);
                                if (e.target.checked) {
                                  setValue("fireSafetyCheck", true);
                                  setFireSafetyCheck(true);
                                } else {
                                  setValue("fireSafetyCheck", false);
                                  setFireSafetyCheck(false);
                                }
                              }}
                            />
                          }
                          label={
                            <span
                              style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                            >
                              Fire Safety Check
                            </span>
                          }
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="electricalSafetyCheck"
                      control={control}
                      defaultValue={
                        formData?.electricalSafetyCheck
                          ? electricalSafetyCheck
                          : false
                      }
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={field.value}
                              style={{ transform: "scale(1)" }}
                              onChange={(e) => {
                                field.onChange(e);
                                if (e.target.checked) {
                                  setValue("electricalSafetyCheck", true);
                                  setElectricalSafetyCheck(true);
                                } else {
                                  setValue("electricalSafetyCheck", false);
                                  setElectricalSafetyCheck(false);
                                }
                              }}
                            />
                          }
                          label={
                            <span
                              style={{ fontSize: "1.2rem", fontWeight: "bold" }}
                            >
                              Electrical Safety Check
                            </span>
                          }
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Grid>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="structuralRepairs"
                  control={control}
                  defaultValue={
                    formData?.structuralRepairs ? structuralRepairs : false
                  }
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...field}
                          checked={field.value}
                          style={{ transform: "scale(1)" }}
                          onChange={(e) => {
                            field.onChange(e);
                            if (e.target.checked) {
                              setValue("structuralRepairs", true);
                              setStructuralRepairs(true);
                            } else {
                              setValue("structuralRepairs", false);
                              setStructuralRepairs(false);
                            }
                          }}
                        />
                      }
                      label={
                        <span
                          style={{ fontSize: "1.32rem", fontWeight: "bold" }}
                        >
                          Structural Repairs
                        </span>
                      }
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="majorRepairs"
                  control={control}
                  defaultValue={formData?.majorRepairs ? majorRepairs : false}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...field}
                          checked={field.value}
                          style={{ transform: "scale(1)" }}
                          onChange={(e) => {
                            field.onChange(e);
                            if (e.target.checked) {
                              setValue("majorRepairs", true);
                              setMajorRepairs(true);
                            } else {
                              setValue("majorRepairs", false);
                              setMajorRepairs(false);
                            }
                          }}
                        />
                      }
                      label={
                        <span
                          style={{ fontSize: "1.32rem", fontWeight: "bold" }}
                        >
                          Major Repairs
                        </span>
                      }
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <center>
                <Button
                  size="medium"
                  sx={{ mr: 3 }}
                  variant="outlined"
                  color="primary"
                  onClick={() => onCancel()}
                >
                  Cancel
                </Button>
                <Button
                  size="medium"
                  type="button"
                  variant="contained"
                  onClick={handleSubmit(submit)}
                >
                  Save
                </Button>
              </center>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default StructuralServicesOffered;
