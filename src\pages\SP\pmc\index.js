// ** MUI Imports
import Grid from "@mui/material/Grid";
import TableCell from "@mui/material/TableCell";
import Typography from "@mui/material/Typography";
import { styled } from "@mui/material/styles";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Styled Component

import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import PageHeader from "src/@core/components/page-header";
import CompanyDetailsPmc from "src/pages/SP/pmc/sections/CompanyDetailsPmc";
import ProjectDetailsPmc from "src/pages/SP/pmc/sections/ProjectDetailsPmc";
import TeamDetailsPmc from "src/pages/SP/pmc/sections/TeamDetailsPmc";

import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import Remarks from "src/@core/components/custom-components/Remarks";
import ServicesView from "src/@core/components/custom-components/ServicesView";
import { useRouter } from "next/router";
import { useRBAC } from "src/pages/permission/RBACContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const Pmc = () => {
  const { entityData, getEntityProfile } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");
  const router = useRouter();
  const { can } = useRBAC();

  const [expanded, setExpanded] = useState(true);

  
  useEffect(() => {
    getEntityProfile();
    console.log("use effect -PMC")
  },[])

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "PMC") {
      router.push("/401");
    }
  }, []);

  if (entityCategory === "PMC") {
    return (
      <div>
        <>
          <style>
            {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={2} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">PMC Registration</Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <CompanyDetailsPmc
                  data={entityData}
                  expanded={expanded}
                ></CompanyDetailsPmc>
              </Grid>

              <Grid item xs={12}>
                <TeamDetailsPmc
                  data={entityData}
                  expanded={expanded}
                ></TeamDetailsPmc>
              </Grid>

              <Grid item xs={12}>
                <ProjectDetailsPmc
                  data={entityData}
                  expanded={expanded}
                ></ProjectDetailsPmc>
              </Grid>
              <Grid item xs={12}>
                <ServicesView
                  data={entityData}
                  expanded={expanded}
                  read={'pmc_services_READ'}
                  permission={'pmc_services_UPDATE'}
                ></ServicesView>             
              </Grid>
              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'pmc_otherServices_READ'}
                  permission={'pmc_otherServices_UPDATE'}
                ></OtherServices>             
              </Grid>
              <Grid item xs={12}>           
                <Remarks data={entityData} expanded={expanded} readPermission={'pmc_remarks_READ'} permission={'pmc_remarks_UPDATE'}></Remarks>
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default Pmc;
