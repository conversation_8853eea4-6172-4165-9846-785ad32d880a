// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports

import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section3 from './Section3'
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const ServiceDetailsAdvocates = ({data,expanded}) => {

    // ** Hook
  const theme = useTheme()

  const [state3, setState3] = useState(true)

  const handleState3 = () => {
    setState3(!state3)
  }
    
    // Pre-Populating code Start
    // const [otherDetails, setOtherDetails] = useState({
    //     professionalDetails:"",
    //     redevelopment:"",
    //     documentsAvailable:""
    //   });

    return (
        <>
         <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Other Details'}
                body={
                  <>
                    {state3 && (
                     
                          <TableContainer sx={{ padding:'4px 6px' }}
                            className='tableBody'
                            onClick={handleState3}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Developer/ Society Services</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.developerSocietyServices}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Home Buyer Services:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.homeBuyerServices}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Team Size</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.redevelopment}</Typography>
                                  </MUITableCell>
                                </TableRow>

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Other Refernces</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.otherReferneces}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {!state3 && <Section3 formData={data} onCancel={handleState3} />}
                  </>
                }
                expanded={expanded}
              />
        </>
    );

}
export default ServiceDetailsAdvocates;