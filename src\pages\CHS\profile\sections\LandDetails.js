// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section1 from 'src/pages/SP/broker/sections/Section1'
import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section2 from './Section2'
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const LandDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
    const theme = useTheme()

    const [state2, setState2] = useState(true)

    const handleState2 = () => {
      setState2(!state2)
    }
    
    // Pre-Populating code Start
    // const [landDetails, setLandDetails] = useState({
    //     grossPlotArea:"",
    //     builtUpAreaResidential:"",
    //     builtUpAreaCommercial:"",
    //     noOfResidence:"",
    //     noOfCommercial:""
    //   });

    return (
        <>
        {/* {can('society_landDetails_READ') && */}
       <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Land Details'}
                body={
                  <>
                    {state2 && (
                      
                          <TableContainer sx={{ padding:'4px 6px' }}
                            className='tableBody'
                            //onClick={can('society_landDetails_UPDATE') ? handleState2 : null}>
                            onClick={ handleState2}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <PageHeader
                                    title={<Typography variant='body1'><strong>Total Plot Area:</strong>
                                    </Typography>}
                                    subtitle={<Typography variant='body2'></Typography>}
                                  />
                                </TableRow>
                                


                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Built-up area a Residential(Sq Mtrs):</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.builtUpAreaResidential}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field} >Built-up area Commercial(Sq Mtrs):</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.builtUpAreaCommercial}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <PageHeader
                                  title={<Typography variant='body1' sx={{ mt: 6 ,mb:1 }}><strong>No.of Residence/Commercial:</strong>
                                  </Typography>}
                                  subtitle={<Typography variant='body2'></Typography>}
                                />

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field} >No.of Residence:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfResidence}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>No.of Commercial:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfCommercial}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        

                    )}
                    {!state2 && <Section2 formData={data} onCancel={handleState2} />}
                  </>
                }
                expanded={expanded}
              /> 
        {/* }     */}
        </>
    );

}
export default LandDetails;