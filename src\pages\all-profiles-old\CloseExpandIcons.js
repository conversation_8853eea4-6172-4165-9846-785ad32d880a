import { Box, Tooltip } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { useMediaQuery, useTheme } from "@mui/material";


const CloseExpandIcons = (props) => {
  const { expanded, onToggle } = props;

  const handleToggle = () => {
    onToggle(!expanded); // Toggle the expanded state by calling the onToggle function with the opposite value
  };

  const theme = useTheme(); // Access current theme
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  return (
    <Box sx={{ display: "flex", alignItems: "center", "& svg": { mr: 2 } }}>
      <Tooltip title={expanded ? "Collapse" : "Expand"}>
        <IconButton
          sx={{ color: "text.viewData" , py: 0}}
          variant="contained"
          onClick={handleToggle}
        >
          <Icon
            icon={
              expanded ? "tabler:arrow-bar-to-up" : "tabler:arrow-bar-to-down"
            }
            fontSize={
              isSmallScreen ? '20px' : '30px'
            }
          />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default CloseExpandIcons;
