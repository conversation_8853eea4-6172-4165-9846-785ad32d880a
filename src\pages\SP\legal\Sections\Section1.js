// ** React Imports
import { forwardRef, useState, useEffect } from "react";

// ** MUI Imports

import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { Box, Typography } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";
import { yupResolver } from "@hookform/resolvers/yup";
import LegalValidations from "./LegalValidations";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";

const Section1 = ({ onCancel, formData }) => {
  const auth = useAuth();
  const fields = ["name", "companyName", "email", "mobileNumber", "address", "websiteUrl"];
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(LegalValidations(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    console.log("Submitted Data",data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Legal Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
      <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="companyName"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.companyName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Company Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter company name"
                  error={Boolean(errors.companyName)}
                  helperText={errors.companyName?.message}
                  aria-describedby="validation-basic-last-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">System Code</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.systemCode}
            </Typography>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Address"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.address)}
                  helperText={errors.address?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Contact number"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.mobileNumber)}
                  helperText={errors.mobileNumber?.message}
                  placeholder="+91 1234567890"
                  aria-describedby="validation-mobileNumber"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
              defaultValue={formData?.email}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="Email Id"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.email)}
                  helperText={errors.email?.message}
                  placeholder="Enter email address"
                  aria-describedby="validation-email"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              defaultValue={formData?.websiteUrl}
              render={({ field }) => (
                <TextField
                  {...field}
                 
                  label="Website URL"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.websiteUrl)}
                  helperText={errors.websiteUrl?.message}
                  placeholder="https://www.example.com"
                  aria-describedby="validation-websiteUrl"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section1;
