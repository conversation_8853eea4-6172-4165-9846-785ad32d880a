// ** React Imports
import { useContext, useEffect, useState, useRef } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import { styled, useTheme } from "@mui/material/styles";
import StepLabel from "@mui/material/StepLabel";
import Typography from "@mui/material/Typography";
import MuiStep from "@mui/material/Step";
import MuiStepper from "@mui/material/Stepper";
import CardContent from "@mui/material/CardContent";
import { CircularProgress, DialogContentText } from "@mui/material";
import ContactCollapsibleCard from "./contactWrapper";

// ** Axios
import axios from "axios";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Custom Components Imports
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Step Components

// ** Util Import
import { hexToRGBA } from "src/@core/utils/hex-to-rgba";

// ** Styled Components
import StepperWrapper from "src/@core/styles/mui/stepper";
import PerfectScrollbar from "react-perfect-scrollbar";
import useMediaQuery from "@mui/material/useMediaQuery";

import NeedAssessment from "./NeedAssessment";
import TypesofDevelopment from "./TypesofDevelopment";
import Committee from "./Committee";
import Consents from "./Consents";
import Conveyance from "./Conveyance";
import Documents from "./Documents";
import Appointment from "./Appointment";
import PreTenderingStage from "./PreTenderingStage";
import TenderingStage from "./TenderingStage";
import FinancialClosure from "./FinancialClosure";
import HorizontalTabs from "./HorizontalTabs";
import Checkbox from "@mui/material/Checkbox";

import Submission from "./Submission";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  makeStyles,
  Alert,
  AlertTitle,
} from "@mui/material";

// ** Config
import authConfig from "src/configs/auth";
import auth from "src/configs/auth";
import { getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import ValidManagingCommittee from "./ValidManagingCommittee";

import { useSettings } from "src/@core/hooks/useSettings";

import themeConfig from "src/configs/themeConfig";
import { useForm } from "react-hook-form";
import NameTextField from "src/@core/components/custom-components/NameTextField";

const steps = [
  {
    title: "Initiating Redevelopment",
    icon: "tabler:users",
  },
  {
    icon: "tabler:id",
    title: "Types of Redevelopment",
  },
  {
    title: "Redevelopment Committee",
    icon: "tabler:id",
  },
  {
    title: "Consents",
    icon: "tabler:id",
  },
  {
    title: "Conveyance",
    icon: "tabler:id",
  },
  {
    title: "Managing Committee",
    icon: "tabler:id",
  },
  {
    title: "Redevelopment Documents",
    icon: "tabler:id",
  },
  {
    title: "Appointment of Professionals",
    icon: "tabler:id",
  },
  {
    title: "Pre-Tendering",
    icon: "tabler:id",
  },
  {
    title: "Tendering",
    icon: "tabler:id",
  },
  {
    title: "Financial Closure",
    icon: "tabler:id",
  },
  {
    icon: "tabler:checkbox",
    title: "Submit",
  },
];

const Stepper = styled(MuiStepper)(({ theme }) => ({
  "& .MuiStep-root:not(:last-of-type) .MuiStepLabel-root": {
    paddingBottom: theme.spacing(3),
  },
  [theme.breakpoints.down("md")]: {
    minWidth: 0,
  },
}));

const StepperHeaderContainer = styled(CardContent)(({ theme }) => ({
  borderRight: `1px solid ${theme.palette.divider}`,
  padding: theme.spacing(4),
  [theme.breakpoints.down("md")]: {
    borderRight: 0,
    padding: theme.spacing(2.5),
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
}));

const ScrollWrapper = ({ children, hidden }) => {
  if (hidden) {
    return (
      <Box sx={{ maxHeight: 330, overflowY: "auto", overflowX: "hidden" }}>
        {children}
      </Box>
    );
  } else {
    return (
      <PerfectScrollbar options={{ wheelPropagation: true }}>
        {children}
      </PerfectScrollbar>
    );
  }
};

const Step = styled(MuiStep)(({ theme }) => ({
  "& .MuiStepLabel-root": {
    paddingTop: theme.spacing(1.5),
  },
  "&:not(:last-of-type) .MuiStepLabel-root": {
    paddingBottom: theme.spacing(1),
  },
  "&:last-of-type .MuiStepLabel-root": {
    paddingBottom: 0,
  },
  "& .MuiStepLabel-iconContainer": {
    display: "none",
  },
  "& + svg": {
    color: theme.palette.text.disabled,
  },
  "&.Mui-completed .step-title": {
    color: theme.palette.text.disabled,
  },
  "&.Mui-completed .step-subtitle": {
    color: `${theme.palette.text.disabled} !important`,
  },
  "& .MuiStepLabel-label": {
    cursor: "pointer",
  },
}));

const BoxFooter = styled("Box")(({ theme }) => ({
  zIndex: 11,
  width: "100%",
  borderTop: `1px solid ${theme.palette.divider}`,
  backgroundColor: hexToRGBA(theme.palette.primary.main, 0.2),
  borderColor: hexToRGBA(theme.palette.primary.main, 0.2),
  bottom: 0,
  right: 0,
  position: "absolute",
  [theme.breakpoints.down("lg")]: {
    width: "100%",
  },
}));

const ReadinessWizard = () => {
  const { settings, saveSettings } = useSettings();
  const [loadGenerateReport, setLoadGenerateReport] = useState(false);

  // ** States
  const [activeStep, setActiveStep] = useState(0);
  const hidden = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { navCollapsed } = settings;

  const [nextActive, setNextActive] = useState(false);
  const [email, setEmail] = useState("");
  const [editIndex, setEditIndex] = useState(null);
  const { readinessProfileData, getReadinessProfile } = useContext(AuthContext);
  const { updateReadiness } = useContext(AuthContext);

  const [data, setData] = useState(readinessProfileData);
  const [settingsNew, setSettingsNew] = useState({});
  const [postsData, setPostsData] = useState([]);

  const [openDialog, setOpenDialog] = useState(false);
  const [alertConfig, setAlertConfig] = useState(null);

  const [open, setOpen] = useState(false);
  const handleClickOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [dialogMessage, setDialogMessage] = useState("");

  useEffect(() => {
    setData(readinessProfileData);
  }, [readinessProfileData]);

  useEffect(() => {
    getReadinessProfile();
  }, []);

  const [showGenerate, setShowGenerate] = useState(false);

  const handleContactNoChange = (event) => {
    let value = event.target.value.replace(/[^0-5\d()+-]/g, "");
    value = value.replace(/^[^5-9]/, "");
    value = value.slice(0, 10);
    handleContactNumberChange(value);
  };

  const handleName = (event) => {
    if (!event || !event.target) {
      return;
    }
    const name = event.target.value;
    let formattedName = "";

    if (name.length > 0) {
      const firstCharacter = /^[a-zA-Z]+$/.test(name.charAt(0))
        ? name.charAt(0)
        : "";
      const remainingCharacters = name.slice(1);
      const formattedRemainingCharacters = remainingCharacters.replace(
        /[^a-zA-Z\s]/g,
        ""
      );

      const singleSpaceFormattedName = formattedRemainingCharacters.replace(
        /\s+/g,
        " "
      );

      formattedName = `${firstCharacter}${singleSpaceFormattedName}`;
    }
    handleNameChange(formattedName);
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const handleSocietyName = (event) => {
    const societyName = event.target.value;
    let formattedSocietyName = "";

    if (societyName.length > 0) {
      const firstCharacter = /^[a-zA-Z]+$/.test(societyName.charAt(0))
        ? societyName.charAt(0)
        : "";
      const remainingCharacters = societyName.slice(1);
      const formattedRemainingCharacters = remainingCharacters.replace(
        /[^a-zA-Z0-9\s]/g,
        ""
      );

      const singleSpaceFormattedName = formattedRemainingCharacters.replace(
        /\s+/g,
        " "
      );

      formattedSocietyName = `${firstCharacter}${singleSpaceFormattedName}`;
    }
    handleSocietyNameChange(formattedSocietyName);
  };

  const generateReportActive = () => {
    if (data?.needAssessment?.hasYourSocietyConsideredForRedevelopment == "") {
      setShowGenerate(false);
      return;
    }
    if (data?.needAssessment?.name == "") {
      setShowGenerate(false);
      return;
    }
    if (data?.needAssessment?.societyName == "") {
      setShowGenerate(false);
      return;
    }

    if (
      data?.typesOfRedevelopment
        ?.hasAwareOfTypesOfRedevelopment_ChallengesBenefits === ""
    ) {
      setShowGenerate(false);
      return;
    }
    if (data?.committee?.haveFormedRedevelopmentCommittee === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.consents?.noOfConsents === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.consents?.totalNoOfMembers === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.consents?.percentageOfMembersConsent === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.conveyance?.doesSocietyHaveConveyance === "") {
      setShowGenerate(false);
      return;
    }
    if (
      data?.managingCommittee?.isExistingManagingCommitteeProvisionsAct === ""
    ) {
      setShowGenerate(false);
      return;
    }

    if (data?.documents?.hasPropertyCard === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.documents?.hasConveyanceDeed === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.documents?.hasCitySurveyPlan === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.documents?.hasLastApprovedMunicipalDrawings === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.documents?.hasDpRemarks === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.documents?.hasSurveyPlan === "") {
      setShowGenerate(false);
      return;
    }

    if (
      data?.appointmentOfProfessionals?.haveAppointedAnyArchitectOrPmc === ""
    ) {
      setShowGenerate(false);
      return;
    }

    if (data?.appointmentOfProfessionals?.haveAppointedAnyPmc === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.appointmentOfProfessionals?.haveAppointedAnyCa === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.appointmentOfProfessionals?.haveAppointedAnyAdvocate === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.preTenderingStage?.hasFsiCalculations === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.preTenderingStage?.hasDataAndDocumentation === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.preTenderingStage?.hasCreationOfDesignLayout === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.preTenderingStage?.hasMarketAnalysis === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.preTenderingStage?.hasSiteAnalysisReport === "") {
      setShowGenerate(false);
      return;
    }

    if (data?.tenderingStage?.hasAppointmentOfDevelopers === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.tenderingStage?.hasPreQualificationOfDevelopers === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.tenderingStage?.hasScrutinizationOfTenderDocuments === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.tenderingStage?.hasSigningOfDaOrDm === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.tenderingStage?.hasTenderingDocuments === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.financialClosure?.haveSecuredFinancialClosure === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.financialClosure?.contactNumber === "") {
      setShowGenerate(false);
      return;
    }
    if (data?.financialClosure?.emailId === "") {
      setShowGenerate(false);
      return;
    }

    setShowGenerate(true);
  };

  useEffect(() => {
    generateReportActive();
  }, [data]);

  const [percentage, setPercentage] = useState(data?.percentage);
  const [generateReport, setGenerateReport] = useState(data?.generateReport);

  // Fetching the settings from the DB.
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.settings) + "?settingsType=CHECK_READINESS_POSTS",
    })
      .then((res) => {
        setSettingsNew(res.data?.readinessResourcesDTO);
      })
      .catch((err) => console.log("error", err));
  }, []);

  // Fetching posts from the wordpress site.
  useEffect(() => {
    axios({
      method: "get",
      url: authConfig.resourcesURL + "wp-json/wp/v2/posts?_embed&per_page=100",
    })
      .then((res) => {
        sortThePosts(res.data);
      })
      .catch();
  }, []);

  //Sorting the Posts data in the Form of an DTO we need for our backend and for the Multi Select input field.
  const sortThePosts = (data) => {
    let sortedData = [];
    data.map((record) => {
      let obj = {
        wordPressId: record.id,
        title: record.title.rendered,
        slug: record.slug,
        imageId:
          record._embedded["wp:featuredmedia"] &&
          record._embedded["wp:featuredmedia"][0].media_details.sizes.thumbnail
            .source_url,
        link: record.link,
        resourceType: "WORD_PRESS",
        format: record.format,
        youtube_url: record.youtube_url,
      };
      sortedData.push(obj);
    });
    setPostsData(sortedData);
  };

  const findPostById = (id) => {
    return postsData.find((post) => post.wordPressId === parseInt(id));
  };

  useEffect(() => {
    const newSettings = { ...settingsNew };

    const mapItem = (item) => {
      const matchingPost = findPostById(item.wordPressId);

      if (matchingPost) {
        return {
          ...item,
          imageId: matchingPost.imageId,
          link: matchingPost.link,
          resourceType: matchingPost.resourceType,
          slug: matchingPost.slug,
          title: matchingPost.title,
          format: matchingPost.format,
          youtube_url: matchingPost.youtube_url,
        };
      } else {
        return null;
      }
    };

    [
      "needAssessment",
      "typesOfRedevelopment",
      "committee",
      "appointmentOfProfessionals",
      "consents",
      "conveyance",
      "documents",
      "financialClosure",
      "managingCommittee",
      "preTenderingStage",
      "tenderingStage",
    ].forEach((key) => {
      newSettings[key] = newSettings[key]?.map(mapItem);
    });

    setSettingsNew(newSettings);
  }, [postsData]);

  useEffect(() => {
    handleEmailIdChange(email);
  }, [email]);

  const styles = {
    opacity: nextActive ? "" : 0.5,
    pointerEvents: nextActive ? "" : "none",
  };

  // Handle the Step Click
  const handleStepClick = (index) => {
    if (activeStep > index) {
      setActiveStep(index);
    }
  };

  // Handle Stepper
  const handleNext = () => {
    setActiveStep(activeStep + 1);
    setNextActive(false);
  };

  const [checked, setChecked] = useState(false);

  const handleChange = (event) => {
    setChecked(event.target.checked);
  };

  const handlePrev = () => {
    if (activeStep !== 0) {
      setActiveStep(activeStep - 1);
    }
  };

  function handleHaveSecuredFinancialClosureChange(value) {
    setData({
      ...data,
      financialClosure: {
        ...data.financialClosure,
        haveSecuredFinancialClosure: value,
      },
    });
  }

  function handleContactNumberChange(value) {
    setData({
      ...data,
      financialClosure: {
        ...data.financialClosure,
        contactNumber: value,
      },
    });
  }
  function handleEmailIdChange(value) {
    setData({
      ...data,
      financialClosure: {
        ...data.financialClosure,
        emailId: value,
      },
    });
  }

  function handleTenderingDocumentsChange(value) {
    setData({
      ...data,
      tenderingStage: {
        ...data.tenderingStage,
        hasTenderingDocuments: value,
      },
    });
  }

  function handlePreQualificationOfDevelopersChange(value) {
    setData({
      ...data,
      tenderingStage: {
        ...data.tenderingStage,
        hasPreQualificationOfDevelopers: value,
      },
    });
  }

  function handleScrutinizationOfTenderDocumentsChange(value) {
    setData({
      ...data,
      tenderingStage: {
        ...data.tenderingStage,
        hasScrutinizationOfTenderDocuments: value,
      },
    });
  }
  function handleSocietyNameChange(value) {
    setData({
      ...data,
      needAssessment: {
        ...data.needAssessment,
        societyName: value,
      },
    });
  }

  function handleNameChange(value) {
    setData({
      ...data,
      needAssessment: {
        ...data.needAssessment,
        name: value,
      },
    });
  }
  function handleAppointmentOfDevelopersChange(value) {
    setData({
      ...data,
      tenderingStage: {
        ...data.tenderingStage,
        hasAppointmentOfDevelopers: value,
      },
    });
  }
  function handleHaveYouAppointedPmcChange(value) {
    setData({
      ...data,
      appointmentOfProfessionals: {
        ...data.appointmentOfProfessionals,
        haveAppointedAnyPmc: value,
      },
    });
  }

  function handleHaveYouAppointedCaChange(value) {
    setData({
      ...data,
      appointmentOfProfessionals: {
        ...data.appointmentOfProfessionals,
        haveAppointedAnyCa: value,
      },
    });
  }

  function handleHaveYouAppointedAdvocateChange(value) {
    setData({
      ...data,
      appointmentOfProfessionals: {
        ...data.appointmentOfProfessionals,
        haveAppointedAnyAdvocate: value,
      },
    });
  }

  function handleSigningOfDaDmChange(value) {
    setData({
      ...data,
      tenderingStage: {
        ...data.tenderingStage,
        hasSigningOfDaOrDm: value,
      },
    });
  }

  function handleFsiCalculationsChange(value) {
    setData({
      ...data,
      preTenderingStage: {
        ...data.preTenderingStage,
        hasFsiCalculations: value,
      },
    });
  }

  function handleHaveYouAppointedChange(value) {
    setData({
      ...data,
      appointmentOfProfessionals: {
        ...data.appointmentOfProfessionals,
        haveAppointedAnyArchitectOrPmc: value,
      },
    });
  }

  function handleIsExistingManagingCommitteeChange(value) {
    setData({
      ...data,
      managingCommittee: {
        ...data.managingCommittee,
        isExistingManagingCommitteeProvisionsAct: value,
      },
    });
  }

  function handleTenureInYearsChange(value) {
    setData({
      ...data,
      managingCommittee: {
        ...data.managingCommittee,
        tenureYears: value,
      },
    });
  }

  function handleValidTillDateChange(value) {
    setData({
      ...data,
      managingCommittee: {
        ...data.managingCommittee,
        validTillDate: value,
      },
    });
  }

  function handleDataAndDocumentationChange(value) {
    setData({
      ...data,
      preTenderingStage: {
        ...data.preTenderingStage,
        hasDataAndDocumentation: value,
      },
    });
  }

  function handleSiteAnalysisReportChange(value) {
    setData({
      ...data,
      preTenderingStage: {
        ...data.preTenderingStage,
        hasSiteAnalysisReport: value,
      },
    });
  }

  function handleCreationOfDesignLayoutChange(value) {
    setData({
      ...data,
      preTenderingStage: {
        ...data.preTenderingStage,
        hasCreationOfDesignLayout: value,
      },
    });
  }

  function handleMarketAnalysisChange(value) {
    setData({
      ...data,
      preTenderingStage: {
        ...data.preTenderingStage,
        hasMarketAnalysis: value,
      },
    });
  }

  function handlePropertyCardChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasPropertyCard: value,
      },
    });
  }

  function handleConveyanceDeedChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasConveyanceDeed: value,
      },
    });
  }

  function handleCitySurveyPlanChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasCitySurveyPlan: value,
      },
    });
  }

  function handleSurveyPlanChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasSurveyPlan: value,
      },
    });
  }

  function handleLastApprovedMunicipalDrawingsChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasLastApprovedMunicipalDrawings: value,
      },
    });
  }

  function handleDPRemarksChange(value) {
    setData({
      ...data,
      documents: {
        ...data.documents,
        hasDpRemarks: value,
      },
    });
  }
  function handleDoesSocietyHasConveyanceChange(value) {
    setData({
      ...data,
      conveyance: {
        ...data.conveyance,
        doesSocietyHaveConveyance: value,
      },
    });
  }

  function handleTotalNumberOfMembersChange(value) {
    setData({
      ...data,
      consents: {
        ...data.consents,
        totalNoOfMembers: value,
      },
    });
  }

  function handleNoOfConsentsChange(value) {
    setData({
      ...data,
      consents: {
        ...data.consents,
        noOfConsents: value,
      },
    });
  }

  function handlePercentageOfMembersConsentChange(value) {
    setData({
      ...data,
      consents: {
        ...data.consents,
        percentageOfMembersConsent: value,
      },
    });
  }

  function handleSocietyConsideredChange(value) {
    setData({
      ...data,
      needAssessment: {
        ...data.needAssessment,
        hasYourSocietyConsideredForRedevelopment: value,
      },
    });
  }

  function handleAwareOfRedevelopChange(value) {
    setData({
      ...data,
      typesOfRedevelopment: {
        ...data.typesOfRedevelopment,
        hasAwareOfTypesOfRedevelopment_ChallengesBenefits: value,
      },
    });
  }

  function handleRedevelopCommitteeChange(value) {
    setData({
      ...data,
      committee: {
        ...data.committee,
        haveFormedRedevelopmentCommittee: value,
      },
    });
  }
  function percentageCalculation() {
    let percent = 0;
    if (
      data?.needAssessment?.hasYourSocietyConsideredForRedevelopment === "Yes"
    ) {
      percent = percent + 2;
    }
    if (
      data?.typesOfRedevelopment
        ?.hasAwareOfTypesOfRedevelopment_ChallengesBenefits === "Yes"
    ) {
      percent = percent + 2;
    }
    if (data?.committee?.haveFormedRedevelopmentCommittee === "Yes") {
      percent = percent + 2;
    }
    if (parseInt(data?.consents?.percentageOfMembersConsent) > 50) {
      percent = percent + 4;
    }
    if (data?.conveyance?.doesSocietyHaveConveyance === "Yes") {
      percent = percent + 4;
    }
    if (
      data?.managingCommittee?.isExistingManagingCommitteeProvisionsAct ===
      "Yes"
    ) {
      percent = percent + 4;
    }

    if (data?.documents?.hasPropertyCard === "Yes") {
      percent = percent + 4;
    }
    if (data?.documents?.hasConveyanceDeed === "Yes") {
      percent = percent + 4;
    }
    if (data?.documents?.hasCitySurveyPlan === "Yes") {
      percent = percent + 4;
    }
    if (data?.documents?.hasLastApprovedMunicipalDrawings === "Yes") {
      percent = percent + 4;
    }
    if (data?.documents?.hasDpRemarks === "Yes") {
      percent = percent + 4;
    }
    if (data?.documents?.hasSurveyPlan === "Yes") {
      percent = percent + 4;
    }

    if (
      data.appointmentOfProfessionals?.haveAppointedAnyArchitectOrPmc === "Yes"
    ) {
      percent = percent + 1;
    }

    if (data.appointmentOfProfessionals?.haveAppointedAnyAdvocate === "Yes") {
      percent = percent + 1;
    }

    if (data.appointmentOfProfessionals?.haveAppointedAnyPmc === "Yes") {
      percent = percent + 1;
    }

    if (data.appointmentOfProfessionals?.haveAppointedAnyCa === "Yes") {
      percent = percent + 1;
    }

    if (data?.preTenderingStage?.hasFsiCalculations === "Yes") {
      percent = percent + 5;
    }
    if (data?.preTenderingStage?.hasDataAndDocumentation === "Yes") {
      percent = percent + 5;
    }
    if (data?.preTenderingStage?.hasCreationOfDesignLayout === "Yes") {
      percent = percent + 5;
    }
    if (data?.preTenderingStage?.hasMarketAnalysis === "Yes") {
      percent = percent + 4;
    }

    if (data?.preTenderingStage?.hasSiteAnalysisReport === "Yes") {
      percent = percent + 5;
    }

    if (data?.tenderingStage?.hasAppointmentOfDevelopers === "Yes") {
      percent = percent + 5;
    }
    if (data?.tenderingStage?.hasPreQualificationOfDevelopers === "Yes") {
      percent = percent + 5;
    }
    if (data?.tenderingStage?.hasScrutinizationOfTenderDocuments === "Yes") {
      percent = percent + 5;
    }
    if (data?.tenderingStage?.hasSigningOfDaOrDm === "Yes") {
      percent = percent + 5;
    }
    if (data?.tenderingStage?.hasTenderingDocuments === "Yes") {
      percent = percent + 5;
    }
    if (data?.financialClosure?.haveSecuredFinancialClosure === "Yes") {
      percent = percent + 5;
    }

    return percent;
  }

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <NeedAssessment
            setNextActive={setNextActive}
            handleSocietyConsideredChange={handleSocietyConsideredChange}
            handleSocietyNameChange={handleSocietyNameChange}
            handleNameChange={handleNameChange}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
            defaultData={data?.needAssessment}
            posts={settingsNew?.needAssessment}
          />
        );
      case 1:
        return (
          <TypesofDevelopment
            setNextActive={setNextActive}
            handleAwareOfRedevelopChange={handleAwareOfRedevelopChange}
            defaultData={data?.typesOfRedevelopment}
            posts={settingsNew?.typesOfRedevelopment}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 2:
        return (
          <Committee
            setNextActive={setNextActive}
            handleRedevelopCommitteeChange={handleRedevelopCommitteeChange}
            defaultData={data?.committee}
            posts={settingsNew?.committee}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 3:
        return (
          <Consents
            setNextActive={setNextActive}
            handleTotalNumberOfMembersChange={handleTotalNumberOfMembersChange}
            handleNoOfConsentsChange={handleNoOfConsentsChange}
            handlePercentageOfMembersConsentChange={
              handlePercentageOfMembersConsentChange
            }
            defaultData={data?.consents}
            posts={settingsNew?.consents}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 4:
        return (
          <Conveyance
            setNextActive={setNextActive}
            handleDoesSocietyHasConveyanceChange={
              handleDoesSocietyHasConveyanceChange
            }
            defaultData={data?.conveyance}
            posts={settingsNew?.conveyance}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 5:
        return (
          <ValidManagingCommittee
            setNextActive={setNextActive}
            handleIsExistingManagingCommitteeChange={
              handleIsExistingManagingCommitteeChange
            }
            handleTenureInYearsChange={handleTenureInYearsChange}
            handleValidTillDateChange={handleValidTillDateChange}
            defaultData={data.managingCommittee}
            posts={settingsNew?.managingCommittee}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 6:
        return (
          <Documents
            setNextActive={setNextActive}
            handlePropertyCardChange={handlePropertyCardChange}
            handleConveyanceDeedChange={handleConveyanceDeedChange}
            handleCitySurveyPlanChange={handleCitySurveyPlanChange}
            handleSurveyPlanChange={handleSurveyPlanChange}
            handleLastApprovedMunicipalDrawingsChange={
              handleLastApprovedMunicipalDrawingsChange
            }
            handleDPRemarksChange={handleDPRemarksChange}
            defaultData={data?.documents}
            posts={settingsNew?.documents}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 7:
        return (
          <Appointment
            setNextActive={setNextActive}
            handleHaveYouAppointedChange={handleHaveYouAppointedChange}
            handleHaveYouAppointedAdvocateChange={
              handleHaveYouAppointedAdvocateChange
            }
            handleHaveYouAppointedCaChange={handleHaveYouAppointedCaChange}
            handleHaveYouAppointedPmcChange={handleHaveYouAppointedPmcChange}
            defaultData={data?.appointmentOfProfessionals}
            posts={settingsNew?.appointmentOfProfessionals}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 8:
        return (
          <PreTenderingStage
            setNextActive={setNextActive}
            handleFsiCalculationsChange={handleFsiCalculationsChange}
            handleDataAndDocumentationChange={handleDataAndDocumentationChange}
            handleSiteAnalysisReportChange={handleSiteAnalysisReportChange}
            handleCreationOfDesignLayoutChange={
              handleCreationOfDesignLayoutChange
            }
            handleMarketAnalysisChange={handleMarketAnalysisChange}
            defaultData={data?.preTenderingStage}
            posts={settingsNew?.preTenderingStage}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 9:
        return (
          <TenderingStage
            setNextActive={setNextActive}
            handleTenderingDocumentsChange={handleTenderingDocumentsChange}
            handlePreQualificationOfDevelopersChange={
              handlePreQualificationOfDevelopersChange
            }
            handleScrutinizationOfTenderDocumentsChange={
              handleScrutinizationOfTenderDocumentsChange
            }
            handleAppointmentOfDevelopersChange={
              handleAppointmentOfDevelopersChange
            }
            handleSigningOfDaDmChange={handleSigningOfDaDmChange}
            defaultData={data?.tenderingStage}
            posts={settingsNew?.tenderingStage}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 10:
        return (
          <FinancialClosure
            setNextActive={setNextActive}
            handleHaveSecuredFinancialClosureChange={
              handleHaveSecuredFinancialClosureChange
            }
            handleContactNumberChange={handleContactNumberChange}
            handleEmailIdChange={handleEmailIdChange}
            defaultData={data?.financialClosure}
            posts={settingsNew?.financialClosure}
            societyName={data?.needAssessment?.societyName}
            name={data?.needAssessment?.name}
            contactNumber={data?.financialClosure?.contactNumber}
            email={data?.financialClosure?.emailId}
          />
        );
      case 11:
        return <Submission data={data} />;

      default:
        return null;
    }
  };

  const renderContent = () => {
    return getStepContent(activeStep);
  };

  async function onSubmit(errorCallback, generateReport) {
    const percent = percentageCalculation();

    const updatedData = {
      ...data,
      percentage: percent,
      role: "SOCIETY",
      generateReport: generateReport,
    };

    try {
      await updateReadiness(updatedData, handleFailure, handleSuccess);
    } catch (error) {
      console.error("Readiness Update failed:", error);
      handleFailure();
    }
  }

  const handleSuccess = (percent, generateReport) => {
    setLoadGenerateReport(false);
    const message = `
    <div>
      <h1>Your Score is ${percent}%</h1>
      <h2>${getPercentageMessage(percent)}</h2>     
      ${
        generateReport
          ? " The report will be shared with you by email in a short period of time."
          : "Data Saved"
      }
    </div>
  `;
    setDialogMessage(message);
    setOpen(true);
  };

  const handleFailure = () => {
    setLoadGenerateReport(false);
    const message = `
    <div> 
      <h3> Failed to Save Data. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpen(true);
  };

  const handleButtonClick = () => {
    setLoadGenerateReport(false);
    setOpen(false);
  };

  const getPercentageMessage = (percentage) => {
    if (percentage === 0) {
      return "Please start your Redevelopment journey soon!";
    } else if (percentage >= 1 && percentage <= 20) {
      return "Great! Journey towards the Redevelopment Process is moving!";
    } else if (percentage >= 21 && percentage <= 40) {
      return "Promising, Keep Pushing Forward towards your Redevelopment Journey!";
    } else if (percentage >= 41 && percentage <= 60) {
      return "Commendable, Almost midway into your Journey for Redevelopment";
    } else if (percentage >= 61 && percentage <= 80) {
      return "Impressive, Keep going Strong towards your Redevelopment journey!";
    } else {
      return "Exceptional, Let's start the Construction process!";
    }
  };

  const renderFooter = () => {
    const stepCondition = activeStep === steps.length - 1;
    const [expanded, setExpanded] = useState(false);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };


    return (
      <Box
        sx={{
          m: 2,
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: { sm: "space-between" },
          alignItems: { sm: "center" },
        }}
      >
        <Dialog
          open={open}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
          }}
        >
          <Box
            sx={{
              "@media (max-width:900px)": { width: "auto !important", mb: 2 },
            }}
          >
            {activeStep === 0 ? null : (
              <Button
                color="secondary"
                variant="outlined"
                onClick={handlePrev}
                sx={{
                  fontSize: { xs: "0.65rem", sm: "0.71rem", md: "0.73rem" },
                  padding: { xs: "5px 10px", lg: "6px 16px" },
                }}
                startIcon={<Icon icon="tabler:chevron-left" />}
              >
                Previous
              </Button>
            )}
          </Box>

          <Box
            sx={{
              "@media (max-width:1600px)": {
                width: "auto !important",
                mb: 2,
                ml: 2,
              },
              "@media (max-width:600px)" : {
                ml:0,
              }
            }}
          >
            <Button
              color="primary"
              variant="contained"
              disabled={!showGenerate}
              startIcon={<Icon icon="tabler:report" />}
              sx={{
                fontSize: { xs: "0.65rem", sm: "0.71rem", md: "0.73rem" },
                padding: { xs: "5px 10px", lg: "6px 16px" },
              }}
              onClick={() => {
                setLoadGenerateReport(true);
                onSubmit((error) => {
                  console.error("On Submit Error", error);
                }, true /** Generate Report*/);
              }}
            >
              {loadGenerateReport ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Generate Report"
              )}
            </Button>
          </Box>
        </Box>

        <Box sx={{ display: "flex", marginBottom:'10px !important' }}>
          <Box
            sx={{
              "@media (max-width:900px)": { width: "auto !important" },
            }}
          >
            <Button
              size="medium"
              type="submit"
              variant="contained"
              color={"success"}
              sx={{
                color: "primary.contrastText",
                mr: 2,
                borderColor: "success.main",
                fontSize: { xs: "0.65rem", sm: "0.71rem", md: "0.73rem" },

                '@media (max-width:900px)': { width: '80px !important', mb: 2, px: 2 },
              }}
              onClick={() => {
                setLoadGenerateReport(false);
                onSubmit((error) => {
                  console.error("On Submit Error", error);
                }, false /** Generate Report*/);
              }}
            >
              Save
            </Button>
          </Box>

          <Box
            sx={{ "@media (max-width:900px)": { width: "auto !important" } }}
          >
            {stepCondition ? (
              <></>
            ) : nextActive ? (
              <Button
                type="button"
                variant="contained"
                color={stepCondition ? "success" : "primary"}
                {...(!stepCondition
                  ? { endIcon: <Icon icon="tabler:chevron-right" /> }
                  : {})}
                sx={{
                  fontSize: { xs: "0.65rem", sm: "0.71rem", md: "0.73rem" },
                  padding: { xs: "5px 10px", lg: "6px 16px" },
                }}
                onClick={() =>
                  nextActive ? handleNext() : alert("Please select any fields.")
                }
              >
                Next
              </Button>
            ) : (
              <Button
                style={styles}
                type="button"
                variant="contained"
                color={stepCondition ? "success" : "primary"}
                {...(!stepCondition
                  ? { endIcon: <Icon icon="tabler:chevron-right" /> }
                  : {})}
                sx={{
                  fontSize: { xs: "0.65rem", sm: "0.71rem", md: "0.73rem" },
                  padding: { xs: "5px 10px", lg: "6px 16px" },
                }}
                onClick={() =>
                  nextActive ? handleNext() : alert("Please select any fields.")
                }
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <>
      <div>
      <ContactCollapsibleCard
        data={data}
        errors={errors}
        handleSocietyName={handleSocietyName}
        handleName={handleName}
        handleContactNoChange={handleContactNoChange}
      />
    </div>
      <Card
        sx={{
          height: { lg: 480 },
          display: "flex",
          flexDirection: { xs: "column", lg: "row" },
          marginX: { lg: "auto" },
        }}
      >
        {!hidden ? (
          <StepperHeaderContainer>
            <ScrollWrapper hidden={hidden}>
              <StepperWrapper>
                <Stepper
                  connector={<></>}
                  orientation="vertical"
                  activeStep={activeStep}
                  sx={{ height: "100%", width: "15rem" }}
                >
                  {steps.map((step, index) => {
                    const RenderAvatar =
                      activeStep >= index ? CustomAvatar : Avatar;

                    return (
                      <Step
                        key={index}
                        onClick={() => handleStepClick(index)}
                        sx={{
                          ...(activeStep === index && {
                            paddingX: "3px",
                            "&.Mui-completed + svg": { color: "primary.main" },
                            display: "flex",
                            alignItems: "center",
                            paddingBottom: "0px !important",
                          }),
                        }}
                      >
                        <StepLabel style={{ paddingBottom: "4px" }}>
                          <div
                            className="step-label"
                            style={{ paddingBottom: "0px !important" }}
                          >
                            <RenderAvatar
                              variant="rounded"
                              {...(activeStep >= index && { skin: "light" })}
                              {...(activeStep === index && { skin: "filled" })}
                              {...(activeStep >= index && { color: "primary" })}
                              sx={{
                                paddingBottom: "0px !important",
                                fontSize: "0.8rem !important",
                                ...(activeStep === index && {
                                  boxShadow: (theme) => theme.shadows[3],
                                }),
                                ...(activeStep > index && {
                                  color: (theme) =>
                                    hexToRGBA(theme.palette.primary.main, 0.4),
                                }),
                              }}
                            >
                              <Icon icon={step.icon} />
                            </RenderAvatar>

                            <div>
                              <Typography
                                className="step-title"
                                sx={{
                                  ...(activeStep === index && {
                                    fontWeight: "bold !important",
                                  }),
                                  fontSize: "0.865rem !important", // Set font size to 0.8rem
                                  paddingBottom: "0px !important",
                                }}
                              >
                                {step.title}
                              </Typography>
                              <Typography
                                className="step-subtitle"
                                sx={{
                                  fontSize: "0.865rem !important",
                                  paddingBottom: "0px !important",
                                }}
                              >
                                {step.subtitle}
                              </Typography>
                            </div>
                          </div>
                        </StepLabel>
                      </Step>
                    );
                  })}
                </Stepper>
              </StepperWrapper>
            </ScrollWrapper>
          </StepperHeaderContainer>
        ) : (
          <HorizontalTabs
            activeStep={activeStep}
            handleStepClick={handleStepClick}
            steps={steps}
          />
        )}

        <CardContent
          sx={{
            position: "relative",
            width: hidden ? "100%" : " ",
            "@media (min-width:1200px)": {
              width: navCollapsed
                ? "calc(100vw - 26.12rem)"
                : "calc(100vw - 35.12rem)",
            },
            px: { xs: 2.75, md: 4.75 },
            pt: (theme) => `${theme.spacing(2.5)} !important`,
            pb: (theme) => `${theme.spacing(16)} !important`,
            "@media (max-width:600px)": {
              pb: (theme) => `${theme.spacing(38)} !important`,
            },
          }}
        >
          <ScrollWrapper hidden={hidden}>
            <Box>{renderContent()}</Box>
          </ScrollWrapper>
          <BoxFooter>{renderFooter()}</BoxFooter>
        </CardContent>
      </Card>
    </>
  );
};

export default ReadinessWizard;
