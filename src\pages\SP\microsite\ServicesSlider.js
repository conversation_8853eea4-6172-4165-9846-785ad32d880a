
import Slider from 'react-slick'

import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Box, Card, Typography } from '@mui/material'

const ServicesSlider = ({ slides }) => {

  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  let slidesToShow = 4; // Default to 4 slides

  // If number of slides is less than 4, update slidesToShow accordingly
  if (slides?.length < 4) {
    slidesToShow = slides.length;
  }

  const sliderSettings = {
    infinite: true,
    dots: true,
    speed: 1000,
    slidesToShow: hidden ? 1 : slidesToShow,
    slidesToScroll: 1, // Number of slides to scroll at a time
    autoplay: true,
    autoplaySpeed: 3000,
  };


  return (
    <>
      <Slider {...sliderSettings}>
        {slides?.map((slide) => (
          <div key={slide.index}>
            <Card sx={{
              position: 'relative', height: '280px',
              backgroundPosition: 'center', backgroundSize: 'cover'
            }}>
              <img src={slide.imageURL} alt={slide.serviceTitle} onError={(e) => { e.target.src = '/images/logo.png'; }} style={{ width: '100%', height: '100%' }} />
              <Box sx={{
                height: '30%',
                position: 'absolute', bottom: 0, background: 'rgb(0,0,0)',
                background: 'rgba(0 , 0, 0, 0.5)',
                width: '100%', padding: '10px'
              }}>
                <Typography fontSize={22} fontWeight={600} color={'primary.dark'}>
                  {slide.serviceTitle}
                </Typography>
              </Box>
            </Card>
          </div>
        ))}
      </Slider>
      <style jsx global>{`
        .slick-slide {
          padding: 0 10px;
        }
        .slick-slide img {
          border-radius: 6px;
          height: 220px;
          width: 100%
        }
        .slide-item {
          outline: none; // Remove outline on the slides
        }
.slick-prev,
.slick-next {
    width: 22px;
    height: 22px;
}
.slick-prev:before, .slick-next:before
{
    font-size: 30px !important;
	color: black;
}
.slick-dots{
	bottom: -44px;
	padding-bottom: 10px;
}
.slick-dots li{
	margin: 0;
	width:17px;
	height: 17px
}
.slick-dots li button:before{
	font-size: 13px;
	color: #FDF4E9
}
.slick-dots li.slick-active button:before{
	color: #FDF4E9
}

@media only screen and (max-width: 600px){
	.slick-prev, .slick-next {
		display:none !important
	}
	.slick-slide {
		padding: 0 4px; }
}
      `}
      </style>

    </>
  )
}

export default ServicesSlider;
