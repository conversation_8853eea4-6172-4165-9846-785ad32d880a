// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
import MemberShipEdit from "./MemberShipEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const MemberShipView = ({ data, expanded, userData }) => {
  
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };
  
  const hasMembership = data && data.memberShipList && data.memberShipList.length > 0
  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Membership"}
        body={
          <>
            {state3 === "view" && (
              <>
              {hasMembership ? (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick3}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Description</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {data &&
                      data?.memberShipList?.map((membership, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography
                              className="data-field"
                              marginLeft={"20px"}
                            >
                              {membership?.description}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
              ) :  (
                <Typography
                  variant="body1"
                  sx={{ textAlign: "left", mt: 3, cursor: "pointer"}}
                  onClick={viewClick3}
                >
                  Click here to add Membership
                </Typography>
              )}
            </>
          )}

            {state3 === "edit" && (
              <MemberShipEdit
                userData={userData}
                data={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default MemberShipView;
