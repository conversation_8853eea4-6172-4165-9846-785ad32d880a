import { <PERSON><PERSON>, <PERSON>, <PERSON>rid, Typography } from "@mui/material";


import NavTabsProfiles from "src/@core/components/custom-components/NavTabsProfiles";
import UsersOverView from "./UsersOverview";
import QuotationDetails from "../../quotations/QuotationDetails";
import WorkOrderDetails from "../../work-orders/WorkOrderDetails";
import Invoices from "../../transactions/invoices";
import UserViewSecurity from "../../security";
import Notifications from "src/pages/notifications";
import { useRBAC } from "src/pages/permission/RBACContext";

const AllUsers = () => {
  const { can } = useRBAC();
  if(can('spLeads_READ')){
  return (
    <Card>
      <NavTabsProfiles
        tabContent1={
          <>
          <UsersOverView/>
          </>
        }
        tabContent2={
          <>
            <QuotationDetails/>
          </>
        }
        tabContent3={
            <>
             <WorkOrderDetails/>
            </>
        }
        tabContent4={
            <>
              
            </>
        }
        tabContent5={
            <>
             <Invoices/>
            </>
        }
        tabContent6={
            <>
             <UserViewSecurity/>
            </>
        }
        tabContent7={
            <>
             
            </>
        }
        tabContent8={
            <>
             <Notifications/>
            </>
        }
        tabContent9={
            <>
             
            </>
        }
      />
    </Card>
  )}
  else{
    return null;
  }
};

export default AllUsers;
