// ** Config
import authConfig from "src/configs/auth";

export function returnEntity(response, entityCategory) {
  switch (entityCategory) {
    case "ARCHITECT":
      return response;
    case "PLUMBER":
      return response;
    case "SOCIETY":
      return response;
    case "STRUCTURAL_ENGINEER":
      return response;
    case "PMC":
      return response;
    case "BROKER":
      return response;
    case "CHARTERED_ACCOUNTANT":
      return response;
    case "LEGAL":
      return response;
    default:
      console.error("UnKnown Category at returnEntity", entityCategory);
      return null;
  }
}

export function getUrl(endpoint) {
  return authConfig.baseURL + endpoint;
}

export function getAuthorizationHeaders() {

  return {
    "Content-Type": "application/json",
    Authorization:
      "Bearer " + window.localStorage.getItem(authConfig.storageTokenKeyName),
  };
}

export function getFileUploadHeaders() {
  return {
    "Content-Type": "multipart/form-data",
    Authorization:
      "Bearer " + window.localStorage.getItem(authConfig.storageTokenKeyName),
  };
}
