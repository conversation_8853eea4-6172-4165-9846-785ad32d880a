// ** React Imports
import { forwardRef, useContext, useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";

// ** Third Party Imports

import { useForm, Controller, useWatch } from "react-hook-form";
import toast, { Toaster } from "react-hot-toast";
// ** Hooks
// ** Icon Imports
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { AuthContext } from "src/context/AuthContext";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import authConfig from "src/configs/auth";
import DocumentUploadDialog from "src/@core/components/custom-components/DocumentDialogUpload";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";
import Icon from "src/@core/components/icon";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { fi } from "date-fns/locale";

const companyTypeOptions = [
  { value: "INDIVIDUAL", key: "Individual" },
  { value: "PROPRIETORSHIP", key: "Proprietorship" },
  { value: "PARTNERSHIP", key: "Partnership" },
  { value: "HUF", key: "HUF" },
  { value: "PRIVATE_LIMITED", key: "Private Limited" },
  { value: "PUBLIC_LIMITED", key: "Public Limited" },
  { value: "AOP", key: "AOP" },
];

const typeOfClientServedOptions = [
  { value: "RESIDENTIAL", name: "Residential" },
  { value: "COMMERCIAL", name: "Commercial" },
  { value: "HEALTHCARE", name: "Healthcare" },
  { value: "HOSPITALITY", name: "Hospitality" },
  { value: "EDUCATION", name: "Education" },
  { value: "LEISURE", name: "Leisure" },
  { value: "MANUFACTURING_INDUSTRY", name: "Manufacturing Industry" },
  { value: "RETAIL", name: "Retail" },
  { value: "WAREHOUSING", name: "Warehousing" },
];

const salesTeamMembers = [
  {
    value: "0",
    key: "0",
  },
  {
    value: "1_TO_5",
    key: "1-5",
  },
  {
    value: "_5_TO_10",
    key: "5-10",
  },
  {
    value: "_10_TO_20",
    key: "10-20",
  },
  {
    value: "_20_TO_30",
    key: "20-30",
  },
  {
    value: "MORE_THAN_30",
    key: "More than 30",
  },
];

const referenceOptions = [
  { value: "Broker", key: "Broker" },
  { value: "Friend", key: "Friend" },
  { value: "Professional", key: "Professional" },
  { value: "Social Media", key: "Social Media" },
  { value: "Advertisement", key: "Advertisement" },
  { value: "Website", key: "Website" },
  { value: "Houzer Team Member", key: "Houzer Team Member" },
  { value: "Any Other", key: "Any Other" },
];

const ProfileEdit = ({
  onCancel,
  formData,
  userData,
  employeesData,
  handleAssignedToChange,
  assignedTo,
  assignedToName,
  createdBy,
}) => {
  const {
    uploadDocuments,
    getAllDocuments,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    shortFormData,
    getAllListValuesByListNameId,
    listValues,
  } = useContext(AuthContext);

  const [entityType, setEntityType] = useState(formData?.entityType);

  const [noOfSalesTeamMembers, setNoOfSalesTeamMembers] = useState(
    formData?.noOfSalesTeamMembers
  );

  const [businessType, setBusinessType] = useState(formData?.businessType);
  const [businessNature, setBusinessNature] = useState(
    formData?.businessNature
  );

  const [companyType, setCompanyType] = useState(formData?.companyType);
  const [typeOfClientServed, setTypeOfClientServed] = useState(
    formData?.typeOfClientServed
  );

  const [modalPopup, setModalPopup] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [disableButton, setDisableButton] = useState(false);

  const [selectedCompany, setSelectedCompany] = useState(false);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState(false);
  const [dailogSuccess, setDialogSuccess] = useState(false);

  const [documents, setDocuments] = useState([]);

  //Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [allServicesList, setAllServicesList] = useState([]);

  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.locationId
  );

  const [selectedYearsOfExperienceId, setSelectedYearsOfExperienceId] =
    useState(formData?.yearsOfExperienceId || "");

  const [selectedPortalsRegisteredId, setSelectedPortalsRegisteredId] =
    useState(formData?.portalsRegistered || "");

  const [selectedPortalsRegisteredName, setSelectedPortalsRegisteredName] =
    useState("");

    const [anyOtherPortalsRegistered, setAnyOtherPortalsRegistered] = useState(formData?.anyOtherPortalRegistered || "");


  const [leadStatus, setLeadStatus] = useState(formData?.leadStatus);

  const [leadPriority, setLeadPriority] = useState(formData?.leadPriority);

  const handleLeadStatusChange = (event) => {
    const selectedId = event.target.value;
    setLeadStatus(selectedId);
  };

  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };

  const [sourceGroup, setSourceGroup] = useState(formData?.sourceGroup);
  const handleSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSourceGroup(selectedId);
  };

  const [subSourceGroup, setSubSourceGroup] = useState(
    formData?.subSourceGroup
  );
  const handleSubSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSubSourceGroup(selectedId);
  };

  const [createdOn, setCreatedOn] = useState(formData?.createdOn);

  const [locationsData, setLocationsData] = useState(null);

  const [selectedLocationName, setSelectedLocationName] = useState(null);

  const [yearsOfExperienceData, setYearsOfExperienceData] = useState(null);
  const [selectedYearsOfExperienceNumber, setSelectedYearsOfExperienceNumber] =
    useState(null);

  const [portalsRegisteredData, setPortalsRegisteredData] = useState(null);

  // const [packageTypeData, setPackageTypeData] = useState(null);
  // const [selectedPackageTypeId, setSelectedPackageTypeId] = useState(formData?.packageTypeId || "");


  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);

  const [reference, setReference] = useState(formData?.reference);
  const [otherReference, setOtherReference] = useState(
    formData?.otherReference
  );

  const handleReferenceTypeChange = (e) => {
    const selectedValue = e.target.value;
    setReference(selectedValue);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  // const handlePackageTypeSuccess = (data) => {
  //   setPackageTypeData(data?.listValues);
  // };

  // const handlePackageTypeChange = (event) => {
  //   const selectedId = event.target.value;
  //   setSelectedPackageTypeId(selectedId);
  // };
  
  

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };
  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
    const selectedLocation = locationsData.find(
      (location) => location.id === selectedId
    );
    const locationName = selectedLocation ? selectedLocation.listValue : "";
    setSelectedLocationName(locationName);
  };

  const handleYearsOfExperienceChange = (event) => {
    const selectedId = event.target.value;
    setSelectedYearsOfExperienceId(selectedId);
    const selectedYearsOfExperience = yearsOfExperienceData.find(
      (yearsOfExperience) => yearsOfExperience.id === selectedId
    );
    const yearsOfExperienceNumbers = selectedYearsOfExperience
      ? selectedYearsOfExperience.listValue
      : "";
    setSelectedYearsOfExperienceNumber(yearsOfExperienceNumbers);
  };

  const handlePortalsRegisteredChange = (event) => {
    const selectedId = event.target.value;
    setSelectedPortalsRegisteredId(selectedId);
    const selectedPortalsRegistered = portalsRegisteredData.find(
      (portalsRegistered) => portalsRegistered.id === selectedId
    );
    const portalsRegisteredName = selectedPortalsRegistered
      ? selectedPortalsRegistered?.listValue
      : "";
    setSelectedPortalsRegisteredName(portalsRegisteredName);

    if (portalsRegisteredName !== "any other") {
      setAnyOtherPortalsRegistered("");
    }
  };

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  const handleYearsOfExperienceSuccess = (data) => {
    setYearsOfExperienceData(data?.listValues);
  };

  const handlePortalsRegisteredSuccess = (data) => {
    setPortalsRegisteredData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const handleDialogClose = () => {
    setSelectedCompany(null);
  };

  const handleViewIconClick = (company) => {
    setSelectedCompany(company);
  };

  async function handleDelete() {
    await documentDelete(companyToDelete, userUniqueId);
    setSelectedCompany(null);
    setConfirmDeleteDialogOpen(false);
    fetchAllDocuments();
  }

  const fetchAllDocuments = async () => {
    if (allCategories.length > 0 && allSubCategories.length > 0) {
      const allDocs = await getAllDocuments(documentJson);

      setDocuments(allDocs);
    }
  };

  useEffect(() => {
    fetchAllDocuments();
  }, []);

  useEffect(() => {
    if (companyType === "INDIVIDUAL") {
      setValue("companyName", ""); // Clear the Company Name field
    }
  }, [companyType, setValue]);
  

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationSPId,
        handleDesignationSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        handleLocationSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.yearsOfExperienceListNameId,
        handleYearsOfExperienceSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.portalsRegisteredListNameId,
        handlePortalsRegisteredSuccess,
        handleError
      );
      // getAllListValuesByListNameId(
      //   authConfig.packageTypeListNameId,
      //   handlePackageTypeSuccess,
      //   handleError
      // );
    }
  }, [authConfig]);

  const onFormInvalid = (toastError) => {
    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        toast.error(toastError[error].message);
      }
    });
  };

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusData(data?.listValues);
  };

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityData(data?.listValues);
  };
  const handleGroupDataSuccess = (data) => {
    setGroupData(data?.listValues);
  };
  const handleSubGroupDataSuccess = (data) => {
    setSubGroupData(data?.listValues);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [groupData, setGroupData] = useState(null);
  const [subGroupData, setSubGroupData] = useState(null);

  const gstOption = useWatch({ control, name: "gstOption" }); // Add useWatch to monitor gstOption
  const watchServicesProvided = useWatch({ control, name: "servicesProvided" });

  const designationName = designationsData?.find(
    (designation) => designation.id === selectedDesignationId
  )?.listValue;

  const portalsRegisteredName = portalsRegisteredData?.find(
    (portalsRegistered) => portalsRegistered.id === selectedPortalsRegisteredId
  )?.listValue;

  const servicesProvidedValues = watchServicesProvided?.map(
    (id) => allServicesList.find((service) => service.id === id)?.listValue
  );

  useEffect(() => {
    if (formData) {
      setSelectedYearsOfExperienceNumber(formData.yearsOfExperience || "");
      setSelectedYearsOfExperienceId(formData.yearsOfExperienceId || "");
      setSelectedPortalsRegisteredId(formData.portalsRegistered || "");
      setAnyOtherPortalsRegistered(formData.anyOtherPortalRegistered || "");    

    }
  }, [formData]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handleLeadPrioritySuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.groupDataListNameId,
        handleGroupDataSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        handleLeadStatusSuccess,
        handleError
      );
    }
  }, [authConfig]);

  async function submit(data) {
    data.location = selectedLocationName;
    data.locationId = selectedLocationId;
    data.designation = selectedDesignationId;
    data.reference = reference;
    data.assignedTo = assignedTo;
    data.sourceGroup = sourceGroup;
    data.subSourceGroup = subSourceGroup;
    data.leadStatus = leadStatus;
    data.leadPriority = leadPriority;
    data.yearsOfExperience = selectedYearsOfExperienceNumber;
    data.yearsOfExperienceId = selectedYearsOfExperienceId;
    data.portalsRegistered = selectedPortalsRegisteredId;
    //data.packageType = selectedPackageTypeId;

    if (Array.isArray(data?.typeOfClientServed)) {
      data.typeOfClientServed = data.typeOfClientServed.join(",");
    }

    const userUniqueId =
      userData && userData.id !== undefined ? userData?.id : user?.id;
    await auth.basicProfile(data, userUniqueId);
    onCancel();
  }

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const userUniqueId =
    userData && userData.id !== undefined ? userData.id : user?.id;

  const documentDetails = {
    userId: userUniqueId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("company"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const documentJson = {
    userId: userUniqueId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("company"),
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();

    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });

    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append("documentSectionsEnum", "COMPANY");

    // API call
    await uploadDocuments(
      formData,
      userUniqueId,
      () => {
        setModalPopup(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },

      () => {
        console.log("Failure");
      }
    );

    fetchAllDocuments();
    setLoading(false);
    setDisableButton(false);
  };

  const selectedValues = formData?.typeOfClientServed
    ? formData.typeOfClientServed.split(",").map((value) => value.trim())
    : [];

  return (
    <Box sx={{ pt: 3, height: "calc(100vh - 4px)", overflowY: "auto" }}>
      <Grid container spacing={5}>
        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: 2, mb: 2 }}
          >
            Basic Information
          </Typography>
          <Divider />
        </Grid>
        {/* Individual Name */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="individualName"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.individualName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Individual Name"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your name"
                  // error={Boolean(errors.name)}
                  // helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
        </Grid>
        
        {/* Designation */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.selectedDesignationId)}>
            <InputLabel id="designation-select-label">Designation</InputLabel>
            <Select
              labelId="designation-select-label"
              id="designation-select"
              {...register("selectedDesignationId", { required: false })}
              size="small"
              value={selectedDesignationId}
              label="Designation"
              onChange={handleSelectDesignationChange}
            >
              {designationsData?.map((designation) => (
                <MenuItem key={designation.id} value={designation.id}>
                  {designation.listValue}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {errors.selectedDesignationId && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-selectedDesignationId"
            >
              {errors.selectedDesignationId?.message}
            </FormHelperText>
          )}
        </Grid>

        {designationName === "Any Other" && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="otherDesignation"
                control={control}
                defaultValue={formData?.otherDesignation || ""}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Specify Designation"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter your designation"
                    error={Boolean(errors.otherDesignation)}
                    helperText={errors.otherDesignation?.message}
                    aria-describedby="validation-basic-other-designation"
                  />
                )}
              />
              {errors.otherDesignation && (
                <FormHelperText sx={{ color: "error.main" }}>
                  {errors.otherDesignation.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
        )}

        {/* Email */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Grid container alignItems="center" wrap="nowrap">
              <Grid item>
                <Typography
                  fontSize="body1"
                  sx={{ fontWeight: "bold", mr: 1, ml: 1 }}
                >
                  Email:
                </Typography>
              </Grid>
              <Grid
                item
                xs
                container
                alignItems="center"
                justifyContent="flex-start"
                sx={{ overflow: "hidden" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                  }}
                >
                  {formData?.email}
                </Typography>
              </Grid>
              <Grid item>
                <CheckCircleOutlineIcon
                  sx={{ color: "green", marginLeft: 1 }}
                />
              </Grid>
            </Grid>
          </FormControl>
        </Grid>

        {/* Mobile No. */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Mobile Number"
                  size="small"
                  error={Boolean(errors.mobileNumber)}
                  helperText={errors.mobileNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
            {/* {errors.mobileNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Alt Mob. No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="alternateMobileNumber"
              control={control}
              defaultValue={formData?.alternateMobileNumber}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="tel"
                  label="Alternate Number"
                  size="small"
                  error={Boolean(errors.alternateMobileNumber)}
                  helperText={errors.alternateNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        {/* Website URl */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              rules={{
                required: false,
                pattern: /^(ftp|http|https):\/\/[^ "]+$/,
              }}
              defaultValue={formData?.websiteUrl}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="url"
                  value={value}
                  size="small"
                  label="Website URL"
                  onChange={onChange}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.websiteUrl)}
                  placeholder="https://www.example.com"
                  aria-describedby="validation-websiteUrl"
                />
              )}
            />
            {/* {errors.websiteUrl?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-websiteUrl"
              >
                Website URL is required
              </FormHelperText>
            )}
            {errors.websiteUrl?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-websiteUrl"
              >
                Please enter a valid Website URL
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Social Media Presence Field */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="socialMediaPresence"
              control={control}
              rules={{ required: false }} // Add any validation rules here
              defaultValue={formData?.socialMediaPresence} // Set default value if available
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Social Media Presence"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your social media presence"
                  error={Boolean(errors.socialMediaPresence)}
                  helperText={errors.socialMediaPresence?.message}
                  aria-describedby="validation-basic-social-media"
                />
              )}
            />
            {/* {errors.socialMediaPresence?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: 2, mb: 2 }}
          >
            Business Information
          </Typography>
          <Divider />
        </Grid>

        {/* Multi-Select Servcies Providing*/}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.servicesProvided)}>
            <InputLabel style={{ zIndex: 0 }}>Type of Profession</InputLabel>
            <Controller
              name="servicesProvided"
              control={control}
              defaultValue={formData?.servicesProvided || []}
              rules={{ required: false }}
              render={({ field }) => (
                <Select
                  multiple
                  size="small"
                  labelId="service-providing"
                  label="Select Services Providing"
                  {...field}
                  value={field.value || []}
                  onChange={(event) => {
                    field.onChange(event.target.value);
                  }}
                  renderValue={(selected) => (
                    <span>
                      {selected
                        .map(
                          (selectedValue) =>
                            allServicesList.find(
                              (service) => service.id === selectedValue
                            )?.listValue
                        )
                        .join(", ")}
                    </span>
                  )}
                >
                  {allServicesList.map((service) => (
                    <MenuItem key={service.id} value={service.id}>
                      {service.listValue}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            {/* {errors.servicesProvided && (
      <FormHelperText>{errors.servicesProvided.message}</FormHelperText>
    )} */}
          </FormControl>
        </Grid>

        {servicesProvidedValues &&
          servicesProvidedValues.includes("Any other") && (
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="anyOtherServiceProvided"
                  control={control}
                  defaultValue={formData?.anyOtherServiceProvided || ""}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Specify Profession Type"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter your profession type"
                      error={Boolean(errors.anyOtherServiceProvided)}
                      helperText={errors.anyOtherServiceProvided?.message}
                      aria-describedby="validation-basic-other-service"
                      value={field.value}
                    />
                  )}
                />
                {/* {errors.anyOtherServiceProvided && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherServiceProvided.message}
        </FormHelperText>
      )} */}
              </FormControl>
            </Grid>
          )}

        {/* Company Type */}
        <Grid item xs={12} sm={4}>
          <SelectCategory
            register={() =>
              register("companyType", { required: "this field is required" })
            }
            id={"companyType"}
            label={"Registration Type"}
            name="companyType"
            nameArray={companyTypeOptions}
            value={companyType}
            defaultValue={formData?.companyType}
            onChange={(e) => setCompanyType(e.target.value)}
            clearErrors={clearErrors}
            error={Boolean(errors.companyType)}
            aria-describedby="validation-companyType"
          />
          {errors.companyType && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-companyType"
            >
              Please select company type
            </FormHelperText>
          )}
        </Grid>

        {companyType !== "INDIVIDUAL" && (
  <Grid item xs={12} sm={4}>
    <FormControl fullWidth>
      <Controller
        name="companyName"
        control={control}
        defaultValue={formData?.companyName || ""}
        rules={{ required: false }}
        render={({ field }) => (
          <TextField
            {...field}
            label="Company Name"
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Enter your company name"
            error={Boolean(errors.companyName)}
            aria-describedby="Section1-companyName"
          />
        )}
      />
      {/* {errors.companyName && (
      <FormHelperText sx={{ color: "error.main" }} id="Section1-companyName">
        {errors.companyName.message}
      </FormHelperText>
    )} */}
    </FormControl>
  </Grid>
)}


        {/* Address */}
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={2}
                  multiline
                  {...field}
                  label="Address"
                  error={Boolean(errors.address)}
                  aria-describedby="Section1-address"
                />
              )}
            />
            {errors.address && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="Section1-address"
              >
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {/* Location */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.selectedLocationId)}>
            <InputLabel id="location-select-label">Location</InputLabel>
            <Select
              labelId="location-select-label"
              id="location-select"
              {...register("selectedLocationId", {
                required: false,
              })}
              size="small"
              value={selectedLocationId}
              label="Location"
              onChange={handleSelectChange}
            >
              {locationsData?.map((location) => (
                <MenuItem key={location.id} value={location.id}>
                  {location.listValue}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* {errors.selectedLocationId && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-selectedLocationId"
            >
              {errors.selectedLocationId?.message}
            </FormHelperText>
          )} */}
        </Grid>

        {/* Portals Registered Field */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="portals-registered-label">
              Portals Registered
            </InputLabel>
            <Controller
              name="portalsRegistered"
              control={control}
              defaultValue={formData?.portalsRegistered || ""}
              render={({ field }) => (
                <Select
                  {...field}
                  size="small"
                  labelId="portals-registered-label"
                  label="Portals Registered"
                  onChange={(e) => {
                    handlePortalsRegisteredChange(e);
                    field.onChange(e);
                  }}
                  value={selectedPortalsRegisteredId}
                >
                  {portalsRegisteredData?.map((portalsRegistered) => (
                    <MenuItem
                      key={portalsRegistered.id}
                      value={portalsRegistered.id}
                    >
                      {portalsRegistered?.listValue}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {portalsRegisteredName === "any other" && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="anyOtherPortalRegistered"
                control={control}
                rules={{ required: false }}
                defaultValue={anyOtherPortalsRegistered}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Specify Portals Registered"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter more details"
                    error={Boolean(errors.anyOtherPortalRegistered)}
                    helperText={errors.anyOtherPortalRegistered?.message}
                    aria-describedby="validation-basic-other-portals-registered-details"
                  />
                )}
              />
              {/* {errors.anyOtherPortalsRegistered && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherPortalsRegistered.message}
        </FormHelperText>
        )} */}
            </FormControl>
          </Grid>
        )}

        {/* GST No. */}

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="gst-label">Do you have GST?</InputLabel>
            <Controller
              name="gstOption"
              control={control}
              defaultValue={formData?.gstOption || ""}
              render={({ field }) => (
                <Select
                  {...field}
                  size="small"
                  labelId="gst-label"
                  label="Do you have GST?"
                >
                  <MenuItem value="yes">Yes</MenuItem>
                  <MenuItem value="no">No</MenuItem>
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {gstOption === "yes" && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="gstNo"
                control={control}
                rules={{ required: false }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="GST No."
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your GST No."
                    error={Boolean(errors.gstNo)}
                    helperText={errors.gstNo ? "GST No. is required" : ""}
                  />
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* PAN NO. */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="panNo"
              control={control}
              defaultValue={formData?.panNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="PAN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your PAN No."
                  error={Boolean(errors.panNo)}
                  helperText={errors.panNo?.message}
                  aria-describedby="validation-basic-panNo"
                  inputProps={{
                    maxLength: 10, // Limits the input to 10 characters
                  }}
                />
              )}
            />
            {/* {errors.panNo?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Pan No is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* TAN No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="tanNo"
              control={control}
              defaultValue={formData?.tanNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="TAN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your TAN No."
                />
              )}
            />
          </FormControl>
        </Grid>
        {/* CIN No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="cinNo"
              control={control}
              defaultValue={formData?.cinNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="CIN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your CIN No."
                />
              )}
            />
          </FormControl>
        </Grid>
        {/* years of experience */}

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="years-of-experience-label">
              Years of Experience
            </InputLabel>
            <Controller
              name="yearsOfExperience"
              control={control}
              defaultValue={selectedYearsOfExperienceId}
              render={({ field }) => (
                <Select
                  {...field}
                  size="small"
                  labelId="years-of-experience-label"
                  label="Years of Experience"
                  onChange={(e) => {
                    handleYearsOfExperienceChange(e);
                    field.onChange(e);
                  }}
                  value={selectedYearsOfExperienceId} // Ensure value is linked to state
                >
                  {yearsOfExperienceData?.map((yearsOfExperience) => (
                    <MenuItem
                      key={yearsOfExperience.id}
                      value={yearsOfExperience.id}
                    >
                      {yearsOfExperience.listValue}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {/* No of Team  Members */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="teamSize"
              control={control}
              rules={{
                required: false,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Number of Team Members"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder="Enter number of team members (1-100)"
                  aria-describedby="validation-noOfTeamMembers"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {/* {errors.teamSize?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-teamSize"
              >
                This field is required
              </FormHelperText>
            )} */}
            {errors.noOfTeamMembers?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-noOfTeamMembers"
              >
                Please enter a valid number of team members (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* no of sales team members */}
        <Grid item xs={12} sm={4}>
          <SelectCategory
            clearErrors={clearErrors}
            register={() =>
              register("noOfSalesTeamMembers", { required: false })
            }
            id={"noOfSalesTeamMembers"}
            label={"Number of Sales Team Members"}
            name="noOfSalesTeamMembers"
            nameArray={salesTeamMembers}
            defaultValue={formData?.noOfSalesTeamMembers}
            value={noOfSalesTeamMembers}
            onChange={(e) => setNoOfSalesTeamMembers(e.target.value)}
            error={Boolean(errors.noOfSalesTeamMembers)}
            aria-describedby="validation-noOfSalesTeamMembers"
          />
          {/* {errors.noOfSalesTeamMembers && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-noOfSalesTeamMembers"
            >
              {errors.noOfSalesTeamMembers?.message}
            </FormHelperText>
          )} */}
        </Grid>

        {/* type of client Served */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.typeOfClientServed)}>
            <InputLabel style={{ zIndex: 0 }}>Type of Client Served</InputLabel>
            <Controller
              name="typeOfClientServed"
              control={control}
              defaultValue={selectedValues} // Prepopulate selected values
              rules={{ required: false }}
              render={({ field }) => (
                <Select
                  multiple
                  size="small"
                  labelId="type-of-client-served"
                  label="Select Type of Client Served"
                  value={field.value || []}
                  onChange={(event) => {
                    field.onChange(event.target.value);
                  }}
                  renderValue={(selected) => (
                    <span>
                      {selected
                        .map(
                          (selectedValue) =>
                            typeOfClientServedOptions.find(
                              (option) => option.value === selectedValue
                            )?.name
                        )
                        .join(", ")}
                    </span>
                  )}
                >
                  {typeOfClientServedOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.name}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            {/* {errors.typeOfClientServed && (
              <FormHelperText>
                {errors.typeOfClientServed.message}
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {/* Field for lastThreeYearsTurnOver */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="lastThreeYearsTurnOver"
              control={control}
              rules={{
                required: false,
                pattern: /^[0-9]+$/,
              }}
              defaultValue={formData?.lastThreeYearsTurnOver}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Last 3 Years Turnover (in Lakhs)"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.lastThreeYearsTurnOver)}
                  placeholder="Enter turnover (in Lakhs)"
                  aria-describedby="validation-lastThreeYearsTurnOver"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {errors.lastThreeYearsTurnOver?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-lastThreeYearsTurnOver"
              >
                This field is required
              </FormHelperText>
            )}
            {/* {errors.lastThreeYearsTurnOver?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-lastThreeYearsTurnOver"
              >
                Please enter a valid value for Turnover (in Lakhs)
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Field for completedProjectsOrCases */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="completedProjectsOrCases"
              control={control}
              rules={{
                required: false,
                min: 0,
              }}
              defaultValue={formData?.completedProjectsOrCases}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Completed Projects or Cases"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.completedProjectsOrCases)}
                  placeholder="Enter completed projects or cases"
                  aria-describedby="validation-completedProjectsOrCases"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {/* {errors.completedProjectsOrCases?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-completedProjectsOrCases"
              >
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Field for onGoingProjectsOrCases */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="onGoingProjectsOrCases"
              control={control}
              rules={{
                required: false,
                min: 0,
              }}
              defaultValue={formData?.onGoingProjectsOrCases}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Ongoing Projects or Cases"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.onGoingProjectsOrCases)}
                  placeholder="Enter ongoing projects or cases"
                  aria-describedby="validation-onGoingProjectsOrCases"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {/* {errors.onGoingProjectsOrCases?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-onGoingProjectsOrCases"
              >
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: 2, mb: 2 }}
          >
            Bank Details
          </Typography>
          <Divider />
        </Grid>

        {/* Bank Name */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="bankName"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.bankName}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Bank Name"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter Bank Name"
                  error={Boolean(errors.bankName)}
                  helperText={errors.bankName?.message}
                  aria-describedby="validation-basic-bankName"
                />
              )}
            />
            {errors.bankName?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {/* branch */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="branch"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.branch}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Branch"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter branch name"
                  error={Boolean(errors.branch)}
                  helperText={errors.branch?.message}
                  aria-describedby="validation-basic-branch"
                />
              )}
            />
            {/* {errors.branch?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {/* Account nUmber */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="accountNumber"
              control={control}
              rules={{
                required: false,
                maxLength: 18,
                validate: {
                  isAlphanumeric: (value) =>
                    /^[a-zA-Z0-9]*$/.test(value) ||
                    "Please enter a valid alphanumeric account number",
                },
              }}
              defaultValue={formData?.accountNumber}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="text"
                  value={value}
                  onChange={onChange}
                  size="small"
                  label="Account Number"
                  InputLabelProps={{ shrink: true }}
                  inputProps={{
                    maxLength: 18,
                  }}
                  error={Boolean(errors.accountNumber)}
                  placeholder="Enter account number"
                />
              )}
            />
            {/* {errors.accountNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
            {errors.accountNumber?.type === "maxLength" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Account number cannot exceed 18 characters
              </FormHelperText>
            )}
            {errors.accountNumber?.type === "pattern" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Please enter a valid alphanumeric account number
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* ifsc code*/}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="ifscCode"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.ifscCode}
              render={({ field }) => (
                <TextField
                  {...field}
                  onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                  label="IFSC Code"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter IFSC Code"
                  error={Boolean(errors.ifscCode)}
                  helperText={errors.ifscCode?.message}
                  aria-describedby="validation-basic-ifscCode"
                  inputProps={{
                    maxLength: 11,
                  }}
                />
              )}
            />
            {/* {errors.ifscCode?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid> 

        {userData && userData.id !== undefined && (
          <>
            <Grid
              item
              xs={12}
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2, mb: 2 }}
              >
                Status & Assignment Details
              </Typography>
              <Divider />
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel id="assigned-to">Assigned To</InputLabel>
                <Select
                  labelId="assigned-to"
                  id="assigned-to"
                  size="small"
                  value={assignedTo}
                  label="Assigned To"
                  placeholder="Assigned To"
                  onChange={handleAssignedToChange}
                >
                  {employeesData
                    ?.map((data) => ({
                      id: data.id,
                      label: data.name,
                    }))
                    .map((emp) => (
                      <MenuItem key={emp.id} value={emp.id}>
                        {emp.label}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel id="lead-status">Lead Status</InputLabel>
                <Select
                  labelId="lead-status"
                  id="lead-status-id"
                  size="small"
                  value={leadStatus}
                  label="Lead Status"
                  defaultValue={formData?.leadStatus}
                  onChange={handleLeadStatusChange}
                >
                  {leadStatusData?.map((status) => (
                    <MenuItem key={status.id} value={status.id}>
                      {status.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.leadStatus && (
                  <FormHelperText
                    id="validation-leadStatus"
                    sx={{ color: "error.main" }}
                  >
                    {errors.leadStatus.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel id="lead-priority">Lead Priority</InputLabel>
                <Select
                  labelId="lead-priority"
                  id="lead-priority"
                  size="small"
                  value={leadPriority}
                  label="Lead Priority"
                  defaultValue={formData?.leadPriority}
                  onChange={handleLeadPriorityChange}
                >
                  {leadPriorityData?.map((priority) => (
                    <MenuItem key={priority.id} value={priority.id}>
                      {priority.listValue}
                    </MenuItem>
                  ))}
                </Select>
                {errors.leadPriority && (
                  <FormHelperText
                    id="validation-leadPriority"
                    sx={{ color: "error.main" }}
                  >
                    {errors.leadPriority.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <SelectCategory
                clearErrors={clearErrors}
                register={() => register("reference", { required: false })}
                id={"reference"}
                label={"Reference Type"}
                name="reference"
                nameArray={referenceOptions}
                defaultValue={formData?.reference}
                value={reference}
                onChange={handleReferenceTypeChange}
                error={Boolean(errors.reference)}
                aria-describedby="validation-reference"
              />
              {/* {errors.reference && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-reference"
            >
              please select reference type
            </FormHelperText>
          )} */}
            </Grid>
            {/* other reference */}
            {reference && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="otherReference"
                    control={control}
                    defaultValue={formData?.otherReference}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Referral Name"
                        size="small"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Enter Referral Name"
                        error={Boolean(errors.otherReference)}
                        helperText={errors.otherReference?.message}
                        aria-describedby="validation-basic-otherReference"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            )}
            {/* <Grid item xs={12} sm={4}></Grid>
            {/* Package Type */}
            {/* <Grid item xs={12} sm={4}>
  <FormControl fullWidth>
    <InputLabel id="package-type-label">Package Type</InputLabel>
    <Controller
      name="packageType"
      control={control}
      defaultValue={formData?.packageType}
      render={({ field }) => (
        <Select
          {...field}
          size="small"
          labelId="package-type-label"
          label="Package Type"
          onChange={(e) => {
            handlePackageTypeChange(e);
            field.onChange(e);
          }}
          value= {selectedPackageTypeId}
        >
          {packageTypeData?.map((packageType) => (
            <MenuItem key={packageType.id} value={packageType.id}>
              {packageType.listValue}
            </MenuItem>
          ))}
        </Select>
      )}
    />
  </FormControl>
</Grid> */}




            {/* <Grid item xs={12} sm={4}></Grid>  */}

            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Grid container spacing={1} alignItems="center">
                  <Grid item xs={12} sm={2}>
                    <Typography>Created On: </Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography
                      variant="h6"
                      style={{ fontSize: "16px", fontWeight: "500" }}
                    >
                      {formData.createdOn}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Typography>Created By</Typography>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Typography variant="h6" style={{ fontSize: "14px" }}>
                      {createdBy}
                    </Typography>
                  </Grid>
                </Grid>
              </FormControl>
            </Grid>

            <Grid item sm={8} xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="remarks"
                  control={control}
                  defaultValue={formData?.remarks}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Smart Summary"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.remarks)}
                      helperText={errors.remarks?.message}
                      aria-describedby="statusAssignmentDetails_remarks"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </>
        )}

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: 2, mb: 2 }}
          >
            Uploads
          </Typography>
          <Divider />
        </Grid>

        <Grid item xs={12}>
          <Button
            aria-controls="simple-menu"
            aria-haspopup="true"
            onClick={() => {
              setModalPopup(true);
            }}
            variant="contained"
            sx={{ px: 4, ml: 2 }}
          >
            Upload Profile
          </Button>
        </Grid>

        {documents && documents?.data && documents?.data?.length > 0 && (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Upload Profile</TableCell>
                <TableCell>Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {documents.data.map((company, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography className="data-field">
                      {company && company.split("/").pop()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => {
                        setConfirmDeleteDialogOpen(true);
                        setCompanyToDelete(company);
                      }}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                    <IconButton
                      onClick={() => handleViewIconClick(company)}
                      color="error"
                      disabled={selectedCompany}
                    >
                      <Icon icon="iconamoon:eye" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        <DocumentUploadDialog
          open={modalPopup}
          onClose={() => setModalPopup(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          loading={loading}
          disableButton={disableButton}
        />

        <Grid>
          <Dialog
            open={dailogSuccess}
            onClose={handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            PaperProps={{
              sx: {
                p: (theme) => `${theme.spacing(2.5)} !important`,
                backgroundColor: (theme) => theme.palette.primary.background,
              },
            }}
          >
            <Box
              sx={{
                width: "100%",
                borderRadius: 1,
                textAlign: "center",
                border: (theme) => `1px solid ${theme.palette.divider}`,
                borderColor: "primary.main",
              }}
            >
              <DialogContent>
                <DialogContentText
                  id="alert-dialog-description"
                  color="primary.main"
                >
                  Successfully uploaded
                </DialogContentText>
              </DialogContent>
              <DialogActions>
                <Button
                  variant="contained"
                  onClick={handleClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Ok
                </Button>
        </DialogActions>
            </Box>
          </Dialog>
          <ViewDialogByLocation
            location={selectedCompany}
            setSelectedLocation={setSelectedCompany}
            onClose={handleDialogClose}
          />
          <DeleteConfirmationDialog
            open={confirmDeleteDialogOpen}
            onClose={() => setConfirmDeleteDialogOpen(false)}
            onConfirm={handleDelete}
          />
        </Grid>

        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit, onFormInvalid)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProfileEdit;
