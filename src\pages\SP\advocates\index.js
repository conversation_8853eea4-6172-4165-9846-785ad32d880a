// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Config
import authConfig from "src/configs/auth";

// ** Styled Component
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import {
  Box,
  Button,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import { useTheme } from "@emotion/react";

import CompanyDetailsAdvocate from "./sections/CompanyDetailsAdvocate";
import TeamDetailsAdvocates from "./sections/TeamDetailsAdvocates.js";
import ServiceDetailsAdvocates from "./sections/ServiceDetailsAdvocates";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";

import axios from "axios";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const AdvocateForm = () => {
  const { entityData, getEntityProfile } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    getEntityProfile();
    console.log("use effect");
  }, []);

  return (
    <>
      <style>
        {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
      </style>

      <DatePickerWrapper>
        <Grid container spacing={6} className="match-height"
        sx={{'& .MuiPaper-root.MuiAccordion-root':{margin:'0 !important'}}} >
          <Grid item xs={12} sx={{ position: "relative" }}>
            <PageHeader
              title={
                <Typography variant="h5">
                  Advocates/ Solicitors Registrationn
                </Typography>
              }
              subtitle={<Typography variant="body2"></Typography>}
            />
            <Box sx={{ position: "absolute", top: 18, right: 0 }}>
              <CloseExpandIcons expanded={expanded} onToggle={handleToggle} />
            </Box>
          </Grid>

          <Grid item xs={12}>
            <CompanyDetailsAdvocate
              data={entityData}
              expanded={expanded}
            ></CompanyDetailsAdvocate>
          </Grid>

          <Grid item xs={12}>
            <TeamDetailsAdvocates
              data={entityData}
              expanded={expanded}
            ></TeamDetailsAdvocates>
          </Grid>

          <Grid item xs={12}>
            <ServiceDetailsAdvocates
              data={entityData}
              expanded={expanded}
            ></ServiceDetailsAdvocates>
          </Grid>
        </Grid>
      </DatePickerWrapper>
    </>
  );
};

export default AdvocateForm;
