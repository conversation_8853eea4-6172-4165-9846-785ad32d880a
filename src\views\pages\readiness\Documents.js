// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import FormControlLabel from "@mui/material/FormControlLabel";

import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import { FormControl, Radio, RadioGroup } from "@mui/material";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";

const Documents = ({
  setNextActive,
  posts,
  handlePropertyCardChange,
  handleConveyanceDeedChange,
  handleCitySurveyPlanChange,
  handleSurveyPlanChange,
  handleLastApprovedMunicipalDrawingsChange,
  handleDPRemarksChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
  // ** Hook
  const theme = useTheme();

  const {
    settings: { direction },
  } = useSettings();

  const [propertyCard, setPropertyCard] = useState(
    defaultData?.hasPropertyCard
  );
  const [conveyanceDeed, setConveyanceDeed] = useState(
    defaultData?.hasConveyanceDeed
  );
  const [citySurveyPlan, setCitySurveyPlan] = useState(
    defaultData?.hasCitySurveyPlan
  );
  const [surveyPlan, setSurveyPlan] = useState(defaultData?.hasSurveyPlan);
  const [lastApprovedMunicipal, setLastApprovedMunicipal] = useState(
    defaultData?.hasLastApprovedMunicipalDrawings
  );
  const [dpRemarks, setDpRemarks] = useState(defaultData?.hasDpRemarks);
  const [documents, setDocuments] = useState([]);

  const handleChange = (event) => {
    const { name, value } = event.target;

    switch (name) {
      case "property-card":
        setPropertyCard(value);
        handlePropertyCardChange(value);
        break;
      case "conveyance-deed":
        setConveyanceDeed(value);
        handleConveyanceDeedChange(value);
        break;
      case "city-survey-plan":
        setCitySurveyPlan(value);
        handleCitySurveyPlanChange(value);
        break;
      case "survey-plan":
        setSurveyPlan(value);
        handleSurveyPlanChange(value);
        break;
      case "last-approved-municipal":
        setLastApprovedMunicipal(value);
        handleLastApprovedMunicipalDrawingsChange(value);
        break;
      case "dp-remarks":
        setDpRemarks(value);
        handleDPRemarksChange(value);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setDocuments(data);
    }
  }, [posts]);
  useEffect(() => {
    if (
      propertyCard &&
      conveyanceDeed &&
      citySurveyPlan &&
      surveyPlan &&
      lastApprovedMunicipal &&
      dpRemarks && name && societyName &&email&& contactNumber
    ) {
      setNextActive(true);
    }else {
      setNextActive(false);
    }
  }),
    [
      propertyCard,
      conveyanceDeed,
      citySurveyPlan,
      surveyPlan,
      lastApprovedMunicipal,
      dpRemarks,email,contactNumber,societyName,name
    ];

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 3,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold", fontSize:{xs:'1rem !important',lg:'1.2rem !important'} }}>
                Do you have the following Documents?
              </Typography>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mt: 3, width: { md: "65%" } }}
          >
            {console.log(defaultData, "rani")}

            <Typography
              variant="h6"
              sx={{ mb: 1, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              a. Property Card
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={propertyCard}
                  name="property-card"
                  onChange={handleChange}
                  aria-label="property-card"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='propertyCard-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          Yes
                        </Typography>
                      }
                      checked={propertyCard === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='PropertyCard-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          No
                        </Typography>
                      }
                      checked={propertyCard === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'} }}
            >
              b. Conveyance Deed
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={conveyanceDeed}
                  name="conveyance-deed"
                  onChange={handleChange}
                  aria-label="conveyance-deed"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='conveyanceDeed-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          Yes
                        </Typography>
                      }
                      checked={conveyanceDeed === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='ConveyanceDeed-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          No
                        </Typography>
                      }
                      checked={conveyanceDeed === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              c. City Survey Plan
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={citySurveyPlan}
                  name="city-survey-plan"
                  onChange={handleChange}
                  aria-label="city-survey-plan"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio  id='citySurveyPlan-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          Yes
                        </Typography>
                      }
                      checked={citySurveyPlan === "Yes"}
                    />
                    <FormControlLabel 
                      value="No"
                      control={<Radio id='citySurveyPlan-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          No
                        </Typography>
                      }
                      checked={citySurveyPlan === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold" , fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'}}}
            >
              d. Survey Plan
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={surveyPlan}
                  name="survey-plan"
                  onChange={handleChange}
                  aria-label="survey-plan"
                >
                  <Box display="flex">
                    <FormControlLabel 
                      value="Yes"
                      control={<Radio id='surveyPlan-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          Yes
                        </Typography>
                      }
                      checked={surveyPlan === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='surveyPlan-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold" , fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                          No
                        </Typography>
                      }
                      checked={surveyPlan === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'} }}
            >
              e. Last Approved Municipal set of drawings
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={lastApprovedMunicipal}
                  name="last-approved-municipal"
                  onChange={handleChange}
                  aria-label="last-approved-municipal"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='municipaldrawings-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          Yes
                        </Typography>
                      }
                      checked={lastApprovedMunicipal === "Yes"}
                    />
                    <FormControlLabel
                      value="No"
                      control={<Radio id='Municipaldrawings-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          No
                        </Typography>
                      }
                      checked={lastApprovedMunicipal === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{ width: { md: "65%" } }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 0, mr: { xs: 2, md: 16 }, fontWeight: "bold", fontSize:{xs: '0.8rem !important', lg:'1rem !important', md:'1rem !important', sm:'1rem !important'} }}
            >
              f. DP Remarks
            </Typography>
            <Box display="flex" alignItems="center">
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={dpRemarks}
                  name="dp-remarks"
                  onChange={handleChange}
                  aria-label="dp-remarks"
                >
                  <Box display="flex">
                    <FormControlLabel
                      value="Yes"
                      control={<Radio id='dpRemarks-yes' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          Yes
                        </Typography>
                      }
                      checked={dpRemarks === "Yes"}
                    />
                    <FormControlLabel 
                      value="No"
                      control={<Radio id='dpRemarks-no' sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' , 
                        transform: 'scale(0.8)'}}}/>}
                      label={
                        <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}  }}>
                          No
                        </Typography>
                      }
                      checked={dpRemarks === "No"}
                    />
                  </Box>
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
          {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
          {documents.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(2)} !important`,
                  mb: `${theme.spacing(2)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {documents.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={documents} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default Documents;
