// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";


// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import DataDetailsEdit from "./DataDetailsEdit";



const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const DataDetailsView = ({ masterDataType,data, expanded, fetchUsers }) => {
  // ** Hook
  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Master Data Details"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {" "}
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}> {masterDataType === 'LOCATION_FSI' && 'Location'}
                    {masterDataType === 'TYPE_FSI' && 'Type'}
                    {masterDataType === 'CITY_FSI' && 'city'}
                    {masterDataType === 'WARD_FSI' && 'Ward'} Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Active:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.isActive? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <DataDetailsEdit  onCancel={editClick} formData={data} fetchUsers={fetchUsers} masterDataType={masterDataType} />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default DataDetailsView;
