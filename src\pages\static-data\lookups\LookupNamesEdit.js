// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { DialogActions, FormControlLabel, Switch } from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useState } from "react";




const LookupNamesEdit = ({ onCancel, formData,fetchUsers }) => {


  const [isActive, setIsActive] = useState(formData?.isActive); 

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  //Hooks
  const auth = useAuth();

  
  
  
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  async function submit(data) {   

    const fields = {
      id:formData.id,
      name: data.name.trim(),
      isActive:isActive, 
      
    };


    const response = await auth.patchListNames(fields, () => {
      console.error(" Editing list name failed");
    });

    const currentPage = 1;  
    const currentPageSize = 10;

    fetchUsers(currentPage, currentPageSize);

    onCancel();
  }

  


  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: 'Service name is required' }} 
              defaultValue={formData?.name}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="Section1-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={3} sm={6} sx={{  mb: 1.5 }}>
      <Controller
        name="isActive" 
        control={control}
        render={() => (
          <FormControlLabel
            control={
              <Switch
                checked={isActive} 
                onChange={handleOnChange}
                name="isActive" 
              />
            }
            label="Is Active"
          />
        )}
      />
    </Grid>


        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit" 
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LookupNamesEdit;
