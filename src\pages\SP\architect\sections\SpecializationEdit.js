// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";

const SpecializationEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [mhada, setMhada] = useState(formData?.mhada);
  const [mcgm, setMcgm] = useState(formData?.mcgm);
  const [srasra, setSrasra] = useState(formData?.srasra);
  const [collector, setCollector] = useState(formData?.collector);
  const [allSpecializations, setAllSpecializations] = useState(
    formData?.allSpecializations
  );
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(true);

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    const isAnyCheckboxChanged =
      mhada !== formData?.mhada ||
      mcgm !== formData?.mcgm ||
      srasra !== formData?.srasra ||
      collector !== formData?.collector;
    setIsSaveButtonDisabled(!isAnyCheckboxChanged);
  }, [mhada, mcgm, srasra, collector]);
  useEffect(() => {
    if (mhada && mcgm && srasra && collector) {
      setValue("allSpecializations", true);
      setAllSpecializations(true);
    } else {
      setValue("allSpecializations", false);
      setAllSpecializations(false);
    }
  }, [mcgm, mhada, srasra, collector]);

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 1.5 }}>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="allSpecializations"
                control={control}
                defaultValue={
                  formData?.allSpecializations
                    ? formData.allSpecializations
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("allSpecializations", true);
                            setAllSpecializations(true);

                            setValue("mhada", true);
                            setMhada(true);
                            setValue("mcgm", true);
                            setMcgm(true);
                            setValue("srasra", true);
                            setSrasra(true);
                            setValue("collector", true);
                            setCollector(true);
                          } else {
                            setValue("allSpecializations", false);
                            setAllSpecializations(false);

                            setValue("mhada", false);
                            setMhada(false);
                            setValue("mcgm", false);
                            setMcgm(false);
                            setValue("srasra", false);
                            setSrasra(false);
                            setValue("collector", false);
                            setCollector(false);
                          }
                        }}
                      />
                    }
                    label={<span>ALL</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="mcgm"
                control={control}
                defaultValue={formData?.mcgm ? mcgm : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("mcgm", true);
                            setMcgm(true);
                          } else {
                            setValue("mcgm", false);
                            setMcgm(false);
                          }
                        }}
                      />
                    }
                    label={<span>MCGM</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="mhada"
                control={control}
                defaultValue={formData?.mhada ? mhada : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("mhada", true);
                            setMhada(true);
                          } else {
                            setValue("mhada", false);
                            setMhada(false);
                          }
                        }}
                      />
                    }
                    label={<span>MHADA</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="collector"
                control={control}
                defaultValue={formData?.collector ? collector : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("collector", true);
                            setCollector(true);
                          } else {
                            setValue("collector", false);
                            setCollector(false);
                          }
                        }}
                      />
                    }
                    label={<span>Collector</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl>
              <Controller
                name="srasra"
                control={control}
                defaultValue={formData?.srasra ? srasra : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("srasra", true);
                            setSrasra(true);
                          } else {
                            setValue("srasra", false);
                            setSrasra(false);
                          }
                        }}
                      />
                    }
                    label={<span>SRASRA</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSaveButtonDisabled}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default SpecializationEdit;
