import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";
import NavTabDocuments from "src/@core/components/custom-components/NavTabDocuments";
import CategoryTab from "./CategoryTab";
import SubCategoryTab from "./SubCategoryTab";
import { useRBAC } from "src/pages/permission/RBACContext";

const EmployeeTabs = () => {
  const { can } = useRBAC();

  if(can('masterDataDocuments_READ')){
  return (
    <Card>
      <NavTabDocuments
        tabContent1={
          <>
           <CategoryTab/>
          </>
        }
        tabContent2={
          <>
           <SubCategoryTab/>
          </>
        }
       
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default EmployeeTabs;
