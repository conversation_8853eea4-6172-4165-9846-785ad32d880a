# Deployment Guide - Sample App

## 🚀 Quick Deployment Options

### 1. Vercel (Recommended)

Vercel is the easiest way to deploy Next.js applications:

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from project directory
cd sample-app
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? (select your account)
# - Link to existing project? No
# - Project name: sample-app
# - Directory: ./
# - Override settings? No
```

**Environment Variables in Vercel:**
1. Go to your project dashboard
2. Navigate to Settings → Environment Variables
3. Add the following:
   ```
   NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com/api
   NEXT_PUBLIC_APP_NAME=Sample App
   ```

### 2. Netlify

Deploy to Netlify with continuous deployment:

```bash
# Build the project
npm run build

# Install Netlify CLI
npm install -g netlify-cli

# Deploy
netlify deploy --prod --dir=out
```

**Netlify Configuration:**
Create `netlify.toml` in project root:
```toml
[build]
  command = "npm run build"
  publish = ".next"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. AWS Amplify

Deploy using AWS Amplify Console:

1. Connect your GitHub repository
2. Set build settings:
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

### 4. DigitalOcean App Platform

Deploy using DigitalOcean's App Platform:

1. Create `app.yaml`:
   ```yaml
   name: sample-app
   services:
   - name: web
     source_dir: /
     github:
       repo: your-username/sample-app
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     routes:
     - path: /
   ```

## 🔧 Build Configuration

### Production Build

```bash
# Create production build
npm run build

# Start production server
npm start

# Or export static files (if using static export)
npm run export
```

### Environment Variables

Create production environment file:

```bash
# .env.production
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_NAME=Sample App
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_production_google_client_id
NEXT_PUBLIC_FACEBOOK_APP_ID=your_production_facebook_app_id
```

### Build Optimization

Add to `next.config.mjs`:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  compress: true,
  images: {
    domains: ['your-image-domain.com'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
}

export default nextConfig
```

## 🔒 Security Configuration

### HTTPS Setup

Ensure HTTPS is enabled in production:

```javascript
// next.config.mjs
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },
}
```

### Environment Security

Never commit sensitive data:

```bash
# .gitignore
.env.local
.env.production
.env.development
*.log
.DS_Store
```

## 📊 Performance Monitoring

### Analytics Setup

Add Google Analytics or similar:

```javascript
// pages/_app.js
import { useEffect } from 'react'
import { useRouter } from 'next/router'

function MyApp({ Component, pageProps }) {
  const router = useRouter()

  useEffect(() => {
    const handleRouteChange = (url) => {
      // Track page views
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: url,
      })
    }
    
    router.events.on('routeChangeComplete', handleRouteChange)
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router.events])

  return <Component {...pageProps} />
}
```

### Performance Monitoring

Add performance monitoring:

```bash
npm install @vercel/analytics
```

```javascript
// pages/_app.js
import { Analytics } from '@vercel/analytics/react'

function MyApp({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  )
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

USER nextjs

EXPOSE 3000

CMD ["npm", "start"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
    restart: unless-stopped
```

## 📋 Pre-Deployment Checklist

- [ ] Environment variables configured
- [ ] Build completes successfully
- [ ] All tests passing
- [ ] Security headers configured
- [ ] HTTPS enabled
- [ ] Analytics/monitoring setup
- [ ] Error tracking configured
- [ ] Performance optimized
- [ ] SEO meta tags added
- [ ] Favicon and manifest configured

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and reinstall
   rm -rf .next node_modules package-lock.json
   npm install
   npm run build
   ```

2. **Environment Variables Not Loading**
   - Ensure variables start with `NEXT_PUBLIC_` for client-side
   - Restart development server after changes
   - Check `.env.local` is in project root

3. **Routing Issues**
   - Verify file structure matches routes
   - Check for conflicting route names
   - Ensure dynamic routes are properly configured

4. **Performance Issues**
   - Analyze bundle with `npm run analyze`
   - Optimize images and assets
   - Implement code splitting
   - Use React.memo for expensive components

---

**Need help?** Check the [troubleshooting guide](./README.md#troubleshooting) or create an issue.
