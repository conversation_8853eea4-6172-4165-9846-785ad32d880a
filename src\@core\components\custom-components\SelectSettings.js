// SelectSettings.js
import React, { useState } from 'react';
import Box from "@mui/material/Box";
import YouTubeIcon from '@mui/icons-material/YouTube';
import CallToActionIcon from '@mui/icons-material/CallToAction';
import CustomAutocomplete from './CustomAutoComplete';
import CustomTextField from './CustomTextField';

const SelectSettings = (props) => {
  const {
    id,
    nameArray,
    value,
    onChange,
    isOptionDisabled,
  } = props;

  const [isDropdownOpen, setDropdownOpen] = useState(false); // Add state for dropdown open/close

  const customStyles = {
    menu: (provided, state) => ({
      ...provided,
      zIndex: 2,
    }),
  };

  // Handle option selection
  const handleOptionSelect = (event, option) => {
    if (option && isOptionDisabled(option)) {
      event.preventDefault();
    } else {
      const selectedValue = option ? option.value : null;
      onChange({ target: { value: selectedValue } });
      setDropdownOpen(false); // Close the dropdown after selection
    }
  };

  const handleDropdownOpen = () => {
    setDropdownOpen(true); // Open the dropdown when clicking the input
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false); // Close the dropdown when clicking outside
  };

  return (
    <Box sx={{ position: 'relative', zIndex: 1, maxWidth: "100%", minWidth: "100%", marginTop: "15px", marginBottom: "15px" }}>
      <CustomAutocomplete
        autoHighlight
        sx={{ width: { xs: 300, md: 500 }, mb: 3 }}
        id={id}
        options={nameArray}
        getOptionLabel={option => option.key || ''}
        value={value}
        onChange={handleOptionSelect}
        onOpen={handleDropdownOpen} // Open the dropdown on input click
        onClose={handleDropdownClose} // Close the dropdown when clicking outside
        open={isDropdownOpen} // Pass the open state
        renderOption={(props, option) => (
          <Box
            component='li'
            {...props}
            onClick={(event) => handleOptionSelect(event, option)}
            style={{
              cursor: option && isOptionDisabled(option) ? 'not-allowed' : 'pointer',
              color: option && isOptionDisabled(option) ? 'gray' : 'inherit',
            }}
          >
            {option && option.format === "standard" ? (
              <CallToActionIcon style={{ marginRight: '10px' }} />
            ) : (
              <YouTubeIcon style={{ marginRight: '10px' }} />
            )}
            {option && option.key}
          </Box>
        )}
        renderInput={params => (
          <CustomTextField
            {...params}
            size='medium'
            placeholder='Search '
            sx={{ borderRadius: 1 }}
            inputProps={{
              ...params.inputProps,
            }}
          />
        )}
      />
    </Box>
  );
};

export default SelectSettings;
