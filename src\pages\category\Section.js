import { useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import Checkbox from "@mui/material/Checkbox";
import { Box } from "@mui/system";
import { useForm, Controller } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import { DialogActions, FormControlLabel, Switch } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

import { yupResolver } from "@hookform/resolvers/yup";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { RoleTypesValidation } from "./roleTypesValidation";

const Section = ({ onCancel, data, setData }) => {
  const auth = useAuth();
  const [openField, setOpenField] = useState(false);
  const [subRoleType, setSubRoleType] = useState("");
  const [isActive, setIsActive] = useState(data?.isActive);

  const {
    handleSubmit,
    control,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(RoleTypesValidation([])),
    defaultValues: {
      id: data.id,
      roleType: data.roleType,
      isActive: data.isActive,
      hasSubTypes: data.hasSubTypes,
      metadata: data.metadata,
    },
    mode: "onChange",
  });

  useEffect(() => {
    if (data.hasSubTypes) {
      setOpenField(true);
    }
    setValue("roleType", data.roleType);
    setValue("hasSubTypes", data.hasSubTypes);
  }, [data, setValue]);

  const handleSubRoleChange = (index, newValue) => {
    setData((prevData) => {
      const newSubRoles = [...prevData.metadata.subRoleTypes];
      newSubRoles[index].name = newValue;
      return {
        ...prevData,
        metadata: {
          ...prevData.metadata,
          subRoleTypes: newSubRoles,
        },
      };
    });
  };

  const handleDeleteSubRole = (index) => {
    setData((prevData) => {
      const newSubRoles = [...prevData.metadata.subRoleTypes];
      // Set isActive to false for the item at the specified index
      newSubRoles[index].isActive = false;
      return {
        ...prevData,
        metadata: {
          ...prevData.metadata,
          subRoleTypes: newSubRoles,
        },
      };
    });
  };

  const handleAddItem = () => {
    if (subRoleType.trim() !== "") {
      const isDuplicate = data.metadata.subRoleTypes.some(
        (subRole) => subRole.name.toUpperCase() === subRoleType.toUpperCase()
      );
  
      if (isDuplicate) {
        setData((prevData) => {
          const updatedSubRoleTypes = prevData.metadata.subRoleTypes.map(
            (subRole) => {
              if (subRole.name.toUpperCase() === subRoleType.toUpperCase()) {
                if (!subRole.isActive) {
                  return { ...subRole, isActive: true };
                }
                // If isActive is already true, don't make any changes
                return subRole;
              }
              return subRole;
            }
          );
  
          return {
            ...prevData,
            metadata: {
              ...prevData.metadata,
              subRoleTypes: updatedSubRoleTypes,
            },
          };
        });
      } else {
        setData((prevData) => {
          const updatedSubRoleTypes = [
            ...prevData.metadata.subRoleTypes,
            { name: subRoleType, isActive: true },
          ];
          return {
            ...prevData,
            metadata: {
              ...prevData.metadata,
              subRoleTypes: updatedSubRoleTypes,
            },
          };
        });
      }
      setSubRoleType(""); // Clear the input field
    }
  };
  

  

  const handleHasSubTypesChange = (checked) => {
    if (checked) {
      setOpenField(true);
      setData((prevData) => ({
        ...prevData,
        hasSubTypes: true,
        metadata: data.metadata,
      }));
    } else {
      setOpenField(false);
      setData((prevData) => ({
        ...prevData,
        hasSubTypes: false,
        metadata: {
          ...prevData.metadata,
        },
      }));
    }
  };

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };
  async function submit(formData) {
    try {
      let updatedSubRoleTypes = [];
      if (formData.hasSubTypes) {
        updatedSubRoleTypes = data.metadata?.subRoleTypes || [];
      }

      const updatedData = {
        ...formData,
        metadata: {
          ...formData.metadata,
          subRoleTypes: updatedSubRoleTypes,
        },
      };

      updatedData.isActive = isActive;
      await auth.updateRole(updatedData);
      setData(updatedData);
    } catch (error) {
      console.error("role types Details failed", error);
    }
    onCancel();
  }

  return (
    <Box sx={{ pt: 4 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="roleType"
              control={control}
              render={({ field }) => (
                <NameTextField
                  id="roleType"
                  label="Category"
                  placeholder="Enter Category Name"
                  inputProps={{ maxLength: 30 }}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.roleType)}
                  helperText={errors.roleType?.message}
                  {...field}
                />
              )}
            />
          </FormControl>
        </Grid>

        {openField && (
          <>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="subRoleType"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Sub Category"
                      value={subRoleType}
                      onChange={(e) => setSubRoleType(e.target.value)}
                      placeholder="Enter Sub Category "
                      aria-describedby="Section-name"
                      inputProps={{ maxLength: 30 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4} sx={{display:'flex', flexWrap:'wrap', alignItems:'center'}}>
              <Button
                variant="contained"
                onClick={handleAddItem}
                style={{
                  width: "50px",
                  height: "40px", marginRight: 50,
                }}
              >
                Add
              </Button>
              <Controller
                name="isActive"
                control={control}
                render={() => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={isActive}
                        onChange={handleOnChange}
                        name="isActive"
                      />
                    }
                    label="Status"
                  />
                )}
              />
            </Grid>
          </>
        )}
        
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="hasSubTypes"
              control={control}
              render={({ field }) => (
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={data.hasSubTypes}
                        onChange={(e) => {
                          field.onChange(e.target.checked);
                          handleHasSubTypesChange(e.target.checked);
                        }}
                      />
                    }
                    label="Have sub-category types?"
                  />
                  {field.value &&
                    data.metadata?.subRoleTypes?.map((subRole, index) => {
                      if (subRole.isActive) {
                        return (
                          <div
                            key={index}
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <TextField
                              key={index}
                              label={`Sub categories`}
                              value={subRole.name}
                              onChange={(e) =>
                                handleSubRoleChange(index, e.target.value)
                              }
                              fullWidth
                              margin="normal"
                              inputProps={{ maxLength: 30 }}
                              InputProps={{
                                endAdornment: (
                                  <IconButton
                                    sx={{ p: 0, width: 26, height: 26 }}
                                    size="small"
                                    color="error"
                                    onClick={() => handleDeleteSubRole(index)}
                                  >
                                    <Icon icon="tabler:trash" />
                                  </IconButton>
                                ),
                              }}
                            />
                          </div>
                        );
                      }
                      return null;
                    })}
                </>
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section;
