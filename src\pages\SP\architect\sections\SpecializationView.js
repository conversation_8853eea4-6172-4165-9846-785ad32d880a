// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow
} from "@mui/material";
import MUITableCell from "../../MUITableCell";
import SpecializationEdit from "./SpecializationEdit";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const SpecializationView = ({ data, expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
     {/* {can('architect_specializations_READ') && */}
      <AccordionBasic
      id={"panel-header-2"}
      ariaControls={"panel-content-2"}
      heading={"Specializations"}
      body={
        <>
          {state3 === "view" && (
            <TableContainer
              sx={{ padding: "4px 6px" }}
              className="tableBody"
             // onClick={can('architect_specializations_UPDATE') ? viewClick3 : null}
              onClick={viewClick3}
            >
              <Table>
                <TableBody
                  sx={{
                    "& .MuiTableCell-root": {
                      p: `${theme.spacing(1.35, 1.125)} !important`,
                    },
                  }}
                >
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Specialization:
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        (Yes/No):
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>MCGM</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.mcgm ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>MHADA</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.mhada ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography> Collector</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.collector ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>SRASRA</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.srasra ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {state3 === "edit" && (
            <SpecializationEdit formData={data} onCancel={editClick3} />
          )}
        </>
      }
      expanded={expanded}
    />
     {/* } */}
    </>
  );
};
export default SpecializationView;
