// ** MUI Imports
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import authConfig from "src/configs/auth";
import { useAuth } from "src/hooks/useAuth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import FallbackSpinner from "src/@core/components/spinner";

import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import {
  Grid,
  Button,
  Dialog,
  DialogContent,
  DialogContentText,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { DialogActions } from "@mui/material";
import NavTabsSettings from "src/@core/components/custom-components/NavTabsSettings";
//import { yup } from "../profile/architect/sections/ArchitectValidationSection1";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import SelectSettings from "src/@core/components/custom-components/SelectSettings";
import DraggableTable from "./DraggableTable";
import { useRouter } from 'next/router'
import { useRBAC } from "../permission/RBACContext";

const TableBasic = () => {
  const authContext = useContext(AuthContext);
  const auth = useAuth();
  const router = useRouter()
  const { can,rbacRoles } = useRBAC();

  const [postsData, setPostsData] = useState([]);
  const [metaPostsData, setMetaPostsData] = useState([]);
  const [initiatingData, setInitiatingData] = useState([]);

  const [settings, setSettings] = useState([]);
  const [entityCategory, setEntityCategory] = useState('');
  const [needAssessmentData, setNeedAssessmentData] = useState([]);
  const [needAssessmentList, setNeedAssessmentList] = useState([]);
  const [initiatingRedevelopment, setInitiatingRedevelopment] = useState([]);
  const [typesOfDevelopment, setTypesOfDevelopment] = useState([]);
  const [typesOfDevelopmentList, setTypesOfDevelopmentList] = useState([]);
  const [typesOfDevelopmentData, setTypesOfDevelopmentData] = useState([]);
  const [committee, setCommittee] = useState([]);
  const [committeeList, setCommitteeList] = useState([]);
  const [committeeData, setCommitteeData] = useState([]);
  const [consents, setConsents] = useState([]);
  const [consentsList, setConsentsList] = useState([]);
  const [consentsData, setConsentsData] = useState([]);
  const [conveyance, setConveyance] = useState([]);
  const [conveyanceList, setConveyanceList] = useState([]);
  const [conveyanceData, setConveyanceData] = useState([]);
  const [managingCommittee, setManagingCommittee] = useState([]);
  const [managingCommitteeList, setManagingCommitteeList] = useState([]);
  const [managingCommitteeData, setManagingCommitteeData] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [documentsList, setDocumentsList] = useState([]);
  const [documentsData, setDocumentsData] = useState([]);
  const [appointment, setAppointment] = useState([]);
  const [appointmentList, setAppointmentList] = useState([]);
  const [appointmentData, setAppointmentData] = useState([]);
  const [preTendering, setPreTendering] = useState([]);
  const [preTenderingList, setPreTenderingList] = useState([]);
  const [preTenderingData, setPreTenderingData] = useState([]);
  const [tendering, setTendering] = useState([]);
  const [tenderingList, setTenderingList] = useState([]);
  const [tenderingData, setTenderingData] = useState([]);
  const [financial, setFinancial] = useState([]);
  const [financialList, setFinancialList] = useState([]);
  const [financialData, setFinancialData] = useState([]);
  const [dialogMessage, setDialogMessage] = useState("");

  const [refreshList, setRefreshList] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const [loading,setLoading] = useState(true);

  const validate = yup.object().shape({
    needAssessment: yup.array().nullable(),
    typesOfRedevelopment: yup.array().nullable(),
    consents: yup.array().nullable(),
    committee: yup.array().nullable(),
    documents: yup.array().nullable(),
    conveyance: yup.array().nullable(),
    financialClosure: yup.array().nullable(),
    managingCommittee: yup.array().nullable(),
    preTenderingStage: yup.array().nullable(),
    needAssessmentVideos: yup.array().nullable(),
    tenderingStage: yup.array().nullable(),
    appointmentOfProfessionals: yup.array().nullable(),
  });

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(validate),
  });

  // Fetching posts from the wordpress site.
  useEffect(() => {
    axios({
      method: "get",
      url: authConfig.resourcesURL + "wp-json/wp/v2/posts?_embed&per_page=100",
    })
      .then((res) => {
        sortThePosts(res.data);
        setLoading(false);
      })
      .catch();
  }, []);

  // Fetching the settings from the DB.
  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.settings) +
        "?settingsType=CHECK_READINESS_POSTS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        console.log("All", res.data.readinessResourcesDTO);
        setSettings(res.data?.readinessResourcesDTO);
        setNeedAssessmentList(res.data?.readinessResourcesDTO?.needAssessment);
        setTypesOfDevelopmentList(res.data?.readinessResourcesDTO?.typesOfRedevelopment);
        setCommitteeList(res.data?.readinessResourcesDTO?.committee);
        setConveyanceList(res.data?.readinessResourcesDTO?.conveyance);
        setConsentsList(res.data?.readinessResourcesDTO?.consents);
        setManagingCommitteeList(res.data?.readinessResourcesDTO?.managingCommittee);
        setDocumentsList(res.data?.readinessResourcesDTO?.documents);
        setAppointmentList(res.data?.readinessResourcesDTO?.appointmentOfProfessionals);
        setPreTenderingList(res.data?.readinessResourcesDTO?.preTenderingStage);
        setTenderingList(res.data?.readinessResourcesDTO?.tenderingStage);
        setFinancialList(res.data?.readinessResourcesDTO?.financialClosure);

        /**
         * On Succes we have to set the list with data from DB.
         */
        console.log("********************** Trigget=ring refresh");
        setRefreshList(true);
      })
      .catch((err) => console.log("error", err));
  }, []);

  useEffect(() => {
    const updateList = (value, listSetter) => {
      if (value) {
        const newRecords = fetchRecordsByWordPressId(value);

        if (newRecords) {
          listSetter((prevList) => {
            /**
             * Checking if the Item wants to add already there in the list of the selected values for the section.
             */
            const addRecord =
              prevList?.find(
                (item) => item.wordPressId == newRecords.wordPressId
              ) || null;

            if (addRecord) {
              console.log("ALready exist row, newRecords:", newRecords);
              return prevList;
            }
            if (!prevList) {
              return [newRecords];
            }
            console.log("Add new row, newRecords:", newRecords);
            return [...prevList, newRecords];
          });
        }
      }
    };

    updateList(initiatingRedevelopment, setNeedAssessmentList);
    updateList(typesOfDevelopment, setTypesOfDevelopmentList);
    updateList(committee, setCommitteeList);
    updateList(consents, setConsentsList);
    updateList(conveyance, setConveyanceList);
    updateList(managingCommittee, setManagingCommitteeList);
    updateList(documents, setDocumentsList);
    updateList(appointment, setAppointmentList);
    updateList(preTendering, setPreTenderingList);
    updateList(tendering, setTenderingList);
    updateList(financial, setFinancialList);
  }, [
    refreshList,
    initiatingRedevelopment,
    typesOfDevelopment,
    committee,
    consents,
    conveyance,
    managingCommittee,
    documents,
    appointment,
    preTendering,
    tendering,
    financial,
  ]);
  //Sorting the Posts data in the Form of an DTO we need for our backend and for the Multi Select input field.
  const sortThePosts = (data) => {
    let sortedData = [];
    data.map((record) => {
      let obj = {
        wordPressId: record.id,
        title: record.title.rendered,
        slug: record.slug,
        imageId:
          record._embedded["wp:featuredmedia"] &&
          record._embedded["wp:featuredmedia"][0].media_details.sizes.thumbnail
            .source_url,
        link: record.link,
        resourceType: "WORD_PRESS",
        format: record.format,
        youtube_url: record.youtube_url,
      };
      sortedData.push(obj);
    });
    console.log("Sorted data:", sortedData);
    setPostsData(sortedData);

    let sortMetaPostsData = [];
    sortedData.map((record) => {
      let title = record.title;
      if (title == "") {
        title = record.slug;
      }
      let obj = {
        name: title,
        title: title,
        value: title,
        wordPressId: record.wordPressId,
        resourceType: record.resourceType,
        link: record.link,
        format: record.format,
        youtube_url: record.youtube_url,
      };
      sortMetaPostsData.push(obj);
    });
    console.log("sortMetaPostsData:", sortMetaPostsData);
    setMetaPostsData(sortMetaPostsData);
    let initiatingPostsData = [];
    sortMetaPostsData.map((record) => {
      let title = record.title;
      if (title == "") {
        title = record.slug;
      }
      let obj = {
        key: title,
        value: record.wordPressId,
        resourceType: record.resourceType,
        format: record.format,
        youtube_url: record.youtube_url,
      };
      initiatingPostsData.push(obj);
    });
    setInitiatingData(initiatingPostsData);
  };
  //Use this to Set the Pre-populated data from the database
  useEffect(() => {
    if (settings.length != 0) {
      //For Need Assessment
      let need = [];
      settings.needAssessment?.map((record) => {
        need.push(record.title);
        console.log("record slug", record.slug);
      });
      setNeedAssessmentData(need.join(","));
      setValue("needAssessment", need);

      //For Types of Development
      let types = [];
      settings.typesOfRedevelopment?.map((record) => {
        types.push(record.title);
      });
      setTypesOfDevelopmentData(types.join(","));
      setValue("typesOfRedevelopment", types);

      //For Committee
      let committee = [];
      settings.committee?.map((record) => {
        committee.push(record.title);
      });
      setCommitteeData(committee.join(","));
      setValue("committee", committee);

      //For Consents
      let consents = [];
      settings.consents?.map((record) => {
        consents.push(record.title);
      });
      setConsentsData(consents.join(","));
      setValue("consents", consents);

      //For Conveyance
      let conveyance = [];
      settings.conveyance?.map((record) => {
        conveyance.push(record.title);
      });
      setConveyanceData(conveyance.join(","));
      setValue("conveyance", conveyance);

      //For  Managing Committee
      let managingCommittee = [];
      settings.managingCommittee?.map((record) => {
        managingCommittee.push(record.title);
      });
      setManagingCommitteeData(managingCommittee.join(","));
      setValue("managingCommittee", managingCommittee);

      //For documents
      let documents = [];
      settings.documents?.map((record) => {
        documents.push(record.title);
      });
      setDocumentsData(documents.join(","));
      setValue("documents", documents);

      //For documents
      let appointmentOfProfessionals = [];
      settings.appointmentOfProfessionals?.map((record) => {
        appointmentOfProfessionals.push(record.title);
      });
      setAppointmentData(appointmentOfProfessionals.join(","));
      setValue("appointmentOfProfessionals", appointmentOfProfessionals);

      //For preTenderingStage
      let preTenderingStage = [];
      settings.preTenderingStage?.map((record) => {
        preTenderingStage.push(record.title);
      });
      setPreTenderingData(preTenderingStage.join(","));
      setValue("preTenderingStage", preTenderingStage);

      //For tenderingStage
      let tenderingStage = [];
      settings.tenderingStage?.map((record) => {
        tenderingStage.push(record.title);
      });
      setTenderingData(tenderingStage.join(","));
      setValue("tenderingStage", tenderingStage);

      //For financialClosure
      let financialClosure = [];
      settings.financialClosure?.map((record) => {
        financialClosure.push(record.title);
      });
      setFinancialData(financialClosure.join(","));
      setValue("financialClosure", financialClosure);
    }
  }, [settings, setValue]);

  const handleDelete = (index, list, setList) => {
    const updatedList = [...list];
    updatedList.splice(index, 1);
    setList(updatedList);
  };
  async function submit(data) {
    data.needAssessment = needAssessmentList;
    data.typesOfRedevelopment = typesOfDevelopmentList;
    data.committee = committeeList;
    data.consents = consentsList;
    data.conveyance = conveyanceList;
    data.managingCommittee = managingCommitteeList;
    data.documents = documentsList;
    data.appointmentOfProfessionals = appointmentList;
    data.preTenderingStage = preTenderingList;
    data.tenderingStage = tenderingList;
    data.financialClosure = financialList;

    await axios({
      method: "patch",
      url: getUrl(authConfig.settings),
      headers: getAuthorizationHeaders(),
      data: {
        settingsType: "CHECK_READINESS_POSTS",
        readinessResourcesDTO: data,
      },
    })
      .then((res) => {
        console.log("updated", res.data);
        console.log("updated", data);
        handleSuccess();
      })
      .catch((err) => {
        console.log("error", err);
        handleFailure();
      });
  }

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSuccess = () => {
    const message = `
      <div>
        <h3>Data Saved Successfully!</h3>
      </div>`;

    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleFailure = (error) => {
    const message = `
      <div>
        <h3>Failed to Save data. Please try again later.</h3>
      </div>`;

    setDialogMessage(message);
    setOpenDialog(true);
  };
  const handleDragEnd = (list, setList) => (result) => {
    if (!result.destination) return;

    const reorderedList = Array.from(list);
    const [reorderedItem] = reorderedList.splice(result.source.index, 1);
    reorderedList.splice(result.destination.index, 0, reorderedItem);

    setList(reorderedList);
  };
  const fetchRecordsByWordPressId = (id) => {
    return metaPostsData.find((record) => record.wordPressId == id) || null;
  };

  const isOptionDisabled = (item) => {
    // Check if the format is "video" and the youtube_url is empty
    return item.format === 'video' && !item.youtube_url;
  };


  if (can('blogsVideosReadiness_READ')) {


  return (
    <>
     {loading ? ( 
        <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
          <FallbackSpinner />
        </Box>
      ) : (
        <>
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider} `,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Card>
        <Box
          component={"div"}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            marginRight: "10px",
            alignItems: "center",
          }}
        >
          <CardHeader
            title="Settings"
            sx={{ padding: (theme) => theme.spacing(3.5, 3) }}
          />
          <Button
            size="medium"
            type="button"
            variant="contained"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </Box>

        <NavTabsSettings
          tabContent1={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"needAssessment"}
                        label={"Initiating Redevelopment Posts and Videos"}
                        nameArray={initiatingData}
                        register={register}
                        value={initiatingRedevelopment}
                        defaultValue={needAssessmentData}
                        onChange={(e) => {
                          setInitiatingRedevelopment(e.target.value);
                        }}
                        error={Boolean(errors.initiatingRedevelopment)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={needAssessmentList}
                setDataList={setNeedAssessmentList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent2={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"typesOfRedevelopment"}
                        label={"Types of Redevelopment"}
                        nameArray={initiatingData}
                        register={register}
                        value={typesOfDevelopment}
                        defaultValue={typesOfDevelopmentData}
                        onChange={(e) => {
                          setTypesOfDevelopment(e.target.value);
                        }}
                        error={Boolean(errors.typesOfRedevelopment)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={typesOfDevelopmentList}
                setDataList={setTypesOfDevelopmentList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent3={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"committee"}
                        label={"Redevelopment Committee"}
                        nameArray={initiatingData}
                        register={register}
                        value={committee}
                        defaultValue={committeeData}
                        onChange={(e) => {
                          setCommittee(e.target.value);
                        }}
                        error={Boolean(errors.committee)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={committeeList}
                setDataList={setCommitteeList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent4={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"consents"}
                        label={"Consents"}
                        nameArray={initiatingData}
                        value={consents}
                        register={register}
                        defaultValue={consentsData}
                        onChange={(e) => {
                          setConsents(e.target.value);
                        }}
                        error={Boolean(errors.consents)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={consentsList}
                setDataList={setConsentsList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent5={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"conveyance"}
                        label={"Conveyance"}
                        nameArray={initiatingData}
                        value={conveyance}
                        register={register}
                        defaultValue={conveyanceData}
                        onChange={(e) => {
                          setConveyance(e.target.value);
                        }}
                        error={Boolean(errors.conveyance)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={conveyanceList}
                setDataList={setConveyanceList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent6={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"managingCommittee"}
                        label={"Valid Tenure of Managing Committee"}
                        nameArray={initiatingData}
                        register={register}
                        value={managingCommittee}
                        defaultValue={managingCommitteeData}
                        onChange={(e) => {
                          setManagingCommittee(e.target.value);
                        }}
                        error={Boolean(errors.managingCommittee)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={managingCommitteeList}
                setDataList={setManagingCommitteeList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent7={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"documents"}
                        label={"Redevelopment Documents"}
                        nameArray={initiatingData}
                        value={documents}
                        register={register}
                        defaultValue={documentsData}
                        onChange={(e) => {
                          setDocuments(e.target.value);
                        }}
                        error={Boolean(errors.documents)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={documentsList}
                setDataList={setDocumentsList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent8={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"appointmentOfProfessionals"}
                        label={"Appointment of Professionals"}
                        nameArray={initiatingData}
                        value={appointment}
                        register={register}
                        defaultValue={appointmentData}
                        onChange={(e) => {
                          setAppointment(e.target.value);
                        }}
                        error={Boolean(errors.documents)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={appointmentList}
                setDataList={setAppointmentList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent9={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"preTenderingStage"}
                        label={"PreTendering Stage"}
                        nameArray={initiatingData}
                        value={preTendering}
                        register={register}
                        defaultValue={preTenderingData}
                        onChange={(e) => {
                          setPreTendering(e.target.value);
                        }}
                        error={Boolean(errors.preTendering)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={preTenderingList}
                setDataList={setPreTenderingList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent10={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"tenderingStage"}
                        label={"Tendering Stage"}
                        nameArray={initiatingData}
                        value={tendering}
                        register={register}
                        defaultValue={tenderingData}
                        onChange={(e) => {
                          setTendering(e.target.value);
                        }}
                        error={Boolean(errors.tendering)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={tenderingList}
                setDataList={setTenderingList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
          tabContent11={
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pl: { md: 2 } }}>
                    {metaPostsData?.length != 0 && (
                      <SelectSettings
                        id={"financialClosure"}
                        label={"Financial Closure"}
                        nameArray={initiatingData}
                        value={financial}
                        register={register}
                        defaultValue={financialData}
                        onChange={(e) => {
                          setFinancial(e.target.value);
                        }}
                        error={Boolean(errors.financial)}
                        isOptionDisabled={isOptionDisabled}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
              <DraggableTable
                dataList={financialList}
                setDataList={setFinancialList}
                handleDragEnd={handleDragEnd}
                handleDelete={handleDelete}
                metaPostsData={metaPostsData}
              />
            </>
          }
        />
      </Card>
      </>
      )}
    </>
  );}else{
    return null;
  }
};

export default TableBasic;
