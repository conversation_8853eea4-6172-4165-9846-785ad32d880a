// ** React Imports

// ** MUI Imports

import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import axios from "axios";
import FormControl from "@mui/material/FormControl";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";

// ** Third Party Imports

import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Select,
  InputLabel,
  MenuItem,
} from "@mui/material";
import { useAuth } from "src/hooks/useAuth";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import { yupResolver } from "@hookform/resolvers/yup";
import ArchitectValidationSection1 from "./ArchitectValidationSection1";
import DocumentUploadDialog from "src/@core/components/custom-components/DocumentDialogUpload";
import { AuthContext } from "src/context/AuthContext";
import { useContext, useEffect, useState } from "react";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";

const Section1 = ({ onCancel, formData }) => {
  const auth = useAuth();
  const fields = [
    "name",
    "companyName",
    "email",
    "mobileNumber",
    "address",
    "websiteUrl",
  ];
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(ArchitectValidationSection1(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    trimmedData.location = selectedLocationName;
    trimmedData.locationId = selectedLocationId;

    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });

    onCancel();
  }

  const [disableButton, setDisableButton] = useState(false);
  const [loading, setLoading] = useState(false);
  const [modalPopup, setModalPopup] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [dailogSuccess, setDialogSuccess] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState(false);
  const {
    uploadDocuments,
    getAllDocuments,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    shortFormData,
  } = useContext(AuthContext);

  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        fetchAllDocuments();
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };

    fetchDocuments();
  }, [allCategories, allSubCategories]);

  useEffect(() => {}, [documents]);

  const memberId =
    user.entityCategory === "SUPER_ADMIN" ? shortFormData.id : null;

  const documentDetails = {
    userId: memberId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("company"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();

    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });

    formData.append("documentDetails", JSON.stringify(documentDetails));

    // API call
    await uploadDocuments(
      formData,
      () => {
        setModalPopup(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },

      () => {
        console.log("Failure");
      }
    );

    fetchAllDocuments();
    setLoading(false);
    setDisableButton(false);
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };
  async function handleDelete() {
    await documentDelete(companyToDelete);
    setSelectedCompany(null);
    setConfirmDeleteDialogOpen(false);
    fetchAllDocuments();
  }

  const documentJson = {
    userId: memberId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("company"),
  };

  const fetchAllDocuments = async () => {
    if (allCategories.length > 0 && allSubCategories.length > 0) {
      const allDocs = await getAllDocuments(documentJson);

      setDocuments(allDocs);
    }
  };

  const handleViewIconClick = (company) => {
    setSelectedCompany(company);
  };

  const handleDialogClose = () => {
    setSelectedCompany(null);
  };

  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.locationId || ""
  );
  const [selectedLocationName, setSelectedLocationName] = useState("");

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
    const selectedLocation = locationsData.find(
      (location) => location.id === selectedId
    );
    const locationName = selectedLocation ? selectedLocation.location : "";
    setSelectedLocationName(locationName);
  };

  const [locationsData, setLocationsData] = useState(null);
  console.log("========================", locationsData);
  useEffect(() => {
    async function getLocation() {
      try {
        const response = await axios({
          method: "get",
          url: getUrl(authConfig.getAllListValuesByListNameId),
          headers: getAuthorizationHeaders(),
          params: {
            id: authConfig.locationlistNameId,
          },
        });
        setLocationsData(response.data.listValues);
      } catch (error) {
        console.error("Error fetching location:", error);
      }
    }

    getLocation();
  }, []);

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="companyName"
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.companyName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Company Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter company name"
                  error={Boolean(errors.companyName)}
                  helperText={errors.companyName?.message}
                  aria-describedby="validation-basic-last-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">System Code</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.systemCode}
            </Typography>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              defaultValue={formData?.address}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Address"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.address)}
                  helperText={errors.address?.message}
                  aria-describedby="validation-basic-textarea"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Contact number"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.mobileNumber)}
                  helperText={errors.mobileNumber?.message}
                  placeholder="+91 1234567890"
                  aria-describedby="validation-mobileNumber"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
              defaultValue={formData?.email}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="Email Id"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.email)}
                  helperText={errors.email?.message}
                  placeholder="Enter email address"
                  aria-describedby="validation-email"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              defaultValue={formData?.websiteUrl}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Website URL"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.websiteUrl)}
                  helperText={errors.websiteUrl?.message}
                  placeholder="https://www.example.com"
                  aria-describedby="validation-websiteUrl"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel id="location-select-label">Location</InputLabel>
            <Select
              labelId="location-select-label"
              id="location-select"
              defaultValue={formData?.location}
              value={selectedLocationId}
              label="Location"
              onChange={handleSelectChange}
            >
              {locationsData?.map((location) => (
                <MenuItem key={location.id} value={location.id}>
                  {location.listValue}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Button
            aria-controls="simple-menu"
            aria-haspopup="true"
            onClick={() => {
              setModalPopup(true);
            }}
            variant="contained"
            sx={{ px: 4 }}
          >
            Upload Company Images
          </Button>
        </Grid>

        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Company Files</TableCell>
              <TableCell>Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {documents?.data?.map((company, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Typography className="data-field">
                    {company && company.split("/").pop()}
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={() => {
                      setConfirmDeleteDialogOpen(true);
                      setCompanyToDelete(company);
                    }}
                    color="error"
                  >
                    <Icon icon="iconamoon:trash" />
                  </IconButton>
                  <IconButton
                    onClick={() => handleViewIconClick(company)}
                    color="error"
                    disabled={selectedCompany}
                  >
                    <Icon icon="iconamoon:eye" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              {loading ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Save"
              )}
            </Button>
          </center>
        </Grid>
        <DocumentUploadDialog
          open={modalPopup}
          onClose={() => setModalPopup(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          loading={loading}
          disableButton={disableButton}
        />
      </Grid>
      <Grid>
        <Dialog
          open={dailogSuccess}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                Successfully uploaded
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleClose}
                sx={{ margin: "auto", width: 100 }}
              >
                Ok
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <ViewDialogByLocation
          location={selectedCompany}
          setSelectedLocation={setSelectedCompany}
          onClose={handleDialogClose}
        />
        <DeleteConfirmationDialog
          open={confirmDeleteDialogOpen}
          onClose={() => setConfirmDeleteDialogOpen(false)}
          onConfirm={handleDelete}
        />
      </Grid>
    </Box>
  );
};

export default Section1;
