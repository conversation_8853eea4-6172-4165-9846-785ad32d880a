// ** React Imports
import { useState } from "react";

// ** Next Import
import Link from "next/link";

// ** MUI Components
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import InputLabel from "@mui/material/InputLabel";
import IconButton from "@mui/material/IconButton";
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import useMediaQuery from "@mui/material/useMediaQuery";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled, useTheme } from "@mui/material/styles";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Configs
import themeConfig from "src/configs/themeConfig";

// ** Config
import authConfig from "src/configs/auth";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";
import { Grid, List, ListItem, Radio, RadioGroup } from "@mui/material";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import { useDropzone } from "react-dropzone";

// ** Styled Components
const feedback = [
  {
    label:
      "Administration and Audit (admin center, user management, activity lot)",
  },
  { label: "Attachments" },
  { label: "Authentication and Sign-in (including SSO)" },
  { label: "Brandfolder" },
  { label: "Bridge by Smartshee" },
  { label: "Calendar App" },
  {
    label: "Connectors (Data Shuttle, Jira, Salesforce, Dynamics, ServiceNow)",
  },
  { label: "Control Center" },
  { label: "Conversations" },
  { label: "Dashboards" },
  { label: "DataMesh" },
  { label: "DataTable" },
  { label: "Data Shuttle" },
  { label: "Desktop App" },
  { label: "Developers and API" },
  { label: "Document Generation" },
  { label: "Dynamic View" },
  { label: "Forms" },
  { label: "Governance" },
  { label: "Home and Navigation (favorites, folders, workspaces)" },
  { label: "In-app Solution Center" },
  { label: "In-app Workflows" },
  { label: "Integrations" },
  {
    label:
      "Messaging App Notifications (Hangouts, Microsoft Teams, Slack, Workplace)",
  },
  { label: "Mobile" },
  { label: "Notifications (in-app and email)" },
];

const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: "0.875rem",
  textDecoration: "none",
  color: theme.palette.primary.main,
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));

const FeedbackForm = () => {
  const [files, setFiles] = useState([]);

  const [value, setValue] = useState("");
  const handleChange = (event) => {
    setValue(event.target.value);
  };

  // ** Hooks
  const theme = useTheme();

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      setFiles(acceptedFiles.map((file) => Object.assign(file)));
    },
  });

  const renderFilePreview = (file) => {
    if (file.type.startsWith("image")) {
      return (
        <img
          width={38}
          height={38}
          alt={file.name}
          src={URL.createObjectURL(file)}
        />
      );
    } else {
      return <Icon icon="tabler:file-description" />;
    }
  };

  const handleRemoveFile = (file) => {
    const uploadedFiles = files;
    const filtered = uploadedFiles.filter((i) => i.name !== file.name);
    setFiles([...filtered]);
  };

  const fileList = files.map((file) => (
    <ListItem key={file.name}>
      <div className="file-details">
        <div className="file-preview">{renderFilePreview(file)}</div>
        <div>
          <Typography className="file-name">{file.name}</Typography>
          <Typography className="file-size" variant="body2">
            {Math.round(file.size / 100) / 10 > 1000
              ? `${(Math.round(file.size / 100) / 10000).toFixed(1)} mb`
              : `${(Math.round(file.size / 100) / 10).toFixed(1)} kb`}
          </Typography>
        </div>
      </div>
      <IconButton onClick={() => handleRemoveFile(file)}>
        <Icon icon="tabler:x" fontSize={20} />
      </IconButton>
    </ListItem>
  ));

  const handleRemoveAllFiles = () => {
    setFiles([]);
  };

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={6}>
        <Box
          sx={{
            height: { md: "100%" },
            display: "flex",
            position: "relative",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "text.viewData",
            padding: (theme) => theme.spacing(10),
            "@media (max-width:900px)": {
              padding: (theme) => theme.spacing(8),
            },
          }}
        >
          <Box sx={{ width: "100%", maxWidth: 525 }}>
            <a href={authConfig.guestURL + "home"}>
              <img width="30" height="28" alt="" src="/images/logo.webp" />
              <Typography
                sx={{
                  textDecoration: "none",
                  display: "inline-block",
                  fontWeight: 600,
                  ml: 2,
                  mb: 4,
                  color: "primary.dark",
                  fontSize: "1.3rem",
                  lineHeight: 1.5,
                }}
              >
                Houzer
              </Typography>
            </a>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: "primary.dark",
                lineHeight: 1.7,
              }}
            >
              Houzer Product Feedback
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "primary.dark",
              }}
            >
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Maxime
              mollitia, molestiae quas vel sint commodi repudiandae consequuntur
              voluptatum laborum numquam blanditiis harum quisquam eius sed odit
              .
            </Typography>
            <Typography
              sx={{
                fontSize: 16,
                fontWeight: 600,
                my: 4,
                color: "primary.dark",
              }}
            >
              Houzer Product Team
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "primary.dark",
              }}
            >
              To file a support ticket contact us at
              https://app.houzer.co.in/contact.
            </Typography>
          </Box>
        </Box>
      </Grid>
      <Grid item xs={12} md={6}>
        <Box
          sx={{
            p: [4, 12],
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            overflowY: "scroll",
          }}
        >
          <Box
            sx={{
              height: "100%",
              width: "100%",
              maxWidth: 535,
              "& .MuiTypography-root": {
                "&.small-font": { fontSize: 13, fontWeight: 550, mb: 2.5 },
              },
            }}
          >
            <form
              noValidate
              autoComplete="off"
              onSubmit={(e) => e.preventDefault()}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  sx={{
                    mb: 1.5,
                    fontWeight: 500,
                    fontSize: "1.4rem",
                    lineHeight: 1.45,
                  }}
                >
                  Product Feedback
                </Typography>
                <Typography
                  className="small-font"
                  sx={{ color: "text.secondary" }}
                >
                  Please complete one form each for every piece of distinct
                  feedback.
                </Typography>
                <Typography
                  className="small-font"
                  sx={{ color: "text.secondary" }}
                >
                  Do not use this form if you are experiencing product or
                  technical issues. Contact us at https://houzer.co.in/contact
                  to file a support ticket.
                </Typography>
              </Box>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <CustomAutocomplete
                    fullWidth
                    autoHighlight
                    id="autocomplete-service-select"
                    options={feedback}
                    getOptionLabel={(option) => option.label || ""}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        {option.label}
                      </Box>
                    )}
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        size="small"
                        placeholder="Select"
                        label="What is your feedback about?"
                        sx={{
                          "& .MuiInputLabel-root": {
                            mb: 2.5,
                            fontWeight: 600,
                          },
                        }}
                        inputProps={{
                          ...params.inputProps,
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <CustomTextField
                    fullWidth
                    rows={3}
                    multiline
                    required
                    sx={{
                      "& .MuiInputLabel-root": {
                        lineHeight: 1.6,
                        fontWeight: 550,
                        whiteSpace: "pre-line",
                      },
                    }}
                    id={""}
                    label={`Tell us about your feedback\nPlease be as descriptive as possible.`}
                    defaultValue=""
                  />
                </Grid>
                <Grid item xs={12}>
                  <CustomTextField
                    fullWidth
                    rows={3}
                    multiline
                    required
                    sx={{
                      "& .MuiInputLabel-root": {
                        mb: 2.5,
                        fontWeight: 550,
                      },
                    }}
                    id={""}
                    label={`How does this impact you today?`}
                    defaultValue=""
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography
                    className="small-font"
                    sx={{ color: "text.secondary" }}
                  >
                    Any image or video that would help explain your feedback,
                    attach it here.
                  </Typography>
                  <>
                    <div {...getRootProps({ className: "dropzone" })}>
                      <input {...getInputProps()} />
                      <Box
                        sx={{
                          height: 70,
                          border: (theme) =>
                            `1px dashed ${theme.palette.divider}`,
                          borderRadius: 1,
                          display: "flex",
                          textAlign: "center",
                          alignItems: "center",
                          justifyContent: "center",
                          flexDirection: "column",
                        }}
                      >
                        <Typography className="small-font" sx={{ mb: 2 }}>
                          Drop files here or click to upload.
                        </Typography>
                      </Box>
                    </div>
                    {files.length ? (
                      <>
                        <List>{fileList}</List>
                        <div className="buttons">
                          <Button
                            sx={{ mr: 2 }}
                            color="error"
                            variant="outlined"
                            onClick={handleRemoveAllFiles}
                          >
                            Remove All
                          </Button>
                          <Button variant="contained">Upload Files</Button>
                        </div>
                      </>
                    ) : null}
                  </>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <CustomTextField
                      sx={{
                        "& .MuiInputLabel-root": {
                          mb: 2,
                          fontWeight: 600,
                        },
                      }}
                      fullWidth
                      required
                      id={""}
                      label="First Name"
                      defaultValue=""
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <CustomTextField
                      sx={{
                        "& .MuiInputLabel-root": {
                          mb: 2,
                          fontWeight: 600,
                        },
                      }}
                      fullWidth
                      required
                      id={""}
                      label="Last Name"
                      defaultValue=""
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <CustomTextField
                    sx={{
                      "& .MuiInputLabel-root": {
                        mb: 2,
                        fontWeight: 600,
                      },
                    }}
                    fullWidth
                    required
                    id={""}
                    label="Work Email"
                    defaultValue=""
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography
                    sx={{ marginBottom: 0, fontSize: 13, fontWeight: 600 }}
                  >
                    Contact Me
                  </Typography>
                  <RadioGroup
                    row
                    required
                    value={value}
                    name="simple-radio"
                    onChange={handleChange}
                    aria-label="simple-radio"
                  >
                    <FormControlLabel
                      value="yes"
                      control={<Radio />}
                      label={<Typography variant="body1">Yes</Typography>}
                    />
                    <FormControlLabel
                      value="no"
                      control={<Radio />}
                      label={<Typography variant="body1">No</Typography>}
                    />
                  </RadioGroup>
                </Grid>
              </Grid>

              <Divider
                sx={{
                  fontSize: "0.875rem",
                  color: "text.disabled",
                  "& .MuiDivider-wrapper": { px: 6 },
                  my: (theme) => `${theme.spacing(4)} !important`,
                }}
              ></Divider>
              <Typography variant="body2">
                By submitting this form, you agree that Houzer will have a
                perpetual right to use and incorporate any feedback or
                suggestions without any obligation of compensation. Our Privacy
                Notice below describes how we process your personal data.
              </Typography>

              <Divider
                sx={{
                  fontSize: "0.875rem",
                  color: "text.disabled",
                  "& .MuiDivider-wrapper": { px: 6 },
                  my: (theme) => `${theme.spacing(4)} !important`,
                }}
              ></Divider>
              <Box
                sx={{
                  mb: 1.75,
                  display: "flex",
                  flexWrap: "wrap",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <FormControlLabel
                  control={<Checkbox />}
                  label="Send me a copy of my responses"
                />
              </Box>
              <Button type="submit" variant="contained" sx={{ mb: 4 }}>
                submit
              </Button>
              <Box
                sx={{
                  pb: 6,
                  display: "flex",
                  alignItems: "center",
                  flexWrap: "wrap",
                  justifyContent: "center",
                }}
              >
                <Typography sx={{ color: "text.secondary", mr: 2 }}>
                  <LinkStyled href="/" sx={{ fontSize: "1rem" }}>
                    Privacy Notice
                  </LinkStyled>
                </Typography>
                <Typography sx={{ color: "text.secondary", mr: 2 }}>
                  |
                </Typography>
                <Typography>
                  <LinkStyled href="/" sx={{ fontSize: "1rem" }}>
                    Houzer
                  </LinkStyled>
                </Typography>
              </Box>
            </form>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};
FeedbackForm.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;

export default FeedbackForm;
