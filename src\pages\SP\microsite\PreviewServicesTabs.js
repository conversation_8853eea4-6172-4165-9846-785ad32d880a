// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'
import ServicesSlider from './ServicesSlider'

const PreviewServicesTabs = props => {
  // ** State
  const [value, setValue] = useState('1')

  // ** Props
  const { transformedData } = props

  const transformData = [];

  for (const category in transformedData) {
    const services = transformedData[category]?.map(service => ({
      serviceTitle: service,
      imageURL: `/images/microsite/services/${category}/${service?.replace('/', '_')}.webp`
    }));
    transformData.push({ [category]: services });
  }

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  const categoryNames = Object?.keys(transformedData);


  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="forced scroll tabs example"
      >
        {categoryNames?.map((tabContent, index) => (
          <Tab
            key={index}
            value={(index + 1).toString()}
            component="a"
            label={tabContent}
            onClick={(e) => e.preventDefault()}
          />
        ))}
      </TabList>
      {categoryNames?.map((tabContent, index) => (


        <TabPanel TabPanel key={index} value={(index + 1).toString()} sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
          <ServicesSlider slides={transformData?.find(data => Object?.keys(data)[0] === tabContent)[tabContent]} />
        </TabPanel>
      ))
      }
    </TabContext >
  );
}

export default PreviewServicesTabs
