// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import {
    Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  FormControlLabel,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  Switch,
} from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";

const LocationEdit = ({ onCancel, formData, fetchUsers }) => {
  const { listValues, getAllListValuesByListNameId } = useContext(AuthContext);

  const { control,handleSubmit, setValue, formState: { errors } } = useForm();
  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  useEffect(() => {
    const loc = formData?.locationId
      ? listValues?.find((item) => item.id === formData?.locationId)?.name
      : null;

      if (loc) {
        setValue('name', loc);
      }

  }, [formData]);

  const [isActive, setIsActive] = useState(formData?.isActive);

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  const [selectedZoneId, setSelectedZoneId] = useState(formData?.zoneId);
  const [zonesData, setZonesData] = useState(null);

  //Hooks
  const auth = useAuth();

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3> Location-Zone updated Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    const  message = `
    <div> 
      <h3> Failed to update Location-Zone. Please try again later.</h3>
    </div>
  `;
    

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {


    const fields = {
      id: formData.locationId,
      name: data.name.trim(),
      listNamesId:authConfig.locationlistNameId,
      isActive: isActive,
    };

    const response = await auth.patchLocation(fields, () => {
      console.error(" Master Data Details failed");
    });

    if(response){
        const zoneFields = {
            id:formData?.id,
            locationId: formData?.locationId,
            zoneId: selectedZoneId,
            isActive:isActive
        }

        const res = await auth.patchLocationZone(zoneFields, () => {
            console.error(" Master Data Details failed");
          });
        if(res){
            handleSuccess();
        }
        else{
            handleFailure();
        }

    }

    const currentPage = 1;
    const currentPageSize = 10;

    fetchUsers(currentPage, currentPageSize);

    onCancel();
  }

  const handleZoneSuccess = (data) => {
    setZonesData(data?.listValues);
  };

  const handleSelectZoneChange = (event) => {
    const selectedId = event.target.value;
    setSelectedZoneId(selectedId);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allZonesListNameId,
        handleZoneSuccess,
        handleError
      );
    }
  }, [authConfig]);

  return (
    <Box sx={{ pt: 3 }}>
         <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

      <Grid container spacing={5}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: "location is required" }}
              defaultValue=""
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  aria-describedby="Section1-name"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="zone-select-label">Zone</InputLabel>
            <Select
              labelId="zone-select-label"
              id="zone-select"
              size="small"
              value={selectedZoneId}
              defaultValue={selectedZoneId}
              label="zone"
              onChange={handleSelectZoneChange}
            >
              {zonesData?.map((zone) => (
                <MenuItem key={zone.id} value={zone.id}>
                  {zone.listValue}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {errors.selectedZoneId && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-selectedZoneId"
            >
              {errors.selectedZoneId?.message}
            </FormHelperText>
          )}
        </Grid>

        <Grid item xs={3} sm={6} sx={{ mb: 1.5 }}>
          <Controller
            name="isActive"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={isActive}
                    onChange={handleOnChange}
                    name="isActive"
                  />
                }
                label="Is Active"
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <DialogActions 
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LocationEdit;
