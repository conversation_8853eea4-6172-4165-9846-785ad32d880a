// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import ServicesBroker from "./sections/ServicesBroker";
import TeamBroker from "./sections/TeamBroker";

// ** Config
import authConfig from "src/configs/auth";

// ** Styled Component
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import {
  Box,
  Button,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import { useTheme } from "@emotion/react";
import CompanyDetailsBroker from "./sections/CompanyDetailsBroker";

import {
  returnEntity,
  getAuthorizationHeaders,
  getUrl,
} from "src/helpers/utils";
import axios from "axios";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";
import Remarks from "src/@core/components/custom-components/Remarks";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import { useRouter } from "next/router";

import { useRBAC } from "src/pages/permission/RBACContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const BrokerForm = () => {
  const { entityData, getEntityProfile } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");
  const router = useRouter();

  const { can } = useRBAC();

  const [expanded, setExpanded] = useState(true);

  
  useEffect(() => {
    getEntityProfile();
    console.log("use effect -Broker")
  },[])

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "BROKER") {
      router.push("/401");
    }
  }, []);

  if (entityCategory === "BROKER") {
    return (
      <div>
        <>
          <style>
            {`
         .tableBody:hover {
             background-color: #f6f6f7;
             cursor: pointer
         }
     `}
          </style>

          <DatePickerWrapper>
            <Grid container spacing={6} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Broker Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <CompanyDetailsBroker
                  data={entityData}
                  expanded={expanded}
                ></CompanyDetailsBroker>
              </Grid>

              <Grid item xs={12}>
                <TeamBroker data={entityData} expanded={expanded}></TeamBroker>
              </Grid>

              <Grid item xs={12}>
                <ServicesBroker
                  data={entityData}
                  expanded={expanded}
                ></ServicesBroker>
              </Grid>
              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'broker_otherServices_READ'}
                  permission={'broker_otherServices_UPDATE'}
                ></OtherServices>
              
              </Grid>
              <Grid item xs={12}>
             
                <Remarks data={entityData} expanded={expanded} readPermission={'broker_remarks_READ'} permission={'broker_remarks_UPDATE'}></Remarks>
              
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default BrokerForm;
