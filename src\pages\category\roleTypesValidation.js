import * as yup from "yup";

export const RoleTypesValidation = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  
  return yup.object().shape({
    roleType: yup
      .string()
      .required("Role Type is required")
      .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
      .min(3,"Role Type must be atleast 3 characters")
      .max(30, "Role Type must not exceed 30 characters"), 
  });
};

export default RoleTypesValidation;

export { yup };
