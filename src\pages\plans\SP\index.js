import React, { useState, useEffect } from "react";
import axios from "axios";
import Card from "@mui/material/Card";
import { styled } from "@mui/material/styles";
import MuiCardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import { Typography } from "@mui/material";

// ** Demo Imports
import PricingPlans from "src/views/pages/pricing/PricingPlans";
import PricingHeader from "src/views/pages/pricing/PricingHeader";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";


import { useRBAC } from "src/pages/permission/RBACContext";


const CardContent = styled(MuiCardContent)(({ theme }) => ({
  [theme.breakpoints.down("xl")]: {
    padding: "1rem 0.5rem 0.5rem 1.2rem !important",
    width: "830px",
    margin: "0 auto",
  },
  [theme.breakpoints.down(775)]: {
    width: "auto",
    height: "auto",
  },
  [theme.breakpoints.down("md")]: {
    padding: "0.5rem 0.5rem 0.5rem 2.5rem !important",
  },
  [theme.breakpoints.down("sm")]: {
    padding: "0.5rem !important",
  },
  [theme.breakpoints.down("xs")]: {
    padding: "0.5rem !important",
  },
}));

const Pricing = () => {
  // ** States
  const [pricingPlans, setPricingPlans] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.settings) + "?settingsType=PACKAGE_TYPES",
      headers: getAuthorizationHeaders(),
    })
    .then((res) => {
      // Check for the packageTypes within the packageDTO
      if (res.data.packageDTO && res.data.packageDTO.packageTypes) {
        setPricingPlans(res.data.packageDTO.packageTypes);
      } else {
        console.log("No packageTypes found in the response.");
      }
    })
    .catch((err) => console.log("PACKAGE_TYPES error", err));
  }, []);

  const { can } = useRBAC();

  if (can('plans_READ')) {
    return (
      <MuiCardContent sx={{ paddingTop: "0 !important" }}>
        <CardContent sx={{ paddingTop: "0 !important" }}>
          <PricingHeader />
          <Grid container spacing={1} sx={{ flexDirection: "column", gap: "0.7rem" }}>
            {Array.isArray(pricingPlans) && pricingPlans.length > 0 ? (
              pricingPlans.map((pricingPlan, index) => (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  key={index}
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    width: "100%",
                  }}
                >
                  <PricingPlans data={[pricingPlan]} />
                </Grid>
              ))
            ) : (
              <Typography variant="h6" sx={{ textAlign: "center", width: "100%" }}>
                
              </Typography>
            )}
          </Grid>
        </CardContent>
      </MuiCardContent>
    );
  } else {
    return null;
  }
};

export default Pricing;