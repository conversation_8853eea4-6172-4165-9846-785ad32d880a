// ** React Imports
import { forwardRef } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import InputAdornment from '@mui/material/InputAdornment'
import { Box } from '@mui/system'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'

// ** Icon Imports
import Icon from 'src/@core/components/icon'
import SelectBasic from 'src/@core/components/custom-components/SelectBasic'

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false,
  Textarea: ''
}

const names = [
    {
      value:'REFERENCE', 
      key:'Reference'
    },
    {
      value:'WHATSAPP',
      key:'Whatsapp'
    },
    {
      value:'OTHER',
      key:'Other'
    }
  ];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const Section1 = ({ onCancel,formData }) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();


  async function submit(data) {

    const response = await auth.updateEntity(data,() => {
      console.error("Professional Requirement  Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      
        <Grid container spacing={5}>

        <Grid item xs={6} >
            <SelectBasic register={register} id={'source'} label={'Source'} name="source" nameArray={names} />
          </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name='nameOfReference/Whatsapp group'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.name}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  label='Name of reference/ Whatsapp group'
                  onChange={onChange}
                  placeholder='Enter your name of reference'
                  error={Boolean(errors.name)}
                  aria-describedby='Section1-name'


                />
              )}
            />
            {errors.name && (
              <FormHelperText sx={{ color: 'error.main' }} id='Section1-name'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
         
         

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name='mobileNumber'
                control={control}
                rules={{ required: true, pattern: /^[0-9]{10,20}$/ }}
                defaultValue={formData?.mobileNumber}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    type='tel'
                    value={value}
                    label='Contact Number'
                    onChange={onChange}
                    error={Boolean(errors.mobileNumber)}
                    placeholder='+91 12 345 678 90'
                    inputProps={{ maxLength: 12 }}
                    aria-describedby='validation-mobileNumber'
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Icon icon='tabler:phone' style={{ fontSize: '16px' }} />
                        </InputAdornment>
                      )
                    }}
                  />
                )}
              />
              {errors.contactNumber?.type === 'required' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-contactNumber'>
                  This field is required
                </FormHelperText>
              )}
              {errors.contactNumber?.type === 'pattern' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-contactNumber'>
                  Please enter a contact number
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name='email'
                control={control}
                rules={{ required: true, pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/ }}
                defaultValue={formData?.email}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    type='email'
                    value={value}
                    label='Email '
                    onChange={onChange}
                    error={Boolean(errors.email)}
                    placeholder='Enter email address'
                    aria-describedby='validation-email'
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Icon icon='tabler:mail' style={{ fontSize: '16px' }} />
                        </InputAdornment>
                      )
                    }}
                  />
                )}
              />
              {errors.email?.type === 'required' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-email'>
                  Email address is required
                </FormHelperText>
              )}
              {errors.email?.type === 'pattern' && (
                <FormHelperText sx={{ color: 'error.main' }} id='validation-email'>
                  Please enter a valid email address
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          

          <Grid item xs={12}>
          <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
        </Grid>
     
    </Box>
  )
}

export default Section1
