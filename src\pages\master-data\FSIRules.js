import {
  <PERSON>,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  FormHelperText,
  Menu,
  MenuItem,
  Tooltip,
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from "src/@core/components/mui/chip";
import DeleteDialog from "./DeleteDialog";

import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";

import DataDetailsView from "./DataDetailsView";
import RuleDataView from "./RuleDataView";
import RuleDeleteDialog from "./RuleDeleteDialog";
import RuleActivateDialog from "./RuleActivateDialog";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const FSIRules = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);

  const [openCreateDialog, setCreateOpenDialog] = useState(false);
  const auth = useAuth();

  const { fsiRulesData, setFsiRulesData, fsiRulesDataDetails } =
    useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm();

  async function submit(data) {
    let basicZonalFSI = parseFloat(data.basicZonalFSI);
    let premiumFSI = parseFloat(data.premiumFSI);
    let tdr = parseFloat(data.tdr);
    let maxFSI = basicZonalFSI + premiumFSI + tdr;
    maxFSI = parseFloat(maxFSI.toFixed(2));
    let maxFungibleFSI = maxFSI * 1.35;
    maxFungibleFSI = parseFloat(maxFungibleFSI.toFixed(4));

    const fields = {
      location: selectedLocation?.name,
      ward: selectedWard?.name,
      type: selectedType?.name,
      city: selectedCityType?.name,
      minRoadWidth: data.minRoadWidth,
      maxRoadWidth: data.maxRoadWidth,
      basicZonalFSI: data.basicZonalFSI,
      premiumFSI: data.premiumFSI,
      tdr: data.tdr,
      maxFSI: maxFSI.toString(),
      fungibleFSI: maxFungibleFSI.toString(),
    };

    try {
      const response = await auth.postFSIRule(
        fields,
        handleFailure,
        handleSuccess
      );
      reset();
    } catch (error) {
      console.error("FSI Rule Creation failed:", error);
      handleFailure();
    }

    setCreateOpenDialog(false);
    reset();

    fetchUsers(page, pageSize, searchKeyword);
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> FSI Rule added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Add FSI Rule. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.fsiRulesGetAllEndpoint);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.fsiRulesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [selectedWard, setSelectedWard] = useState("");
  const [listOfWards, setListOfWards] = useState([]);

  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();
  }, []);

  const handleWardChange = (newValue) => {
    setSelectedWard(newValue);
  };

  const [selectedLocation, setSelectedLocation] = useState("");
  const [listOfLocations, setListOfLocations] = useState([]);

  useEffect(() => {
    const fetchLocations = async () => {
      const data = {
        masterDataType: "LOCATION_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfLocations(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchLocations();
  }, []);

  const handleLocationChange = (newValue) => {
    setSelectedLocation(newValue);
  };

  const [selectedType, setSelectedType] = useState("");
  const [listOfTypes, setListOfTypes] = useState([]);

  useEffect(() => {
    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();
  }, []);

  const handleTypeChange = (newValue) => {
    setSelectedType(newValue);
  };

  const [selectedCityType, setSelectedCityType] = useState("");
  const [listOfCityTypes, setListOfCityTypes] = useState([]);

  useEffect(() => {
    const fetchCityTypes = async () => {
      const data = {
        masterDataType: "CITY_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfCityTypes(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchCityTypes();
  }, []);

  const handleCityTypeChange = (newValue) => {
    setSelectedCityType(newValue);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const columns = [
    { field: "location", headerName: "Location", flex: 0.11, minWidth: 100 },
    { field: "ward", headerName: "Ward", flex: 0.11, minWidth: 100 },
    { field: "type", headerName: "Type", flex: 0.11, minWidth: 100 },
    { field: "city", headerName: "City/SubUrban", flex: 0.11, minWidth: 100 },
    {
      field: "minRoadWidth",
      headerName: "Min Road Width",
      flex: 0.11,
      minWidth: 100,
    },
    {
      field: "maxRoadWidth",
      headerName: "Max Road Width",
      flex: 0.11,
      minWidth: 100,
    },
    {
      field: "basicZonalFSI",
      headerName: "Basic/Zonal FSI",
      flex: 0.11,
      minWidth: 100,
    },
    {
      field: "premiumFSI",
      headerName: "Premium FSI",
      flex: 0.11,
      minWidth: 100,
    },
    { field: "tdr", headerName: "TDR", flex: 0.11, minWidth: 100 },
    { field: "maxFSI", headerName: "Max FSI", flex: 0.11, minWidth: 100 },
    {
      field: "fungibleFSI",
      headerName: "Fungible FSI",
      flex: 0.11,
      minWidth: 100,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.17,
      minWidth: 110,
      sortable: false,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setFsiRulesData({
            ...fsiRulesData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };
        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>

            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const handleOpenDialog = () => {
    setCreateOpenDialog(true);
  };

  const handleCloseCreateDialog = () => {
    setCreateOpenDialog(false);
  };

  return (
    <>
      <Grid>
        <Card>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">FSI Rules</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid item xs={12} sm="auto">
                    <Button variant="contained" onClick={handleOpenDialog}>
                      Add FSI Rule
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <Dialog
            open={openCreateDialog}
            onClose={handleCloseCreateDialog}
            maxWidth={"md"}
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 19, md: 20 },
              }}
              textAlign={"center"}
            >
              Add FSI Rule
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseCreateDialog}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition: "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <CustomAutocomplete
                    autoHighlight
                    id="autocomplete-ward-select"
                    label="Ward"
                    options={listOfWards}
                    getOptionLabel={(option) => option?.name || ""}
                    value={selectedWard}
                    getOptionSelected={(option, value) =>
                      option.id === value.id
                    }
                    onChange={(event, newValue) => handleWardChange(newValue)}
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        placeholder="Choose Your Ward"
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomAutocomplete
                    autoHighlight
                    id="autocomplete-location-select"
                    label="Location"
                    options={listOfLocations}
                    getOptionLabel={(option) => option.name || ""}
                    value={selectedLocation}
                    getOptionSelected={(option, value) =>
                      option.id === value.id
                    }
                    onChange={(event, newValue) =>
                      handleLocationChange(newValue)
                    }
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        placeholder="Select Location"
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomAutocomplete
                    autoHighlight
                    id="autocomplete-type-select"
                    label="Type"
                    options={listOfTypes}
                    getOptionLabel={(option) => option.name || ""}
                    value={selectedType}
                    getOptionSelected={(option, value) =>
                      option.id === value.id
                    }
                    onChange={(event, newValue) => handleTypeChange(newValue)}
                    renderInput={(params) => (
                      <CustomTextField {...params} placeholder="Select Type" />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomAutocomplete
                    autoHighlight
                    id="autocomplete-cityType-select"
                    label="City Type"
                    options={listOfCityTypes}
                    getOptionLabel={(option) => option.name || ""}
                    value={selectedCityType}
                    getOptionSelected={(option, value) =>
                      option.id === value.id
                    }
                    onChange={(event, newValue) =>
                      handleCityTypeChange(newValue)
                    }
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        placeholder="Select City Type"
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="minRoadWidth"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <TextField
                          type="number"
                          size="small"
                          value={value}
                          label="Min Road Width"
                          InputLabelProps={{ shrink: true }}
                          onChange={onChange}
                          error={Boolean(errors.minRoadWidth)}
                          placeholder="Enter min road width"
                          aria-describedby="validation-minRoadWidth"
                        />
                      )}
                    />
                    {errors.minRoadWidth?.type === "required" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-minRoadWidth"
                      >
                        Minimum road width is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="maxRoadWidth"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <TextField
                          type="number"
                          size="small"
                          value={value}
                          label="Max Road Width"
                          InputLabelProps={{ shrink: true }}
                          onChange={onChange}
                          error={Boolean(errors.maxRoadWidth)}
                          placeholder="Enter max road width"
                          aria-describedby="validation-maxRoadWidth"
                        />
                      )}
                    />
                    {errors.maxRoadWidth?.type === "required" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-maxRoadWidth"
                      >
                        Maximum road width is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="basicZonalFSI"
                      control={control}
                      rules={{
                        required: true,
                      }}
                      render={({ field: { value, onChange } }) => (
                        <TextField
                          type="number"
                          size="small"
                          value={value}
                          label="Basic/Zonal FSI"
                          InputLabelProps={{ shrink: true }}
                          onChange={onChange}
                          error={Boolean(errors.basicZonalFSI)}
                          placeholder="Enter basic/zonal FSI"
                          aria-describedby="validation-basicZonalFSI"
                        />
                      )}
                    />
                    {errors.basicZonalFSI?.type === "required" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-basicZonalFSI"
                      >
                        Basic/Zonal FSI is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="premiumFSI"
                      control={control}
                      rules={{
                        required: true,
                      }}
                      render={({ field: { value, onChange } }) => (
                        <TextField
                          type="number"
                          size="small"
                          value={value}
                          label="Premium FSI"
                          InputLabelProps={{ shrink: true }}
                          onChange={onChange}
                          error={Boolean(errors.premiumFSI)}
                          placeholder="Enter premium FSI"
                          aria-describedby="validation-premiumFSI"
                        />
                      )}
                    />
                    {errors.premiumFSI?.type === "required" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-premiumFSI"
                      >
                        Premium FSI is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="tdr"
                      control={control}
                      rules={{
                        required: true,
                      }}
                      render={({ field: { value, onChange } }) => (
                        <TextField
                          type="number"
                          size="small"
                          value={value}
                          label="TDR"
                          InputLabelProps={{ shrink: true }}
                          onChange={onChange}
                          error={Boolean(errors.tdr)}
                          placeholder="Enter tdr"
                          aria-describedby="validation-tdr"
                        />
                      )}
                    />
                    {errors.tdr?.type === "required" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-tdr"
                      >
                        TDR is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleCloseCreateDialog}
              >
                Cancel
              </Button>
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>
          <Divider />
          <RuleDataView
            open={openDialog}
            data={fsiRulesDataDetails}
            expanded={expanded}
            onCancel={handleCloseDialog}
            fetchUsers={fetchUsers}
          />
          <Divider />
          <CardContent>
            <div style={{ height: 430, width: "100%" }}>
              <DataGrid
                rows={userList}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            </div>
          </CardContent>
          <Divider />
          <RuleDeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />

          <RuleActivateDialog
            open={openActivateDialog}
            onClose={handleCloseActivateDialog}
            data={currentRow}
          />
        </Card>
      </Grid>
      <Divider />
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default FSIRules;
