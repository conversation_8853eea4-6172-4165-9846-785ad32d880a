// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import LegalServicesEdit from "src/pages/SP/legal/Sections/LegalServicesEdit"

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  boolean,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";


const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const Services = ({ data, expanded }) => {
  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  return (
    <>
    {/* {can('legal_services_READ') && */}
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Services Offered"}
        body={
          <>
            {state3 === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
               // onClick={can('legal_services_UPDATE') ? viewClick3 : null}
                onClick={viewClick3}

              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Service Name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          (Yes/No):
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          Legal Consultation
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.legalConsultation ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Bye laws and Rules Drafting</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.bylawsAndRulesDrafting ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Property Documentation Review</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.propertyDocumentationReview ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Property Disputes Resolution</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.propertyDisputesResolution ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          Legal Guidance during Redevelopment and Construction
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.legalGuidanceDuringRedevelopment
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          {" "}
                          Cooperative Housing Society Registration
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.cooperativeHousingSocietyRegistration
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          Advising on conducting General Body Meetings
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.advisingOnGeneralBodyMeetings
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Member Disputes</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.memberDisputes ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Tenant and Landlord Matters</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.tenantAndLandlordMatters ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          Compliance and Regulatory Matters
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.complianceAndRegulatoryMatters ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Government Liaison</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.governmentLiaison ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Legal Notices and Petitions</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.legalNoticesAndPetitions ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell>
                        <Typography>Consumer Complaints</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.consumerComplaints ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Arbitration and Mediation</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.arbitrationAndMediation ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Property Tax Matters</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.propertyTaxMatters ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>
                          Documentation and Legal Records Maintenance
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.documentationAndLegalRecords
                            ? "Yes"
                            : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Legal Due Diligence</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.legalDueDiligence ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography>Insurance Claims</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.insuranceClaims ? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state3 === "edit" && (
              <LegalServicesEdit formData={data} onCancel={editClick3} />
            )}
          </>
        }
        expanded={expanded}
      />
    {/* } */}
    </>
  );
};
export default Services;
