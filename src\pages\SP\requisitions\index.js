import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";

import NavTabsRequisitions from "src/@core/components/custom-components/NavTabsRequisitions";
import RequisitionDetails from "./RequisitionDetails";
import { useRBAC } from "src/pages/permission/RBACContext";



const Requisitions = () => {

  const { can } = useRBAC();

  if(can('spRequisitions_READ')){
  return (
    <Card>
      <NavTabsRequisitions
        tabContent1={
          <>
            <RequisitionDetails/>
          </>
        }
        tabContent2={
          <>
             <RequisitionDetails/>
          </>
        }
        tabContent3={
            <>
               <RequisitionDetails/>
            </>
        }
        tabContent4={
            <>
               <RequisitionDetails/>
            </>
        }
        tabContent5={
            <>
               <RequisitionDetails/>
            </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default Requisitions;
