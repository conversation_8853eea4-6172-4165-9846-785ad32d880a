import {
  <PERSON>,
  Card,
  CardContent, DialogContentText,
  Di<PERSON>r,
  <PERSON>ltip,
  <PERSON>u,
  MenuItem
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from 'src/@core/components/mui/chip';
import SubServicesView from "./SubServicesView";
import DeleteSubServiceDialog from "./DeleteSubServiceDialog";
import UpdateSubServiceDialog from "./UpdateSubServiceDialog";


const userStatusObj = {
  true: 'Active',
  false: 'InActive'
}


const SubServices = () => {


  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();


  const { servicesData, setServicesData, servicesDataDetails } =
    useContext(AuthContext);


  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [5, 10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);




  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    }
  });


  const handleSuccess = () => {
    const message = `
    <div> 
      <h3> Sub Service Values added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Sub Service Values. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };



  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchUsers(page,pageSize,searchKeyword)
  };



  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.subServicesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };


    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.dataFieldsResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };


  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize,searchKeyword);
  };

  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchUsers(page, pageSize,searchKeyword);
  }

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || 'Unknown';
  }

  const columns = [
    { field: "serviceName", headerName: "Service Type", flex: 2.5, minWidth: 120 },
    { field: "name", headerName: "Sub Services", flex: 2.5, minWidth: 120 },
    {
      field: "isActive",
      headerName: "Status",
      flex: 2,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin='light'
            size='small'
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: 'capitalize' }}
          />
        );
      }
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = useState(null);
        const open = Boolean(anchorEl);
  
        const handleClick = (event) => {
          setAnchorEl(event.currentTarget);
        };
  
        const handleClose = () => {
          setAnchorEl(null);
        };
  
        const onClickViewProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenDialog(true);
          handleClose();
        };
  
        const onClickDeleteProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenDeleteDialog(true);
          handleClose();
        };
  
        const onClickUpdateProfile = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenUpdateDialog(true);
          handleClose();
        };
  
        return (
          <div>
            <IconButton
              aria-label="more"
              id="long-button"
              aria-controls={open ? 'long-menu' : undefined}
              aria-expanded={open ? 'true' : undefined}
              aria-haspopup="true"
              onClick={handleClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              id="long-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              PaperProps={{
                style: {
                  maxHeight: 48 * 4.5,
                  width: '20ch',
                },
              }}
            >
              <MenuItem onClick={onClickViewProfile}>
                <Icon icon="iconamoon:edit" style={{ marginRight: '10px' }} />
                Edit
              </MenuItem>
              {params.row.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>
                  <Icon icon="iconamoon:trash" style={{ marginRight: '10px' }} />
                  Deactivate
                </MenuItem>
              ) : (
                <MenuItem onClick={onClickUpdateProfile}>
                  <Icon icon="mdi:check-circle" style={{ marginRight: '10px' }} />
                  Activate
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];
  
  

  return (
    <>
      <Grid>
        <Card>
        <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">
                  {'List of Services with Sub Services'}
                </Typography>
              </Grid>


            </Grid>
          </Box>
          <Divider />
          <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"md"}>
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 15, md: 21 },
              }}
              textAlign={"center"}
            >
              Edit Service Details
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseDialog}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: 
                '#66BB6A',
                 transition: 'background 0.5s ease, transform 0.5s ease',                       
                },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
             <SubServicesView
                  data={currentRow}
                  expanded={expanded}
                  onCancel={handleCloseDialog}
                  fetchUsers={fetchUsers}
              />

            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "center",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleCloseDialog}
              >
                Close
                            </Button>
          </DialogActions>
        </Dialog>

          <Divider />
          <CardContent>
          <div style={{ height: 380, width: "100%", overflow: "auto" }}>    
              <DataGrid
                rows={userList}
                columns={columns}

                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38} 
              />
            </div>
          </CardContent>
          <Divider />
          <DeleteSubServiceDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />

          <UpdateSubServiceDialog
            open={openUpdateDialog}
            onClose={handleCloseUpdateDialog}
            data={currentRow}
          />
        </Card>
      </Grid>
      <Divider/>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

    </>
  );
};

export default SubServices;