// ** React Imports
import { forwardRef, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import { yupResolver } from "@hookform/resolvers/yup";

import BrokerValidationSection1 from "./BrokerValidation";

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const names = [
  {
    value: "LESS_THAN_5_YEARS",
    key: "Less than 5 years",
  },
  {
    value: "_5_TO_10_YEARS",
    key: "5-10 years",
  },
  {
    value: "MORE_THAN_10_YEARS",
    key: "More than 10 years",
  },
];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section2 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();
  const fields = ["awards","briefProfile","yearsOfExperience"];
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(BrokerValidationSection1(fields)),
    mode: "onChange",
  });

  const [yearsOfExperience, setYearsOfExperience] = useState(
    formData?.yearsOfExperience
  );

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Broker Team Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={4}>
          <SelectProject
            register={register}
            id={"yearsOfExperience"}
            label={"Year of Experience"}
            name="yearsOfExperience"
            nameArray={names}
            defaultValue={formData?.yearsOfExperience}
            value={yearsOfExperience}
            onChange={(e) => setYearsOfExperience(e.target.value)}
            error={Boolean(errors.yearsOfExperience)}
          />
          {errors.yearsOfExperience && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-yearsOfExperience"
            >
             {errors.yearsOfExperience.message}
            </FormHelperText>
          )}
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="awards"
              control={control}
              defaultValue={formData?.awards}
              render={({ field }) => (
                <TextField
                  {...field}
                  rows={4}
                  multiline
                  type="text"
                  label="Awards"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.awards)}
                  helperText={errors.awards?.message}
                  aria-describedby="Section2-awards"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="briefProfile"
              control={control}
              defaultValue={formData?.briefProfile}
              render={({ field }) => (
                <TextField
                  {...field}
                  rows={4}
                  multiline
                  label="Brief Profile in your words"
                  InputLabelProps={{ shrink: true }}
                  helperText={errors.briefProfile?.message}
                  error={Boolean(errors.briefProfile)}
                  aria-describedby="Section2-briefProfile"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Section2;
