// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import axios from "axios";
// ** Third Party Imports
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, FormControlLabel, InputLabel, MenuItem, Select, Switch, Typography } from "@mui/material";
import { Box } from "@mui/system";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";
import EmployeeValidations from "./EmployeeValidations";
import { useContext, useEffect, useState } from "react";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";




const EmployeeDetailsEdit = ({ onCancel, formData,fetchUsers }) => {


  const [isActive, setIsActive] = useState(formData?.status); 

  const handleOnChange = (event) => {
    if(event.target.checked){
      setIsActive("ACTIVE")
    }else{
      setIsActive("INACTIVE")
    }
   ;
  };

  const { getAllListValuesByListNameId } =
    useContext(AuthContext);

  const [employeeId, setEmployeeId] = useState(formData?.reportingTo);
  const [departmentId, setDepartmentId] = useState(formData?.department);
  const [selectedLocationId, setSelectedLocationId] = useState(formData?.employeeMetaData?.employeeData?.workLocation);
  const [locationsData, setLocationsData] = useState(null);
  const [selectedDesignationId, setSelectedDesignationId] = useState(formData?.designation);
  const [designationsData,setDesignationsData] = useState(null);

  console.log("DEP_INITAL ID",formData?.department)

  //Hooks
  const auth = useAuth();
  const fields = [
    "firstName",
    "lastName",
    "mobileNumber"
  ];
  
  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(EmployeeValidations(fields)),
    mode: "onChange"
  });

  async function submit(data) {

    const fields = {
      id:formData.id,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email: data.email,
      designation: selectedDesignationId,
      reportingTo: employeeId,
      department:departmentId,
      status:isActive,
      employeeMetaData:{
        employeeData: {
        address: {
          street1: data.street1||'',
          street2: data.street2||'',
          city: data.city||'',
          state: data.state||'',
          country: data.country||'',
          pinCode: data.pinCode||'',
        },
        workLocation: selectedLocationId
      }
      },
    };

    const response = await auth.patchEmployee(fields, () => {
      console.error(" Employee Details failed");
    });

    const currentPage = 1;  
    const currentPageSize = 5;

    fetchUsers(currentPage, currentPageSize);
    onCancel();
  }

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] =  useState([]);

  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [departmentOptions, setDepartmentOptions] =  useState([]);

  useEffect(() => {
    // Fetch employees //to do as we fetching same data in edit. define in auth context to use in both files
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown) + "?selectionType=EMPLOYEE_SUBCATEGORIES",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setListOfRoleTypes(res.data.data);
        })
        .catch((err) => console.log("Role Types error", err));
    
  }, []);

  useEffect(() => {
    if(!!authConfig) {

      getAllListValuesByListNameId(
        authConfig.workLocation,
        handleLocationSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.designationId,
        handleDesignationSuccess,
        handleError
      );
    }
  }, [authConfig]);


  useEffect(()=>{
    if(!!listOfEmployees) {
      let data = [];
      listOfEmployees.map((entry)=>{
        data.push({value:entry.id, key:entry.name});
      });
      setEmployeesOptions(data);
    }
  }, [listOfEmployees])

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = [];
      listOfRoleTypes[0]?.metaData?.subRoleTypes?.forEach((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setDepartmentOptions(data);
    }
  }, [listOfRoleTypes]);

  const handleEmployeeChange = (newValue) => {
    setEmployeeId(newValue);
  };

  const handleDepartmentChange = (newValue) => {
    console.log("DEP _ID",newValue)
    setDepartmentId(newValue);
  };

  const handleLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  
  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">Employee Id:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.systemCode}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">Email:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {formData?.email}
            </Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={4} sx={{ mt: 1.5, mb: 1.5 }}>
      <Controller
        name="status" 
        control={control}
        render={() => (
          <FormControlLabel
            control={
              <Switch
                checked={isActive=="ACTIVE"?true:false} 
                onChange={handleOnChange}
                name="status" 
              />
            }
            label="Is Active"
          />
        )}
      />
    </Grid>
        
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="firstName"
              control={control}
              defaultValue={formData?.firstName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  size="small"
                  label=" First Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your firstName"
                  error={Boolean(errors.firstName)}
                  helperText={errors.firstName?.message}
                  aria-describedby="Section1-firstName"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="lastName"
              control={control}
              defaultValue={formData?.lastName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  size="small"
                  label="Last Name"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your firstName"
                  error={Boolean(errors.lastName)}
                  helperText={errors.lastName?.message}
                  aria-describedby="Section1-lastName"
                />
              )}
            />
          </FormControl>
        </Grid>

        
<Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="designation-select-label">Designation</InputLabel>
                    <Select
                      labelId="designation-select-label"
                      id="designation-select"
                      size="small"
                      defaultValue={selectedDesignationId}
                      value={selectedDesignationId}
                      label="Designation"
                      onChange={handleSelectDesignationChange}
                    >
                      {designationsData?.map((designation) => (
                        <MenuItem key={designation.id} value={designation.id}>
                          {designation.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  {errors.selectedDesignationId && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-selectedDesignationId"
                    >
                      {errors.selectedDesignationId?.message}
                    </FormHelperText>
                  )}
                </Grid>
                
        <Grid item xs={12} sm={4}>
                  <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"employeeId-select"}
                      label={"Reporting to "}
                      name="employeeId-select"
                      nameArray={employeesOptions}
                      defaultValue={employeeId}
                      value={employeeId}
                      onChange={(event) =>
                        handleEmployeeChange(event.target.value)
                      }
                      aria-describedby="employeeId-select"
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                  <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"departmentId-select"}
                      label={"Department"}
                      name="departmentId-select"
                      nameArray={departmentOptions}
                      defaultValue={departmentId}
                      value={departmentId}
                      onChange={(event) =>
                        handleDepartmentChange(event.target.value)
                      }
                      aria-describedby="departmentId-select"
                    />
                  </Grid>

       
        <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="location-select-label">Work Location</InputLabel>
                    <Select
                      labelId="location-select-label"
                      id="location-select"
                      size="small"
                      defaultValue={selectedLocationId}
                      value={selectedLocationId}
                      label="Work Location"
                      placeholder="Work Location"
                      onChange={handleSelectChange}
                    >
                      {locationsData?.map((location) => (
                        <MenuItem key={location.id} value={location.id}>
                          {location.listValue}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  {errors.selectedLocationId && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-selectedLocationId"
                    >
                      {errors.selectedLocationId?.message}
                    </FormHelperText>
                  )}
                </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  size="small"
                  type="tel"
                  label="Contact Number"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.mobileNumber)}
                  placeholder="Enter 10 digit Mobile Number"
                  helperText={errors.mobileNumber?.message}
                  aria-describedby="Section1-contactNumber"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <Divider sx={{ margin: "20px 0" }} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="street1"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.street1}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Street1"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.street1)}
                  helperText={errors.street1?.message}
                  aria-describedby="Section1-street1"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="street2"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.street2}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Street2"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.street2)}
                  helperText={errors.street2?.message}
                  aria-describedby="Section1-street2"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="pinCode"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.pinCode}
              rules={{
                required: "This field is required",
                minLength: {
                  value: 6,
                  message: "Enter a 6 digit pincode",
                },
              }}
              render={({ field: { value, onChange } }) => (
                <TextField
                  value={value}
                  type="text"
                  size="small"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={6}
                  label="Pin Code"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  onKeyDown={(event) => {
                    if (
                      !/^\d+$/.test(event.key) &&
                      event.key !== "Backspace" &&
                      event.key !== "Delete"
                    ) {
                      event.preventDefault();
                    }
                    if (
                      value &&
                      value.length === 6 &&
                      event.key !== "Backspace" &&
                      event.key !== "Delete"
                    ) {
                      event.preventDefault();
                    }
                  }}
                  placeholder="Enter Pin Code"
                  error={Boolean(errors.pinCode)}
                  helperText={errors.pinCode?.message}
                  aria-describedby="validation-basic-pin-code"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="city"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.city}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  size="small"
                  label="City"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your city"
                  error={Boolean(errors.city)}
                  helperText={errors.city?.message}
                  aria-describedby="Section1-city"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="state"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.state}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  size="small"
                  label="State"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your state"
                  error={Boolean(errors.state)}
                  helperText={errors.state?.message}
                  aria-describedby="Section1-state"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <Controller
              name="country"
              control={control}
              defaultValue={formData?.employeeMetaData?.employeeData?.address?.country}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  size="small"
                  label="Country"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your country"
                  error={Boolean(errors.country)}
                  helperText={errors.country?.message}
                  aria-describedby="Section1-country"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit" 
              variant="contained"
              onClick={() => {
                handleSubmit(submit)();
              }}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EmployeeDetailsEdit;