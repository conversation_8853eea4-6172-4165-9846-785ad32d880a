// ** React Imports
import { useCallback, useState } from "react";
import { useEffect, useContext, useRef } from "react";

// ** Next Imports
import Link from "next/link";
import CustomChip from "src/@core/components/mui/chip";
import Tooltip from "@mui/material/Tooltip";

// ** MUI Components
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import Box from "@mui/material/Box";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  CircularProgress,
  Grid,
  Divider,
  Card,
  Menu,
  MenuItem,
} from "@mui/material";
import FormControl from "@mui/material/FormControl";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import { AuthContext } from "src/context/AuthContext";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

// ** Hooks
import { useAuth } from "src/hooks/useAuth";
import useBgColor from "src/@core/hooks/useBgColor";
import { useSettings } from "src/@core/hooks/useSettings";
import { Tab, Tabs } from "@mui/material";

// ** Configs

// ** Config
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Demo Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";

const TabContainer = styled(Box)`
  height: ${({ maxHeight }) => (maxHeight ? `${maxHeight}px` : "auto")};
  overflow: auto;
`;


const CustomTab = styled(Tab)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    fontSize: "0.9rem",
  },
  [theme.breakpoints.down("md")]: {
    fontSize: "0.83rem",
  },
  [theme.breakpoints.down("sm")]: {
    fontSize: "0.78rem",
  },
  [theme.breakpoints.up("xs")]: {
    fontSize: "0.6rem",
  },
}));

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ mb: 4, width: "100%" }}>
          {" "}
          {/* Ensure Box takes full width */}
          <Typography component="div">{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

// ** Styled Components
const LoginIllustration = styled("img")(({ theme }) => ({
  zIndex: 6,
  maxHeight: 300,

  margin: theme.spacing(10),

  [theme.breakpoints.down(1540)]: {
    maxHeight: 300,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 300,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
    minHeight: "400px",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "40%",
  },
}));

const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: { xs: "0.7rem", lg: "0.9rem" },
  textDecoration: "none",
  color: theme.palette.primary.main,
}));


const emailValidationSchema = yup.object().shape({
  email: yup.string().email("Invalid email").required("Email is required"),
  password: yup.string().required("Password is required"),
});

const mobileValidationSchema = yup.object().shape({
  mobileNumber: yup.string().required("Mobile number is required").max(10),
  password: yup.string().required("Password is required").min(8),
});

const defaultValues = {
  password: "",
  email: "",
  mobileNumber: "",
};

const CustomTabDesktop = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      // width: "40%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
    "& button:nth-child(2)": {
      // width: "60%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));

const CustomizedTabStyle = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      width: "50%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));

//LoginPage

const LoginPage = () => {

  const [value, setValue] = useState(0);
  const router = useRouter();
  const isLargeScreen = useMediaQuery("(min-width:600px)");
  const isMobileView = useMediaQuery((theme) => theme.breakpoints.down("xs"));

  const currentValidationSchema =
    value === 0 ? emailValidationSchema : mobileValidationSchema;

  const CustomTabsComponent = isMobileView ? CustomTabMobile : CustomTabDesktop;

  useEffect(() => {
    Promise.resolve().then(() => {
      if (
        window.localStorage?.getItem("userData") &&
        window.localStorage?.getItem("accessToken")
      ) {
        window.location.href = "/dashboard";
      }
    });
  }, [router.route]);

  useEffect(() => {
    // localStorage.removeItem(authConfig.storageUserKeyName) //userData
    // localStorage.removeItem(authConfig.storageTokenKeyName) //accessToken
    localStorage.removeItem("refreshToken");
  }, []);

  const [showTooltip, setShowTooltip] = useState(false);

  const handleTabChange = (event, newValue) => {
    if (newValue === 1) {
      // If the user tries to switch to the second tab, prevent switching and show tooltip
      setShowTooltip(true);
    } else {
      setValue(newValue);
      setShowTooltip(false);
    }
  };
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [mobileVerified, setMobileVerified] = useState(false);
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const [timer, setTimer] = useState(30);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const inputsRef = useRef([]);
  
  const handleResendClick = () => {
    setTimer(30);
    setIsResendDisabled(true);
    setOtp(new Array(6).fill(""));
  };

  const handleOtpChange = (element, index) => {
    const value = element.value;
    if (/[^0-9]/.test(value)) return; // Ensure only numbers are allowed

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Move to next field if the current field is populated
    if (index < otp.length - 1 && value) {
      inputsRef.current[index + 1].focus();
    }
  };


  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [password, setPassword] = useState("");
  const [overrideExistingLogins, setOverrideExistingLogins] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [isFailed, setIsFailed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPopup, setShowPopup] = useState(true);

  // ** Hooks
  const auth = useAuth();
  const theme = useTheme();
  const bgColors = useBgColor();
  const { settings } = useSettings();
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  const url = authConfig.googleAuthUrl;

  const googleUrl = url;

  const facebookUrl = authConfig.facebookAuthUrl;

  const { fetchProfile } = useContext(AuthContext);

  // ** Vars
  const { skin } = settings;

  const {
    control,
    setError,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    resolver: yupResolver(currentValidationSchema),
  });

  const handleFailure = () => {
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Email or password credentials are invalid.</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleSuccess = () => {
    setIsFailed(false);
    const message = `
    <div>
      <h2>Login Successful.</h2>
      <h5>Redirecting to your dashboard.</h5>
    </div>
`;
    setLoading(true);
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  const StyledCard = styled(Card)(({ theme }) => ({
    width: "100%",
    margin: "10%",

    [theme.breakpoints.down("md")]: {
      margin: "5%",
      borderRadius: "20px",
    },

    [theme.breakpoints.down("lg")]: {
      margin: "1%",
      marginTop: "10% !important",
      marginBottom: "12% !important",
      marginRight: "5% !important",
      marginLeft: "1% !important",
      borderRadius: "20px",
    },
    [theme.breakpoints.up("lg")]: {
      marginTop: "4% !important",
      marginBottom: "4% !important",
      marginRight: "8% !important",
      marginLeft: "10% !important",
      borderRadius: "20px",
    },

    [theme.breakpoints.down("lg")]: {
      marginTop: "10% !important",
      marginBottom: "12% !important",
      marginRight: "1% !important",
      marginLeft: "1% !important",
      marginBottom: "15%",
      borderRadius: "20px",
    },

    overflow: "hidden",
  }));

  const handlePopup = () => {
    setIsFailed(false);
    setShowPopup(true);
    const message = ` 
    <div>
    <h3>You are currently logged in on multiple devices. Do you want to log out from all other devices</h3>
    </div>
    `;
    // setLoading(true);
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const handleYes = async (data) => {
    console.log("HANDLE YES", data);
    setOverrideExistingLogins(true);
  
    const ipAddress = await fetchIpAddress();
    
    const { email, password } = data; // Destructuring the data object to extract email and password
    
    await auth.login(
      { email, password, overrideExistingLogins: true, ipAddress }, // Using the extracted email and password
      handleFailure,
      handleSuccess,
      handlePopup
    );
  };
  

  const [anchorEl, setAnchorEl] = useState(null);

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (role) => {
    localStorage.setItem("role", role);
    setAnchorEl(null);
    window.location.href = "/register?role=" + role;
    handleCloseMenu();
    
  };

  const SocialLoginBlock = ({ imageUrl, text, url }) => {
    const [isHovered, setIsHovered] = useState(false);

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    // Append action to URL

    return (
      <Grid item>
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          style={{
            border: isHovered ? "1px solid #aaa" : "1px solid #ccc",
            padding: "5px",
            borderRadius: "5px",
            textAlign: "center",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "10px",
            cursor: "pointer",
            backgroundColor: isHovered ? "#f2f7f2" : "transparent",
          }}
        >
          <Box
            component="img"
            sx={{
              height: {
                xs: "1.3rem",
                lg: "1.5rem",
              },
            }}
            src={imageUrl}
            alt={text}
          />
          <LinkStyled
            href={url}
            sx={{ color: "black", fontSize: { xs: "0.75rem", lg: "0.8rem" } }}
          >
            {text}
          </LinkStyled>
        </div>
      </Grid>
    );
  };

  const SocialLogin = () => {
    return (
      <Grid container spacing={2} justifyContent="center">
        <Grid item xs={12} >
          <SocialLoginBlock
            imageUrl="/images/google-logo.png"
            text="Continue with Google"
            url={googleUrl}
          />
        </Grid>
        {/* <Grid item xs={6} style={{ paddingLeft: 4 }}>
          <SocialLoginBlock
            imageUrl="/images/fb-logo.png"
            text="Facebook"
            url={facebookUrl}
          />
        </Grid> */}
      </Grid>
    );
  };

  const handleChange = () => {
    setOverrideExistingLogins(true);
  };

  const handleEmailChange = useCallback((event) => {
    setEmail(event.target.value);
  }, []);

  const handlePasswordChange = useCallback((event) => {
    setPassword(event.target.value.replace(/\s/g, ""));
  }, []);

  //Submit Function
  const onSubmit = async (data) => {
    console.log("E Mobil", errors.mobileNumber);

    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password
    if (value === 0) {
      fields = {
        email: data.email,
        password: data.password,
      };
      await emailValidationSchema.validate(fields);
    }
    // If the mobile number tab is active, populate fields with mobileNumber and password
    else if (value === 1) {
      fields = {
        mobileNumber: data.mobileNumber,
        password: data.password,
      };
      await mobileValidationSchema.validate(fields);
    }

    console.log("F F", fields);

    // Fetch the IP address
    const ipAddress = await fetchIpAddress();

    // Assuming overrideExistingLogins is defined somewhere in your code
    // If it's not required, you can remove it from the auth.login call

    // await schema.validate(fields);

    await auth.login(
      {
        email: fields.email,
        password: fields.password,
        overrideExistingLogins, // Make sure this is defined or required
        ipAddress,
      },
      handleFailure,
      handleSuccess,
      handlePopup
    );
  };

  const imageSource =
    skin === "bordered"
      ? "auth-v2-login-illustration-bordered"
      : "auth-v2-login-illustration";
  if (
    !(
      window.localStorage?.getItem("userData") &&
      window.localStorage?.getItem("accessToken")
    )
  ) {
    return (
      <Box className="content-right" sx={{ backgroundColor: "#f2f7f2" }}>
        <Dialog
          open={openDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            {isFailed && (
              <>
                <DialogActions>
                  <Button
                    onClick={handleClose}
                    style={{ margin: "10px auto", width: 100, color:'white' }}
                  >
                    Okay
                  </Button>
                </DialogActions>
              </>
            )}
            {showPopup &&
              dialogMessage &&
              !dialogMessage.includes("Login Successful.") && (
                <>
                  <Button
                    onClick={handleSubmit(handleYes)}
                    checked={overrideExistingLogins}
                    onChange={handleChange}
                    style={{ margin: "10px auto", width: 100 }}
                  >
                    Yes
                  </Button>
                  <Button
                    onClick={handleClose}
                    style={{ margin: "10px auto 10px 20px", width: 100 }}
                  >
                    No
                  </Button>
                </>
              )}
            {showPopup &&
              dialogMessage &&
              dialogMessage.includes("Login Successful.") && (
                <>
                  <Button
                    style={{
                      margin: "10px auto",
                      display: "block",
                      width: 100,
                    }}
                  >
                    <CircularProgress color="inherit" size={24} />
                  </Button>
                </>
              )}
          </Box>
        </Dialog>
        {!hidden ? (
          <Box
            sx={{
              flex: 1,
              display: "flex",
              position: "relative",
              alignItems: "center",
              borderRadius: "20px",
              justifyContent: "center",
              backgroundColor: "customColors.bodyBg",

              marginTop: "5% !important",
              marginBottom: "15% !important",
              marginRight: "8% !important",
              marginLeft: "8% !important",
              [theme.breakpoints.down("lg")]: {
                marginTop: "10% !important",
                marginBottom: "15% !important",

                marginBottom: "15% !important",
              },
              [theme.breakpoints.up("lg")]: {
                marginTop: "4% !important",
                marginBottom: "4% !important",
                borderRadius: "20px",
              },
            }}
          >
            <LoginIllustration
              alt="login-illustration"
              src={`/images/pages/login.webp`}
            />
            <FooterIllustrationsV2 />
          </Box>
        ) : null}

        <StyledCard>
          <RightWrapper>
            <Box
              sx={{
                p: [6, 10],
                height: "80%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Box sx={{ width: "100%", maxWidth: 400 }}>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <a href={authConfig.guestURL + "home"}>
                    <img
                      width="40"
                      height="40"
                      alt=""
                      src="/images/logo.webp"
                      className=""
                    />
                  </a>
                  <Typography
                    sx={{
                      mb: 1,
                      ml: 0,
                      fontWeight: 500,
                      fontSize: { xs: "1rem", lg: "1.2rem" },
                      lineHeight: 1.385,
                    }}
                  >
                    {`Welcome to Houzer! 👋🏻`}
                  </Typography>
                </div>

                <Typography
  sx={{
    color: "text.secondary",
    ml: { xs: 9, lg: 11 },
    mb: { xs: 4, lg: 4 },
    mt: { xs: -2, lg: -3 }, // add this line
    fontSize: { xs: "0.6rem", lg: "0.8rem" },
  }}
>
  Login via
</Typography>

                <form
                  noValidate
                  autoComplete="off"
                  onSubmit={handleSubmit(onSubmit)}
                >
                  <SocialLogin />

                  <Divider
                    sx={{ mt: 4, fontSize: { xs: "0.65rem", lg: "0.85rem" } }}
                  >
                    or
                  </Divider>

                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "column" }, // Stack on mobile, row on larger screens
                      alignItems: "center",
                      justifyContent: "space-between",
                      mb: 2,
                      mt: 2,
                      height: "100%",
                    }}
                  >
                    {/* <Typography
                      sx={{
                        whiteSpace: "nowrap",
                        mb: { xs: 1, md: 1 }, // Add margin bottom on mobile to separate from tabs
                        fontSize: { xs: "0.65rem", lg: "0.85rem" },
                      }}
                    >
                     Email / Mobile Login.
                    </Typography> */}

                    <CustomizedTabStyle
                      value={value}
                      onChange={handleTabChange}
                      aria-label="email or mobile number tabs"
                    >
                      <CustomTab label="Email" {...a11yProps(0)} />
                      <CustomTab label="Mobile Number" {...a11yProps(1)} />
                    </CustomizedTabStyle>
                  </Box>

                  <TabPanel
                    value={value}
                    index={0}
                    sx={{ height: "120px", overflow: "auto" }}
                  >
                    <FormControl fullWidth sx={{ width: "100%", mt: 1.5 }}>
                      <Controller
                        name="email"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="*Email"
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.email?.message}
                            error={Boolean(errors.email)}
                            fullWidth
                            placeholder="<EMAIL>"
                            sx={{
                              "& .MuiInputBase-input::placeholder": {
                                // Targeting the placeholder
                                fontSize: "0.9rem", // Default font size for larger screens
                                "@media (max-width:600px)": {
                                  // Media query for mobile devices
                                  fontSize: "0.75rem", // Reduced font size for mobile devices
                                },
                              },
                              "& .MuiInputBase-input": {
                                padding: "8px",
                                fontSize: "0.9rem", // Adjust padding for larger screens
                                "@media (max-width:600px)": {
                                  padding: "6px", // Reduce padding for mobile devices
                                  fontSize: "0.75rem", // Reduced font size for mobile devices
                                },
                              },
                              "& .MuiInputLabel-root": {
                                // Correct class for targeting the label
                                fontSize: "0.9rem",
                                "@media (max-width:600px)": {
                                  fontSize: "0.75rem",
                                },
                              },
                            }}
                          />
                        )}
                      />
                    </FormControl>

                    <FormControl
                      fullWidth
                      sx={{ width: "100%", mb: 1, mt: 2.5 }}
                    >
                      <Controller
                        name="password"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="*Password"
                            size="small"
                            error={Boolean(errors.password)}
                            helperText={errors.password?.message}
                            InputLabelProps={{ shrink: true }}
                            type={showPassword ? "text" : "password"}
                            fullWidth
                            sx={{
                              "& .MuiInputBase-input::placeholder": {
                                // Targeting the placeholder
                                fontSize: "0.9rem", // Default font size for larger screens
                                "@media (max-width:600px)": {
                                  // Media query for mobile devices
                                  fontSize: "0.75rem", // Reduced font size for mobile devices
                                },
                              },
                              "@media (max-width:600px)": {
                                maxHeight: "0.2", // Set the max height for mobile
                              },
                              "& .MuiInputBase-input": {
                                padding: "8px",
                                fontSize: "0.9rem", // Adjust padding for larger screens
                                "@media (max-width:600px)": {
                                  padding: "6px", // Reduce padding for mobile devices
                                  fontSize: "0.75rem", // Reduced font size for mobile devices
                                },
                              },
                              "& .MuiInputLabel-root": {
                                // Correct class for targeting the label
                                fontSize: "0.9rem",
                                "@media (max-width:600px)": {
                                  fontSize: "0.75rem",
                                },
                              },
                            }}

                            onCut={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }} 
                            onCopy={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    edge="end"
                                    onMouseDown={(e) => e.preventDefault()}
                                    onClick={() =>
                                      setShowPassword(!showPassword)
                                    }
                                    disabled={!field.value}
                                  >
                                    <Icon
                                      icon={
                                        showPassword
                                          ? "tabler:eye"
                                          : "tabler:eye-off"
                                      }
                                      fontSize={{ xs: 5, lg: 20 }}
                                    />
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>

                    <Box
                      sx={{
                        mb: 1.5,
                        display: "flex",
                        flexWrap: "wrap",
                        alignItems: "end",
                        justifyContent: "flex-end", // Changed from "space-between" to "flex-end"
                      }}
                    >
                      {/* <FormControlLabel
                    label="Remember Me"
                    control={
                      <Checkbox
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                      />
                    }
                  /> */}
                      <LinkStyled
                        href={`/forgot-password?email=${encodeURIComponent(
                          email
                        )}`}
                        sx={{
                          fontWeight: 500,
                          fontSize: { xs: "0.65rem", lg: "0.85rem" },
                        }}
                      >
                        Forgot password?
                      </LinkStyled>
                    </Box>
                  </TabPanel>

                  <TabPanel
                    value={value}
                    index={1}
                    sx={{ height: "120px", overflow: "auto" }}
                  >
                    {showTooltip ? (
                      // Tooltip for Work In Progress
                      <Tooltip
                        title="Work In Progress"
                        open={true}
                        arrow
                      ></Tooltip>
                    ) : (
                      <FormControl
                        fullWidth
                        sx={{
                          width: "100%",
                          mt: 1.5,
                          mb: { xs: "3.93rem", lg: "4.8rem" },
                        }}
                      >
                        <Box display="flex" alignItems="center">
                          <TextField
                            value="+91"
                            disabled
                            size="small"
                            sx={{
                              marginRight: 1,
                              maxWidth: "40px",
                              "& .MuiInputBase-input": {
                                padding: "8px",
                                fontSize: "0.9rem", // Adjust padding for larger screens
                                "@media (max-width:600px)": {
                                  padding: "6px", // Reduce padding for mobile devices
                                  fontSize: "0.75rem", // Reduced font size for mobile devices
                                  maxWidth: "30px",
                                },
                              },
                            }}
                          />

                          <Controller
                            name="mobileNumber"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="*Mobile Number"
                                sx={{
                                  "& .MuiInputBase-input::placeholder": {
                                    // Targeting the placeholder
                                    fontSize: "0.9rem", // Default font size for larger screens
                                    "@media (max-width:780px)": {
                                      // Media query for mobile devices
                                      fontSize: "0.75rem", // Reduced font size for mobile devices
                                    },
                                  },
                                  "& .MuiInputBase-input": {
                                    padding: "8px",
                                    fontSize: "0.9rem", // Adjust padding for larger screens
                                    "@media (max-width:600px)": {
                                      padding: "6px", // Reduce padding for mobile devices
                                      fontSize: "0.75rem", // Reduced font size for mobile devices
                                    },
                                  },
                                  "& .MuiInputLabel-root": {
                                    // Correct class for targeting the label
                                    fontSize: "0.9rem",
                                    "@media (max-width:600px)": {
                                      fontSize: "0.75rem",
                                    },
                                  },
                                }}
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                helperText={errors.mobileNumber?.message}
                                error={Boolean(errors.mobileNumber)}
                                placeholder="999 999 99 99"
                                fullWidth
                                onChange={(e) => {
                                  field.onChange(e); // Update form state
                                  //setMobile(e.target.value); // Update mobile state
                                }}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      {mobileVerified ? (
                                        <CustomChip
                                          rounded={true}
                                          skin="light"
                                          size="small"
                                          label={
                                            <span
                                              style={{ fontSize: "inherit" }}
                                            >
                                              Verified
                                            </span>
                                          }
                                          color="success"
                                          sx={{
                                            textTransform: "capitalize",
                                            fontSize: {
                                              xs: "0.7rem",
                                              lg: "0.8rem",
                                            }, // Responsive font size
                                          }}
                                        />
                                      ) : (
                                        <Box
                                          // onClick={
                                          //    handleVerifyClick
                                          // }
                                          sx={{
                                            border: "1px solid #f2f7f2", // Grey border color
                                            borderRadius: "4px",
                                            padding: "0 5px",
                                            cursor: "pointer",
                                            color: "blue",
                                          }}
                                        >
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              fontSize: {
                                                xs: "0.7rem",
                                                lg: "0.8rem",
                                              },
                                              color: "blue",
                                            }}
                                          >
                                            Verify
                                          </Typography>
                                        </Box>
                                      )}
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        </Box>
                        <>
                          {" "}
                          {showOtpInput && (
                            <Box>
                              {/* OTP Input Fields */}
                              <Box
                                display="flex"
                                justifyContent="right"
                                marginTop="20px"
                              >
                                {otp.map((data, index) => (
                                  <TextField
                                    key={index}
                                    type="text"
                                    value={data}
                                    onChange={(e) =>
                                      handleOtpChange(e.target, index)
                                    }
                                    onFocus={(e) => e.target.select()}
                                    inputProps={{
                                      maxLength: 1,
                                      style: {
                                        textAlign: "center",
                                        height: "5px",
                                        backgroundColor: "transparent",
                                      },
                                    }}
                                    sx={{
                                      width: {
                                        xs: "25px",
                                        sm: "30px",
                                        md: "45px",
                                      }, // Responsive width
                                      margin: "0 2px",
                                    }}
                                    ref={(ref) =>
                                      (inputsRef.current[index] = ref)
                                    }
                                  />
                                ))}
                              </Box>
                              {/* Resend OTP Button */}
                              <Box
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                marginTop="20px"
                              >
                                <Button
                                  variant="text"
                                  onClick={handleResendClick}
                                  disabled={isResendDisabled}
                                >
                                  Resend OTP
                                </Button>
                                {isResendDisabled && (
                                  <Typography
                                    variant="body2"
                                    sx={{ marginLeft: "10px" }}
                                  >
                                    Resend available in {timer} seconds
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          )}
                        </>
                      </FormControl>
                    )}
                  </TabPanel>

                  <Button
                    fullWidth
                    size={isLargeScreen ? "medium" : "small"}
                    type="submit"
                    variant="contained"
                    sx={{
                      width: "100%",
                      mb: { xs: 2, lg: 4 },
                      fontSize: { xs: "0.7rem", lg: "0.9rem" },
                    }}
                  >
                    Login
                  </Button>

                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      justifyContent: "start", // Align items to the start of the box
                      alignItems: "center", // Vertically center the items in the box
                    }}
                  >
                    <Typography
                      sx={{
                        color: "text.secondary",
                        mr: 2,
                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      New on Houzer?
                    </Typography>
                    <Typography
                      sx={{
                        color: "text.secondary",

                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      <span
                        onClick={handleOpenMenu}
                        style={{
                          cursor: "pointer",
                          fontWeight: 500,
                          fontSize: { xs: "0.7rem", lg: "0.9rem" },
                          textDecoration: "none",
                          color: theme.palette.primary.main,
                        }}
                      >
                        Create an account
                      </span>
                      <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleCloseMenu}
                        anchorReference="anchorPosition"
                        anchorPosition={{
                          top: anchorEl
                            ? anchorEl.getBoundingClientRect().bottom
                            : 0,
                          left: anchorEl
                            ? anchorEl.getBoundingClientRect().left
                            : 0,
                        }}
                       
                      >
                        <MenuItem
                          onClick={() =>
                            handleMenuItemClick("SERVICE_PROVIDER")

                          }
                          style={{ padding: '3px 6px' }}
                        >
                          Service Provider
                        </MenuItem>
                        <MenuItem
                          onClick={() => handleMenuItemClick("SOCIETY")}
                          style={{ padding: '3px 6px' }}
                        >
                          Society Member
                        </MenuItem>
                      </Menu>
                    </Typography>
                  </Box>
                </form>
              </Box>
            </Box>
          </RightWrapper>
        </StyledCard>
      </Box>
    );
  }
};
LoginPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
LoginPage.guestGuard = true;

export default LoginPage;
