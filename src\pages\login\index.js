import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Checkbox,
  FormControlLabel,
  Divider,
  Alert,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Email as EmailIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Google as GoogleIcon,
  Facebook as FacebookIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { validationRules } from '../../utils/validation';
import LoadingSpinner from '../../components/common/LoadingSpinner';

/**
 * Login page component
 */
const LoginPage = () => {
  const router = useRouter();
  const { login, socialLogin, isAuthenticated, loading, error, clearError } = useAuth();
  
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.replace('/dashboard');
    }
  }, [isAuthenticated, loading, router]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setLoginError('');
  }, [clearError]);

  // Pre-fill demo credentials
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('demo') === 'true') {
      setValue('email', '<EMAIL>');
      setValue('password', 'Demo123!');
    }
  }, [setValue]);

  /**
   * Handle form submission
   * @param {Object} data - Form data
   */
  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setLoginError('');
    clearError();

    try {
      const result = await login(data);
      
      if (result.success) {
        // Redirect to dashboard or intended page
        const redirectTo = router.query.redirect || '/dashboard';
        router.push(redirectTo);
      } else {
        setLoginError(result.error);
      }
    } catch (err) {
      setLoginError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle social login
   * @param {string} provider - Social provider (google, facebook)
   */
  const handleSocialLogin = async (provider) => {
    setLoginError('');
    clearError();

    try {
      // Mock social login for demo
      const mockToken = `mock-${provider}-token-${Date.now()}`;
      
      const result = await socialLogin({
        provider,
        token: mockToken,
      });

      if (result.success) {
        const redirectTo = router.query.redirect || '/dashboard';
        router.push(redirectTo);
      } else {
        setLoginError(result.error);
      }
    } catch (err) {
      setLoginError(`${provider} login failed. Please try again.`);
    }
  };

  /**
   * Toggle password visibility
   */
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner loading={true} message="Checking authentication..." overlay />;
  }

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
          }}
        >
          {/* Header */}
          <Typography component="h1" variant="h4" gutterBottom>
            Welcome Back
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Sign in to your account
          </Typography>

          {/* Demo Credentials Info */}
          <Alert severity="info" sx={{ mb: 3, width: '100%' }}>
            <Typography variant="body2">
              <strong>Demo Credentials:</strong><br />
              Email: <EMAIL><br />
              Password: Demo123!
            </Typography>
          </Alert>

          {/* Error Alert */}
          {(error || loginError) && (
            <Alert severity="error" sx={{ mb: 3, width: '100%' }}>
              {error || loginError}
            </Alert>
          )}

          {/* Login Form */}
          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ width: '100%' }}>
            {/* Email Field */}
            <TextField
              {...register('email', validationRules.email)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Email Address"
              autoComplete="email"
              autoFocus
              error={!!errors.email}
              helperText={errors.email?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />

            {/* Password Field */}
            <TextField
              {...register('password', validationRules.password)}
              margin="normal"
              required
              fullWidth
              size="small"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              error={!!errors.password}
              helperText={errors.password?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                      size="small"
                    >
                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Remember Me Checkbox */}
            <FormControlLabel
              control={
                <Checkbox
                  {...register('rememberMe')}
                  color="primary"
                  size="small"
                />
              }
              label="Remember me"
              sx={{ mt: 1, mb: 2 }}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isSubmitting}
              sx={{ mt: 2, mb: 2, py: 1.5 }}
            >
              {isSubmitting ? 'Signing In...' : 'Sign In'}
            </Button>

            {/* Links */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
              <Link
                href="/forgot-password"
                variant="body2"
                sx={{ textDecoration: 'none' }}
              >
                Forgot password?
              </Link>
              <Link
                href="/register"
                variant="body2"
                sx={{ textDecoration: 'none' }}
              >
                Don't have an account? Sign up
              </Link>
            </Box>

            {/* Divider */}
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                Or continue with
              </Typography>
            </Divider>

            {/* Social Login Buttons */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<GoogleIcon />}
                onClick={() => handleSocialLogin('google')}
                sx={{ py: 1.5 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FacebookIcon />}
                onClick={() => handleSocialLogin('facebook')}
                sx={{ py: 1.5 }}
              >
                Facebook
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
