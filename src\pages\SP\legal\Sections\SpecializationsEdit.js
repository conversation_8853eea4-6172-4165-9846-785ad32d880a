// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useAuth } from "src/hooks/useAuth";

const SpecializationsEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  const [mhada, setMhada] = useState(formData?.mhada);

  const [nclt, setNclt] = useState(formData?.nclt);
  const [rera, setRera] = useState(formData?.rera);
  const [drt, setDrt] = useState(formData?.drt);
  const [arbitration, setArbitration] = useState(formData?.arbitration);
  const [litigation, setLitigation] = useState(formData?.litigation);
  const [criminal, setCriminal] = useState(formData?.criminal);

  const [allSpecializations, setAllSpecializations] = useState(
    formData?.allSpecializations
  );

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    if (nclt && rera && drt && arbitration && litigation && criminal) {
      setValue("allSpecializations", true);
      setAllSpecializations(true);
    } else {
      setValue("allSpecializations", false);
      setAllSpecializations(false);
    }
  }, [nclt, rera, drt, arbitration, litigation, criminal]);

  async function submit(data) {
    console.log("Submitted Data Checkboxes", data);
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const hasWhiteSpace = Object.values(trimmedData).some(
      (value) => typeof value === "string" && value === ""
    );
    if (hasWhiteSpace) {
      toast.error("Fields cannot contain only white spaces");
      return;
    }
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 1.5 }}>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="nclt"
                control={control}
                defaultValue={formData?.nclt ? nclt : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("nclt", true);
                            setNclt(true);
                          } else {
                            setValue("nclt", false);
                            setNclt(false);
                          }
                        }}
                      />
                    }
                    label={<span>NCLT</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="rera"
                control={control}
                defaultValue={formData?.rera ? rera : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("rera", true);
                            setRera(true);
                          } else {
                            setValue("rera", false);
                            setRera(false);
                          }
                        }}
                      />
                    }
                    label={<span>RERA</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="drt"
                control={control}
                defaultValue={formData?.drt ? drt : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("drt", true);
                            setDrt(true);
                          } else {
                            setValue("drt", false);
                            setDrt(false);
                          }
                        }}
                      />
                    }
                    label={<span>DRT</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="arbitration"
                control={control}
                defaultValue={formData?.arbitration ? arbitration : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("arbitration", true);
                            setArbitration(true);
                          } else {
                            setValue("arbitration", false);
                            setArbitration(false);
                          }
                        }}
                      />
                    }
                    label={<span>Arbitration</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="litigation"
                control={control}
                defaultValue={formData?.litigation ? litigation : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("litigation", true);
                            setLitigation(true);
                          } else {
                            setValue("litigation", false);
                            setLitigation(false);
                          }
                        }}
                      />
                    }
                    label={<span>Litigation</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="criminal"
                control={control}
                defaultValue={formData?.criminal ? criminal : false}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.checked) {
                            setValue("criminal", true);
                            setCriminal(true);
                          } else {
                            setValue("criminal", false);
                            setCriminal(false);
                          }
                        }}
                      />
                    }
                    label={<span>Criminal</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="allSpecializations"
                control={control}
                defaultValue={
                  formData?.allSpecializations
                    ? formData.allSpecializations
                    : false
                }
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        style={{ transform: "scale(1)" }}
                        onChange={(e) => {
                          field.onChange(e);

                          if (e.target.checked) {
                            setValue("allSpecializations", true);
                            setAllSpecializations(true);

                            setValue("nclt", true);
                            setNclt(true);

                            setValue("rera", true);
                            setRera(true);

                            setValue("drt", true);
                            setDrt(true);

                            setValue("arbitration", true);
                            setArbitration(true);

                            setValue("litigation", true);
                            setLitigation(true);

                            setValue("criminal", true);
                            setCriminal(true);
                          } else {
                            setValue("allSpecializations", false);
                            setAllSpecializations(false);

                            setValue("nclt", false);
                            setNclt(false);
                            setValue("rera", false);
                            setRera(false);
                            setValue("drt", false);
                            setDrt(false);
                            setValue("arbitration", false);
                            setArbitration(false);
                            setValue("litigation", false);
                            setLitigation(false);
                            setValue("criminal", false);
                            setCriminal(false);
                          }
                        }}
                      />
                    }
                    label={<span>ALL</span>}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default SpecializationsEdit;
