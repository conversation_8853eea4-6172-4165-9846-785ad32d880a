import { styled } from "@mui/material/styles";
import TableCell from "@mui/material/TableCell";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
  "& .MuiTypography-root": {
    "&.data-field": {
      color: theme.palette.text.viewData,
      fontSize: "16px",
      fontWeight: 600,
    },
  },
}))

export default MUITableCell
