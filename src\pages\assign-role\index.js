import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";

import { useCallback } from "react";

import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import SiteMapData from "src/@core/components/custom-components/SiteMapData";

const AssignRole = ({}) => {
  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [listOfProfiles, setListOfProfiles] = useState([]);
  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [listOfRoles, setListOfRoles] = useState([]);

  
  const [selectedOption, setSelectedOption] = useState("");
  
  useEffect(() => {
    // Fetch employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        console.log("Employees Response", res.data);
        console.log("Employees Response1", res.data.data);
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    // Fetch profiles
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=PROFILES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        console.log("Profiles Response", res.data);
        setListOfProfiles(res.data.data);
      })
      .catch((err) => console.log("Profiles error", err));

    // Fetch role types
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ROLE_TYPES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        console.log("Role Types Response", res.data);
        setListOfRoleTypes(res.data.data);
      })
      .catch((err) => console.log("Role Types error", err));

    //fetch Roles
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ROLES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        console.log("Roles Response", res.data);
        setListOfRoles(res.data.data);
      })
      .catch((err) => console.log("Roles error", err));
  }, [selectedOption]);

  const {
    register,
    setError,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const rowsPerPageOptions = [1000];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);

  //RoleType Data
  const [roleTypeList, setRoleTypesList] = useState([]);

  //selectedRoleTypeSubRoles Data
  const [selectedRoleTypeSubRoles, setSelectedRoleTypeSubRoles] = useState([]);
  //Roles States
  const [selectedRole, setSelectedRole] = useState("");
  const [selectedSubRole, setSelectedSubRole] = useState("");

  const [selectedEmployee, setSelectedEmployee] = useState("");
  const [selectedProfile, setSelectedProfile] = useState("");

  const [email, setEmail] = useState("");
  const [contactNumber, setContactNumber] = useState("");

  const [roleState,setRoleState] = useState(false)

  const [listOfSettings, setListOfSettings] = useState([]);
  const [data, setData] = useState({
    userManagementDTO: { pages: listOfSettings },
  });

  const { roleTypeUser, setRoleTypeUser, roleTypeData,roleData,roleUser, setRoleUser, } =
    useContext(AuthContext);

    console.log("Role data from auth context",roleData)
    
  const [renderedData, setRenderedData] = useState([]);

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    setSelectedEmployee("");
    setSelectedProfile("");
    setEmail("");
    setContactNumber("");
  };

  const handleNameSelectionChange = (newValue) => {
    if (!newValue) {
      setEmail("");
      setContactNumber("");
      return;
    }

    let selectedItem;
    if (selectedOption === "employee") {
      setSelectedEmployee(newValue ? newValue.label : "");
      selectedItem = listOfEmployees.find((e) => e.id === newValue.id);
    } else if (selectedOption === "profile") {
      setSelectedProfile(newValue ? newValue.label : "");
      selectedItem = listOfProfiles.find((e) => e.id === newValue.id);
    }

    console.log("selectedItem.metaData", selectedItem.metaData);
    console.log("Email", selectedItem.metaData.email);
    console.log("Mobile Number", selectedItem.metaData.mobileNumber);
    if (selectedItem) {
      setEmail(selectedItem.metaData.email);
      setContactNumber(selectedItem.metaData.mobileNumber);
    } else {
      setEmail("");
      setContactNumber("");
    }
  };

  const handleRoleChange = (event, newValue) => {
    setRoleState(true)
    console.log("Selected Role:", newValue?.id);
          newValue?setRoleUser({
        ...roleUser,
        newValue,
      }):'';  
  };


  
  const [expandedSections, setExpandedSections] = useState({});

  // Function to toggle expand/collapse of "Section Name" rows
  const toggleSection = (childId) => {
    setExpandedSections((prevState) => ({
      ...prevState,
      [childId]: !prevState[childId],
    }));
  };

  const handleCheckboxChange = (profileIndex, childIndex, subChildIndex, permissionValue) => {
    const updatedData = [...renderedData];

    // Function to update permissions recursively
    const updatePermissions = (item, value, shouldSet) => {
        if (shouldSet) {
            item.permissions |= value; // Set the permission bit
        } else {
            item.permissions &= ~value; // Clear the permission bit
        }

        // Recursively update children, if any
        item.children?.forEach(child => updatePermissions(child, value, shouldSet));
    };

    // Determine the target and its state (set or clear)
    let target, shouldSet;
    if (subChildIndex !== null) {
        target = updatedData[profileIndex].children[childIndex].children[subChildIndex];
        shouldSet = (target.permissions & permissionValue) !== permissionValue;
    } else if (childIndex !== null) {
        target = updatedData[profileIndex].children[childIndex];
        shouldSet = (target.permissions & permissionValue) !== permissionValue;
    } else {
        target = updatedData[profileIndex];
        shouldSet = (target.permissions & permissionValue) !== permissionValue;
    }

    // Update the target and its descendants
    updatePermissions(target, permissionValue, shouldSet);

    // Check and update parent if needed
    const updateParentPermissions = (parent) => {
        const allChildrenSelected = parent.children.every(child => (child.permissions & permissionValue) === permissionValue);
        if (allChildrenSelected) {
            parent.permissions |= permissionValue;
        } else {
            parent.permissions &= ~permissionValue;
        }
    };

    // Update parent permissions if needed
    if (childIndex !== null) {
        updateParentPermissions(updatedData[profileIndex]);
    }
    if (subChildIndex !== null) {
        updateParentPermissions(updatedData[profileIndex].children[childIndex]);
    }

    setRenderedData(updatedData);
};


  const permissionBits = {
    Create: 1,
    Read: 2,
    Update: 4,
    Delete: 8,
  };

  const renderData = useCallback(() => {
    const profiles = data.userManagementDTO || [];

    console.log("profiles-->", profiles);
    console.log("data-->", data);

    if (!selectedRole) {
      setRenderedData(profiles);
      return profiles;
    }

    const result = profiles.map((profile) => {
      let modifiedProfile = { ...profile };

      if (profile.code === "profile") {
        if (
          selectedSubRole?.name &&
          selectedRole?.name?.toLowerCase().includes("professional")
        ) {
          modifiedProfile.children = modifiedProfile.children.filter(
            (child) =>
              child.code.toLowerCase() === selectedSubRole.name.toLowerCase()
          );
        } else if (
          !selectedRole?.name?.toLowerCase().includes("professional")
        ) {
          modifiedProfile.children = [];
        }
      }
      return modifiedProfile;
    });

    setRenderedData(result);
    console.log("result------>", result);
    return result;
  }, [selectedSubRole, selectedRole, data]);

  useEffect(() => {
    renderData();
    setRenderedData(roleData?.roleMetaData?.siteMapData?.pages)
  }, [data, selectedRole, selectedSubRole]);

  useEffect(() => {

    if(!!authConfig) {
      // Fetch Site Map Data
      axios({
        method: "get",
        url: getUrl(authConfig.settings) + "?settingsType=USER_MANAGEMENT_DATA",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          console.log(
            "USER_MANAGEMENT_DATA Response",
            res.data?.userManagementDTO?.pages
          );
          setListOfSettings(res.data?.userManagementDTO?.pages);
          setData({ userManagementDTO: res.data?.userManagementDTO?.pages });
        })
        .catch((err) => console.log("USER_MANAGEMENT_DATA error", err));
    }    

  }, [authConfig]);

  return (
    <>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={4}>
          <FormControl variant="outlined" fullWidth>
            <InputLabel>Select an Option</InputLabel>
            <Select
              label="Select an Option"
              value={selectedOption}
              onChange={handleOptionChange}
            >
              <MenuItem value="employee">Employee</MenuItem>
              <MenuItem value="profile">Profile</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <CustomAutocomplete
            autoHighlight
            id="autocomplete-name-select"
            options={
              selectedOption === "employee"
                ? listOfEmployees.map((data) => ({
                    id: data.id,
                    label: data.name,
                  }))
                : listOfProfiles.map((data) => ({
                    id: data.id,
                    label: data.name,
                  }))
            }
            getOptionLabel={(option) => option.label || ""}
            value={
              selectedOption === "employee"
                ? { label: selectedEmployee }
                : { label: selectedProfile }
            }
            disabled={!selectedOption}
            onChange={(event, newValue) => handleNameSelectionChange(newValue)}
            renderInput={(params) => (
              <CustomTextField
                {...params}
                placeholder="Select Name"
                label="Select Name"
              />
            )}
          />
        </Grid>

    {/* RoleTypeCode */}

        {/* <Grid item xs={12} sm={4}>
          <CustomAutocomplete
            autoHighlight
            id="autocomplete-role-select"
            options={listOfRoleTypes}
            getOptionLabel={(option) => option.name || ""}
            value={selectedRole}
            getOptionSelected={(option, value) => option.id === value.id}
            onChange={(event, newValue) => handleRoleTypeChange(newValue)}
            renderInput={(params) => (
              <CustomTextField
                {...params}
                placeholder="Select Role Type"
                label="Select Role Type"
              />
            )}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <CustomAutocomplete
            autoHighlight
            id="autocomplete-sub-role-select"
            options={selectedRoleTypeSubRoles}
            getOptionLabel={(option) => option.name || ""}
            value={selectedSubRole}
            getOptionSelected={(option, value) => option.id === value.id}
            onChange={(event, newValue) => handleSubRoleTypeChange(newValue)}
            renderInput={(params) => (
              <CustomTextField
                {...params}
                placeholder="Select Sub Role Type"
                label="Select Sub Role Type"
              />
            )}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography style={{ fontWeight: "bold" }}>
           {console.log("ROLE TYPE DATA-GET END POINT",roleTypeData)}
            </Typography>
          </FormControl>
        </Grid> */}

<Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography style={{ fontWeight: "bold" }}>
           {console.log("ROLE DATA-GET END POINT",roleData)}
            </Typography>
          </FormControl>
        </Grid> 


        <Grid item xs={12} sm={4}>
          <CustomAutocomplete
            autoHighlight
            id="roles-autocomplete"
            options={listOfRoles.map((role) => ({
              id: role.id,
              label: role.name,
            }))}
            getOptionLabel={(option) => option.label || ""}
            onChange={handleRoleChange}
            renderInput={(params) => (
              <CustomTextField
                {...params}
                placeholder="Select Role"
                label="Select Role"
              />
            )}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">Email:</Typography>
            <Typography style={{ fontWeight: "bold" }}>{email}</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Typography className="data-field">Mobile Number:</Typography>
            <Typography style={{ fontWeight: "bold" }}>
              {contactNumber}
            </Typography>
          </FormControl>
        </Grid>
            {roleState && (
              <SiteMapData
              renderedData={renderedData}
              handleCheckboxChange={handleCheckboxChange}
              permissionBits={permissionBits}
              toggleSection={toggleSection}
              expandedSections={expandedSections}
              />
            )}
        
      </Grid>
    </>
  );
};

export default AssignRole;
