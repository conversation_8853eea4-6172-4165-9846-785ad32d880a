// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState, useEffect } from "react";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Demo
import {
  Box,
  Button,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";

import { styled, useTheme } from "@mui/material/styles";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import CompanyDetails from "./Sections/CompanyDetails";
import Team from "./Sections/Team";
import ProjectDetails from "./Sections/ProjectDetails";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import LegalServicesView from "src/pages/SP/legal/Sections/LegalServicesView";
import SpecializationsView from "./Sections/SpecializationsView";
import { useRouter } from "next/router";
import { useRBAC } from "src/pages/permission/RBACContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const LegalRegistrationForm = () => {
  const { entityData, getEntityProfile } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");
  const router = useRouter();
  
  const { can } = useRBAC();

  const [expanded, setExpanded] = useState(true);

  
  useEffect(() => {
    getEntityProfile();
    console.log("use effect -Legal")
  },[])

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "LEGAL") {
      router.push("/401");
    }
  }, []);

  if (entityCategory === "LEGAL") {
    return (
      <div>
        <>
          <style>
            {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
          </style>

          <DatePickerWrapper>
            <Grid container spacing={6} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Legal Services Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <CompanyDetails
                  data={entityData}
                  expanded={expanded}
                ></CompanyDetails>
              </Grid>
              <Grid item xs={12}>
                <Team data={entityData} expanded={expanded}></Team>
              </Grid>
              <Grid item xs={12}>
                <ProjectDetails
                  data={entityData}
                  expanded={expanded}
                ></ProjectDetails>
              </Grid>
              <Grid item xs={12}>
                <LegalServicesView
                  data={entityData}
                  expanded={expanded}
                ></LegalServicesView>
              </Grid>
              <Grid item xs={12}>
                <SpecializationsView
                  data={entityData}
                  expanded={expanded}
                ></SpecializationsView>
              </Grid>
              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'legal_otherServices_READ'}
                  permission={'legal_otherServices_UPDATE'}
                ></OtherServices>           
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default LegalRegistrationForm;
