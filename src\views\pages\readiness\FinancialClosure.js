// ** React Imports
import { useEffect, useState } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import FormControlLabel from "@mui/material/FormControlLabel";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";
import SwiperPosts from "./SwiperPosts";
import KeenSliderWrapper from "src/@core/styles/libs/keen-slider";
import { useSettings } from "src/@core/hooks/useSettings";
import KeenSliderStyle from "./KeenSliderStyle";
import {
  FormControl,
  FormHelperText,
  Radio,
  RadioGroup,
  TextField,
} from "@mui/material";
import { useForm } from "react-hook-form";
import CardIconBox from "src/views/ui/cards/basic/CardIconBox";
import YouTubeCardSettings from "src/@core/components/custom-components/YouTubeCardSettings";

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
  Textarea: "",
};

const FinancialClosure = ({
  setNextActive,
  posts,
  handleHaveSecuredFinancialClosureChange,
  contactNumber,
  email,
  societyName,
  name,
  defaultData,
}) => {
 
  const [financial, setFinancial] = useState([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({ defaultValues });

  // ** Hook
  const theme = useTheme();
  const {
    settings: { direction },
  } = useSettings();
 
  const [value, setValue] = useState(defaultData?.haveSecuredFinancialClosure);

  const handleChange = (event) => {
    const value = event.target.value;
    setValue(event.target.value);
    handleHaveSecuredFinancialClosureChange(value);
  };

  useEffect(() => {
    if (posts) {
      let data = [];

      posts?.map((item, index) => {
        if (item?.format === 'video' && item?.youtube_url) {
          data.push(
            <YouTubeCardSettings
              key={index}
              videoUrl={item?.youtube_url}
              title={item?.title}
              height={150}
            />
          );
        } else if (item?.format === 'standard') {
          data.push(
            <CardIconBox
              key={index}
              wordPressId={item?.wordPressId}
              title={item?.title}
              slug={item?.slug}
              imageId={item?.imageId}
              link={item?.link}
              resourceType={item?.resourceType}
            />
          );
        }
      });

      setFinancial(data);
    }
  }, [posts]);

  useEffect(() => {
    if (value && name && email && contactNumber && societyName) {
      setNextActive(true);
    } else {
      setNextActive(false);
    }
  }, [value,name,email,contactNumber,societyName]);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              p: { xs: 1, lg: 3 },
              mt: 1,
              mb: 5,
              padding:{xs:'0.6rem'}

            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ mb: 0, fontWeight: "bold" , fontWeight: 'bold', fontSize:{xs:'1rem !important',lg:'1.2rem !important'}}}>
                Have you secured Financial Closure for the proposed
                redevelopment project?
              </Typography>
            </Box>

            <Box>
              <FormControl sx={{ flexWrap: "wrap", flexDirection: "row" }}>
                <RadioGroup
                  value={value}
                  name="simple-radio"
                  aria-label="simple-radio"
                >
                  <FormControlLabel 
                    control={
                      <Radio
                        id='financialClosure-yes'
                        value="Yes"
                        checked={value === "Yes"}
                        onChange={handleChange}
                        sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}
                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'} }}>
                        Yes
                      </Typography>
                    }
                  />
                  <FormControlLabel 
                    control={
                      <Radio
                        id='financialClosure-no'
                        value="No"
                        checked={value === "No"}
                        onChange={handleChange}
                        sx={{ padding:{xs: '4px !important',lg: '6px !important', sm: '6px !important' ,transform: 'scale(0.8)'}}}/>}

                    label={
                      <Typography variant="h6" sx={{ fontWeight: "bold" ,fontSize:{xs: '0.8rem !important',lg: '1rem !important',md: '1rem !important',sm: '1rem !important'}}}>
                        No
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        </Grid>
        {(!name || !societyName || !contactNumber) && (
            <Typography
              variant="body1"
              sx={{ mt: 2, color: "error.main", fontWeight: "bold" }}
            >
              Please fill out contact details to move forward.
            </Typography>
          )}
        <Grid item xs={12} sm={12}>
          {financial.length > 0 && (
            <Typography
              variant="body1"
              sx={{ mb: 3.5, fontSize: "18px", fontWeight: 600 }}
            >
              <Divider
                sx={{
                  mt: `${theme.spacing(3)} !important`,
                  mb: `${theme.spacing(3)} !important`,
                }}
              />
              Review below articles for more info
            </Typography>
          )}
        </Grid>
      </Grid>

      {financial.length > 0 && (
        <KeenSliderStyle>
          <SwiperPosts direction={direction} post={financial} />
        </KeenSliderStyle>
      )}
    </>
  );
};

export default FinancialClosure;
