// RBACContext.js
import React, { createContext, useContext, useState, useEffect } from "react";

import axios from "axios";
import { AuthContext } from "src/context/AuthContext";

// ** Config
import authConfig from "src/configs/auth";

import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";

const defaultProvider = {
  rbacRoles: null,
};

const RBACContext = createContext(defaultProvider);

export const useRBAC = () => {
  return useContext(RBACContext);
};

export const RBACProvider = ({ children }) => {

  /**
   * rbacRoles array sample data
   * ['transaction_DELETE', 'transaction_UPDATE', 'transaction_READ', 'transaction_CREATE','allProfiles_UPDATE', 'allProfiles_READ', 'allProfiles_CREATE','structuralEngineer_serviceDetails_READ', 'structuralEngineer_serviceDetails_CREATE']

   */
  const [rbacRoles, setRbacRoles] = useState([]);

  const { user } = useContext(AuthContext);

  const [accessData, setAccessData] = useState([]);

  useEffect(()=>{
    if(!!user) {
      user.permissions && setRbacRoles(user.permissions);
    }
  }, [user]);

  const can = (permission) => {
    const permissions = permission.split(' '); // Splitting the permission string by space
  
    // Flag to track if any permission exists in rbacRoles array
    let hasPermission = false;
  
    // Checking if any of the permissions exist in rbacRoles array
    for (const perm of permissions) {
      if (rbacRoles.includes(perm)) {
        hasPermission = true; // Set flag to true if any permission exists
        break; // Exit loop if permission found
      }
    }
  
    // If any permission exists, return true
    if (hasPermission) {
      return true;
    }
  
    // Otherwise, check the remaining permission against rbacRoles array in the old way
    return rbacRoles.includes(permission);
  };

  const values = {
    rbacRoles,
    can,
  };

  return (
    <RBACContext.Provider value={values}>{children}</RBACContext.Provider>
  );
};

export default RBACContext;
