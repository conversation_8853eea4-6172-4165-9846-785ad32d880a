import React, { useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import MUITableCell from "src/pages/SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import Icon from "src/@core/components/icon";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useForm } from "react-hook-form";
const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
const QuotationDetails = ({ open,onClose, data }) => {
  const [expanded, setExpanded] = useState(true);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [state, setState] = useState("view");
  const { reset } = useForm();



  const theme = useTheme();

  
  const handleToggle = (value) => {
    setExpanded(value);
  };
  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  return (
    <>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
        
        </Box>
      </Dialog>
      <Dialog fullScreen open={open} onClose={onClose}>
        {/* handleDialogClose */}
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          View Quote
          <Box sx={{ position: "absolute", top: "0px", right: "36px" }}>
            <CloseExpandIcons expanded={expanded} onToggle={handleToggle} />
          </Box>
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Requisition Details"}
            body={
              <>
                {state === "view" && (
                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                    
                  >
                    <Table>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>System Code:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.systemCode}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Society Name:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.companyName}
                            </Typography>
                          </MUITableCell>
                        </TableRow>

                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>System Code:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.systemCode}
                            </Typography>
                          </MUITableCell>
                        </TableRow>

                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Request Date :
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.requestDate}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>
                              Service Requested :
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.serviceRequested}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Status:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.status}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Priority:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {data?.priority}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Quotation Details"}
            body={
              <>
                <div style={{ marginTop: "20px" }}></div>
                <AccordionBasic
                  id={"panel-header-3"}
                  ariaControls={"panel-content-3"}
                  heading={"Scope of Work"}
                  body={
                    <>
                      {state === "view" && (
                        <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                          
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Specification:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.specification}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Salary:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.salary}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Quantity:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.quantity}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Amount</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.amount}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </>
                  }
                  expanded={expanded}
                />
                <AccordionBasic
                  id={"panel-header-4"}
                  ariaControls={"panel-content-4"}
                  heading={"Number of shifts"}
                  body={
                    <>
                      {state === "view" && (
                        <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                        
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Specification:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.specification}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Salary:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.salary}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Quantity:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.quantity}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Amount</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.amount}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </>
                  }
                  expanded={expanded}
                />
                <AccordionBasic
                  id={"panel-header-4"}
                  ariaControls={"panel-content-4"}
                  heading={"Preferred Work Timings"}
                  body={
                    <>
                      {state === "view" && (
                        <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                          
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Specification:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.specification}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Salary:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.salary}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Quantity:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.quantity}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Amount</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.amount}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </>
                  }
                  expanded={expanded}
                />
                <AccordionBasic
                  id={"panel-header-4"}
                  ariaControls={"panel-content-4"}
                  heading={"Level of Compliance"}
                  body={
                    <>
                      {state === "view" && (
                        <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                         
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Specification:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.specification}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Salary:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.salary}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Quantity:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.quantity}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Amount</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.amount}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </>
                  }
                  expanded={expanded}
                />
                <AccordionBasic
                  id={"panel-header-4"}
                  ariaControls={"panel-content-4"}
                  heading={"Number of Staff"}
                  body={
                    <>
                      {state === "view" && (
                        <TableContainer
                          sx={{ padding: "4px 6px" }}
                          className="tableBody"
                        
                        >
                          <Table>
                            <TableBody
                              sx={{
                                "& .MuiTableCell-root": {
                                  p: `${theme.spacing(1.35, 1.125)} !important`,
                                },
                              }}
                            >
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Specification:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.specification}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Salary:</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.salary}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>
                                    Quantity:
                                  </Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.quantity}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                              <TableRow>
                                <MUITableCell>
                                  <Typography style={field}>Amount</Typography>
                                </MUITableCell>
                                <MUITableCell>
                                  <Typography className="data-field">
                                    {data?.amount}
                                  </Typography>
                                </MUITableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </>
                  }
                  expanded={expanded}
                />
              </>
            }
            expanded={expanded}
          />
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={onClose}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default QuotationDetails;
