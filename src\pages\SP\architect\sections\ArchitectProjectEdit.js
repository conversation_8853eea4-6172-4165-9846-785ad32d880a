import React, { useState, useContext } from "react";
import { useForm, Controller } from "react-hook-form";
import {
  Button,
  Divider,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";

const ArchitectProjectEdit = ({ data, onCancel }) => {
  const {
    uploadProject,
    allCategories,
    deleteProject,
    allSubCategories,
    user,
    shortFormData,
    profileUser,
    tempGetArchitectEntityProfile,
  } = useContext(AuthContext);

  const [disableButton, setDisableButton] = useState(true);

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const [projects, setProjects] = useState([]);

  const addEntry = () => {
    setDisableButton(true);
    setProjects([...projects, { id: Date.now(), name: "", file: null }]);
  };

  const removeEntry = (projectId) => {
    setProjects(projects.filter((project) => project.id !== projectId));
  };
  const [projectErrors, setProjectErrors] = useState({});

  const handleFileChange = (projectId, file) => {
    if (file && file.type === "image/png") {
      setDisableButton(false);
      setProjects(
        projects.map((project) => {
          if (project.id === projectId) {
            return { ...project, file };
          }
          return project;
        })
      );
    }
  };

  const memberId =
    user.entityCategory === "SUPER_ADMIN" ? shortFormData.id : null;

  const documentDetails = {
    userId: memberId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("projectsList"),
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const onSubmit = async () => {
    for (const project of projects) {
      const formData = new FormData();
      formData.append("documentDetails", JSON.stringify(documentDetails));
      formData.append("projectName", project.name);
      formData.append("file", project.file);

      try {
        const response = await uploadProject(formData);

        tempGetArchitectEntityProfile();
        console.log("Success");
      } catch (error) {
        console.error("Failure", error);
      }
    }
    onCancel();
  };

  console.log("data from authContext", data);

  const [selectedProject, setSelectedProject] = useState(null);
  const [projectToDelete, setprojectToDelete] = useState(false);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);

  async function handleDelete() {
    if (user.entityCategory === "SUPER_ADMIN") {
      await deleteProject(
        memberId,
        profileUser.entityId,
        projectToDelete.filePath
      );
    } else {
      await deleteProject(null, user.entityId, projectToDelete.filePath);
    }
    await tempGetArchitectEntityProfile();
    setprojectToDelete(null);
    setConfirmDeleteDialogOpen(false);
  }

  const handleViewIconClick = (projectFilePath) => {
    setSelectedProject(projectFilePath);
  };

  const handleDialogClose = () => {
    setSelectedProject(null);
  };

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Paper>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Project Name</TableCell>
                  <TableCell>Image</TableCell>
                  <TableCell>Delete</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {projects.map((project, index) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <Controller
                        name={`projects[${index}].name`}
                        control={control}
                        defaultValue={project.name}
                        rules={{ required: "Project Name is required" }}
                        render={({ field }) => (
                          <>
                            <TextField
                              {...field}
                              label="Project Name"
                              variant="outlined"
                              fullWidth
                              error={Boolean(errors?.projects?.[index]?.name)}
                              helperText={
                                errors?.projects?.[index]?.name?.message || ""
                              }
                              onChange={(e) => {
                                field.onChange(e);
                                setProjects((prevProjects) =>
                                  prevProjects.map((prevProject) =>
                                    prevProject.id === project.id
                                      ? { ...prevProject, name: e.target.value }
                                      : prevProject
                                  )
                                );
                              }}
                            />
                          </>
                        )}
                      />
                    </TableCell>

                    <TableCell>
                      <Button
                        component="label"
                        htmlFor={`file-input-${project.id}`}
                      >
                        Choose Image
                        <input
                          id={`file-input-${project.id}`}
                          type="file"
                          accept=".png"
                          style={{ display: "none" }}
                          onChange={(e) =>
                            handleFileChange(project.id, e.target.files[0])
                          }
                        />
                      </Button>
                      <br />
                      <span>{project.file ? project.file.name : ""}</span>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => removeEntry(project.id)}
                        color="error"
                      >
                        <Icon icon="iconamoon:trash" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}

                <TableRow>
                  <TableCell colSpan={4}>
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        justifyContent: "flex-start",
                        margin: "10px",
                      }}
                    >
                      <Button
                        onClick={addEntry}
                        color="primary"
                        variant="contained"
                      >
                        Add
                      </Button>
                    </Grid>

                    <Divider />
                    <Grid>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell>Image</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {data?.projectsList?.map((project, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Typography
                                  className="data-field"
                                  marginLeft={"12px"}
                                >
                                  {project?.projectName}
                                </Typography>
                              </TableCell>

                              <TableCell>
                                <Typography className="data-field">
                                  {project?.filePath &&
                                    project.filePath.split("/").pop()}
                                </Typography>
                              </TableCell>

                              <TableCell>
                                <IconButton onClick={() => {}} color="error">
                                  <Icon icon="iconamoon:trash" />
                                </IconButton>
                                <IconButton
                                  onClick={() =>
                                    handleViewIconClick(project?.filePath)
                                  }
                                  color="error"
                                  disabled={selectedProject}
                                >
                                  <Icon icon="iconamoon:eye" />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>

                      <ViewDialogByLocation
                        location={selectedProject}
                        setSelectedLocation={setSelectedProject}
                        open={Boolean(selectedProject)}
                        onClose={handleDialogClose}
                      />
                    </Grid>

                    <Grid
                      container
                      justifyContent="center"
                      sx={{
                        mt: { xs: 2, lg: 4 },
                        mb: { xs: 2, lg: 4 },
                      }}
                    >
                      <Button
                        size="medium"
                        sx={{ mr: 3 }}
                        variant="outlined"
                        color="primary"
                        onClick={() => onCancel()}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        color="primary"
                        disabled={disableButton}
                        variant="contained"
                      >
                        Submit
                      </Button>
                    </Grid>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <DeleteConfirmationDialog
              open={confirmDeleteDialogOpen}
              onClose={() => setConfirmDeleteDialogOpen(false)}
              onConfirm={handleDelete}
            />
          </Paper>
        </form>
      </Grid>
    </Grid>
  );
};

export default ArchitectProjectEdit;
