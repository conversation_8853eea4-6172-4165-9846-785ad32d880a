// ParentComponent.js
import React, { useState, useEffect, useContext } from "react";
import axios from "axios";
import authConfig from "src/configs/auth";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import ServicesTabs from "./ServiceTabs";

const ParentComponent = (userDataAllProfile) => {
  const { can } = useRBAC();
  const [data, setData] = useState([]);
  const [serviceId, setServiceId] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [isEditMode, setIsEditMode] = useState(false);

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [userList, setUserList] = useState([]);

  const { user, getBasicProfileData, basicProfileGetData, fetchOne, userData } =
    useContext(AuthContext);

  const [listValues, setListValues] = useState(null);

  const auth = useAuth();

  function replaceUnderscoresWithSpace(text) {
    text = text?.replace(/_/g, " ");
    text = text?.replace(/-/g, " ");
    // Capitalize the first letter and convert the rest to lowercase ?.toLowerCase().replace(/(?:^|\s)\S/g, function(a) { return a.toUpperCase(); });
    return text;
  }
  const userId = userDataAllProfile?.userDataAllProfile?.id;

  const { handleSubmit } = useForm();

  const handleChange = (category, event) => {
       setSelectedOptions({
      ...selectedOptions,
      [category]: event.target.value,
    });
  };

  const handleChangeSelect = (category, event) => {
  
    setSelectedOptions({
      ...selectedOptions,
      [category]: [event.target.value],
    });
  };

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, []);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  const userUniqueId = userId !== undefined ? userId : user.id;

  useEffect(() => {
    getBasicProfileData(userUniqueId);
  }, []);

  useEffect(() => {
    fetchOne(userUniqueId);
  }, []);

  //Based on the serviceId we fetch the labels and it's values assigned to the service in the service_profile table.
  const fetchAll = async (serviceId, data) => {
    const url = `${getUrl(
      authConfig.getAllServiceProfiles
    )}/${serviceId}/datafields`;
    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "get",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  function reverseTransformData(transformedData) {
    const originalData = {};

    transformedData?.forEach((item) => {
      // Check if the listValues array has more than one item.
      // If so, map through the array and extract the listValueId, forming a new array.
      // If not, just extract the single listValueId.
      if (item.listValues.length > 0) {
        originalData[item.listNameId] = item.listValues.map(
          (valueItem) => valueItem.listValueId
        );
      } else {
        originalData[item.listNameId] = item.otherValue;
      }
    });

    return originalData;
  }

  const [finalUser, setFinalUser] = useState({});
  useEffect(() => {
    if (userData && userData.length > 0) {
            // Find the user whose serviceNameId matches the serviceId
      const userWithMatchingService = userData.find(
        (user) => user.serviceNameId === serviceId
      );
      if (userWithMatchingService) {
        // If a user is found, set the user data in the state variable
        setFinalUser(userWithMatchingService);
        const reverse = reverseTransformData(
          userWithMatchingService.metadata.listNames
        );
        setSelectedOptions(reverse);
      }
    }
  }, [userData, serviceId]);

  useEffect(() => {
    if (finalUser && finalUser.metadata) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList.reduce((map, item) => {
        item.values.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from userList
      const listNames = userList.map((item) => {
        const metadataItem = finalUser.metadata.listNames?.find(
          (list) => list.listNameId === item.id
        );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setData(listNames);
    } else {
      console.log("finalUser or finalUser.metadata is null");
    }
  }, [finalUser, userList]);

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> Services added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Add services. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  function transformData(originalData) {
    const transformedData = [];
    for (const listNameId in originalData) {
      let listValues = [];
      let otherValue = null;

      if (Array.isArray(originalData[listNameId])) {
        listValues = originalData[listNameId].map((listValueId) => ({
          listValueId,
        }));
      } else {
        otherValue = originalData[listNameId];
      }

      transformedData.push({
        listNameId,
        otherValue,
        listValues,
      });
    }

    return transformedData;
  }

  async function submit(data) {
    const transformedData = transformData(selectedOptions);

    const payload = {
      id: finalUser.id,
      metadata: {
        listNames: transformedData,
      },
      isActive: true,
      serviceNameId: finalUser.serviceNameId,
      userServicesDataGroup: finalUser.userServicesDataGroup,
    };

    const userUniqueId = userId !== undefined ? userId : user.id;
    const response = await auth.patchServices(payload, userUniqueId, () => {
      console.error("Service Profile Details failed");
    });

    if (response) {
      handleSuccess();
    } else {
      handleFailure();
    }
    setIsEditMode(false);
    fetchAll(serviceId);
    fetchOne(userUniqueId);
  }

  const handleTabChange = (serviceId) => {
    fetchAll(serviceId);
    setServiceId(serviceId);
    setSelectedOptions({});
  };
if(can('serviceProfile_READ')){
  return (
    <>
      <>
        <ServicesTabs
          tabContents={serviceNames} //Tab names. plumber, electrician
          data={data} //Existing user data mapping the ids with their names.
          userList={userList} //based on the serviceId we fetch the labels and values of each label based on the tab selected.
          formData={data} //Existing user data mapping the ids with their names.
          selectedOptions={selectedOptions}
          handleChange={handleChange}
          handleChangeSelect={handleChangeSelect}
          handleSubmit={handleSubmit(submit)}
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
          onTabChange={handleTabChange}
        />
        <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    </>
  );}
  else{
    return null;
  }
};

export default ParentComponent;
