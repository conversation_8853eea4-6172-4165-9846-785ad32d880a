const baseURL = process.env.NEXT_PUBLIC_API_URL;

const googleAuthUrl = process.env.NEXT_PUBLIC_GOOGLE_AUTH_URL;

const facebookAuthUrl = process.env.NEXT_PUBLIC_FACEBOOK_AUTH_URL;

export default {
  baseURL: baseURL,
  googleAuthUrl: googleAuthUrl,
  facebookAuthUrl: facebookAuthUrl,
  authActionUrl: "auth/flow",
  guestURL: "https://v4.beta.houzer.co.in/",
  resourcesURL: "https://reducate.houzer.co.in/",
  readinessEndpoint: "auth/readiness",
  getReadinessProfileEndpoint: "users/readiness",
  loginEndpointNew: "auth/login",
  searchEndpoint: "entity",
  addNewUserEndpoint: "member/createMember",
  saveUserSpEndpoint: "users/all-profiles/create-short-form-sp",   //sp user form
  userPatch: "users/society/profile-data",
  userGet: "users/society-profile",
  readinessPatch: "users/updateReadiness",
  projectDataEndpoint: "entity",
  registerEndpointNew: "auth/signup",
  registerEndpointNewV2: "auth/signupV2",
  registerEndpointNewV3: "auth/signupV3",
  profileEndpointNew: "users/profile",
  profileUpdateEndpoint: "users",
  emailVerificationEndpoint: "auth/verify",
  getAllMembersEndpoint: "member/all",
  getAllRoleTypesEndpoint: "roleTypes/all",
  roleTypeEndpoint: "roleTypes",
  roleEndpoint: "role",
  roleUpdateEndpoint:"role/update",
  roleTypeUpdateEndpoint:"roleTypes/update",
  getAllEmployeesList: "employee/list",
  getAllRolesEndpoint: "role/all",
  usersEndpoint: "users",
  getAllUsersEndpointNewTable: "users/all-users",
  getAllProfilesEndpointNewTable: "users/all-profiles",
  getAllConversations: "conversation/getAllConversations",
  getAllTimelineConversations:'conversation/getAllConversationsByTimeLine',
  passwordResetEndpoint: "auth/reset",
  settings: "settings",
  storageTokenKeyName: "accessToken",
  storageUserKeyName: "userData",
  onTokenExpiration: "logout", // logout | refreshToken
  triggerForgetPasswordEndpoint: "auth/forgotPassword",
  otpSendToEmailEndpoint: "auth/emailVerify",
  employeeCreateEndpoint: "employee",
  requisitionEndpoint:"service-requisition",
  requisitionGetAll:"service-requisition/all",
  requisitionsByUserId:"service-requisition/getServiceRequisitionByUserId",
  requisitionGet:"service-requisition/getServiceRequisitionById",
  requisitionUpdate:"service-requisition/updateServiceRequisitionByDTO",
  employeeUpdateEndpoint: "employee",
  employeeGetAllEndpoint: "employee/all",
  employeeDeleteEndpoint: "employee",
  employeeStatusUpdateEndpoint:"employee/update",
  unSubscribeEndpoint: "auth/unSubscribe",
  otpVerifyEndpoint: "auth/verifyOTPV2",
  otpVerifyEndpoint3: "auth/verifyOTPV3",
  notificationEndpoint: "notification",
  selectDropdown: "selectDropdown",
  reminderEndpoint: "reminder",
  documents: "documents",
  snapshots:"lead-snapshots",
  snapshotsGetAll:"lead-snapshots/allLeadSnapshots",
  snapshotsDelete:"lead-snapshots/delete",
  snapshotsActivate:"lead-snapshots/activate",
  snapshotsDeleteById:"lead-snapshots/deleteImage",
  packageTypeListNameId: "5c1b4c4e-8b9b-4b3d-9f77-8c44f63b254a",
  yearsOfExperienceListNameId:"918a41cd-e3ec-46c5-84da-0cf2727842bc",
  portalsRegisteredListNameId:"7b7244dc-1a64-4e98-930f-eb07ba9c308e", 
  documentCategories: "documentCategories/all",
  documentSubCategories: "documentSubCategories/all",
  uploadProject: "documents/projects",
  getAllDocuments: "documents/allDocuments",
  getFileByLocation: "api/getFile",
  profileLogout: "logoutProfile",
  masterDataCreateEndpoint: "master-data",
  masterDataActivate:"master-data/activate",
  masterDataGetAllEndpoint: "master-data/all",
  servicesGetAll: "list-values/allValuesByNameId",
  locationZonesGetAll:"location-zone/all",
  locationZoneMapping:"location-zone",
  locationZoneActivate:"location-zone/activate",
  subServicesGetAll: "service-profile/sub-services",
  listValuesEndpoint: "list-values",
  listValueUpdate:"list-values/update",
  listValuesGetAll: "list-values/all",
  listNameEndpoint: "list-names",
  listNamesGetAll:"list-names/all",
  listNameStatisticsQuestion:"list-names/statistics-questions",
  uiComponentId:"612e5e29-dbe4-49cf-b693-f935c69219e1",
  fsiRuleEndpoint: "fsi-rules",
  fsiRulesActivateEndpoint:"fsi-rules/activate",
  fsiRulesGetAllEndpoint: "fsi-rules/all",
  fsiCalculatorEndpoint: "fsi-calculator",
  fsiCalculatorUpdateEndpoint:"fsi-calculator/update",
  fsiCalculatorSocietyGetALL: "fsi-calculator/all",
  fsiCalculatorGetALL: "fsi-calculator/all-records",
  assignRoleEndpoint: "users/assignRole",
  getAllUserTypes: "users/userType",
  getAllServiceProfiles: "service-profile",

  updateServiceProfiles:"service-profile/update",

  specificationActivate:"service-profile/activate",
  serviceProfileEndpoint: "user-services",
  statisticsEndpoint: "user-services/statistics",
  servicesDataEndpoint: "user-services/services",
  statisticsDataEndpoint: "user-services/statistics",
  projectsDataEndpoint: "user-services/projects",
  locationNames: "service-profile/locations",
  getAllSpecifications:"service-profile/all",
  getAllListValuesByListNameId: "list-values/listNameId",
  locationlistNameId: "c49dd276-79aa-4e4d-9292-5a1b3f6aaf48",
  workLocation: "9e36c6d5-3545-4b77-af6d-d1fb3c29585c",
  designationId: "67f1c22d-0a95-4db3-aae8-45294f112e99",
  designationSPId:"8e98a6de-9b9f-4c70-84fb-8821fd724657",
  allServicesListNameId: "88cf3b1d-8548-41d3-9910-c2f92dcf2815",
  uiComponentsId:"5b50c3e1-bf42-4c50-90a4-109bc42e3a38",
  allSectionsId:"9c53ef49-04db-4c0f-8f19-7f75f58a066d",
  societiesId: "2b5a9a3a-9440-4217-a5e5-784d1a42d53b",
  allBusinessLineId: "8aee6092-0c04-46fc-8b0a-d860eb2e36e2",
  basicProfileEndpoint: "users/basic-profile",
  allProfilesBasicProfileEndpoint: "users/all-profiles/createUser",
  allProfilesCHSCreateEndpoint:"users/all-profiles/chs-short-form",
  allListNamesValues: "listNamesValues/all",
  micrositeListsEndpoint: "microsite/lists",
  micrositeLevel2Endpoint: "microsite/level2",
  micrositeLevel1Endpoint: "microsite/level1",
  micrositeBasicProfileGetEndpoint: "public/microsite/basic-profile-microsite",
  leadStatusListNamesId: "550e8400-e29b-41d4-a716-446655440000",
  groupDataListNameId: "4f4050bc-4f07-42a2-8f2c-56005d1af5bb",
  getAllConversationsByUserId: "conversation/getAllConversationsByUserId",
  conversationType: "2b5a9a3a-9440-4217-a5e5-784d1a42d123",
  outcomeConversation: "0a6e0dc5-92a4-4d7e-8f6b-ee95b8e07e1d",
  target: "9aeb2e4e-8902-4f52-a8f7-3b92c374c55f",
  shallRemind: "57c2f3a3-5b84-4d98-bc09-9e18d40aeb36",
  userCategorySociety: "SOCIETY",
  userCategoryServiceProvider: "SERVICE_PROVIDER",
  userCategoryEmployee: "EMPLOYEE",
  conversationEndpoint: "conversation",
  taskEndpoint: "task",
  getAllTasks: "task/all",
  leadPriorityListNamesId: "7819267b-59e0-4b02-80e8-43d0e6db9d80",
  priorityListNamesId: "f59db722-9be2-4f5a-b072-27cf4247f3e4",
  statusListNamesId: "c166e081-9b82-49ec-9a1a-0428e426e33b",
  allZonesListNameId:"3dcb4434-849f-4b5c-a881-70b31ebde9d2",
  allProfilesUpdateActiveStatus:"users/all-profiles/updateStatus",
  allProfilesUpdateInActiveStatus:"users/all-profiles/deleteUser",
  allUsersForContacts:"users/all-users-contacts",
  watiEndPoint: "api/wati/sendTemplateMessage",
  eventEndpoint:'event',
  calendarUpdateEventEndpoint:'apps/calendar/update-event',
  calendarGetByCalendarEventEndpoint:'apps/calendar/event',
  calendarDeleteEventEndpoint:'apps/calendar/remove-event',
  watiMutliMessageEndPoint: "api/wati/TemplateMessages",
  contactGroupsEndpoint:"contact-group",
  contactGroupsGetAll:"contact-group/all",
  contactGroupsActivate:"contact-group/activate",
  microSiteGet: 'public/microsite/basic-profile-microsite',
  getStatisticsData: "user-services/statistics",
  getProjectsImages: "documents/projects/images",

};
