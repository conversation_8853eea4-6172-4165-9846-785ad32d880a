// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import Section2 from "src/pages/SP/broker/sections/Section2";
import { useTheme } from "@emotion/react";
import { useRBAC } from "src/pages/permission/RBACContext";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const TeamBroker = ({ data,expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state2, setState2] = useState("view");

  const viewClick2 = () => {
    setState2("edit");
  };

  const editClick2 = () => {
    setState2("view");
  };

  return (
    <>
    {/* {can('broker_teamBroker_READ') &&  */}
      <AccordionBasic
      id={"panel-header-1"}
      ariaControls={"panel-content-1"}
      heading={"Team"}
      body={
        <>
          {state2 === "view" && (
            
                <TableContainer
                  sx={{ padding:'4px 6px' }}
                  className="tableBody"
                  //onClick={can('broker_teamBroker_UPDATE') ? viewClick2 : null}
                  onClick={viewClick2}
                >
                  <Table>
                  <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>
                            Year Of Experience:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field" >
                            {data?.yearsOfExperience}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>Awards:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field" >
                            {data?.awards}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>
                            Brief Profile:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field" >
                            {data?.briefProfile}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
            
          )}
          {state2 === "edit" && (
            <Section2 formData={data} onCancel={editClick2} />
          )}
        </>
      }
      expanded={expanded}
    />
    {/* } */}
      
    </>
  );
};
export default TeamBroker;
