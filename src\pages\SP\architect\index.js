// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";

// ** Custom Components Imports
import PageHeader from "src/@core/components/page-header";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

// ** Demo
import { TableCell } from "@mui/material";

import { styled } from "@mui/material/styles";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CompanyDetailsArchitect from "./sections/CompanyDetailsArchitect";
import TeamArchitect from "./sections/TeamArchitect";

import ProjectDetailsArchitect from "./sections/ProjectDetailsArchitect";
import Remarks from "src/@core/components/custom-components/Remarks";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import ServicesOffered from "./sections/ServicesOfferedView";
import ServicesView from "src/@core/components/custom-components/ServicesView";
import SpecializationView from "./sections/SpecializationView";
import { LegendToggleSharp } from "@mui/icons-material";
import ServicesArchitectView from "./sections/ServicesArchitectView";
import AreaOfExperties from "./sections/AreaOfExperties";
import TestimonialForm from "./sections/TestimonialEdit";
import EducationalInsightsForm from "./sections/EducationalInsightsEdit";
import TestimonialView from "./sections/TestimonialView";
import EducationalInsightsView from "./sections/EducationalInsightsView";
import IntroductionFieldsView from "./sections/IntroductionFieldsView";
import ArchitecturalDesignView from "./sections/ArchitecturalDesignView";
import MembershipView from "./sections/MembershipView";
import ArchitectAwardsView from "./sections/ArchitectAwardsView";
import ArchitectProjectView from "./sections/ArchitectProjectView";

import { useRBAC } from "src/pages/permission/RBACContext";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const field = {
  fontWeight: 400,
};

const basic_details = {
  name: "Architect Name",
  company: "Chidhagni Pvt Ltd",
  address: "Hyderabad",
  website: "chidhagni.com",
  contact: "9001329151",
  email: "<EMAIL>",
};

const basic_schema = {
  location: "Island, Western Suburb",
  design: "Residential, Retail",
  liasoning: "MCGM, MHADA",
  years_of_experience: "5-10 years",
  awards: "Excellence in Residential",
  brief_profile: "I am a professional in Houzer!",
  indicative_fees_per_sqft: "1000",
};

const ArchitectRegistrationForm = () => {
  const { entityData, getEntityProfile, user } = useContext(AuthContext);
  const [entityCategory, setEntityCategory] = useState("");

  const { can } = useRBAC();

  console.log("ENTITY DATA", entityData, user?.entityId);

  const router = useRouter();

  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    getEntityProfile();
    console.log("use effect -Architect");
  }, []);

  const handleToggle = (value) => {
    setExpanded(value);
  };

  useEffect(() => {
    let value = localStorage.getItem("userData");
    value = JSON.parse(value);
    setEntityCategory(value.entityCategory);
    if (value.entityCategory !== "ARCHITECT") {
      router.push("/401");
    }
  }, []);

  if (entityCategory === "ARCHITECT") {
    return (
      <div>       
        <>        
          <style>
            {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={6} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      title={
                        <Typography variant="h5">
                          Architect Registration
                        </Typography>
                      }
                      subtitle={<Typography variant="body2"></Typography>}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={0.5}
                    sx={{
                      width: "auto",
                      textAlign: "end",
                      justifyItems: "end",
                    }}
                  >
                    <CloseExpandIcons
                      expanded={expanded}
                      onToggle={handleToggle}
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <IntroductionFieldsView
                  data={entityData}
                  expanded={expanded}
                ></IntroductionFieldsView>
              </Grid>
              <Grid item xs={12}>
                <CompanyDetailsArchitect
                  data={entityData}
                  expanded={expanded}
                ></CompanyDetailsArchitect>
              </Grid>
              <Grid item xs={12}>
                <TeamArchitect
                  data={entityData}
                  expanded={expanded}
                ></TeamArchitect>
              </Grid>
              <Grid item xs={12}>
                <ProjectDetailsArchitect
                  data={entityData}
                  expanded={expanded}
                ></ProjectDetailsArchitect>
              </Grid>

              <Grid item xs={12}>
                <MembershipView
                  data={entityData}
                  expanded={expanded}
                ></MembershipView>
              </Grid>

              <Grid item xs={12}>
                <ServicesOffered
                  data={entityData}
                  expanded={expanded}
                ></ServicesOffered>
              </Grid>
              <Grid item xs={12}>
                <ArchitecturalDesignView
                  data={entityData}
                  expanded={expanded}
                ></ArchitecturalDesignView>
              </Grid>
              <Grid item xs={12}>
                <SpecializationView
                  data={entityData}
                  expanded={expanded}
                ></SpecializationView>
              </Grid>
              <Grid item xs={12}>
                <ArchitectAwardsView
                  data={entityData}
                  expanded={expanded}
                ></ArchitectAwardsView>
              </Grid>

              <Grid item xs={12}>
                <ArchitectProjectView
                  data={entityData}
                  expanded={expanded}
                ></ArchitectProjectView>
              </Grid>

              <Grid item xs={12}>
               
                  <ServicesView
                  data={entityData}
                  expanded={expanded}
                  readPermission={'architect_services_READ'}
                  permission={'architect_services_UPDATE'}
                ></ServicesView>
                             
              </Grid>
              <Grid item xs={12}>
                <AreaOfExperties
                  data={entityData}
                  expanded={expanded}
                ></AreaOfExperties>
              </Grid>

              <Grid item xs={12}>
                <ServicesArchitectView
                  data={entityData}
                  expanded={expanded}
                ></ServicesArchitectView>
              </Grid>

              <Grid item xs={12}>
                <TestimonialView
                  data={entityData}
                  expanded={expanded}
                ></TestimonialView>
              </Grid>
              <Grid item xs={12}>
                <EducationalInsightsView
                  data={entityData}
                  expanded={expanded}
                ></EducationalInsightsView>
              </Grid>

              <Grid item xs={12}>
                <OtherServices
                  data={entityData}
                  expanded={expanded}
                  readPermission={'architect_otherServices_READ'}
                  permission={'architect_otherServices_UPDATE'}
                ></OtherServices>             
              </Grid>
              <Grid item xs={12}>
                <Remarks data={entityData} expanded={expanded} readPermission={'architect_remarks_READ'} permission={'architect_remarks_UPDATE'}></Remarks>           
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  } else {
    return null;
  }
};

export default ArchitectRegistrationForm;
