import React from "react";
import {
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
} from "@mui/material";

const RemarksTable = ({ remarksList,employeesData }) => {
   
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>S No</TableCell>
            <TableCell>User </TableCell>
            <TableCell>Remarks</TableCell>
            <TableCell>Created On</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {remarksList?.map((remark, index) => (
            <TableRow key={index}>
              <TableCell>{index + 1}</TableCell>
              <TableCell> {employeesData?.find((employee) => employee.id === remark?.userId)?.name}</TableCell>
              <TableCell>{remark?.remarks}</TableCell>
              <TableCell> {remark?.createdOn ? remark.createdOn.split('T')[0] : ''}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default RemarksTable;
