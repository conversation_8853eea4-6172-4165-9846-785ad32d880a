import React, { useEffect, useState } from 'react';
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Paper,
  ClickAwayListener,
  IconButton,
  Drawer,
  Dialog,
  DialogActions,
  Typography,
  Box,
  Button,
  Card,
  DialogContent,
  DialogContentText,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import Icon from "src/@core/components/icon";

import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

// ** Util Import
import { getInitials } from 'src/@core/utils/get-initials'

import { styled } from '@mui/material/styles'
import CustomAvatar from 'src/@core/components/mui/avatar'

const Avatar = styled(CustomAvatar)({
  width: 38,
  height: 38,
  fontSize: '1.125rem'
})


const NotificationDropdown = ({ notifications, markAsRead, deleteNotification,setNotifications, fetchNotifications,notificationContent,closeDropdown ,openDrawer,open,handleDrawerClose,createdOnDate}) => {
  // Define a fixed width for the notification container and the content's fixed height
  const containerWidth = '300px'; // Adjust the width as needed
  const contentHeight = '40px'; // Adjust the height as needed


  const [hoveredCard, setHoveredCard] = useState(null);
  const [openDialogContent,setOpenDialogContent] = useState(false)
  
  const [dialogMessage, setDialogMessage] = useState("");

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const cardStyle = (id) => ({
    maxWidth: '440px',  // Use maxWidth to ensure it does not exceed the drawer's width
    height: '50%',
    margin: '1.2 auto', // Top and bottom margin 10px, left and right auto for centering
    // transition: 'box-shadow 0.2s ease',
    // boxShadow: hoveredCard === id ? '0px 4px 20px rgba(0, 0, 0, 0.2)' : 'none',
  });
  
  
  // Style for ListItemText to increase font size
  const listItemTextStyle = {
    fontFamily: 'Lato, sans-serif',
    fontSize: '15px',
    fontWeight: 'normal',
    fontStyle: 'normal', 
    whiteSpace: 'normal', // Allows text to wrap
   // display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    lineHeight: '1.5', // Adjust line height to ensure lines are spaced nicely
    marginBottom: '12px'
  };



  const listItemStyle = {
    backgroundColor: 'white',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: '70px',
    padding: windowWidth < 426 ? '5px 5px' : '10px 15px', 

   
  };

  const listItemStyleDynamic = (windowWidth) => ({
    ...listItemStyle,
    padding: windowWidth < 426 ? '5px 5px' : '10px 15px', // Adjust padding based on screen width
  });
  
  const listItemTextStyleDynamic = (windowWidth) => ({
    ...listItemTextStyle,
    fontSize: windowWidth < 426 ? '14px' : '15px', // Adjust font size for small screens
    WebkitLineClamp: windowWidth < 426 ? 3 : 2, // Allow more text lines on smaller screens if needed
  });

  
  const deleteIconStyle = (id) => ({
    visibility: hoveredCard === id ? 'visible' : 'hidden',
    alignSelf: 'center',
    flexShrink: 0, // Prevent the icon from shrinking
  });
  
  const handleClearAllNotifications = ({notifications}) => {
    if (!Array.isArray(notifications)) {
      console.error('handleClearAll expects an array of notifications');
      return;
    }
    const deletePromises = notifications.map((notification) => {
      return axios({
        method: "DELETE",
        url: getUrl(authConfig.notificationEndpoint) + `/${notification.id}`,
        headers: getAuthorizationHeaders(),
      });
    });
    Promise.all(deletePromises)
    .then((results) => {
     
      setNotifications([]); // Clear all notifications after successful deletion
      setOpenDialogContent(false)
      fetchNotifications(); // Refetch notifications, if necessary
    })
    .catch((err) => {
      setOpenDialogContent(false)
      console.error("An error occurred while deleting notifications", err);
    });
  }


  const handleClearAll = ({notifications}) => {
    setOpenDialogContent(true)
    const message = `
    <div>
    <h3><b>Clear All Notifications</b></h3>
    <h4>
      By proceeding, you will permanently erase all notifications. This action cannot be undone. Please confirm if you wish to continue. 
    </h4>
    </div>
    `;
    setDialogMessage(message);
  }

  const handleClose = () => {
    setOpenDialogContent(false)
  }

  const RenderAvatar = ({ notification }) => {
    const {  avatarColor } = notification

    const getAbbreviatedName = (name) => {
      const words = name?.split(' ');
      if (words && words.length > 2) {
        return `${words[0]} ${words[1]}`;
      }
      return name;
    };
    
    
      return (
        <Avatar skin='light' color={avatarColor} sx={{  fontSize: notification?.createdBy && notification.createdBy.length == 1 ? '36px' : '20px',width: 45, height: 45 }}>
          {getInitials(getAbbreviatedName(notification?.createdBy))}
        </Avatar>
      )
  }

  function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options); // Format the date to "December 27, 2023"
  }

  function formatCreatedOnDate(dateString) {
    const options = { year: '2-digit', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', hour12: true };
    const date = new Date(dateString);
    return date.toLocaleString('en-US', options);
  }

  return (
    <>
    <Box sx={{ display: 'flex', flexDirection: 'row' }}>
    <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={closeDropdown}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "100%", sm: 440 } } }}
      >
        <Box
        sx={{
         
          "@media screen and (min-width: 321px)": {
            width: 315, // Applied when viewport width is at least 321px
          },
          "@media screen and (min-width: 375px)": {
            width: 375, // Applied when viewport width is at least 376px
          },
          "@media screen and (min-width: 425px)": {
            width: 420, // Applied when viewport width is at least 426px
          },
          "@media screen and (min-width: 601px)": {
            width: 440, // Overrides previous widths when viewport width is at least 601px
          },
        }}
      >
        <Typography
          sx={{
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start", 
            paddingLeft: "20px",
            fontSize: "25px !important",
            textAlign: "left",
            fontWeight: "bold",
            position: "sticky",
            top: 12,
            backgroundColor: "white",
            zIndex: 1,
            ml: "20px",
          }}
          variant="h4"
        >
           Notifications
           <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                <IconButton
                  size="small"
                  onClick={closeDropdown}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: (theme) =>
                        '#66BB6A',
                      transition: 'background 0.5s ease, transform 0.5s ease',                         },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
        </Typography>
		
        {notifications.length > 0 ? (
  <List>
    {notifications.map(notification => (
      <Card 
        key={notification.id}
        style={cardStyle(notification.id)}
        onMouseEnter={() => setHoveredCard(notification.id)}
        onMouseLeave={() => setHoveredCard(null)}
      >
        <ListItem
          button
          onClick={notification.isRead ? null : () => markAsRead(notification.id, notification.content, notification.createdOn)}
          style={{
            ...listItemStyleDynamic(windowWidth),
            backgroundColor: notification.isRead ? 'white' : '#f2f7f2',
            cursor: notification.isRead ? 'default' : 'pointer',
          }}
        >
          <RenderAvatar notification={notification}/>
          <Box style={{ flex: 1, marginLeft: '12px', overflow: 'hidden', marginRight: '40px' }}> {/* Add marginRight to ensure space for delete icon */}

            <ListItemText 
              primary={notification.content} 
              primaryTypographyProps={{ style: listItemTextStyleDynamic(windowWidth) }}
            />
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{
                paddingTop: '4px',
                fontSize: {
                  xs: '0.7rem', // Assuming you want `rem` units, adjust as necessary
                  lg: '0.8rem',
                },
              }}
            >
              {formatDate(notification?.createdOn)} • {notification?.createdBy}
            </Typography>
          </Box>
          <ListItemSecondaryAction style={{ ...deleteIconStyle(notification.id), marginRight: '5px' }}> {/* Adjust marginRight if needed */}

            <Tooltip title="Delete">
              <IconButton edge="end" onClick={() => deleteNotification(notification.id)}>
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </ListItemSecondaryAction>
        </ListItem>
      </Card>
    ))}
  </List>
) : (
  <div style={{ textAlign: 'center', margin: '20px' }}>No Notifications to Show</div>
)}

      </Box>
{/* only make it responsive in mobile view 320 , 375,425 in this make it stick to left */}
     <Box
  sx={{
    position: "fixed",
    bottom: 0,
    right: 0,
    borderTop: (theme) => `1px solid ${theme.palette.divider}`,
    p: (theme) => theme.spacing(2),
    display: "flex",
    alignItems: "center",
    backgroundColor: "background.paper", // Use theme's paper color for background
    width: 'auto', // Adjust width dynamically
    "@media screen and (min-width: 601px)": {
      width: 440, // Adjust width for larger screens to match the Drawer's width
    },
    "@media screen and (max-width: 426px)": {
      right: 'auto', // Stick to the left on small devices
      left: 0, // Align to the left edge
      width: '100%', // Full width on small screens
    },
  }}
>
<Typography
  variant="body1"
  sx={{
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    cursor: notifications.length > 0 ? "pointer" : "default", // Conditional cursor style
    "&:hover": {
      textDecoration: notifications.length > 0 ? "underline" : "none", // Conditional underline on hover
    }
  }}
  onClick={notifications.length > 0 ? () => handleClearAll({ notifications }) : undefined} // Conditional onClick handler
>
  <IconButton
    onClick={notifications.length > 0 ? () => handleClearAll({ notifications }) : undefined} // Conditional onClick handler
    sx={{
      ml: 1, // Add some space between the text and the icon
      pointerEvents: notifications.length > 0 ? "auto" : "none", // Prevent click events when notifications are empty
    }}
  >
    <DeleteIcon />
  </IconButton>
  Clear all notification(s)
</Typography>

</Box>

    </Drawer>
    <Drawer
        open={openDrawer}
        anchor="right"
        variant="temporary"
        onClose={handleDrawerClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "100%", sm: 400 } } }}
      >
         <Box
        sx={{
          width: 300,
          "@media screen and (min-width: 601px)": {
            width: 400,
          },
        }}
      >
        <Typography
          sx={{
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start", 
            textAlign: "left",
            position: "sticky",
            top: 0,
            backgroundColor: "white",
            zIndex: 1,
          }}
        >
          {formatCreatedOnDate(createdOnDate)}
           <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                <IconButton
                  size="small"
                  onClick={handleDrawerClose}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                    backgroundColor: "primary.main",
                    "&:hover": {
                    backgroundColor: 
                    '#66BB6A',
                     transition: 'background 0.5s ease, transform 0.5s ease',                       
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
        </Typography>
        <Box sx={{ p: 3 }}>
          <Typography>
             {notificationContent}          
          </Typography>
        </Box>
      </Box>

    </Drawer>
    </Box>
    <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              onClick={handleClose}
              sx={{ width: 100 }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => handleClearAllNotifications({ notifications })}
              sx={{  ml: 2, width: 130 }}
            >
              Clear ALL
            </Button>           
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default NotificationDropdown;
