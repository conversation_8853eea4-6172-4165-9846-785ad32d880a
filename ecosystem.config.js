module.exports = {
  apps: [{
    name: 'houzer-app-dev-v4',
    script: 'npm',
    args: 'run start:dev',
    watch: true,
    env: {
      NODE_ENV: 'development',
      PORT: 3019 // Specify the development port here

      // You can add more environment variables here if necessary
    }
  }, {
    name: 'houzer-app-prod',
    script: 'npm',
    args: 'run start:prod',

    // instances: 'max', // for production, you might want to run multiple instances

    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3004 // Specify the production port here

      // You can add more environment variables here if necessary
    }
  }]
};
