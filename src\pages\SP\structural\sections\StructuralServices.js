
import { forwardRef, useState, useEffect } from 'react'
import AccordionBasic from 'src/@core/components/custom-components/AccordionBasic'
import { Box, Button, CardContent, Table, TableBody, TableContainer, TableRow } from '@mui/material'
// ** MUI Imports

import Grid from '@mui/material/Grid'
import { styled, useTheme } from '@mui/material/styles'
import TableCell from '@mui/material/TableCell'
import Typography from '@mui/material/Typography'
//import CardContent from '@mui/material/CardContent'
import Section4 from 'src/pages/SP/structural/sections/Section4'
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const StructuralServices = ({data,expanded}) =>{

  const { can } = useRBAC();
    const theme = useTheme()
    const [state7, setState7] = useState('view')

    const viewClick = () => {
        setState7('edit')
    }

    const editClick = () => {
        setState7('view')
    }

    return(
        <>
        {/* {can('structuralEngineer_serviceDetails_READ') && */}
            <AccordionBasic
                id={'panel-header-1'}
                ariaControls={'panel-content-1'}
                heading={'Service Details'}
                body={
                  <>
                    {state7 === 'view' && (
                        
                          <TableContainer
                            sx={{ padding:'4px 6px' }}
                            className='tableBody'
                           // onClick={can('structuralEngineer_serviceDetails_UPDATE') ? viewClick : null}
                            onClick={viewClick}
                          >
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Area of Expertise:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.areaOfExpertise}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Other Services:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.otherServices}</Typography>
                                  </MUITableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {state7 === 'edit' && <Section4 formData={data} onCancel={editClick} />}
                  </>
                }
                expanded={expanded}
            />
              {/* } */}
        </>
    )
            
}
export default StructuralServices