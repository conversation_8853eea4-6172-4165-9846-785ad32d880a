import React, { useState } from "react";
import Typography from "@mui/material/Typography";
import { useContext, useEffect} from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";
import { AuthContext } from "src/context/AuthContext";


// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableHead,
  TableCell,
} from "@mui/material";
import AwardEdit from "./AwardEdit";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const AwardView = ({ data, expanded, userData }) => {
  const theme = useTheme();
  const [state3, setState3] = useState("view");
   const {
    getAllDocuments,
    allCategories,
    allSubCategories,
    user,

  } = useContext(AuthContext);

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };
  
  const [docs,setDocs] = useState([]);
  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };
  const userUniqueId =
      userData && userData.id !== undefined ? userData.id : user.id;
  const documentJson = {
    userId: userUniqueId,
    documentCategory: getDocumentCategoryId("profile"),
    documentSubCategory: getDocumentSubCategoryId("awards"),
  };

  const hasAwards = data && data.awardList && data.awardList.length > 0;
  const fetchAllDocuments = async () => {
    if (allCategories.length > 0 && allSubCategories.length > 0) {
      const allDocs = await getAllDocuments(documentJson);
      setDocs(allDocs);
    }
  };
 
  useEffect(() => {
    fetchAllDocuments();
  }, []);
  const hasDocuments = data && data.documents && data.documents.length > 0;

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Awards"}
        body={
          <>
            {state3 === "view" && (
              <>
                {hasAwards ? (
                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                    onClick={viewClick3}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>Description</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        {data?.awardList?.map((award, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography
                                className="data-field"
                                marginLeft={"18px"}
                              >
                                {award?.name}
                              </Typography>
                            </TableCell>

                            <TableCell>
                              <Typography
                                className="data-field"
                                paddingLeft={"10px"}
                              >
                                {award?.description}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                   <Table>
                   <TableHead>
                        <TableRow>
                          <TableCell>File name</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                    {docs?.data?.map((award, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography className="data-field">
                            {award && award.split("/").pop()}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                    </Table>   

                  </TableContainer>
                ) : (
                  <Typography
                    variant="body1"
                    sx={{ textAlign: "left", mt: 3, cursor: "pointer" }}
                    onClick={viewClick3}
                  >
                    Click here to add Awards and Upload Awards Images
                  </Typography>
                )}

                {hasDocuments && (
                  <TableContainer sx={{ padding: "4px 6px" }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>File Name</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        {data && data?.documents?.map((document, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography
                                className="data-field"
                                marginLeft={"18px"}
                              >
                                {document.split("/").pop()}
                              </Typography>
                            </TableCell>

                            <TableCell>
                              <IconButton
                                onClick={() => {
                                }}
                                color="primary"
                              >
                                <Icon icon="iconamoon:eye" />
                              </IconButton>
                              <IconButton
                                onClick={() => {
                                }}
                                color="error"
                              >
                                <Icon icon="iconamoon:trash" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </>
            )}
            {state3 === "edit" && (
              <AwardEdit
                userData={userData}
                data={data}
                onCancel={editClick3}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};

export default AwardView;
