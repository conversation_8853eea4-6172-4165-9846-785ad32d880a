// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";


// ** Styled Component
import { Box, Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import LookupValuesEdit from "./LookupValuesEdit";



const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const LookupValuesView = ({ data, expanded, fetchUsers }) => {

  // ** Hook
  const theme = useTheme();

  const { listNames } = useContext(AuthContext)

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const listNameObject = data?.listNamesId ? listNames?.find(item => item?.id === data?.listNamesId)?.name : null;

  return (
    <Box>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {" "}
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}> Look up value:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Look up Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {listNameObject}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Active:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.isActive? "Yes" : "No"}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <LookupValuesEdit onCancel={editClick} formData={data} fetchUsers={fetchUsers} />
            )}
        
    </Box>
  );
};
export default LookupValuesView;
