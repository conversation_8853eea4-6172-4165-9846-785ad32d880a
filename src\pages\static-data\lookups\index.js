import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";

import NavTabsStaticLookups from "src/@core/components/custom-components/NavTabsStaticLookups";
import LookupNames from "./LookupNames";
import LookUpValues from "./LookupValues";
import { useRBAC } from "src/pages/permission/RBACContext";
import ServiceTypes from "./ServiceTypes";

const Lookups = () => {
  const { can } = useRBAC();
  if(can('staticDataLookups_READ')){
  return (
    <Card>
      <NavTabsStaticLookups
        tabContent1={
          <>
            <LookupNames/>
          </>
        }
        tabContent2={
          <>
            <LookUpValues/>
          </>
        }
        tabContent3={
          <>
            <ServiceTypes/>
          </>
        }
      />
    </Card>
  );}
  else{
    return null;
  }
};

export default Lookups;
