import { useState, useEffect, useCallback, useRef, useContext } from "react";
import axios from "axios";
import dynamic from "next/dynamic";
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  Button,
  TextField,
  IconButton,
  FormControl,
  FormHelperText,
  FormControlLabel,
  Grid,
  Divider,
  useMediaQuery,
  useTheme,
  Typography,
  InputAdornment,
  TableContainer,
  Table,
  TableBody,
  TableRow,
} from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import PeopleIcon from "@mui/icons-material/People";
import AddIcon from "@mui/icons-material/Add";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import PlaceIcon from "@mui/icons-material/Place";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useForm, Controller } from "react-hook-form";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import Header from "./Header";
import Footer from "./Footer";
import dayjs from "dayjs";
import Icon from "src/@core/components/icon";
import { useRouter } from "next/router";
import toast, { Toaster } from "react-hot-toast";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
import "react-quill/dist/quill.snow.css";
import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";

const capitalize = (string) =>
  string && string[0].toUpperCase() + string.slice(1);

// Function to remove <p> and </p> tags
function removeHtmlTags(description) {
  return description.replace(/<\/?p>/g, "");
}

const defaultState = {
  url: "",
  title: "",
  allDay: true,
  description: "",
  endDate: new Date(),
  startDate: new Date(),
  attendees: [],
  startTime: "00:00",
  endTime: "23:59",
  inPersonEvent: false,
  googleMeet: false,
  searchForRoom: "",
};

const AddEventRightBar = (props) => {
  const [attendeesId, setAttendeesId] = useState([]);
  const [attendeesData, setListOfAttendees] = useState([]);
  const [listAttendeesOptions, setListAttendeesOptions] = useState([]);
  const allDayRef = useRef(false);

  const [allDay, setAllDay] = useState(false);
  const [inPersonEvent, setInPersonEvent] = useState(false);

  const { user } = useContext(AuthContext);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down(375));
  const isExtraSmallScreen = useMediaQuery(theme.breakpoints.down(320));

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=USERS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfAttendees(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!attendeesData) {
      let data = [];
      attendeesData.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListAttendeesOptions(data);
    }
  }, [attendeesData]);

  const {
    store,
    dispatch,
    addEvent,
    updateEvent,
    calendarApi,
    deleteEvent,
    handleSelectEvent,
    addEventSidebarOpen,
    handleAddEventSidebarToggle,
    fetchEventById,
  } = props;

  const [values, setValues] = useState(defaultState);

  const today = dayjs().format("YYYY-MM-DD"); // Get today's date in 'YYYY-MM-DD' format
  const [startDate, setStartDate] = useState(today);
  const [startTime, setStartTime] = useState("00:00");

  const {
    control,
    setValue,
    clearErrors,
    handleSubmit,
    getValues,
    reset,
    setError,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: "",
      url: "",
      title: "",
      allDay: true,
      description: "",
      endDate: new Date(),
      startDate: new Date(),
      attendees: [],
      startTime: "00:00",
      endTime: "23:59",
      inPersonEvent: false,
      googleMeet: false,
      searchForRoom: "",
    },
  });

  const [eventData, setEventData] = useState({});

  const handleDialogClose = async () => {
    clearErrors();
    reset();
    setAttendeesId([]);
    setStartDate(today); // Reset start date to today's date
    setStartTime("00:00"); // Reset start time to the default time
    dispatch(handleSelectEvent(null));
    handleAddEventSidebarToggle();
  };

  const [usedLinks, setUsedLinks] = useState([]); // State to store used links

  const formatPayload = (data) => {
    return {
      id: store.selectedEvent ? store.selectedEvent.id : "",
      url: data?.url,
      searchForRoom: data?.searchForRoom,
      title: data?.title,
      startDate: startDate,
      endDate: data?.endDate,
      startTime: startTime,
      endTime:
        data?.endTime && data.endTime.trim() !== "" ? data.endTime : "23:59",
      allDay: allDay,
      isActive: true,
      inPersonEvent: inPersonEvent,
      googleMeet: data?.googleMeet,
      googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
      attendees: attendeesId.map((attendee) => attendee.value),
      description: removeHtmlTags(data?.description),
    };
  };

  async function onSubmit(data) {
    if (usedLinks.includes(data.url)) {
      setError("url", {
        type: "manual",
        message:
          "This Google Meet link has already been used. Please use a different link.",
      });
      return;
    }

    data.allDay = allDayRef.current;
    const createPayload = formatPayload(data);

    try {
      if (store.selectedEvent !== null && store.selectedEvent.title.length) {
        dispatch(updateEvent(createPayload));
      } else {
        dispatch(addEvent(createPayload));
        setUsedLinks([...usedLinks, data.url]); // Update the used links state
      }

      handleDialogClose();
    } catch (error) {
      console.error("Error saving event:", error);
    }
  }
  const handleDeleteEvent = () => {
    if (store.selectedEvent) {
      dispatch(deleteEvent(store.selectedEvent?._def?.publicId));
    }
    handleDialogClose();
  };

  const handleStartDate = (date) => {
    setValues((prevState) => ({
      ...prevState,
      startDate: date,
      endDate: date && date > prevState.endDate ? date : prevState.endDate,
    }));
  };

  const [url, setUrl] = useState("");

  const resetToStoredValues = useCallback(() => {
    if (store.selectedEvent !== null) {
      const event = store.selectedEvent;

      const date = new Date(store.selectedEvent.start);

      // Extract year, month, and day
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      // Format the date to 'YYYY-MM-DD'
      const formattedDate = `${year}-${month}-${day}`;

      // setValue("startDate", formattedDate);
      setStartDate(formattedDate);

      const time = date.toTimeString().split(" ")[0].slice(0, 5);

      setStartTime(time);

      dispatch(fetchEventById(event?._def?.publicId))
        .then((eventDataFetched) => {
          setEventData(eventDataFetched?.payload);
          setValue("id", eventDataFetched?.payload?.id || "");
          setValue("url", eventDataFetched?.payload?.url || "");
          setUrl(eventDataFetched?.payload?.url);
          setValue(
            "searchForRoom",
            eventDataFetched?.payload?.extendedProps?.searchForRoom || ""
          );
          setValue("title", eventDataFetched?.payload?.title || "");
          // setStartDate(eventDataFetched?.payload?.startDate);
          // setStartTime( eventDataFetched?.payload?.startTime);
          setValue("endDate", eventDataFetched?.payload?.endDate || "");
          setValue("endTime", eventDataFetched?.payload?.endTime || "");
          setAllDay(eventDataFetched?.payload?.allDay);
          setInPersonEvent(
            eventDataFetched?.payload?.extendedProps?.inPersonEvent
          );
          setValue(
            "googleMeet",
            eventDataFetched?.payload?.extendedProps?.googleMeet || false
          );
          setValue(
            "googleMeetOrLocationUrl",
            eventDataFetched?.payload?.extendedProps?.googleMeetOrLocationUrl ||
              ""
          );
          setValue("isActive", eventDataFetched?.payload?.isActive || false);

          //  eventDataFetched?.payload?.extendedProps?.attendees
          // check above ids in this listAttendeesOptions and form array --
          const attendeesArray =
            eventDataFetched?.payload?.extendedProps?.attendees || [];

          const matchedAttendees = attendeesArray
            .map((attendeeId) => {
              const matchedAttendee = listAttendeesOptions.find(
                (option) => option.value === attendeeId
              );
              return matchedAttendee
                ? { value: matchedAttendee.value, key: matchedAttendee.key }
                : null;
            })
            .filter((attendee) => attendee !== null);

          setAttendeesId(matchedAttendees || []);
          setValue(
            "description",
            eventDataFetched?.payload?.extendedProps?.description || ""
          );
        })
        .catch((error) => {
          console.error("Error fetching event by ID:", error);
        });
    }
  }, [setValue, store.selectedEvent, dispatch]);

  const resetToEmptyValues = useCallback(() => {
    reset();
    setAttendeesId([]);
    setStartDate(today); // Reset start date to today's date
    setStartTime("00:00"); // Reset start time to the default time
  }, [reset, today]);

  const handleStartDateChange = (date) => {
    setStartDate(date);
  };

  const handleGoogleMeet = () => {
    const url = "https://meet.google.com/landing";
    window.open(url, "_blank");
  };

  const handleCopyClick = () => {
    const url = getValues("url");
    const pattern = /^(https:\/\/)?meet.google.com\/[a-zA-Z0-9-]+$/;

    if (toastDisplay !== null) {
      toast.dismiss(toastDisplay);
    }

    if (!url) {
      setToastDisplay(toast.error("Please enter a URL."));
    } else if (!pattern.test(url)) {
      setToastDisplay(toast.error("Please enter a correct Google Meet link."));
    } else {
      navigator.clipboard
        .writeText(url)
        .then(() => {
          setToastDisplay(toast.success("Copied to clipboard"));
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
          setToastDisplay(toast.error("Failed to copy"));
        });
    }
  };

  const [toastDisplay, setToastDisplay] = useState(null);
  const handleIconHover = () => {
    if (toastDisplay !== null) {
      toast.dismiss(toastDisplay);
    }

    setToastDisplay(
      toast(
        "Click on the icon to navigate to Google Meet.\n1. Click on 'New Meeting'.\n2. Select 'Create a meeting for later'.\n3. Copy the generated link.\n4. Paste it in the field.",
        {
          autoClose: false,
        }
      )
    );
  };

  useEffect(() => {
    if (store.selectedEvent !== null) {
      resetToStoredValues();
    } else {
      resetToEmptyValues();
    }
  }, [
    addEventSidebarOpen,
    resetToStoredValues,
    resetToEmptyValues,
    store.selectedEvent,
  ]);

  const handleJoinClick = () => {
    if (url) {
      // Open the URL in a new tab
      window.open(url, "_blank");
    } else {
      // Handle the case when the URL is not available
      alert("Please enter a valid URL");
    }
  };

  return (
    <>
      <Dialog
        fullWidth
        open={addEventSidebarOpen}
        onClose={handleDialogClose}
        keepMounted
        PaperProps={{
          sx: {
            top: 0,
            marginTop: 0,
            width: "1000px",
            height: "850px",
            display: "flex",
            flexDirection: "column",
            maxWidth: "none",
          },
        }}
      >
        <DialogTitle>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "5px",
            }}
          >
            <Header
              title={
                store.selectedEvent !== null &&
                store.selectedEvent.title.length ? (
                  <Typography
                    sx={{
                      fontSize: {
                        md: "1.125rem !important", // Font size for extra-small screens
                        sm: "inherit", // Inherit font size for small screens and up
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.9rem !important", // Specific font size for max-width 320px
                      },
                    }}
                  >
                    Update Event
                  </Typography>
                ) : (
                  <Typography
                    sx={{
                      fontSize: {
                        md: "1.125rem !important", // Font size for extra-small screens
                        sm: "inherit", // Inherit font size for small screens and up
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.9rem !important", // Specific font size for max-width 320px
                      },
                    }}
                  >
                    Add Event
                  </Typography>
                )
              }
              onClose={handleDialogClose}
              showCloseButton={false}
            />
            <IconButton sx={{ display: "none !important" }}></IconButton>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                "@media (max-width:320px)": {
                  gap: "4px !important", // Override gap for max-width 320px
                },
              }}
            >
              {store.selectedEvent !== null &&
                store.selectedEvent.title.length > 0 && (
                  <Button
                    variant="contained"
                    color="primary"
                    sx={{
                      fontSize: {
                        xs: "0.7rem !important",
                        lg: "0.873rem !important",
                      },
                      padding: {
                        xs: "5px 11px !important",
                        sm: "6px 13px !important",
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.6rem !important",
                        padding: "4px 7px !important",
                      },
                    }}
                    onClick={handleJoinClick}
                  >
                    Join
                  </Button>
                )}
              <Button
                variant="outlined"
                onClick={handleDialogClose}
                sx={{
                  color: "black !important",
                  fontSize: {
                    xs: "0.7rem !important",
                    lg: "0.873rem !important",
                  },
                  padding: {
                    xs: "5px 11px !important",
                    sm: "6px 13px !important",
                  },
                  "@media (max-width:320px)": {
                    fontSize: "0.6rem !important",
                    padding: "4px 5px !important",
                  },
                }}
              >
                Close
              </Button>
            </Box>
          </div>
          <Divider sx={{ border: "1px solid black" }} />
        </DialogTitle>
        <DialogContent sx={{ flex: "1 1 auto", overflowY: "auto" }}>
          {user?.id === eventData?.createdBy ? (
            <DatePickerWrapper>
              <Grid container spacing={5}>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                    error={Boolean(errors.attendeesId)}
                  />
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <Icon icon="pepicons-pencil:pen" />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <Controller
                        name="title"
                        control={control}
                        rules={{
                          required: true,
                          pattern: /^[a-zA-Z0-9]+(\s?[a-zA-Z0-9]+)*$/,
                          message: "Special characters are not allowed",
                        }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Title"
                            value={value}
                            onChange={(e) => {
                              if (e.target.value.length <= 255) {
                                onChange(e);
                              }
                            }}
                            error={Boolean(errors.title) || value.length > 255}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            InputProps={{
                              readOnly:
                                store.selectedEvent !== null && eventData,
                              maxLength: 255,
                            }}
                            helperText={
                              value.length > 255
                                ? "Character limit exceeded! Please enter 255 characters or less."
                                : `${value.length} / 255 characters`
                            }
                          />
                        )}
                      />
                      {errors.title && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-title-error"
                        >
                          {errors.title.type === "pattern"
                            ? "Special characters and multiple whitespace are not allowed"
                            : "This field is required"}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <PeopleIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <MultiSelectAutoComplete
                      id="attendeesId"
                      label="Add required attendees"
                      nameArray={listAttendeesOptions}
                      value={attendeesId}
                      onChange={(e) => {
                        setAttendeesId(e.target.value);
                      }}
                      error={Boolean(errors.attendeesId)}
                    />
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={1}
                    md={1}
                    lg={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <AccessTimeIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                        md: "0 !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                      <Controller
                        name="startDate"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <TextField
                            label="start date"
                            type="date"
                            inputProps={{
                              min: startDate,
                            }}
                            value={startDate}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleStartDateChange(e.target.value);
                            }}
                            error={Boolean(errors.start)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          />
                        )}
                      />
                      {errors.start && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-start-date-time-error"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        lg: "25px",
                        md: "25px !important",
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {!allDay && (
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="startTime"
                          control={control}
                          rules={{ required: "Start Time is required" }}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              size="small"
                              label="Start Time"
                              type="time"
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.startTime)}
                              value={startTime}
                              helperText={errors.startTime?.message}
                              aria-describedby="startTime"
                              onChange={(e) => {
                                field.onChange(e); // This is important to update the form state
                                setStartTime(e.target.value); // Update your state
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={3}
                    md={2}
                    lg={3}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "0px !important",
                        lg: "1rem !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="All Day"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={allDay}
                            onChange={(e) => {
                              setAllDay(e.target.checked);
                            }}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  <Grid
                    container
                    item
                    lg={8}
                    md={8}
                    sm={10}
                    xs={12}
                    spacing={2}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "1rem !important",
                        md: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <Grid
                      item
                      xs={12}
                      sm={4.7}
                      md={6}
                      sx={{ paddingLeft: { sm: "0px !important" } }}
                    >
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="endDate"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <TextField
                              label="end date"
                              type="date"
                              value={field.value}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                              }}
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.end)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                              inputProps={{
                                min: startDate, // Minimum selectable date
                              }}
                            />
                          )}
                        />
                        {errors.end && (
                          <FormHelperText
                            sx={{ color: "error.main" }}
                            id="event-end-date-error"
                          >
                            This field is required
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      xs={12}
                      sm={4.9}
                      md={6}
                      sx={{
                        paddingLeft: {
                          lg: "25px",
                          md: "25px !important",
                          xs: "0.5rem !important",
                        },
                      }}
                    >
                      {!allDay && (
                        <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                          <Controller
                            name="endTime"
                            control={control}
                            defaultValue=""
                            rules={{
                              required: "End Time is required",
                              validate: (value) => {
                                const startTimeValue = getValues("startTime");
                                if (startTimeValue && value) {
                                  const startTime = new Date(
                                    `1970-01-01T${startTimeValue}:00`
                                  );
                                  const endTime = new Date(
                                    `1970-01-01T${value}:00`
                                  );
                                  if (endTime < startTime) {
                                    return "End Time cannot be before Start Time";
                                  }
                                }
                                return true;
                              },
                            }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                size="small"
                                label="End Time"
                                type="time"
                                InputLabelProps={{ shrink: true }}
                                error={Boolean(errors.endTime)}
                                helperText={errors.endTime?.message}
                                aria-describedby="endTime"
                                InputProps={{
                                  readOnly:
                                    store.selectedEvent !== null && eventData,
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      )}
                    </Grid>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2}>
                  <Grid
                    item
                    xs={1}
                    sx={{ paddingLeft: { md: "1.5rem !important" } }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          md: "block !important",
                        },
                      }}
                    >
                      <PlaceIcon />
                    </IconButton>
                  </Grid>
                  <Grid item xs={11}>
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="In-person Meet"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={inPersonEvent}
                            onChange={(e) => {
                              setInPersonEvent(e.target.checked);
                            }}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{
                      paddingLeft: { xs: "25px", md: "0rem !important" },
                      paddingTop: { xs: "0rem !important" },
                    }}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="searchForRoom"
                        control={control}
                        // rules={{ required: true }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="search for room"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.searchForRoom)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          />
                        )}
                      />
                      {errors.searchForRoom && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-search-for-room"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                    mt={1.5}
                    mb={1.5}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="url"
                        control={control}
                        rules={{
                          required: true,
                          pattern: {
                            value:
                              /^(https:\/\/)?meet.google.com\/[a-zA-Z0-9-]+$/,
                            message: "Invalid Google Meet link",
                          },
                          validate: (value) =>
                            !usedLinks.includes(value) ||
                            "This Google Meet link has already been used. Please use a different link.",
                        }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Meeting Link"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.url)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            placeholder="Click on the Google-meet icon to navigate & paste the url here"
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "0.5rem",
                                    }}
                                  >
                                    <Tooltip title="Copy" arrow>
                                      <Icon
                                        onClick={() => handleCopyClick(value)}
                                        cursor="pointer"
                                        icon="ph:copy"
                                        width="30"
                                        height="30"
                                        mr={1}
                                      />
                                    </Tooltip>
                                    <Icon
                                      onClick={handleGoogleMeet}
                                      onMouseOver={handleIconHover}
                                      cursor="pointer"
                                      icon="logos:google-meet"
                                    />
                                  </Box>
                                  <Toaster position="top-right" />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                      {errors.url && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-url-error"
                        >
                          {errors.url.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 4 }}>
                      <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                          <ReactQuill
                            {...field}
                            theme="snow"
                            modules={{
                              toolbar: [
                                [
                                  { header: "1" },
                                  { header: "2" },
                                  { font: [] },
                                ],
                                [{ size: [] }],
                                [
                                  "bold",
                                  "italic",
                                  "underline",
                                  "strike",
                                  "blockquote",
                                ],
                                [
                                  { list: "ordered" },
                                  { list: "bullet" },
                                  { indent: "-1" },
                                  { indent: "+1" },
                                ],
                                ["clean"],
                              ],
                            }}
                            formats={[
                              "header",
                              "font",
                              "size",
                              "bold",
                              "italic",
                              "underline",
                              "strike",
                              "blockquote",
                              "list",
                              "bullet",
                              "indent",
                              "link",
                              "image",
                            ]}
                            placeholder="Enter description"
                            style={{ height: "200px" }}
                            onChange={(content) => field.onChange(content)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </DatePickerWrapper>
          ) : (
            <DatePickerWrapper>
              <Grid container spacing={5}>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                    error={Boolean(errors.attendeesId)}
                  />
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <Icon icon="pepicons-pencil:pen" />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <Controller
                        name="title"
                        control={control}
                        rules={{
                          required: true,
                          pattern: /^[a-zA-Z0-9]+(\s?[a-zA-Z0-9]+)*$/,
                          message: "Special characters are not allowed",
                        }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Title"
                            value={value}
                            onChange={(e) => {
                              if (e.target.value.length <= 255) {
                                onChange(e);
                              }
                            }}
                            error={Boolean(errors.title) || value.length > 255}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            InputProps={{
                              readOnly:
                                store.selectedEvent !== null && eventData,
                              maxLength: 255,
                            }}
                            helperText={
                              value.length > 255
                                ? "Character limit exceeded! Please enter 255 characters or less."
                                : `${value.length} / 255 characters`
                            }
                          />
                        )}
                      />
                      {errors.title && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-title-error"
                        >
                          {errors.title.type === "pattern"
                            ? "Special characters and multiple whitespace are not allowed"
                            : "This field is required"}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <PeopleIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Grid container item xs={12} sm={6} spacing={2}>
                        <Grid item>
                          <Typography className="data-field">
                            Attendees:
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Typography style={{ fontWeight: "bold" }}>
                            {attendeesId && Array.isArray(attendeesId)
                              ? attendeesId.map((att) => att.key).join(", ")
                              : "No attendees"}
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <MultiSelectAutoComplete
                        id="attendeesId"
                        label="Add required attendees"
                        nameArray={listAttendeesOptions}
                        value={attendeesId}
                        onChange={(e) => {
                          setAttendeesId(e.target.value);
                        }}
                        error={Boolean(errors.attendeesId)}
                      />
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={1}
                    md={1}
                    lg={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <AccessTimeIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                        md: "0 !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                      <Controller
                        name="startDate"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <TextField
                            label="start date"
                            type="date"
                            inputProps={{
                              min: startDate,
                            }}
                            value={startDate}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleStartDateChange(e.target.value);
                            }}
                            InputProps={{
                              readOnly:
                                store.selectedEvent !== null && eventData,
                            }}
                            error={Boolean(errors.start)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          />
                        )}
                      />
                      {errors.start && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-start-date-time-error"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        lg: "25px",
                        md: "25px !important",
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {!allDay && (
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="startTime"
                          control={control}
                          rules={{ required: "Start Time is required" }}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              size="small"
                              label="Start Time"
                              type="time"
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.startTime)}
                              value={startTime}
                              helperText={errors.startTime?.message}
                              aria-describedby="startTime"
                              onChange={(e) => {
                                field.onChange(e); // This is important to update the form state
                                setStartTime(e.target.value); // Update your state
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={3}
                    md={2}
                    lg={3}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "0px !important",
                        lg: "1rem !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="All Day"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={allDay}
                            onChange={(e) => {
                              setAllDay(e.target.checked);
                            }}
                            disabled={store.selectedEvent !== null && eventData}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  <Grid
                    container
                    item
                    lg={8}
                    md={8}
                    sm={10}
                    xs={12}
                    spacing={2}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "1rem !important",
                        md: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <Grid
                      item
                      xs={12}
                      sm={4.7}
                      md={6}
                      sx={{ paddingLeft: { sm: "0px !important" } }}
                    >
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="endDate"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <TextField
                              label="end date"
                              type="date"
                              value={field.value}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                              }}
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.end)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                              InputProps={{
                                readOnly:
                                  store.selectedEvent !== null && eventData,
                              }}
                              inputProps={{
                                min: startDate, // Minimum selectable date
                              }}
                            />
                          )}
                        />
                        {errors.end && (
                          <FormHelperText
                            sx={{ color: "error.main" }}
                            id="event-end-date-error"
                          >
                            This field is required
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      xs={12}
                      sm={4.9}
                      md={6}
                      sx={{
                        paddingLeft: {
                          lg: "25px",
                          md: "25px !important",
                          xs: "0.5rem !important",
                        },
                      }}
                    >
                      {!allDay && (
                        <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                          <Controller
                            name="endTime"
                            control={control}
                            defaultValue=""
                            rules={{
                              required: "End Time is required",
                              validate: (value) => {
                                const startTimeValue = getValues("startTime");
                                if (startTimeValue && value) {
                                  const startTime = new Date(
                                    `1970-01-01T${startTimeValue}:00`
                                  );
                                  const endTime = new Date(
                                    `1970-01-01T${value}:00`
                                  );
                                  if (endTime < startTime) {
                                    return "End Time cannot be before Start Time";
                                  }
                                }
                                return true;
                              },
                            }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                size="small"
                                label="End Time"
                                type="time"
                                InputLabelProps={{ shrink: true }}
                                error={Boolean(errors.endTime)}
                                helperText={errors.endTime?.message}
                                aria-describedby="endTime"
                                InputProps={{
                                  readOnly:
                                    store.selectedEvent !== null && eventData,
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      )}
                    </Grid>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2}>
                  <Grid
                    item
                    xs={1}
                    sx={{ paddingLeft: { md: "1.5rem !important" } }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          md: "block !important",
                        },
                      }}
                    >
                      <PlaceIcon />
                    </IconButton>
                  </Grid>
                  <Grid item xs={11}>
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="In-person Meet"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={inPersonEvent}
                            onChange={(e) => {
                              setInPersonEvent(e.target.checked);
                            }}
                            disabled={store.selectedEvent !== null && eventData}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{
                      paddingLeft: { xs: "25px", md: "0rem !important" },
                      paddingTop: { xs: "0rem !important" },
                    }}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="searchForRoom"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="search for room"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.searchForRoom)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            InputProps={{
                              readOnly:
                                store.selectedEvent !== null && eventData,
                            }}
                          />
                        )}
                      />
                      {errors.searchForRoom && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-search-for-room"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                    mt={1.5}
                    mb={1.5}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="url"
                        control={control}
                        rules={{
                          required: true,
                          pattern: {
                            value:
                              /^(https:\/\/)?meet.google.com\/[a-zA-Z0-9-]+$/,
                            message: "Invalid Google Meet link",
                          },
                          validate: (value) =>
                            !usedLinks.includes(value) ||
                            "This Google Meet link has already been used. Please use a different link.",
                        }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Meeting Link"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.url)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            placeholder="Click on the Google-meet icon to navigate & paste the url here"
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "0.5rem",
                                    }}
                                  >
                                    <Tooltip title="Copy" arrow>
                                      <Icon
                                        onClick={() => handleCopyClick(value)}
                                        cursor="pointer"
                                        icon="ph:copy"
                                        width="30"
                                        height="30"
                                        mr={1}
                                      />
                                    </Tooltip>
                                    <Icon
                                      onClick={handleGoogleMeet}
                                      onMouseOver={handleIconHover}
                                      cursor="pointer"
                                      icon="logos:google-meet"
                                    />
                                  </Box>
                                  <Toaster position="top-right" />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                      {errors.url && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-url-error"
                        >
                          {errors.url.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 4 }}>
                      <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                          <ReactQuill
                            {...field}
                            theme="snow"
                            modules={{
                              toolbar: [
                                [
                                  { header: "1" },
                                  { header: "2" },
                                  { font: [] },
                                ],
                                [{ size: [] }],
                                [
                                  "bold",
                                  "italic",
                                  "underline",
                                  "strike",
                                  "blockquote",
                                ],
                                [
                                  { list: "ordered" },
                                  { list: "bullet" },
                                  { indent: "-1" },
                                  { indent: "+1" },
                                ],
                                ["clean"],
                              ],
                            }}
                            formats={[
                              "header",
                              "font",
                              "size",
                              "bold",
                              "italic",
                              "underline",
                              "strike",
                              "blockquote",
                              "list",
                              "bullet",
                              "indent",
                              "link",
                              "image",
                            ]}
                            placeholder="Enter description"
                            style={{ height: "200px" }}
                            readOnly={store.selectedEvent !== null && eventData}
                            onChange={(content) => field.onChange(content)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </DatePickerWrapper>
          )}
        </DialogContent>

        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          {user?.id === eventData?.createdBy ? (
            <>
              <Button
                type="submit"
                variant="contained"
                onClick={handleSubmit(onSubmit)}
                sx={{
                  fontSize: {
                    xs: "0.7rem !important",
                    lg: "0.873rem !important",
                  },
                  padding: {
                    xs: "5px 11px !important",
                    sm: "6px 13px !important",
                  },
                  "@media (max-width:320px)": {
                    fontSize: "0.6rem !important",
                    padding: "4px 5px !important",
                  },
                }}
              >
                {store.selectedEvent !== null &&
                store.selectedEvent.title.length
                  ? "Update"
                  : "Add"}
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  if (
                    store.selectedEvent !== null &&
                    store.selectedEvent.title.length
                  ) {
                    handleDeleteEvent();
                  } else {
                    resetToEmptyValues();
                  }
                }}
                sx={{
                  fontSize: {
                    xs: "0.7rem !important",
                    lg: "0.873rem !important",
                  },
                  padding: {
                    xs: "5px 11px !important",
                    sm: "6px 13px !important",
                  },
                  "@media (max-width:320px)": {
                    fontSize: "0.6rem !important",
                    padding: "4px 5px !important",
                  },
                }}
              >
                {store.selectedEvent !== null &&
                store.selectedEvent.title.length
                  ? "Delete"
                  : "Reset"}
              </Button>
            </>
          ) : (
            (store.selectedEvent === null ||
              typeof eventData === "undefined") && (
              <>
                <Button
                  type="submit"
                  variant="contained"
                  onClick={handleSubmit(onSubmit)}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                  }}
                >
                  Add
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    resetToEmptyValues();
                  }}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                  }}
                >
                  Reset
                </Button>
              </>
            )
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AddEventRightBar;
