// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import axios from "axios";
import authConfig from "src/configs/auth";
// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { Card, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, FormControlLabel, IconButton, Switch, Table, TableBody, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useAuth } from "src/hooks/useAuth";
import { useContext, useEffect, useState } from "react";

import Icon from "src/@core/components/icon";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";



const SubServicesEdit = ({ onCancel,setData, formData,fetchUsers }) => {


  const [isActive, setIsActive] = useState(formData?.isActive); 
  const [updatedServices, setUpdatedServices] = useState([]);

  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [subService, setSubService] = useState("");
  const [subServices, setSubServices] = useState([]);

  const [subServiceId, setSubServiceId] = useState("");

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  //Hooks
  const auth = useAuth();

  const { listValues } =
      useContext(AuthContext);
  
  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    formState: { errors },
  } = useForm();

  const [listLabels,setListLabels] = useState([])
  const [labelsOptions,setLabelsOptions] = useState([])
  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_LABELS",
    })
      .then((res) => {
        setListLabels(res.data.data);
      })
      .catch((err) => console.log("LIST LABELS error", err));
  },[])

  useEffect(()=>{
    if(!!listLabels) {
      let data = [];
      listLabels.map((entry)=>{
        data.push({value:entry.id, key:entry.name});
      });
      setLabelsOptions(data);
    }
  }, [listLabels])


  const handleOpenDialog = () => {
    setOpenCreateDialog(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };
  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
    setSubServices([]);
    setSubServiceId("")
  };


  

  async function submitCreate(data) {

    let successCount = 0; // Track the number of successful API calls
    const existingSubServices = []; // Array to store names of existing sub-services

    for (const value of subServices) {
        const fields = {
            name: value,
            listNamesId: formData.id
        };


        try {
            const response = await auth.postService(fields);

            if (response) {
                const { id, name, isActive } = response;

                // Update formData.values with the extracted fields
                setData(prevData => ({
                    ...prevData,
                    values: [...prevData.values, { id, name, isActive }]
                }));

                successCount++; // Increment success count for each successful API call
            }else{
              existingSubServices.push(value);
            }
        } catch (error) {
            if (error.response && error.response.status === 400) {
                // If status code is 400, it indicates existing entry
                existingSubServices.push(value); // Add existing sub-service name to the array
            }
        }
    }

    
    let message ;
    if(successCount>0){
      message = `<div><h3>${successCount} sub service(s) added successfully.</h3></div>`;
    }
    
    if (existingSubServices.length > 0) {
      const existingSubServicesStr = existingSubServices.join(", "); // Join existing sub-services with commas
      message += `<div><h3> Failed to add ${existingSubServices.length} sub service(s) <br><bold>${existingSubServicesStr}</bold><br> already exist in the database </h3></div>`;
  }

    // Show the dialog with the combined message
    setDialogMessage(message);
    setOpenDialogContent(true);

    setOpenCreateDialog(false);
    setSubServices([]);
    setSubServiceId("");

    fetchUsers();
}




  async function submit(data) {   
    const fields = {
        id: formData.serviceProfileId,
        isActive: isActive
    }
    try {
      const response = await auth.patchLabel(fields);
    } catch (error) {
      console.error("Master Data Details failed for", value.name, error);
    }

    for (const value of updatedServices) {
      const fields = {
        id: value.id, // Using the id from the value object
        name: value.name, // Using the name from the value object
        isActive: value.isActive, // Using the isActive from the value object
      };
  
      try {
        const response = await auth.patchServicesData(fields);
      } catch (error) {
        console.error("Master Data Details failed for", value.name, error);
      }
    }
    fetchUsers();
    onCancel();
    
  }

  async function handleDeleteSubService(valueId){


    // Find the service to be deleted
    const deletedService = formData.values.find((service) => service.id === valueId);

    setUpdatedServices((prevServices) => [...prevServices, { ...deletedService, isActive: false }]);

    setData((prevData) => {
      const newSubServices = prevData.values.map((subService) => {
        // Check if the current object's id matches the specified valueId
        if (subService.id === valueId) {
          // If matched, set isActive to false
          return { ...subService, isActive: false };
        }
        // If not matched, return the object unchanged
        return subService;
      });
    
      return {
        ...prevData,
        values: newSubServices,
      };
    }); 

    fetchUsers();
   
  };

  const handleSubServiceChange = (valueId, newValue) => { 

     // Update updatedServices array with the changed service
     const updatedServiceIndex = updatedServices.findIndex(
      (service) => service.id === valueId
    );
    if (updatedServiceIndex !== -1) {
      setUpdatedServices((prevServices) => {
        const updatedServicesCopy = [...prevServices];
        updatedServicesCopy[updatedServiceIndex] = {
          ...updatedServicesCopy[updatedServiceIndex],
          name: newValue,
        };
        return updatedServicesCopy;
      });
    } else {
      const serviceToUpdate = formData.values.find(
        (service) => service.id === valueId
      );
      if (serviceToUpdate) {
        setUpdatedServices((prevServices) => [
          ...prevServices,
          { ...serviceToUpdate, name: newValue },
        ]);
      }
    }
    setData((prevData) => {
      const newSubServices = prevData.values.map((subService) => {
        // Check if the current object's id matches the specified valueId
        if (subService.id === valueId) {
          return { ...subService, name: newValue };
        }
        return subService;
      });
    
      return {
        ...prevData,
        values: newSubServices,
      };
    });
  };

  
  const handleAddItem = () => {
    if (subService.trim() !== "") {
      const lowerCaseSubService = subService.toLowerCase(); // Convert to lowercase
      if (
        !subServices.some(
          (type) => type.toLowerCase() === lowerCaseSubService
        )
      ) {
        setSubServices([...subServices, subService]); // Add to the list
      }
      setSubService("");
    }
  };

  const handleDeleteItem = (indexToDelete) => {
    setSubServices(subServices.filter((_, index) => index !== indexToDelete));
  };


  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
        
        
        <Grid item xs={12} sm={6}>
        <Button
        sx={{ width: "240px" }}
        variant="contained"
        onClick={handleOpenDialog}
      >
        Add New Sub Services
      </Button>
        </Grid>
        <Grid item xs={3} sm={6} container justifyContent="flex-end">
      <Controller
        name="isActive" 
        control={control}
        render={() => (
          <FormControlLabel
            control={
              <Switch
                checked={isActive} 
                onChange={handleOnChange}
                name="isActive" 
              />
            }
            label="Is Active"
          />
        )}
      />
    </Grid>   
     
           
    <Grid item xs={12} sm={6}>
      <FormControl fullWidth>
        {formData?.values.map((subService, index) => {
          if (subService.isActive) {
            return (
              <div
                key={index}
                style={{ display: "flex", alignItems: "center" }}
              >
                <TextField
                  key={index}
                  label={`Sub Services`}
                  value={subService.name}
                  onChange={(e) =>
                    handleSubServiceChange(subService.id, e.target.value)
                  }
                  fullWidth
                  margin="normal"
                  inputProps={{ maxLength: 30 }}
                  InputProps={{
                    endAdornment: (
                      <IconButton
                        sx={{ p: 0, width: 26, height: 26 }}
                        size="small"
                        color="error"
                        onClick={() => handleDeleteSubService(subService.id)}
                      >
                        <Icon icon="tabler:trash" />
                      </IconButton>
                    ),
                  }}
                />
              </div>
            );
          }
          return null;
        })}
      </FormControl>
    </Grid>

        <Grid item xs={12}>
          <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit" 
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>
      </Grid>
      <Dialog
            fullWidth
            maxWidth="md"
            scroll="paper"
            open={openCreateDialog}
            onClose={handleCloseCreateDialog}
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.5, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: { xs: "start", md: "center" },
                fontSize: { xs: 24, md: 25 },
                fontWeight: 600,
              }}
            >
              Add {formData?.name} values
              <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseCreateDialog}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                p: (theme) => `${theme.spacing(10, 8)} !important`,
                "@media (max-width:900px)": {
                  p: (theme) => `${theme.spacing(8, 6)} !important`,
                },
              }}
            >
              <Grid container spacing={4} alignItems={"center"}>
                    <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
  <Controller
    name="name"
    control={control}
    rules={{ required: 'Sub Service name is required' }} 
    render={({ field }) => (
      <TextField
        size="small"
        {...field}
        label="Sub Service"
        value={subService}
        onChange={(e) => setSubService(e.target.value)}
        placeholder="Enter sub services value"
        aria-describedby="Section-name"
        inputProps={{ maxLength: 30 }}
        helperText={errors.name?.message || ""}
        error={Boolean(errors.name)}
      />
    )}
  />
</FormControl>

                    </Grid>
                    <Grid item xs={12} sm={2}>
                      <Button
                        variant="contained"
                        onClick={handleSubmit(handleAddItem)}
                        style={{
                          width: "50px",
                          height: "40px",
                        }}
                      >
                        Add
                      </Button>
                    </Grid>
                <Grid item xs={12}>
                  <TableContainer
                    component={Card}
                    sx={{
                      pt: { xs: 2, md: 3 },
                      px: { xs: 2, md: 3 },
                      width: { xs: "100%", md: "50%" },
                    }}
                  >
                    <Table size="small">
                      <TableHead sx={{ whiteSpace: "nowrap" }}>
                        <TableRow>
                          <MUITableCell
                            sx={{ fontWeight: "bold", noWrap: "nowrap" }}
                          >
                            Sub-Service
                          </MUITableCell>
                          <MUITableCell
                            style={{ textAlign: "right" }}
                            sx={{ fontWeight: "bold" }}
                          >
                            Actions
                          </MUITableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {subServices?.map((item, index) => (
                          <TableRow key={index}>
                            <MUITableCell>{item}</MUITableCell>
                            <MUITableCell style={{ textAlign: "right" }}>
                              <IconButton
                                sx={{ p: 0, width: 26, height: 26 }}
                                size="small"
                                color="error"
                                onClick={() => handleDeleteItem(index)}
                              >
                                <Icon icon="tabler:trash" />
                              </IconButton>
                            </MUITableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                display="flex"
                justifyContent="center"
                variant="outlined"
                color="primary"
                onClick={handleCloseCreateDialog}
              >
                Cancel
              </Button>
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={handleSubmit(submitCreate)}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>
          <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
    </Box>
  );
};

export default SubServicesEdit;
