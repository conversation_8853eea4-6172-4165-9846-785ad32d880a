import React, { useContext, useEffect, useState } from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  Chip,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  InputAdornment,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import Icon from "src/@core/components/icon";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "src/hooks/useAuth";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const CreateTask = ({ open, onClose, employeesData, fetchUsers }) => {
  const auth = useAuth();
  const { getAllListValuesByListNameId, user } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setValue,
    control,
    reset,
    formState: { errors },
  } = useForm();

  const [expanded, setExpanded] = useState(true);

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [targetStartDate, setTargetStartDate] = useState(null);
  const [actualStartDate, setActualStartDate] = useState(null);
  const [openDialog,setOpenDialog] = useState(false)

  const [priorityData, setPriorityData] = useState(null);

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (open) {
        // Prevent default browser dialog
        event.preventDefault();
        // Chrome requires returnValue to be set
        event.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [open]);


  const handleCancel = () => {
    const message = `
        <div> 
          <h3> Changes you made may not be saved. Is it okay?</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {
    if(!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.priorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
      
    }
  }, [authConfig]);

  useEffect(() => {
    
  }, []);

  useEffect(() => {
    
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return "";
    return dateString.slice(0, 10); // Trims the string to display only the date part
  };


  const [tags, setTags] = useState([]);
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && inputValue) {
      event.preventDefault();
      if (!tags.includes(inputValue)) {
        setTags([...tags, inputValue]);
        setInputValue("");
      }
    }
  };

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleDelete = (tagToDelete) => () => {
    setTags((tags) => tags.filter((tag) => tag !== tagToDelete));
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3>Task Created Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
        <div> 
          <h3> Failed to Create task. Please try again later.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    let tasksTags;
    if (Array.isArray(tags)) {
      tasksTags = tags.join(",");
    }

    // const currentDate = new Date();
    const currentDate = new Date().toISOString().slice(0, -1);
    const fields = {
      assignedTo: data?.assignedTo,
      additionalDetails: {
        title: data?.title,
        description: data?.description,
        statusId: data?.statusId,
        priorityId: data?.priorityId,
        targetStartDate: data?.targetStartDate,
        targetEndDate: data?.targetEndDate,
        actualStartDate: data?.actualStartDate,
        actualEndDate: data?.actualEndDate,
        estimatedTime: data?.estimatedTime,
        actualEndTime: data?.actualEndTime,
        tags: tasksTags,
        remarksList: [
          {
            userId: user?.id,
            remarks: data?.remarks,
            createdOn: currentDate,
          },
        ],
      },
      statusId: data?.statusId,
    };

    try {
      await auth.taskPost(fields, handleFailure, handleSuccess);
      fetchUsers();
    } catch (error) {
      console.error("Master Data Creation failed:", error);
      handleFailure();
    }

    reset();
    setValue("title","")
    setValue("description","")
    setValue("statusId","")
    setValue("priorityId","")
    setValue("assignedTo","")
    setValue("targetStartDate","")
    setValue("targetEndDate","")
    setValue("actualStartDate","")
    setValue("actualEndDate","")
    setValue("estimatedTime","")
    setValue("actualEndTime","")
    setValue("tags","")
    setValue("remarks","")
    setTags([])
    onClose();
  }

  const currentDate = new Date();
  const formattedDate = `${currentDate.getDate()}-${
    currentDate.getMonth() + 1
  }-${currentDate.getFullYear()}`;

  const handleToggle = (value) => {
    setExpanded(value);
  };
  const handleDialogClose = () => {
    reset();
    onClose(); // Close the dialog directly
  };
  const handleStartDateChange = (date) => {
    setTargetStartDate(date);
  };

  const handleActualStartDateChange = (date) => {
    setActualStartDate(date);
  };

  const handleCancelClose = () => {
    setOpenDialog(false);
  };

  const handleCancelDialog = () => {
    reset();
    setValue("title","")
    setValue("description","")
    setValue("statusId","")
    setValue("priorityId","")
    setValue("assignedTo","")
    setValue("targetStartDate","")
    setTargetStartDate(""),
    setValue("targetEndDate","")
    setValue("actualStartDate","")
    setActualStartDate("")
    setValue("actualEndDate","")
    setValue("estimatedTime","")
    setValue("actualEndTime","")
    setValue("tags","")
    setValue("remarks","")
    setTags([])
    setOpenDialog(false)
    onClose();
  };

  return (
    <>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openDialog}
        onClose={handleCancelClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
          <Button
              variant="contained"
              onClick={handleCancelDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={handleCancelClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        {/* handleDialogClose */}
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start"},
            fontSize: { xs: 19, md: 20  },
          }}
          textAlign={"center"}
        >
          Create a Task
          <Box sx={{ position: "absolute", top: "0px", right: "36px" }}>
          <CloseExpandIcons
                        expanded={expanded}
                        onToggle={handleToggle}
                      />
              </Box>
         
          <Box sx={{ position: "absolute", top: "4px", right: "12px",}}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Task Details"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="title"
                        control={control}
                        rules={{ required: "Title is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Title"
                            placeholder="Title"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.title)}
                            helperText={errors.title?.message}
                            aria-describedby="validation-basic-title"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <FormControl fullWidth>
                      <Controller
                        name="description"
                        control={control}
                        rules={{ required: "Description is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Description"
                            placeholder="Description"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.description)}
                            helperText={errors.description?.message}
                            aria-describedby="validation-basic-description"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.statusId)}>
                      <InputLabel id="statusId-label">Status</InputLabel>
                      <Controller
                        name="statusId"
                        control={control}
                        rules={{ required: "Status is required" }}
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="statusId-label"
                            label="Status"
                            id="statusId"
                            size="small"
                          >
                            {statusData?.map((status) => (
                              <MenuItem key={status.id} value={status.id}>
                                {status.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      {errors.statusId && (
                        <FormHelperText
                          id="validation-statusId"
                          sx={{ color: "error.main" }}
                        >
                          {errors.statusId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.priorityId)}>
                      <InputLabel id="priorityId"> Priority</InputLabel>
                      <Controller
                        name="priorityId"
                        control={control}
                        rules={{ required: "Priority is required" }} // Validation rules if any
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="priorityId-label"
                            label="Priority"
                            id="priorityId"
                            size="small"
                          >
                            {priorityData?.map((status) => (
                              <MenuItem key={status.id} value={status.id}>
                                {status.listValue}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      {errors.priorityId && (
                        <FormHelperText
                          id="validation-priorityId"
                          sx={{ color: "error.main" }}
                        >
                          {errors.priorityId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                      <InputLabel id="assignedTo"> Assigned To</InputLabel>
                      <Controller
                        name="assignedTo"
                        control={control}
                        rules={{ required: "assigned To is required" }} // Validation rules if any
                        render={({ field }) => (
                          <Select
                            {...field}
                            labelId="assignedTo-label"
                            label="assignedTo"
                            id="assignedTo"
                            size="small"
                          >
                            {employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .map((emp) => (
                                <MenuItem key={emp.id} value={emp.id}>
                                  {emp.label}
                                </MenuItem>
                              ))}
                          </Select>
                        )}
                      />
                      {errors.assignedTo && (
                        <FormHelperText
                          id="validation-assignedTo"
                          sx={{ color: "error.main" }}
                        >
                          {errors.assignedTo.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid container item xs={12} sm={3} alignItems="center">
                    <Grid item xs={4}>
                      <Typography style={field}>Created By :</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography className="data-field">
                        {user?.firstName} {user?.lastName}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid container item xs={12} sm={3} alignItems="center">
                    <Grid item xs={4}>
                      <Typography style={field}>Created Date:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography className="data-field">
                        {formattedDate}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Estimated Details"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="targetStartDate"
                        control={control}
                        defaultValue=""
                        rules={{ required: "target start date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Target start date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.targetStartDate)}
                            helperText={errors.targetStartDate?.message}
                            aria-describedby="targetStartDate"
                            value={targetStartDate || field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleStartDateChange(e.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                    </Grid>
                    <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="targetEndDate"
                        control={control}
                        defaultValue=""
                        rules={{ required: "target End date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Target end date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.targetEndDate)}
                            helperText={errors.targetEndDate?.message}
                            aria-describedby="targetEndDate"
                            value={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                            }}
                            inputProps={{
                              min: targetStartDate // Minimum selectable date
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualStartDate"
                        control={control}
                        defaultValue=""
                        rules={{ required: "Actual start date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual start date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualStartDate)}
                            helperText={errors.actualStartDate?.message}
                            aria-describedby="actualStartDate"
                            value={actualStartDate || field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleActualStartDateChange(e.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualEndDate"
                        control={control}
                        defaultValue=""
                        rules={{ required: "Actual End date is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual end date"
                            type="date"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualEndDate)}
                            helperText={errors.actualEndDate?.message}
                            aria-describedby="actualEndDate"
                            value={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                            }}
                            inputProps={{
                              min: actualStartDate // Minimum selectable date
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="estimatedTime"
                        control={control}
                        defaultValue=""
                        rules={{ required: "Estimated Time is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Estimated Time"
                            type="time"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.estimatedTime)}
                            helperText={errors.estimatedTime?.message}
                            aria-describedby="estimatedTime"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="actualEndTime"
                        control={control}
                        defaultValue=""
                        rules={{ required: "Actual End Time is required" }} // Adding required validation rule
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Actual End Time"
                            type="time"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.actualEndTime)}
                            helperText={errors.actualEndTime?.message}
                            aria-describedby="actualEndTime"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Tags"}
            body={
              <>
                <Grid container spacing={5} style={{ marginTop: "0px" }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth variant="outlined" margin="normal">
                      <Controller
                        name="tags"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            size="small"
                            variant="outlined"
                            label={"Tags"}
                            placeholder="Enter text then Hit Enter to add tags..."
                            value={inputValue}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            InputProps={{
                              startAdornment: (
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    gap: "5px",
                                    marginTop:"10px",
                                    maxWidth: "calc(100% - 100px)",
                                  }}
                                >
                                  {tags.map((tag, index) => (
                                    <Chip
                                      key={index}
                                      label={tag}
                                      onDelete={handleDelete(tag)}
                                    />
                                  ))}
                                </Box>
                              ),
                              style: { flexWrap: "wrap", paddingRight: "50px" },
                            }}
                            error={Boolean(errors.tags)}
                            helperText={errors.tags?.message || " "}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          />
          <AccordionBasic
            id={"panel-header-2"}
            ariaControls={"panel-content-2"}
            heading={"Remarks"}
            body={
              <>
                <Grid item xs={12} style={{ marginTop: "20px" }}>
                  <FormControl fullWidth>
                    <Controller
                      name="remarks"
                      control={control}
                      rules={{ required: "Remarks is required" }}
                      //   defaultValue={formData?.awards}
                      render={({ field }) => (
                        <TextField
                          rows={4}
                          multiline
                          {...field}
                          label="Remarks"
                          InputLabelProps={{ shrink: true }}
                          inputProps={{ maxLength: 1000 }}
                          error={Boolean(errors.remarks)}
                          aria-describedby="Section2_remarks"
                        />
                      )}
                    />
                    {errors.remarks && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="Section2_remarks"
                      >
                        This field is required
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </>
            }
            expanded={expanded}
          />
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CreateTask;
