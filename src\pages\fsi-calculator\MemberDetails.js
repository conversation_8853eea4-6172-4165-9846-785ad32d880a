// ** MUI Imports
import Grid from '@mui/material/Grid'
import Select from '@mui/material/Select'
import Switch from '@mui/material/Switch'
import MenuItem from '@mui/material/MenuItem'
import InputLabel from '@mui/material/InputLabel'

import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  TextField,
} from "@mui/material";

const MemberDetails = () => {

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
  } = useForm();

  return (
    <Grid container spacing={5}>
      <Grid container item xs={12} sm={12}  sx={{ marginTop: 10 }}>
        <Grid item xs={5} sm={5}>
        </Grid>
        <Grid item xs={3} sm={3} alignItems="center">         
            Residential
        </Grid>
        <Grid item xs={3} sm={3} alignItems="center">
            Commercial
        </Grid>
      </Grid>
      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
            Expected Rent / Sq.ft:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="expectedRentResidential"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|[1-4][0-9]{2}|500)$/,
              }}

              //defaultValue={formData?.expectedRentResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}

                  //label="Expected Rent / Sq.ft(Residential):"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedRentResidential)}
                  aria-describedby="validation-expectedRentResidential"
                  inputProps={{ min: 1, max: 500 }}
                />
              )}
            />
            {errors.expectedRentResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedRentResidential"
              >
                Expected Rent(Residential) is required
              </FormHelperText>
            )}
            {errors.expectedRentResidential?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedRentResidential"
              >
                Please enter a valid expected rent(Residential)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="expectedRentCommercial"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|[1-4][0-9]{2}|500)$/,
              }}

              //defaultValue={formData?.expectedRentCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedRentCommercial)}
                  aria-describedby="validation-expectedRentCommercial"
                  inputProps={{ min: 1, max: 500 }}
                />
              )}
            />
            {errors.expectedRentCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedRentCommercial"
              >
                Expected Rent(Commercial) is required
              </FormHelperText>
            )}
            {errors.expectedRentCommercial?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedRentCommercial"
              >
                Please enter a valid expected rent(Commercial)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
            Expected Corpus:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="expectedCorpusResidential"
              control={control}
              rules={{
                required: true,
              }}

              // defaultValue={formData?.expectedCorpusResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedCorpusResidential)}
                  aria-describedby="validation-expectedCorpusResidential"
                />
              )}
            />
            {errors.expectedCorpusResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedCorpusResidential"
              >
                Expected Corpus(Residential) is required
              </FormHelperText>
            )}

          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="expectedCorpusCommercial"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.expectedCorpusCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedCorpusCommercial)}
                  aria-describedby="validation-expectedCorpusCommercial"
                />
              )}
            />
            {errors.expectedCorpusCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedCorpusCommercial"
              >
                Expected Corpus(Commercial) is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
            Hardship Allowance:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="hardshipAllowanceResidential"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.hardshipAllowanceResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.hardshipAllowanceResidential)}
                  aria-describedby="validation-hardshipAllowanceResidential"
                />
              )}
            />
            {errors.hardshipAllowanceResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-hardshipAllowanceResidential"
              >
                Hardship Allowance(Residential) is required
              </FormHelperText>
            )}

          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="hardshipAllowanceCommercial"
              control={control}
              rules={{
                required: true,
              }}

              //defaultValue={formData?.hardshipAllowanceCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.hardshipAllowanceCommercial)}
                  aria-describedby="validation-hardshipAllowanceCommercial"
                />
              )}
            />
            {errors.hardshipAllowanceCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-hardshipAllowanceCommercial"
              >
                Hardship Allowance(Commercial) is required
              </FormHelperText>
            )}

          </FormControl>
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
            Expected Extra Carpet Area:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="expectedExtraCarpetAreaResidential"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}

              //defaultValue={formData?.expectedExtraCarpetAreaResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedExtraCarpetAreaResidential)}
                  aria-describedby="validation-expectedExtraCarpetAreaResidential"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.expectedExtraCarpetAreaResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedExtraCarpetAreaResidential"
              >
                Expected Extra Carpet Area(Residential) is required
              </FormHelperText>
            )}
            {errors.expectedExtraCarpetAreaResidential?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedExtraCarpetAreaResidential"
              >
                Please enter a valid expected extra carpet area(Residential)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="expectedExtraCarpetAreaCommercial"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}

              //defaultValue={formData?.expectedExtraCarpetAreaCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.expectedExtraCarpetAreaCommercial)}
                  aria-describedby="validation-expectedExtraCarpetAreaCommercial"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.expectedExtraCarpetAreaCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedExtraCarpetAreaCommercial"
              >
                Expected Extra Carpet Area(Commercial) is required
              </FormHelperText>
            )}
            {errors.expectedExtraCarpetAreaCommercial?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-expectedExtraCarpetAreaCommercial"
              >
                Please enter a valid expected extra carpet area(Commercial)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
      </Grid>

      <Grid container item xs={12} sm={12} alignItems="center" spacing={1}>
        <Grid item xs={4} sm={4} sx={{ marginTop: 5 }}>
            Rent No Of Months:
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3} sx={{ paddingRight: 10 }}>
          <FormControl fullWidth>
            <Controller
              name="rentNoOfMonthsResidential"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}

              //defaultValue={formData?.rentNoOfMonthsResidential}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.rentNoOfMonthsResidential)}
                  aria-describedby="validation-rentNoOfMonthsResidential"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.rentNoOfMonthsResidential?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-rentNoOfMonthsResidential"
              >
                Rent for No Of months(Residential) is required
              </FormHelperText>
            )}
            {errors.rentNoOfMonthsResidential?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-rentNoOfMonthsResidential"
              >
                Please enter a valid Rent for No Of months(Residential)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item flexGrow={1} xs={3} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="rentNoOfMonthsCommercial"
              control={control}
              rules={{
                required: true,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}

              //defaultValue={formData?.rentNoOfMonthsCommercial}
              render={({ field: { value, onChange } }) => (
                <TextField
                  variant='standard'
                  type="number"
                  value={value}
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.rentNoOfMonthsCommercial)}
                  aria-describedby="validation-rentNoOfMonthsCommercial"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.rentNoOfMonthsCommercial?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-rentNoOfMonthsCommercial"
              >
                Rent for No Of months(Commercial) is required
              </FormHelperText>
            )}
            {errors.rentNoOfMonthsCommercial?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-rentNoOfMonthsCommercial"
              >
                Please enter a valid Rent for No Of months(Commercial)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
      </Grid>

    </Grid>
  )
}

export default MemberDetails
