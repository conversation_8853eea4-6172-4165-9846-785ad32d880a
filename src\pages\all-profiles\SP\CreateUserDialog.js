import React, { useState } from 'react';
import CreateUser from './CreateUser'; // Import the CreateUser component

const CreateUserDialog = ({
  searchData,
  page,
  pageSize,
  searchKeyword,
  fetchServiceProviders,
}) => {
  const [openDialog, setOpenDialog] = useState(false);

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  return (
    <>
      <button onClick={handleOpenDialog}>Open Create User Form</button>
      <CreateUser
        openDialog={openDialog}
        searchData={searchData}
        handleDialogClose={handleCloseDialog}
        page={page}
        pageSize={pageSize}
        searchKeyword={searchKeyword}
        fetchServiceProviders={fetchServiceProviders}
      />
    </>
  );
};

export default CreateUserDialog;
