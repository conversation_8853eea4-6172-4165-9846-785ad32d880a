// ** React Imports
import { forwardRef, useState } from 'react'

// ** MUI Imports
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import { useAuth } from 'src/hooks/useAuth'

// ** Third Party Imports
import toast from 'react-hot-toast'
import { useForm, Controller } from 'react-hook-form'
import SelectMultipleBasic from 'src/@core/components/custom-components/SelectMultipleBasic'
import { AuthContext } from 'src/context/AuthContext'



// ** Icon Imports

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false
}

const developerSocietyServices = [
    {
      value:'RE_PROJECTS', 
      name:'RE Projects'
    },
    {
      value:'NCLT',
      name:'Nclt'
    },
    {
      value:'RERA',
      name:'Rera'
    },
    {
      value:'DRT',
      name:'Drt'
    },
    {
      value:'ARBITRATION',
      name:'Arbitration'
    },
    {
      value:'LITIGATION',
      name:'Litigation'
    },
    {
      value:'CRIMINAL',
      name:'Criminal'
    }
  ];

  const homeBuyerServices = [
    {
        value:'Agreements for Sale', 
        name:'Agreements for Sale'
      },
      {
        value:'Registration Services',
        name:'Registration Services'
      }

  ];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const Section3 = ({ onCancel, formData }) => {

  //Hooks
  const {updateEntity} = useAuth(AuthContext);

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();




  async function submit(data) {

    const response = await updateEntity(data,() => {
      console.error("Service Details failed");
    });
    onCancel();
  }



  return (
   
      <CardContent>
       
        <Grid container spacing={5}>
        <Grid item xs={12} sm={6}>
              <SelectMultipleBasic
                register={register}
                id={'developerSocietyServices'}
                label={'Developer/Society Services?'}
                nameArray={developerSocietyServices}
              />
            
            </Grid>

            <Grid item xs={12} sm={6}>
              <SelectMultipleBasic
                register={register}
                id={'homeBuyerServices'}
                label={'Home Buyer Services'}
                nameArray={homeBuyerServices}
              />
            
            </Grid>





            <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name='teamSize'
              control={control}
              rules={{ required: true, pattern: /^(?:[1-9]|[1-9][0-9]|100)$/ }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type='number'
                  value={value}
                  label='Team Size'
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder='Enter team size(1-100)'
                  aria-describedby='validation-teamSize'
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {errors.teamSize?.type === 'required' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                This field is required
              </FormHelperText>
            )}
            {errors.teamSize?.type === 'pattern' && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-teamSize'>
                Please enter a valid Team Size (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

            <Grid item xs={12} >
          <FormControl fullWidth>
            <Controller
              name='Other Refernces'
              control={control}
              rules={{ required: true }}
              defaultValue={formData?.otherRefernces}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label='Other Reference'
                  error={Boolean(errors.otherReferences)}
                  aria-describedby='advocates-validation-basic-otherRefernces'
                />
              )}
            />
            {errors.awards && (
              <FormHelperText sx={{ color: 'error.main' }} id='advocates-validation-basic-otherRefernces'>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
            

            <Grid item xs={12}>
          <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
        </Grid>
       
      </CardContent>
 
  )
}

export default Section3
