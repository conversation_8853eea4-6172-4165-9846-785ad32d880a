// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabContext from '@mui/lab/TabContext'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

const HorizontalTabs = props => {

    const { activeStep, handleStepClick, steps } = props

    return (
        <TabContext value={activeStep}>
            <TabList scrollButtons='auto' variant='scrollable' onChange={handleStepClick} aria-label='horizontal mobile tabs'>
                {steps.map((step, index) => (
                    <Tab key={index} onClick={() => handleStepClick(index)} label={step.title} icon={<Icon icon={step.icon} />} />
                ))}
            </TabList>
        </TabContext>
    )
}

export default HorizontalTabs
