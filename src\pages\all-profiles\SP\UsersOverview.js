import { DataGrid } from "@mui/x-data-grid";
import React, { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import * as XLSX from "xlsx";
import * as FileSaver from "file-saver";
import SearchIcon from "@mui/icons-material/Search";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import ModeEditRoundedIcon from "@mui/icons-material/ModeEditRounded";
import QuestionAnswerOutlinedIcon from "@mui/icons-material/QuestionAnswerOutlined";
import LocationOnTwoToneIcon from "@mui/icons-material/LocationOnTwoTone";
import {
  Autocomplete,
  FormHelperText,
  Button,
  Card,
  CardContent,
  Grid,
  Menu,
  MenuItem,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Radio,
  RadioGroup,
  Typography,
  FormControlLabel,
  Checkbox,
  FormControl,
  TextField,
  InputLabel,
  Select,
  InputAdornment,
  CardHeader,
  Link,
} from "@mui/material";
import { useTheme, useMediaQuery } from '@mui/material';
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Controller, useForm } from "react-hook-form";

import CreateUser from "./CreateUser";
import { Box } from "@mui/system";
import ProfileView from "../../SP/basic-profile/profileView";

import { AuthContext } from "src/context/AuthContext";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import PageHeader from "src/@core/components/page-header";
import CloseExpandIcons from "../../all-profiles-old/CloseExpandIcons";
import MemberShipView from "../../SP/microsite/sections/MemberShipView";
import AwardView from "../../SP/microsite/sections/AwardView";
import TestimonialView from "../../SP/microsite/sections/TestimonialsView";
import EducationalInsightsView from "../../SP/microsite/sections/EducationalInsightsView";
import FieldsView from "../../SP/microsite/sections/FieldsView";
import AreaOfExperties from "../../SP/microsite/sections/AreaOfExpertiseView";
import ServiceTabs from "../../SP/microsite/sections/ServiceTabs";

import LandDetails from "../../CHS/profile/sections/LandDetails";
import FsiDetails from "../../CHS/profile/sections/FsiDetails";
import Requirements from "../../CHS/profile/sections/Requirements";
import ContactsReferences from "../../CHS/profile/sections/ContactsReferences";
import OtherDetails from "../../CHS/profile/sections/OtherDetails";
import SocietyDetails from "../../CHS/profile/sections/SocietyDetails";
import Index from "../../conversations";
import StatisticsParent from "../../statistics";
import AssignmentAndStatus from "../../CHS/profile/sections/AssignmentAndStatus";
import ParentComponent from "src/pages/SP/service-profile";
import AdvancedSearch from "./AdvancedSearch";
import { set } from "nprogress";
//import SelectCategory from "src/@core/components/custom-components/SelectCategory";

const ExcelDownMenu = ({ children }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleExcelClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Tooltip title="Export to Excel">
        <IconButton onClick={handleExcelClick} size="medium">
          <Icon icon="vscode-icons:file-type-excel" fontSize="2.2rem" />
        </IconButton>
      </Tooltip>

      <Menu
        keepMounted
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            marginTop: "4px",
          },
        }}
      >
        {children}
      </Menu>
    </>
  );
};

const UsersOverView = () => {
  const { can, rbacRoles } = useRBAC();
  const {
    micrositeBasicData,
    setMicrositeBasicData,
    micrositeGetEndpoint,
    getBasicProfileData,
    basicProfileGetData,
    fetchUserProjects,
    projectsData,
    getUserBasicProfileData,
    userData,
    entityData,
    getSocietyProfile,
    setBasicProfileAllProfiles,
    user,
    fetchOne,
    sendWatiMessage,
    sendWatiMessages,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);
  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };
  // console.log("micrositeBasicData in useroverview:", micrositeBasicData);

  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [initialRowCount, setInitialRowCount] = useState(null);

  const [employeesData, setEmployeesData] = useState(null);

  const [matchedLocations, setMatchedLocations] = useState([]);
  const [matchedServices, setMatchedServices] = useState([]);
  const [matchedLeadStatus, setMatchedLeadStatus] = useState([]);
  const [matchedLeadPriority, setMatchedLeadPriority] = useState([]);

  const [whatsappOpenDialog, setWhatsappOpenDialog] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [messageStatus, setMessageStatus] = useState(null); // 'success', 'error', or null
  //const [dialogMessage, setDialogMessage] = useState(""); // Message to display in the dialog
  const [selectedTemplateParams, setSelectedTemplateParams] = useState([]);
  const [viewModeOpen, setViewModeOpen] = useState(false);

  const [societyName, setSocietyName] = useState("");
  const [requirementName, setRequirementName] = useState("");

  const [showSocietyName, setShowSocietyName] = useState(false);
  const [showServiceRequirement, setShowServiceRequirement] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [showGoogleFormLink, setShowGoogleFormLink] = useState(false);
  const [showLocation, setShowLocation] = useState(false);

  const [file, setFile] = useState(null);
  const [allServicesList, setAllServicesList] = useState([]);
  const [whatsappNumberUnavailableDialog, setWhatsappNumberUnavailableDialog] =
    useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationDialogMessage, setConfirmationDialogMessage] =
    useState("");
  const [isActivating, setIsActivating] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const [isBulkAction, setIsBulkAction] = useState(false); // New state to track if it's a bulk action
 

  const [radioValue, setRadioValue] = useState("");
  const [activity, setActivity] = useState(false);
  const [numberAvalabilty, setNumberAvalabilty] = useState(false);

  const [location, setLocation] = useState("");
  const [googleFormLink, setGoogleFormLink] = useState("");
  const [showImageUrl, setShowImageUrl] = useState(false);
const [imageUrl, setImageUrl] = useState("");


  const [missingInfoDialogOpen, setMissingInfoDialogOpen] = useState(false);
  const [missingInfoMessage, setMissingInfoMessage] = useState("");


  const handleRadioChange = (event) => {
    setRadioValue(event.target.value);
  };

  const handleWhatsappOptionClick = () => {
    if (!currentRow?.mobileNumber) {
      setWhatsappNumberUnavailableDialog(true);
    } else {
      setIsBulkAction(false); // Individual action
      setWhatsappOpenDialog(true);
    }
    handleCloseMenu();
  };

  const handleOpenConfirmationDialog = (message, isActivating) => {
    setConfirmationDialogMessage(message);
    setIsActivating(isActivating);
    setConfirmationDialogOpen(true);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmStatusChange = async () => {
    setConfirmationDialogOpen(false);
    try {
      if (isActivating) {
        const response = await axios({
          method: "patch",
          url:
            getUrl(authConfig.allProfilesUpdateActiveStatus) +
            `/${currentRow.id}`,
          headers: getAuthorizationHeaders(),
        });
        setDialogMessage("User activated successfully");
      } else {
        const response = await axios({
          method: "delete",
          url:
            getUrl(authConfig.allProfilesUpdateInActiveStatus) +
            `/${currentRow.id}`,
          headers: getAuthorizationHeaders(),
        });
        setDialogMessage("User de-activated successfully");
      }
      fetchServiceProviders(page, pageSize, searchKeyword, searchData); // Refresh data
    } catch (error) {
      console.log("Error changing status", error);
      setDialogMessage(
        isActivating ? "Error activating status" : "Failed to Deactivate Status"
      );
    } finally {
      setDialogOpen(true);
    }
  };

  const handleWhatsappMessagesOptionClick = () => {
    setIsBulkAction(true); // Bulk action
    setWhatsappOpenDialog(true);
    handleCloseMenu();
  };

  const handleCloseDialog = () => {
    setWhatsappOpenDialog(false);
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setSelectedSociety("");
    setRequirementName("");
    setMeasurements("");
    setIsButtonDisabled(true);
    setDialogOpen(false);
    setMessageStatus(null);
    setGoogleFormLink("");
    setLocation("");
    setImageUrl("");
    setShowImageUrl(false);
    setMissingInfoDialogOpen(false);

  };

  const [detailsExpanded, setDetailsExpanded] = useState(false);

  const toggleDetails = () => {
    setDetailsExpanded(!detailsExpanded);
  };

  const [error, setError] = useState(false);

  // Handle template change
  const handleTemplateChange = (event) => {
    const templateName = event.target.value;
    setSelectedTemplateName(templateName);
    setError(templateName === "");

    const selectedTemplate = templates.find(
      (template) => template.name === templateName
    );

    if (selectedTemplate) {
      setSelectedTemplateParams(selectedTemplate.parameters || []);
      setShowSocietyName(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "society_name"
        )
      );
      setShowServiceRequirement(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "service_requirement"
        )
      );
      setShowMeasurements(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "measurements"
        )
      );
      setShowGoogleFormLink(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "slot_url"
        )
      );
      setShowLocation(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "location"
        )
      );
      setShowImageUrl(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "image_url"
        )
      );
    } else {
      setSelectedTemplateParams([]);
      setShowSocietyName(false);
      setShowServiceRequirement(false);
      setShowMeasurements(false);
      setShowGoogleFormLink(false);
      setShowLocation(false);
      setShowImageUrl(false);      
    }

    // Validate button state
    const isAllFilled =
      templateName &&
      (!showSocietyName || selectedSociety) &&
      (!showServiceRequirement || requirementName) &&
      (!showMeasurements || measurements) &&
      (!showGoogleFormLink || googleFormLink) &&
      (!showImageUrl || imageUrl) &&
      (!showLocation || location);
    setIsButtonDisabled(!isAllFilled);

    setViewModeOpen(true);
  };

  const renderViewModeDialog = () => (
    <Dialog
      open={viewModeOpen}
      onClose={() => setViewModeOpen(false)}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "500px",
          maxWidth: "100%",
        },
      }}
    >
      <DialogTitle>Template Parameters</DialogTitle>
      <DialogContent>
        {selectedTemplateParams.length > 0 ? (
          <Box>
            <Typography variant="h6">Parameters:</Typography>
            <ul>
              {selectedTemplateParams.map((param, index) => (
                <li key={index}>
                  <Typography variant="body1">
                    <strong>{param.name}:</strong> {param.value}
                  </Typography>
                </li>
              ))}
            </ul>
          </Box>
        ) : (
          <Typography variant="body1">
            No parameters available for this template.
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setViewModeOpen(false)}>Close</Button>
      </DialogActions>
    </Dialog>
  );

  const getServiceTypeValue = (id) => {
    const service = allServicesList.find((service) => service.id === id);
    return service ? service.listValue : "";
  };

  const [mobileNumberError, setMobileNumberError] = useState(false);

  const sendMessage = async () => {
    setError(!selectedSociety);
    if (!selectedTemplateName || !currentRow.mobileNumber || currentRow.mobileNumber.trim() === "") {
      setDialogMessage("Template or WhatsApp number is missing");
      setMessageStatus("error");
  
      if (!currentRow.mobileNumber || currentRow.mobileNumber.trim() === "") {
        setMobileNumberError(true);
      }
  
      return;
    }
  
    const whatsappNumber = currentRow.mobileNumber;
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    const parameters = [
      { name: "name", value: currentRow.firstName },
      { name: "society_name", value: selectedSociety },
      {
        name: "service_requirement",
        value: getServiceTypeValue(requirementName),
      },
      {
        name: "measurements",
        value: measurements,
      },
      {
        name: "location",
        value: location,
      },
      {
        name: "slot_url",
        value: googleFormLink,
      },
      {
        name: "image_url",
        value: imageUrl,
      }
    ];
  
    sendWatiMessage(
      whatsappNumber,
      templateName,
      broadcastName,
      parameters,
      (error) => {
        setDialogMessage("Error sending message. Please try again.");
        setMessageStatus("error");
      },
      (data) => {
        setDialogMessage(`WhatsApp message successfully delivered to ${currentRow.firstName}`);
        setMessageStatus("success");
      }
    );
  
    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
    setMobileNumberError(false);
  };
  

  const sendMessages = async () => {
    // Check if any selected rows do not have a mobile number
    const rowsWithoutMobileNumbers = selectedRows.filter(row => !row.mobileNumber || row.mobileNumber.trim() === '');

if (rowsWithoutMobileNumbers.length > 0) {
  // Set the missing phone numbers to display in the dialog
  setMissingPhoneNumbers(rowsWithoutMobileNumbers.map(row => row.email || "Unknown"));
  setShowMissingNumbersDialog(true);
  return;
}

  
    // Proceed with sending messages if all rows have mobile numbers
    if (!selectedTemplateName || selectedRows.length === 0) {
      setDialogMessage("Template or WhatsApp number is missing");
      setMessageStatus("error");
      return;
    }
  
    const usersWithoutPhoneNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );
  
    const inActiveRows = selectedRows.filter(
      (row) => row.status === "INACTIVE"
    );
  
    if (usersWithoutPhoneNumbers.length > 0 && inActiveRows.length > 0) {
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }
  
    if (usersWithoutPhoneNumbers > 0) {
      setActivity(true);
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }
  
    sendMessagesToUsersWithNumbers(selectedRows);
  };
  
  const sendMessagesToUsersWithNumbers = async (rows) => {
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    const receivers = rows
      .filter((row) => row.mobileNumber)
      .map((row) => ({
        whatsappNumber: row.mobileNumber,
        customParams: [
          { name: "name", value: row.firstName },
          { name: "society_name", value: selectedSociety },
          {
            name: "service_requirement",
            value: getServiceTypeValue(requirementName),
          },
          {
            name: "measurements",
            value: measurements,
          },
          {
            name: "location",
            value: location,
          },
          {
            name: "slot_url",
            value: googleFormLink,
          },
          {
            name: "image_url",
            value: imageUrl,
          }
        ],
      }));
  
    sendWatiMessages(
      templateName,
      broadcastName,
      receivers,
      (error) => {
        setDialogMessage("Error sending message. Please try again.");
        setMessageStatus("error");
      },
      (data) => {
        setDialogMessage(
          `WhatsApp message successfully delivered to selected recipients`
        );
        setMessageStatus("success");
      }
    );
  
    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
  };
  
  const [missingPhoneNumbers, setMissingPhoneNumbers] = useState([]);
  const [showMissingNumbersDialog, setShowMissingNumbersDialog] = useState(false);

  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.akjOAtxhzjTui78FKtgAWOgziULQsn0FoTnIAshlGTA";
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch(
          "https://live-mt-server.wati.io/321777/api/v1/getMessageTemplates",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();
        const templateData = data?.messageTemplates?.map((template) => ({
          id: template.id,
          name: template.elementName,
          parameters: template.customParams || [{ name: "name", value: "" }],
          status: template.status,
        }));
        const filteredTemplates = templateData.filter(
          (template) => template.status !== "DELETED"
        );

        setTemplates(filteredTemplates);
      } catch (error) {}
    };

    fetchTemplates();
  }, [token]);

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const handleError = (error) => {};

  // Constants
  const columns = [
    {
      field: "name",
      minWidth: 120,
      headerName: "Name",
      flex: 0.32,
      valueGetter: (params) => {
        const { firstName, lastName } = params?.row;
        return lastName ? `${firstName} ${lastName}` : firstName;
      },
    },
    {
      field: "companyName",
      minWidth: 120,
      headerName: "Company Name",
      flex: 0.3,
      valueGetter: (params) => params?.row?.basicProfileData?.companyName,
    },
    {
      field: "email",
      minWidth: 110,
      headerName: "Email",
      flex: 0.37,
      renderCell: (params) => {
        const email = params?.value;

        return email.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 100,
      headerName: "Mobile No",
      flex: 0.15,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber ? (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        ) : (
          <span>{mobileNumber}</span>
        );
      },
    },
    {
      flex: 0.04,
      minWidth: 75,
      field: "isListingEmpanelled",
      headerName: "Listing Empanelled",
      renderCell: (params) => {
        const isListingEmpanelled = params?.value;

        return (
          <Tooltip title={isListingEmpanelled ? "true" : "false"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: isListingEmpanelled ? 20 : 15,
                height: isListingEmpanelled ? 20 : 15,
                m: 5,
              }}
              color={isListingEmpanelled ? "success" : "error"}
            >
              <Icon
                icon={
                  isListingEmpanelled
                    ? "emojione-v1:left-check-mark"
                    : "twemoji:cross-mark"
                }
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.04,
      minWidth: 75,
      field: "isMicroSiteEmpanelled",
      headerName: "Microsite Empanelled",
      renderCell: (params) => {
        const isMicroSiteEmpanelled = params?.value;

        return (
          <Tooltip title={isMicroSiteEmpanelled ? "true" : "false"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: isMicroSiteEmpanelled ? 20 : 15,
                height: isMicroSiteEmpanelled ? 20 : 15,
                m: 5,
              }}
              color={isMicroSiteEmpanelled ? "success" : "error"}
            >
              <Icon
                icon={
                  isMicroSiteEmpanelled
                    ? "emojione-v1:left-check-mark"
                    : "twemoji:cross-mark"
                }
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.15,
      minWidth: 80,
      field: "assignedTo",
      headerName: "Assigned to",
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      field: "status",
      minWidth: 80,
      headerName: "status",
      flex: 0.12,
      renderCell: (params) => {
        const status = params?.row.status;
        const currentTime = new Date();
        const formattedTime = currentTime.toLocaleString();

        return (
          <Tooltip title={status === "ACTIVE" ? "ACTIVE" : "INACTIVE"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: status === "ACTIVE" ? 13 : 13,
                height: status === "ACTIVE" ? 13 : 13,
                m: 5,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              color={status === "ACTIVE" ? "success" : "error"}
            >
              <Icon
                icon={
                  status === "ACTIVE"
                    ? "fluent-emoji-flat:green-circle"
                    : "fluent-emoji-flat:red-circle"
                }
                style={{ width: 15, height: 15 }}
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.11,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setBasicProfileAllProfiles(params.row.id);
        };

        const onEditClick = () => {
          openEditDialog();
          handleCloseMenu();
        };

        const onConversationsClick = () => {
          openConversationDialog();
          handleCloseMenu();
        };

        const handleActivateStatus = () => {
          handleOpenConfirmationDialog(
            "Are you sure you want to activate this user?",
            true
          );
          handleCloseMenuItems();
        };

        const handleDeactivateStatus = () => {
          handleOpenConfirmationDialog(
            "Are you sure you want to de-activate this user?",
            false
          );
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 30,
                  height: 30,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onEditClick}>
                <ModeEditRoundedIcon style={{ marginRight: 8 }} />
                Edit
              </MenuItem>
              {currentRow?.userCategory !== authConfig?.userCategorySociety && (
                <MenuItem onClick={onConversationsClick}>
                  <QuestionAnswerOutlinedIcon style={{ marginRight: 8 }} />
                  New Conversation
                </MenuItem>
              )}
              <MenuItem onClick={handleWhatsappOptionClick}>
                <WhatsAppIcon style={{ marginRight: 8 }} />
                Send WhatsApp message
              </MenuItem>
              {currentRow?.status === "INACTIVE" && (
                <MenuItem onClick={() => handleActivateStatus()}>
                  {" "}
                  <Icon
                    icon="mdi:account-reactivate"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  Activate
                </MenuItem>
              )}
              {currentRow?.status === "ACTIVE" && (
                <MenuItem onClick={() => handleDeactivateStatus()}>
                  <Icon
                    icon="fluent:person-delete-20-filled"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  De-activate
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];
  // Use States
  const {
    register,
    handleSubmit,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();
const theme = useTheme();
const isSmallScreen = useMediaQuery(theme.breakpoints.down(410));
const isExtraSmallScreen = useMediaQuery(theme.breakpoints.down(365));
  const auth = useAuth();
  const [currentRow, setCurrentRow] = useState(null);
  const [userList, setUserList] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const totalGridHeight = pageSize * 52 + 80;
  const [searchKeyword, setSearchKeyword] = useState("");

  const [searchData, setSearchData] = useState({});

  const [rowCount, setRowCount] = useState(0);
  const [openMenu, setOpenMenu] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expanded, setExpanded] = useState(true);
  const [placeholderText, setPlaceholderText] = useState(
    "Search by Company name"
  );

  const [keyword, setKeyword] = useState("");

  const [selectedSociety, setSelectedSociety] = useState("");
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [measurements, setMeasurements] = useState("");

  const societyOptions = listOfSocieties
    .filter((society) => society?.name)
    .map((society) => ({
      value: society?.name,
      key: society?.name,
    }));

  useEffect(() => {
    const fetchSocieties = async () => {
      try {
        const response = await axios({
          method: "get",
          url:
            getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
          headers: getAuthorizationHeaders(),
        });
        const metadataArray = response.data?.data?.map(
          (item) => item?.metaData
        );
        setListOfSocieties(metadataArray);
      } catch (err) {}
    };
    fetchSocieties();
  }, [openDialog]);

  useEffect(() => {
    const isAllFilled =
      selectedTemplateName &&
      (!showSocietyName || selectedSociety) &&
      (!showServiceRequirement || requirementName) &&
      (!showMeasurements || measurements)&&
      (!showLocation || location) &&
      (!showImageUrl || imageUrl) &&
      (!showGoogleFormLink || googleFormLink);
    setIsButtonDisabled(!isAllFilled);
  }, [
    selectedTemplateName,
    selectedSociety,
    requirementName,
    showSocietyName,
    showServiceRequirement,
    showMeasurements,
    showLocation,
    showGoogleFormLink,
    showImageUrl,
    imageUrl,
  ]);

  const [listValues, setListValues] = useState(null);
  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));

    const phrases = ["Company name", "name", "email", "mobile number"];
    let phraseIndex = 0;
    const intervalId = setInterval(() => {
      phraseIndex = (phraseIndex + 1) % phrases.length;
      setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);
  // HandleChanges
  const handleMenuOpen = (event) => {
    setOpenMenu(event.currentTarget);
  };

  const handleMenuClose = () => {
    setOpenMenu(null);
  };

  const handleOptionSelect = (option) => {
    setOpenMenu(null);
    // setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handleSelectChange = (event) => {
    const value = event.target.value; // Set the selected value for the requirement name
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const navigateToGoogleMaps = () => {
    const url =
      "https://www.google.com/maps/search/?api=1&query=current+location";
    window.open(url, "_blank");
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  useEffect(() => {
    fetchServiceProviders(page, pageSize, searchKeyword, searchData);
  }, [page, pageSize, searchKeyword, searchData]);

  const openConversationDialog = () => {
    setConversationDialogOpen(true);
  };

  const closeConversationDialog = () => {
    setMenu(null);
    setConversationDialogOpen(false);
  };

  const openEditDialog = () => {
    setEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setMenu(null);
    setEditDialogOpen(false);
    setCurrentRow(null);
    setMicrositeBasicData(null);
    setActiveTab(0);
    fetchServiceProviders(page, pageSize, searchKeyword, searchData);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const fetchServiceProviders = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    dataSearch
  ) => {
    setLoading(true);

    const url =
      getUrl(authConfig.getAllProfilesEndpointNewTable) +
      "?profileType=SERVICE_PROVIDER";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      serviceTypeUUIDs: dataSearch?.serviceTypeUUIDs ?? null,
      locationUUIDs: dataSearch?.locationUUIDs ?? null,
      leadStatusUUIDs: dataSearch?.leadStatusUUIDs ?? null,
      leadPriorityUUIDs: dataSearch?.leadPriorityUUIDs ?? null,
      isMicroSiteEmpanelled: dataSearch?.isMicroSiteEmpanelled ?? null,
      isListingEmpanelled: dataSearch?.isListingEmpanelled ?? null,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setUserList(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  useEffect(() => {
    const matchNames = listValues
      ?.filter((item) => searchData?.locationUUIDs?.includes(item.id)) // Filter listValues based on matching ids
      .map((item) => item.name);

    setMatchedLocations(matchNames);

    const matchServices = listValues
      ?.filter((item) => searchData?.serviceTypeUUIDs?.includes(item.id)) // Filter listValues based on matching ids
      .map((item) => item.name);

    setMatchedServices(matchServices);

    const matchStatus = listValues
      ?.filter((item) => searchData?.leadStatusUUIDs?.includes(item.id)) // Filter listValues based on matching ids
      .map((item) => item.name);

    setMatchedLeadStatus(matchStatus);

    const matchPriority = listValues
      ?.filter((item) => searchData?.leadPriorityUUIDs?.includes(item.id)) // Filter listValues based on matching ids
      .map((item) => item.name);

    setMatchedLeadPriority(matchPriority);
  }, [searchData, listValues]);

  const [userId, setUserId] = useState(null);

  useEffect(() => {
    if (!!currentRow && !!currentRow?.basicProfileData) {
      setUserId(currentRow?.id);
    }
    if (!!currentRow && !!currentRow.userCategory) {
      if (currentRow?.userCategory == authConfig?.userCategorySociety) {
        getSocietyProfile(currentRow?.id);
      } else if (
        currentRow.userCategory == authConfig.userCategoryServiceProvider
      ) {
        getBasicProfileData(currentRow?.id);
      }
    }
  }, [currentRow]);

  useEffect(() => {
    if (activeTab === 0) {
      getBasicProfileData(userId);
    } else if (activeTab === 1) {
      micrositeGetEndpoint(userId);
      getBasicProfileData(userId);
      fetchUserProjects(userId);
    }
  }, [activeTab]);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState(columns);
  const [empanelDialog, setEmpanelDialog] = useState(false);

  const [strategicDialog, setStrategicDialog] = useState(false);

  const [strategicDialogOpen, setStrategicDialogOpen] = useState(false);

  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicrositeEmpanelled, setIsMicrositeEmpanelled] = useState(false);

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [entityType, setEntityType] = useState("");
  const [dialogMessage, setDialogMessage] = useState("");
  const [selectedRoleDialog, setSelectedRoleDialog] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [role, setRole] = useState(null);
  const open = Boolean(anchorEl);

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleActionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMessageClose = () => {
    setSubmitSuccess(false);
    setIsListingEmpanelled(false);
    setIsMicrositeEmpanelled(false);
    setPleaseVerifyEmailMessage(false);
  };

  const handleStrategicDialogClose = () => {
    setStrategicDialogOpen(false);
  };

  if (can("allProfiles_READ")) {
    const handleYes = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyEmpanelled = true;
      const successIds = [];
      const failedIds = [];
      const updatePromises = selectedRows.map((row) => {
        if (!row.isListingEmpanelled || !row.isMicrositeEmpanelled) {
          allAlreadyEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: row.isListingEmpanelled
              ? true
              : isListingEmpanelled,
            isMicrositeEmpanelled: row.isMicrositeEmpanelled
              ? true
              : isMicrositeEmpanelled,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyEmpanelled) {
        message += `All Selected Rows are Already Empanelled!`;
      } else if (successFlag) {
        message += `Empanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Empanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchServiceProviders(page, pageSize, searchKeyword, searchData);
    };

    const handleNo = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyUnEmpanelled = true;
      const successIds = [];
      const failedIds = [];

      const updatePromises = selectedRows.map((row) => {
        if (row.isListingEmpanelled || row.isMicrositeEmpanelled) {
          allAlreadyUnEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: isListingEmpanelled
              ? false
              : row.isListingEmpanelled,

            isMicrositeEmpanelled: isMicrositeEmpanelled
              ? false
              : row.isMicrositeEmpanelled,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyUnEmpanelled) {
        message += `All Selected Rows are Already UnEmpanelled!`;
      } else if (successFlag) {
        message += `Unempanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Unempanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchServiceProviders(page, pageSize, searchKeyword, searchData);
    };

    const handleIsEmpanelled = async (selectedRows) => {
      let allRequiredInfoPresent = true;
      const inactiveRows = [];
      const noServicesProvidedRows = [];
    
      selectedRows.forEach((row) => {
        const isActive = row.status === "ACTIVE";
        const servicesProvided = row.basicProfileData?.servicesProvided || [];
        if (!isActive) {
          allRequiredInfoPresent = false;
          inactiveRows.push(row.firstName || row.companyName || "Unknown");
        } else if (servicesProvided.length === 0) {
          allRequiredInfoPresent = false;
          noServicesProvidedRows.push(row.firstName || row.companyName || "Unknown");
        }
      });
    
      if (!allRequiredInfoPresent) {
        const message = `
          <div>
            <h3>Missing Information or Inactive Status</h3>
            <p>The following Service Providers lack required information or are inactive:</p>
            <div>
              <strong>Inactive Service Providers:</strong>
              <div>
                ${inactiveRows.join("<br>")}
              </div>
            </div>
            <div>
              <strong>Service Providers with No Services Provided:</strong>
              <div>
                ${noServicesProvidedRows.join("<br>")}
              </div>
            </div>
            <p>Please ensure that all selected Service Providers have services provided and are active.</p>
          </div>
        `;
        setMissingInfoMessage(message);
        setMissingInfoDialogOpen(true);
        return;
      }
    
      const message = `
        <div>
          <h3>Please select the page(s) to set the status.</h3>
          <p>The status will be applied to the selected row(s).</p>
        </div>
      `;
      setDialogMessage(message);
      setSubmitSuccess(true);
      setEmpanelDialog(true);
    };
    
    

    const handleIsStrategicPartner = async () => {
      const message = `
      <div>
        <h3>Confirm Action</h3>
        <p>Applying "Strategic Partner" status will impact all selected row(s).</p>
      </div>
    `;

      setDialogMessage(message);
      setStrategicDialog(true);
      setStrategicDialogOpen(true);
    };

    const handleAssignStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (!row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: true,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div> <h3>Action Not Required</h3><p>Selected entities are already strategic partners.</p></div>
        `;

        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div><h3>Update Successful</h3><p>Status updated to strategic partner.</p></div>

      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>      
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchServiceProviders(page, pageSize, searchKeyword, searchData);
      }
    };

    const handleRemoveStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: false,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div>
        <h3>Action Not Required</h3>
        <p>No changes were made as the selected entity or entities are not designated as strategic partners.</p>
      </div>`;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div>
        <h3>Update Successful</h3>
        <p>The strategic partner status has been successfully removed.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchServiceProviders(page, pageSize, searchKeyword, searchData);
      }
    };

    const handleSelection = (selectionModel) => {
      // Assuming `userList` is an array of objects and each object has a unique `id` that corresponds to the `selectionModel`
      const selectedData = userList.filter((row) =>
        selectionModel.includes(row.id)
      );
      const selectedRoles = selectedData.map((data) => data.role);
      setRole(selectedRoles);
      setSelectedRows(selectedData);
    };

    const handleSociety = () => {
      if (role.includes("Society")) {
        setDialogMessage(
          "Please deselect Society Profile(s) to Empanel/UnEmpanel Service Provider(s)"
        );
        setSelectedRoleDialog(true);
      }
    };

    const handleClose = () => {
      setPleaseVerifyEmailMessage(false);
      setDisableVerifyEmailButton(false);
      setShowForm(false);
      setShowOTPOptions(false);

      reset({
        firstName: "",
        lastName: "",
        mobileNumber: "",
        email: "",
        entityType: "",
      });
      setEntityType("");
    };

    const exportToCSV = (csvData, fileName) => {
      const message = ` 
      <div>
        <h3>The data have been exported successfully</h3>
      </div>
      `;
      setDialogMessage(message);
      setSubmitSuccess(true);

      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
      const fileExtension = ".xlsx";

      const filteredData = csvData.map((row) =>
        Object.keys(row)
          .filter((key) => selectedColumns.find((col) => col.field === key))
          .reduce((obj, key) => {
            obj[key] = row[key];
            return obj;
          }, {})
      );
      const ws = XLSX.utils.json_to_sheet(filteredData);

      const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const data = new Blob([excelBuffer], { type: fileType });
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");

      const formattedDate = `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;

      const newFileName = `${fileName}_${formattedDate}${fileExtension}`;

      FileSaver.saveAs(data, newFileName);
    };
    const handleColumnSelection = (columnField) => {
      setSelectedColumns((prevState) =>
        prevState.find((col) => col.field === columnField)
          ? prevState.filter((col) => col.field !== columnField)
          : [...prevState, columns.find((col) => col.field === columnField)]
      );
    };

    const handleSocietyDialogClose = () => {
      setSelectedRoleDialog(false);
    };

    const handleOpenDialog = () => {
      setOpenDialog(true);
    };


    return (
      <Grid>
        <Dialog
          open={dialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.dark",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.dark"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>

            <DialogActions>
              <Button
                onClick={handleCloseDialog}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Card>
          <Box
            sx={{
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid
                item
                xs={12}
                sm={2}
                sx={{ textAlign: "flex-start", mt: { xs: 4, lg: 6 } }}
              >
                <Typography variant="h6" fontWeight={"600"}>
                  List of SP's
                </Typography>
              </Grid>

              <Grid item xs={12} sm={10}>
                <Grid container justifyContent="flex-end">
                  <Grid
                    item
                    xs={12}
                    sm="auto"
                    sx={{paddingTop: {xs:'0px',sm:'25px'}, mr: isExtraSmallScreen ? '0' : "8px", textAlign: isExtraSmallScreen ? 'right': 'left'}}
                  >
                    <FormControl>
                      <Controller
                        name="mainSearch"
                        control={control}
                        // defaultValue={name}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            placeholder={placeholderText}
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                                fetchServiceProviders(
                                  page,
                                  pageSize,
                                  searchKeyword,
                                  searchData
                                );
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                height: "40px",
                              },
                              "& .MuiInputBase-input::placeholder": {
                                fontSize: "0.9rem", // Adjust the size as needed
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                      fetchServiceProviders(
                                        page,
                                        pageSize,
                                        searchKeyword,
                                        searchData
                                      );
                                    }}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid
                    item
                    // xs={3}
                    // sm="auto"
                    sx={{paddingTop: {xs:'15px',sm:'25px'}, mr: "6px", ml: "6px",marginTop:{xs:'0.5rem',sm:'0rem'} }}
                  >
                    <AdvancedSearch
                      open={addUserOpen}
                      toggle={toggleAddUserDrawer}
                      searchKeyword={searchKeyword}
                      setSearchKeyword={setSearchKeyword}
                      setSearchData={setSearchData}
                      fetchServiceProviders={fetchServiceProviders}
                      page={page}
                      pageSize={pageSize}
                    />
                  </Grid>
                  <Grid>
                    <Grid sx={{ mr: "8px", paddingTop: "15px" }}>
                      <ExcelDownMenu>
                        {columns.map((column) => (
                          <MenuItem key={column.field}>
                            {column.headerName === "Edit" ? null : (
                              <label>
                                <input
                                  type="checkbox"
                                  checked={
                                    !!selectedColumns.find(
                                      (col) => col.field === column.field
                                    )
                                  }
                                  onChange={() =>
                                    handleColumnSelection(column.field)
                                  }
                                />
                                {column.headerName}
                              </label>
                            )}
                          </MenuItem>
                        ))}
                        <Box sx={{ textAlign: "center", margin: 2 }}>
                          <Button
                            size="medium"
                            type="button"
                            variant="contained"
                            onClick={() => exportToCSV(userList, "users_data")}
                            disabled={selectedColumns.length === 0}
                          >
                            Download
                          </Button>
                        </Box>
                      </ExcelDownMenu>
                    </Grid>
                  </Grid>
                  <Box>
                    <Button
                      size="medium"
                      type="button"
                      variant="contained"
                      onClick={handleActionsClick}
                      sx={{ mr: { xs: isExtraSmallScreen ?'0px' : '15px', lg: "15px" }, mt: 0, mt: 6 ,padding:{  xs: isSmallScreen ? "5px 11px !important" : "0.4375rem 1.1875rem",
                        sm:'0.4375rem 1.1875rem'}}}
                    >
                      Actions
                    </Button>
                    <Menu
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleCloseMenu}
                    >
                      <Tooltip
                        title="Please select the profiles to opt the options"
                        disableHoverListener={selectedRows.length !== 0}
                      >
                        <span>
                          <MenuItem
                            onClick={() => {
                              handleClose();
                              if (role.includes("Society")) {
                                handleSociety();
                              } else {
                                handleIsEmpanelled(selectedRows);
                              }
                            }}
                            disabled={selectedRows.length === 0}
                          >
                            Empanel/UnEmpanel
                          </MenuItem>
                        </span>
                      </Tooltip>
                      <Tooltip
                        title="Please select the profiles to enable the options"
                        disableHoverListener={selectedRows.length !== 0}
                      >
                        <span>
                          <MenuItem
                            onClick={() => {
                              handleClose();
                              handleIsStrategicPartner(selectedRows);
                            }}
                            disabled={selectedRows.length === 0}
                          >
                            Houzer Strategic Partner
                          </MenuItem>
                        </span>
                      </Tooltip>
                      <Tooltip
                        title="Please select the profiles to enable WhatsApp messaging"
                        disableHoverListener={selectedRows.length !== 0}
                      >
                        <span>
                          <MenuItem
                            onClick={handleWhatsappMessagesOptionClick}
                            disabled={selectedRows.length === 0}
                          >
                            Send WhatsApp messages
                          </MenuItem>
                        </span>
                      </Tooltip>
                    </Menu>
                  </Box>
                  <Grid item>
                    <Button
                      aria-controls="simple-menu"
                      aria-haspopup="true"
                      onClick={handleOpenDialog}
                      variant="contained"
                      sx={{
                        mr: 0,
                        marginTop: {
                          xs: isExtraSmallScreen ? "0.3rem !important" : "1.5rem !important",
                          sm: "1.5rem",
                        },
                        ml: 0,
                        padding: {
                          xs: isSmallScreen ? "5px 9px !important" : "0.4375rem 1.1875rem",
                          sm: "0.4375rem 1.1875rem"
                        }
                      }}
                    >
                      Add New SP
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>

          <Menu
            anchorEl={openMenu}
            open={Boolean(openMenu)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => handleOptionSelect("SERVICE_PROVIDER")}>
              Service Provider
            </MenuItem>
            <MenuItem onClick={() => handleOptionSelect("SOCIETY")}>
              Society Member
            </MenuItem>
          </Menu>
          <CardContent>
            <div style={{ fontSize: "13px", marginBottom: "12px" }}>
              {(searchKeyword ||
                searchData?.serviceTypeUUIDs ||
                searchData?.locationUUIDs ||
                searchData?.leadStatusUUIDs ||
                searchData?.leadPriorityUUIDs ||
                searchData?.isMicroSiteEmpanelled ||
                searchData?.isListingEmpanelled) && (
                <>
                  Showing {rowCount} of {initialRowCount} Results
                </>
              )}
              {searchKeyword && ` | results related to : ${searchKeyword}`}

              {searchData?.serviceTypeUUIDs?.length > 0 && (
                <>
                  {" | "}
                  <strong>Selected Services:</strong>{" "}
                  {matchedServices.join(",")}
                </>
              )}
              {searchData?.locationUUIDs?.length > 0 && (
                <>
                  {" | "}
                  <strong>Selected locations:</strong>{" "}
                  {matchedLocations.join(",")}
                </>
              )}
              {searchData?.leadStatusUUIDs?.length > 0 && (
                <>
                  {" | "}
                  <strong>Selected lead Status:</strong>{" "}
                  {matchedLeadStatus.join(",")}
                </>
              )}
              {searchData?.leadPriorityUUIDs?.length > 0 && (
                <>
                  {" | "}
                  <strong>Selected lead priority:</strong>{" "}
                  {matchedLeadPriority.join(",")}
                </>
              )}
              {searchData?.isListingEmpanelled &&
              searchData?.isMicroSiteEmpanelled
                ? `| Filtered who are both listing and microsite empanelled`
                : searchData?.isListingEmpanelled
                ? ` | Filtered who are listing empanelled`
                : searchData?.isMicroSiteEmpanelled
                ? ` | Filtered who are microsite empanelled`
                : ""}
            </div>
            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={userList || []}
                columns={columns}
                getRowId={(row) => row.id}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38}
                components={{
                  NoRowsOverlay: () => (
                    <Typography
                      variant="body1"
                      align="center"
                      sx={{ marginTop: "40px" }}
                    >
                      {userList.length === 0 && searchKeyword
                        ? "No Matches Found"
                        : "No Rows"}
                    </Typography>
                  ),
                }}
              />
            </div>
          </CardContent>
        </Card>

        <CreateUser
          openDialog={openDialog}
          searchData={searchData}
          handleDialogClose={handleDialogClose}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
          fetchServiceProviders={fetchServiceProviders}
          reset={reset}
        />

        <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "row", // Change to row for horizontal alignment
              alignItems: "center",
              justifyContent: "space-between", // Changed to evenly distribute space
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <div style={{ flex: 1, textAlign: "center" }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                sx={{ fontSize: "18px" }}
              >
                Conversation Details
              </Typography>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <CloseExpandIcons
                expanded={expanded}
                onToggle={handleToggle}
                // sx={{ mt: 4 }}
              />
              <IconButton
                size="small"
                onClick={closeConversationDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mt: 5,
                  mb: 3,
                  mr: 2,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </div>
          </DialogTitle>

          <DialogContent>
            <Index
              currentRow={currentRow}
              setCurrentRow={setCurrentRow}
              expanded={expanded}
              closeConversationDialog={close}
              employeeData={employeesData}
            />
          </DialogContent>

          <DialogActions sx={{ justifyContent: "center" }}>
            <Grid item xs={12} sx={{ mt: 2 }}>
              {/* <Button
                size="medium"
                sx={{ mr: 3 }}
                onClick={() => handleClose()}
                variant="outlined"
                color="primary"
              >
                Close
              </Button> */}
            </Grid>
          </DialogActions>
        </Dialog>

        <Dialog
          open={showMissingNumbersDialog}
          onClose={() => setShowMissingNumbersDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle id="alert-dialog-title" style={{ fontWeight: "bold" }}>
            Phone Numbers and Activity Status
          </DialogTitle>
          {!activity && (
            <DialogContent
              dividers
              style={{ overflowY: "auto", maxHeight: 300 }}
            >
              {" "}
              {/* Adjust maxHeight as needed */}
              <DialogContentText id="alert-dialog-description">
                <strong>
                  The following Service Providers do not have phone numbers:
                </strong>
                <ul>
                  {missingPhoneNumbers.map((name, index) => (
                    <li key={index}>{name}</li>
                  ))}
                </ul>
              </DialogContentText>
            </DialogContent>
          )}
          <hr />
          {!numberAvalabilty && (
            <DialogContent>
              {" "}
              {/* This part remains fixed and not scrollable */}
              <DialogContentText style={{ fontWeight: "bold" }}>
                Send message to inactive users as well?
              </DialogContentText>
              <RadioGroup
                aria-label="inactive-users"
                name="inactive-users"
                value={radioValue}
                onChange={handleRadioChange}
              >
                <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                <FormControlLabel value="no" control={<Radio />} label="No" />
              </RadioGroup>
            </DialogContent>
          )}
          <DialogActions>
            <Button onClick={() => setShowMissingNumbersDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowMissingNumbersDialog(false);
                if (radioValue === "no") {
                  sendMessagesToUsersWithNumbers(
                    selectedRows.filter((row) => row.status === "ACTIVE")
                  );
                } else {
                  sendMessagesToUsersWithNumbers(selectedRows);
                }
                setActivity(false);
                setNumberAvalabilty(false);
              }}
              variant="contained"
              color="primary"
              disabled={!radioValue}
            >
              Send Messages
            </Button>
          </DialogActions>
        </Dialog>

<Dialog
  open={showMissingNumbersDialog}
  onClose={() => setShowMissingNumbersDialog(false)}
  aria-labelledby="alert-dialog-title"
  aria-describedby="alert-dialog-description"
  maxWidth="sm"
  fullWidth
>
  <DialogTitle id="alert-dialog-title"  color="primary"
  sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary",
            }}
            >
    Missing Mobile Numbers
  </DialogTitle>
  <DialogContent>
    <DialogContentText id="alert-dialog-description" color="primary">
    The mobile number for the Service Provider with the below email is missing.
    </DialogContentText>
    <ul>
      {missingPhoneNumbers.map((email, index) => (
        <li key={index}>{email}</li>
      ))}
    </ul>
  </DialogContent>
  <DialogActions>
    <Button onClick={() => setShowMissingNumbersDialog(false)} variant="contained" sx={{ margin: "auto", width: 100 }}>OK</Button>
  </DialogActions>
</Dialog>



        <Dialog
          open={whatsappNumberUnavailableDialog}
          onClose={() => setWhatsappNumberUnavailableDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.white,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary",
            }}
          >
            <DialogTitle id="alert-dialog-title" color="primary">
              WhatsApp number unavailable.
            </DialogTitle>
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary"
              >
                Please enter the WhatsApp number.
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{
                display: "flex",
                justifyContent: "center",
                fontWeight: "bold",
              }}
            >
              <Button
                variant="contained"
                onClick={() => setWhatsappNumberUnavailableDialog(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog fullScreen open={isEditDialogOpen} onClose={closeEditDialog}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <Typography sx={{ fontWeight: "bold", mb: 4 }}>
              {currentRow?.userCategory}
            </Typography>
            {currentRow?.userCategory !== authConfig?.userCategorySociety && (
              <>
                <Tabs value={activeTab} onChange={handleTabChange}>
                  <Tab label="Basic Profile" />
                  <Tab label="For Microsite" />
                  <Tab label="Service Profile" />
                </Tabs>
              </>
            )}

            <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>

              <IconButton
                size="small"
                onClick={closeEditDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent>
            {currentRow?.userCategory === authConfig?.userCategorySociety ? (
              <Box>
                <Grid container spacing={2} className="match-height">
                  <Grid item xs={12}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Grid item xs={12} sx={{ position: "relative" }}>
                        <PageHeader
                          title={
                            <Typography variant="h5">
                              Society Profile
                            </Typography>
                          }
                          subtitle={<Typography variant="body2"></Typography>}
                        />
                        <Box sx={{ position: "absolute", top: 1, right: 0 }}>
                          <CloseExpandIcons
                            expanded={expanded}
                            onToggle={handleToggle}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <SocietyDetails
                      data={entityData}
                      userData={currentRow}
                      expanded={expanded}
                      employeesData={employeesData}
                    ></SocietyDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <LandDetails
                      data={entityData}
                      expanded={expanded}
                    ></LandDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <FsiDetails
                      data={entityData}
                      expanded={expanded}
                    ></FsiDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <Requirements
                      data={entityData}
                      expanded={expanded}
                    ></Requirements>
                  </Grid>
                  <Grid item xs={12}>
                    <ContactsReferences
                      data={entityData}
                      expanded={expanded}
                    ></ContactsReferences>
                  </Grid>
                  <Grid item xs={12}>
                    <OtherDetails
                      data={entityData}
                      expanded={expanded}
                    ></OtherDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <AssignmentAndStatus
                      data={entityData}
                      expanded={expanded}
                      employeesData={employeesData}
                    />
                  </Grid>
                </Grid>
              </Box>
            ) : (
              <>
                <Box>
                  {activeTab === 0 && (
                    <Grid>
                      <ProfileView
                        data={basicProfileGetData}
                        userData={currentRow}
                        expanded={expanded}
                        employeesData={employeesData}
                      />
                    </Grid>
                  )}
                  {activeTab === 1 && (
                    <DatePickerWrapper>
                      <Grid container spacing={6} className="match-height">
                        <Grid item xs={12}>
                          <Grid
                            container
                            spacing={2}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <Grid item xs={8}>
                              <PageHeader
                                subtitle={
                                  <Typography variant="body2"></Typography>
                                }
                              />
                            </Grid>
                            <Grid
                              item
                              xs={0.5}
                              sx={{
                                width: "auto",
                                textAlign: "end",
                                justifyItems: "end",
                              }}
                            >
                              <CloseExpandIcons
                                expanded={expanded}
                                onToggle={handleToggle}
                              />
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item xs={12}>
                          <FieldsView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></FieldsView>
                        </Grid>
                        <Grid item xs={12}>
                          <AreaOfExperties
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AreaOfExperties>
                        </Grid>
                        <Grid item xs={12}>
                          <AwardView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AwardView>
                        </Grid>
                        <Grid item xs={12}>
                          <MemberShipView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TestimonialView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></TestimonialView>
                        </Grid>

                        <Grid item xs={12}>
                          <EducationalInsightsView
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></EducationalInsightsView>
                        </Grid>
                        <Grid item xs={12}>
                          <ServiceTabs
                            tabContents={serviceNames}
                            data={projectsData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <StatisticsParent
                            userDataAllProfile={currentRow}
                            expanded={expanded}
                          />
                        </Grid>
                      </Grid>
                    </DatePickerWrapper>
                  )}
                  {activeTab === 2 && (
                    <Grid>
                      <ParentComponent userDataAllProfile={currentRow} />
                    </Grid>
                  )}
                </Box>
              </>
            )}
          </DialogContent>
          <DialogActions>
            {/* <Button onClick={closeEditDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={closeEditDialog} color="primary">
            Save
          </Button> */}
          </DialogActions>
        </Dialog>

        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.dark",
          }}
        >
          <Dialog
            open={confirmationDialogOpen}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            PaperProps={{
              sx: {
                p: (theme) => `${theme.spacing(2.5)} !important`,
                backgroundColor: (theme) => theme.palette.text.primary,
              },
            }}
          >
            <Box
              sx={{
                width: "100%",
                borderRadius: 1,
                textAlign: "center",
                border: (theme) => `1px solid ${theme.palette.divider}`,
                borderColor: "primary.dark",
              }}
            >
              <DialogContent>
                <DialogContentText
                  id="alert-dialog-description"
                  color="primary.dark"
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: confirmationDialogMessage,
                    }}
                  />
                </DialogContentText>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "center",
                }}
              >
                <Button
                  onClick={handleConfirmStatusChange}
                  style={{ margin: "0 10px auto", width: 100 }}
                  variant="contained"
                >
                  Yes
                </Button>
                <Button
                  onClick={handleCloseConfirmationDialog}
                  style={{ margin: "0 10px auto", width: 100 }}
                >
                  No
                </Button>
              </DialogActions>
            </Box>
          </Dialog>
        </Box>

        <Dialog
          open={submitSuccess || pleaseVerifyEmailMessage}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
            },
          }}
          onClose={(event, reason) => {
            if (reason == "backdropClick") {
              handleMessageClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.dark",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.dark"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              {empanelDialog ? (
                <div>
                  <Box
                    sx={{
                      backgroundColor: "white",
                      padding: 2,
                      borderRadius: "8px",
                      mb: { xs: 2, lg: 4 },
                      paddingLeft: 6,
                    }}
                  >
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isListingEmpanelled}
                          onChange={(event) =>
                            setIsListingEmpanelled(event.target.checked)
                          }
                        />
                      }
                      label={
                        <span style={{ color: "black" }}>Listing Page</span>
                      }
                    />

                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isMicrositeEmpanelled}
                          onChange={(event) =>
                            setIsMicrositeEmpanelled(event.target.checked)
                          }
                        />
                      }
                      label={
                        <span style={{ color: "black" }}>Microsite Page</span>
                      }
                    />
                  </Box>
                  <Button
                    variant="contained"
                    onClick={handleYes}
                    sx={{
                      margin: "10px",
                      backgroundColor: "primary.main",
                      "&:disabled": {
                        backgroundColor: "white",
                        color: "grey",
                      },
                    }}
                    disabled={!isListingEmpanelled && !isMicrositeEmpanelled}
                  >
                    Empanel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNo}
                    sx={{
                      margin: "10px",
                      backgroundColor: "primary.main",
                      "&:disabled": {
                        backgroundColor: "white",
                        color: "grey",
                      },
                    }}
                    disabled={!isListingEmpanelled && !isMicrositeEmpanelled}
                  >
                    UnEmpanel
                  </Button>
                </div>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleMessageClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Okay
                </Button>
              )}
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={strategicDialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleStrategicDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.dark",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.dark"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              {strategicDialog ? (
                <div>
                  <Button
                    variant="contained"
                    onClick={handleAssignStrategic}
                    sx={{ margin: "10px" }}
                  >
                    Assign
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleRemoveStrategic}
                    sx={{ margin: "10px" }}
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleStrategicDialogClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Okay
                </Button>
              )}
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={selectedRoleDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleSocietyDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.dark",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.dark"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={handleSocietyDialogClose}
                sx={{
                  margin: "10px",
                  backgroundColor: "primary.main",
                  "&:disabled": {
                    backgroundColor: "white",
                    color: "grey",
                  },
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={whatsappOpenDialog}
          onClose={handleCloseDialog}
          maxWidth="sm"
          fullWidth={!isBulkAction}
          PaperProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              height: isBulkAction ? "50vh" : "60vh",
              width: isBulkAction ? "60vw" : "60vw",
            },
          }}
        >
          <DialogTitle
            sx={{
              position: "sticky",
              top: 0,
              backgroundColor: "inherit",
              zIndex: 1,
            }}
          >
            Send Message via WhatsApp
          </DialogTitle>
          <DialogContent sx={{ flexGrow: 1, overflow: "auto" }}>
  <FormControl fullWidth sx={{ mt: 2 }} error={!errors}>
    <InputLabel id="select-label">Select a Template</InputLabel>
    <Select
      labelId="select-label"
      value={selectedTemplateName}
      onChange={handleTemplateChange}
      label="Select a Template"
      size="small"
    >
      {templates?.map((template) => (
        <MenuItem key={template.id} value={template.name}>
          {template.name}
        </MenuItem>
      ))}
    </Select>
    {!errors && <FormHelperText>This field is required</FormHelperText>}
  </FormControl>

  {!isBulkAction && (
    <Box mt={2}>
      <Typography
        variant="body1"
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          cursor: "pointer",
          marginLeft: "0.1in",
          fontWeight: "bold",
        }}
        onClick={toggleDetails}
      >
        Service provider details:
        {detailsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
      </Typography>
      {!detailsExpanded && (
        <Box mt={2}>
          <Typography variant="body1" sx={{ marginLeft: "0.3in" }}>
            <strong>Name:</strong> {currentRow?.firstName || ""}
          </Typography>
          <Typography
            variant="body1"
            sx={{ marginTop: "10px", marginLeft: "0.3in" }}
          >
            <strong>Services:</strong>
            {currentRow?.basicProfileData?.servicesProvided?.length > 0 ? (
              <ul
                style={{
                  listStyle: "none",
                  padding: 0,
                  marginTop: "10px",
                }}
              >
                {currentRow.basicProfileData.servicesProvided
                  .map((serviceId) => getServiceTypeValue(serviceId))
                  .filter(Boolean)
                  .map((serviceName, index) => (
                    <li key={index} style={{ marginBottom: "5px" }}>
                      {serviceName}
                    </li>
                  ))}
              </ul>
            ) : (
              <span
                style={{ display: "inline-block", marginTop: "10px" }}
              >
                No services provided
              </span>
            )}
          </Typography>
        </Box>
      )}
    </Box>
  )}

  <Box mt={2}>
    {showSocietyName && (
      <FormControl
        fullWidth
        sx={{ mt: 2 }}
        error={Boolean(errors.societyName)}
      >
        <Autocomplete
          id="society-name-autocomplete"
          options={societyOptions}
          getOptionLabel={(option) => option.key}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Select Society"
              size="small"
              error={Boolean(errors.societyName)}
              helperText={errors.societyName?.message}
            />
          )}
          value={
            societyOptions.find(
              (option) => option.value === selectedSociety
            ) || null
          }
          onChange={(event, newValue) => {
            setSelectedSociety(newValue?.value || "");
            setError(newValue?.value.trim() === "");
            const isAllFilled =
              selectedTemplateName &&
              (!showSocietyName || newValue?.value) &&
              (!showServiceRequirement || requirementName) &&
              (!showMeasurements || measurements) &&
              (!showLocation || location) &&
              (!showImageUrl || imageUrl) &&
              (googleFormLink || !showGoogleFormLink);
            setIsButtonDisabled(!isAllFilled);
          }}
        />
        {error && (
          <FormHelperText style={{ color: "red" }}>
            This field is required
          </FormHelperText>
        )}
      </FormControl>
    )}

    {showServiceRequirement && (
      <FormControl
        fullWidth
        error={Boolean(errors.serviceType)}
        sx={{ mt: 2 }}
      >
        <Autocomplete
          id="serviceType"
          options={allServicesList}
          getOptionLabel={(option) => option.listValue}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Service requirement"
              size="small"
              error={Boolean(errors.serviceType)}
              helperText={errors.serviceType?.message}
            />
          )}
          value={
            allServicesList.find(
              (service) => service.id === requirementName
            ) || null
          }
          onChange={(event, newValue) => {
            setRequirementName(newValue?.id || "");
            const isAllFilled =
              selectedTemplateName &&
              (!showSocietyName || selectedSociety) &&
              (!showServiceRequirement || newValue?.id) &&
              (!showMeasurements || measurements) &&
              (!showLocation || location) &&
              (!showImageUrl || imageUrl) &&
              (googleFormLink || !showGoogleFormLink);
            setIsButtonDisabled(!isAllFilled);
          }}
        />
        {error && (
          <FormHelperText style={{ color: "red" }}>
            This field is required
          </FormHelperText>
        )}
      </FormControl>
    )}

    {showMeasurements && (
      <FormControl
        fullWidth
        error={Boolean(errors.measurements)}
        sx={{ mt: 2 }}
      >
        <TextField
          id="measurements"
          label="Measurements"
          size="small"
          value={measurements}
          onChange={(e) => {
            setMeasurements(e.target.value);
            const isAllFilled =
              selectedTemplateName &&
              (!showSocietyName || selectedSociety) &&
              (!showServiceRequirement || requirementName) &&
              (!showMeasurements || e.target.value) &&
              (!showLocation || location) &&
              (!showImageUrl || imageUrl) &&
              (googleFormLink || !showGoogleFormLink);
            setIsButtonDisabled(!isAllFilled);
          }}
          error={Boolean(errors.measurements)}
          helperText={errors.measurements?.message}
        />
        {error && (
          <FormHelperText style={{ color: "red" }}>
            This field is required
          </FormHelperText>
        )}
      </FormControl>
    )}
    {showLocation && (
      <FormControl
        fullWidth
        sx={{ mt: 2 }}
        error={Boolean(errors.location)}
      >
        <Controller
          name="location"
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Society Location"
              InputLabelProps={{ shrink: true }}
              size="small"
              onChange={(e) => {
                setLocation(e.target.value);
                const isAllFilled =
                  selectedTemplateName &&
                  (!showSocietyName || selectedSociety) &&
                  (!showServiceRequirement || requirementName) &&
                  (!showMeasurements || measurements) &&
                  (!showLocation || e.target.value) &&
                  (!showImageUrl || imageUrl) &&
                  (googleFormLink || !showGoogleFormLink);
                setIsButtonDisabled(!isAllFilled);
              }}
              placeholder="Click on the icon to navigate & paste the URL here"
              error={Boolean(errors.location)}
              helperText={errors.location?.message}
              aria-describedby="validation-basic-location"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="Click to navigate to Google Maps">
                      <IconButton
                        sx={{ cursor: "pointer" }}
                        onClick={navigateToGoogleMaps}
                        edge="end"
                      >
                        <LocationOnTwoToneIcon />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </FormControl>
    )}
    {showImageUrl && (
      <FormControl
        fullWidth
        sx={{ mt: 2 }}
        error={Boolean(errors.imageUrl)}
      >
        <TextField
          id="imageUrl"
          label="Image URL"
          size="small"
          InputLabelProps={{ shrink: true }}
          value={imageUrl}
          onChange={(e) => {
            setImageUrl(e.target.value);
            const isAllFilled =
              selectedTemplateName &&
              (!showSocietyName || selectedSociety) &&
              (!showServiceRequirement || requirementName) &&
              (!showMeasurements || measurements) &&
              (!showLocation || location) &&
              (!showImageUrl || e.target.value) &&
              (googleFormLink || !showGoogleFormLink);
            setIsButtonDisabled(!isAllFilled);
          }}
          placeholder="Enter the URL for the image here"
          error={Boolean(errors.imageUrl)}
          helperText={errors.imageUrl?.message}
        />
      </FormControl>
    )}
    {showGoogleFormLink &&(
      <FormControl
        fullWidth
        sx={{ mt: 2 }}
        error={Boolean(errors.googleFormLink)}
      >
        <TextField
          id="googleFormLink"
          label="Google Form Link"
          size="small"
          InputLabelProps={{ shrink: true }}
          value={googleFormLink}
          onChange={(e) => {
            setGoogleFormLink(e.target.value);
            const isAllFilled =
              selectedTemplateName &&
              (!showSocietyName || selectedSociety) &&
              (!showServiceRequirement || requirementName) &&
              (!showMeasurements || measurements) &&
              (!showLocation || location) &&
              (!showImageUrl || imageUrl) &&
              (e.target.value || !showGoogleFormLink);
            setIsButtonDisabled(!isAllFilled);
          }}
          placeholder="Enter the Google Form URL here"
          error={Boolean(errors.googleFormLink)}
          helperText={errors.googleFormLink?.message}
        />
      </FormControl>
    )}
    
  </Box>
  {mobileNumberError && (
      <Typography color="error" variant="body2">
        Mobile number is required.
      </Typography>
    )}
  </DialogContent>

<DialogActions
  sx={{
    position: "sticky",
    bottom: 0,
    backgroundColor: "inherit",
    zIndex: 1,
  }}
>
  <Button onClick={handleCloseDialog}>Cancel</Button>
  {isBulkAction ? (
    <Button
      onClick={sendMessages}
      disabled={isButtonDisabled || selectedRows.length === 0}
      variant="contained"
      color="primary"
    >
      Send Messages
    </Button>
  ) : (
    <Button
      onClick={sendMessage}
      disabled={isButtonDisabled || !currentRow?.mobileNumber}
      variant="contained"
      color="primary"
    >
      Send Message
    </Button>
  )}
</DialogActions>
        </Dialog>

        <Dialog
          open={messageStatus !== null}
          onClose={() => setMessageStatus(null)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
              color: (theme) => theme.palette.text.secondary,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.dark",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.dark"
                fontWeight={"bold"}
              >
                {dialogMessage}
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => {
                  setMessageStatus(null);
                  handleCloseDialog();
                  handleCloseMenu(); // Close WhatsApp dialog as well
                }}
              >
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={missingInfoDialogOpen}
          onClose={() => setMissingInfoDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: missingInfoMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={() => setMissingInfoDialogOpen(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Grid>
    );
  } else {
    return null;
  }
};

export default UsersOverView;
