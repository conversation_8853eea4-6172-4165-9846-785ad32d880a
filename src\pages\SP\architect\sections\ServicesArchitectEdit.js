import { useState, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import {
  Checkbox,
  Divider,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  //   email: "",
  //   lastName: "",
  //   password: "",
  //   firstName: "",
};

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const ServiceArchitectEdit = ({
  onCancel,
  setter,
  defaultData,
  setUnsavedChanges,
}) => {
  const { updateEntityServices } = useContext(AuthContext);

  // ** States
  const [state, setState] = useState({
    password: "",
    showPassword: false,
  });

  // ** Hook
  const {
    control,
    handleSubmit,
    register,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues,
  });
  const [formData, setFormData] = useState(defaultData);

  const [
    projectPlanningAndFeasibilityStudiesSupported,
    setProjectPlanningAndFeasibilityStudiesSupported,
  ] = useState(
    defaultData?.projectPlanningAndFeasibilityStudies?.isSupported || false
  );

  const [
    projectDesignAndDevelopmentSupported,
    setProjectDesignAndDevelopmentSupported,
  ] = useState(defaultData?.projectDesignAndDevelopment?.isSupported || false);

  const [projectSchedulingSupported, setProjectSchedulingSupported] = useState(
    defaultData?.projectScheduling?.isSupported || false
  );

  const [
    costEstimationAndBudgetingSupported,
    setCostEstimationAndBudgetingSupported,
  ] = useState(defaultData?.costEstimationAndBudgeting?.isSupported || false);

  const [
    qualityControlAndAssuranceSupported,
    setQualityControlAndAssuranceSupported,
  ] = useState(defaultData?.qualityControlAndAssurance?.isSupported || false);

  const [
    procurementAndVendorManagementSupported,
    setProcurementAndVendorManagementSupported,
  ] = useState(
    defaultData?.procurementAndVendorManagement?.isSupported || false
  );

  const [riskManagementSupported, setRiskManagementSupported] = useState(
    defaultData?.riskManagement?.isSupported || false
  );

  const [
    projectMonitoringAndReportingSupported,
    setProjectMonitoringAndReportingSupported,
  ] = useState(
    defaultData?.projectMonitoringAndReporting?.isSupported || false
  );

  const [othersSupported, setOthersSupported] = useState(
    defaultData?.others?.isSupported || false
  );
  const [otherText, setOtherText] = useState(
    defaultData?.others?.otherText || ""
  );

  const [otherTextError, setOtherTextError] = useState("");

  const [isCheckboxChanged, setIsCheckboxChanged] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsCheckboxChanged(true);
    handleOnChange(event);
  };

  const handleOnChange = (event) => {
    const { name, checked } = event.target;

    switch (name) {
      case "projectPlanningAndFeasibilityStudies":
        setProjectPlanningAndFeasibilityStudiesSupported(checked);
        break;
      case "projectDesignAndDevelopment":
        setProjectDesignAndDevelopmentSupported(checked);
        break;
      case "projectScheduling":
        setProjectSchedulingSupported(checked);
        break;
      case "costEstimationAndBudgeting":
        setCostEstimationAndBudgetingSupported(checked);
        break;
      case "qualityControlAndAssurance":
        setQualityControlAndAssuranceSupported(checked);
        break;
      case "procurementAndVendorManagement":
        setProcurementAndVendorManagementSupported(checked);
        break;
      case "riskManagement":
        setRiskManagementSupported(checked);
        break;
      case "projectMonitoringAndReporting":
        setProjectMonitoringAndReportingSupported(checked);
        break;
      case "others":
        setOthersSupported(checked);
        break;

      default:
        break;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: { isSupported: checked },
    }));
    setIsCheckboxChanged(true);
  };

  async function onSubmit(data) {
    if (othersSupported && !data.otherText) {
      setOtherTextError("Other text is required if others is selected");
      return;
    } else {
      setOtherTextError("");
    }
    const fields = {
      projectPlanningAndFeasibilityStudies: {
        isSupported: projectPlanningAndFeasibilityStudiesSupported,
        price: null,
      },
      projectDesignAndDevelopment: {
        isSupported: projectDesignAndDevelopmentSupported,
        price: null,
      },
      projectScheduling: {
        isSupported: projectSchedulingSupported,
        price: null,
      },
      costEstimationAndBudgeting: {
        isSupported: costEstimationAndBudgetingSupported,
        price: null,
      },
      qualityControlAndAssurance: {
        isSupported: qualityControlAndAssuranceSupported,
        price: null,
      },
      procurementAndVendorManagement: {
        isSupported: procurementAndVendorManagementSupported,
        price: null,
      },
      riskManagement: { isSupported: riskManagementSupported, price: null },
      projectMonitoringAndReporting: {
        isSupported: projectMonitoringAndReportingSupported,
        price: null,
      },
      others: { isSupported: othersSupported, price: null },
      otherText: data.otherText,
    };
    const response = await updateEntityServices(
      {
        architectServices: fields,
      },
      () => {
        console.error("architectServices Details failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(response);
    }

    console.log("Submitted data from Section3:", response);
    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Project Planning and Feasibility Studies:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="projectPlanningAndFeasibilityStudies"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={projectPlanningAndFeasibilityStudiesSupported}
                    onChange={(event) => {
                      setProjectPlanningAndFeasibilityStudiesSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="projectPlanningAndFeasibilityStudies"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography> Project Design and Development:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="projectDesignAndDevelopment"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={projectDesignAndDevelopmentSupported}
                    onChange={(event) => {
                      setProjectDesignAndDevelopmentSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="projectDesignAndDevelopment"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography> Project Scheduling:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="projectScheduling"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={projectSchedulingSupported}
                    onChange={(event) => {
                      setProjectSchedulingSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="projectScheduling"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Cost Estimation and Budgeting:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="costEstimationAndBudgeting"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={costEstimationAndBudgetingSupported}
                    onChange={(event) => {
                      setCostEstimationAndBudgetingSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="costEstimationAndBudgeting"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Quality Control and Assurance:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="qualityControlAndAssurance"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={qualityControlAndAssuranceSupported}
                    onChange={(event) => {
                      setQualityControlAndAssuranceSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="qualityControlAndAssurance"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Procurement and Vendor Management:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="procurementAndVendorManagement"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={procurementAndVendorManagementSupported}
                    onChange={(event) => {
                      setProcurementAndVendorManagementSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="procurementAndVendorManagement"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Risk Management:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="riskManagement"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={riskManagementSupported}
                    onChange={(event) => {
                      setRiskManagementSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="riskManagement"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Project Monitoring and Reporting:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="projectMonitoringAndReporting"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={projectMonitoringAndReportingSupported}
                    onChange={(event) => {
                      setProjectMonitoringAndReportingSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="projectMonitoringAndReporting"
                  />
                }
              />
            )}
          />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl>
            <Typography>Others:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="others"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={othersSupported}
                    onChange={(event) => {
                      setOthersSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="others"
                  />
                }
              />
            )}
          />
        </Grid>
        {othersSupported && (
          <Grid item xs={12} sm={6}>
            <FormControl>
              <Controller
                name="otherText"
                control={control}
                defaultValue={defaultData?.otherText}
                render={({ field }) => (
                  <>
                    <TextField
                      {...field}
                      label="Other"
                      error={Boolean(errors.otherText)}
                      helperText={errors.otherText?.message}
                      aria-describedby="validation-basic-textarea"
                    />
                    {!field.value && ""}
                  </>
                )}
              />
            </FormControl>
          </Grid>
        )}
        {otherTextError && (
          <Grid item xs={12} sx={{ mt: 1 }}>
            <Typography variant="body2" color="error">
              {otherTextError}
            </Typography>
          </Grid>
        )}

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              onClick={() => onCancel()}
              variant="outlined"
              color="primary"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={handleSubmit(onSubmit)}
              variant="contained"
              disabled={!isCheckboxChanged}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default ServiceArchitectEdit;
