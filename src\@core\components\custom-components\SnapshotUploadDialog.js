import React, { useEffect } from "react";
import { useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  FormHelperText,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SnapshotInput from "./SnapshotFrom";

const SnapshotUploadDialog = ({
  open,
  onClose,
  onSave,
  selectedFiles,
  setSelectedFiles,
  setCategoryId,
  categoryId,
  loading,
}) => {
  const {
    control,
    setValue,
    handleSubmit, 
    formState: { errors },
  } = useForm();

  const handleClose = () => {
    setSelectedFiles([]);
    setValue("categoryId","")
    onClose();
  };

  useEffect(() => {
    if (open) {
      setValue("categoryId", categoryId || "");
    }
  }, [open, categoryId, setValue]);

  const [disableButton, setDisableButton] = useState(true);
  // useEffect(() => {
  //   setDisableButton(selectedFiles.length === 0);
  // }, [selectedFiles]);

  const [categoriesData, setCategoriesData] = useState([]);

  useEffect(() => {
    // Fetch all categories
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setCategoriesData(res.data.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  const handleCategoryChange = (event) => {
    setCategoryId(event.target.value);
  };

  const handleSave = (data) => {
    if (onSave) {
      onSave(data);  // Pass the form data to onSave
    }
  };
  return (
    <Dialog
      fullWidth
      maxWidth="md"
      scroll="paper"
      open={open}
      onClose={(_event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start"},
          fontSize: { xs: 19, md: 20  },
        }}
        textAlign={"center"}
      >
        Add Snapshots
        <Box
          sx={{
            position: "absolute",
            top: "4px",
            right: "14px",
          }}
        >
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          position: "relative",
          ":playing": (theme) => `${theme.spacing(4)} !important`,
          px: (theme) => [
            `${theme.spacing(6)} !important`,
            `${theme.spacing(10)} !important`,
          ],
          wordWrap: 'break-word !important',
          whiteSpace: 'pre-wrap !important',
        }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12} sm={7}>
            <SnapshotInput
              selectedFiles={selectedFiles}
              setSelectedFiles={setSelectedFiles}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth error={Boolean(errors.categoryId)}>
              <InputLabel id="categoryId"> Category Type</InputLabel>
              <Controller
                name="categoryId"
                control={control}
                rules={{ required: "Category is required" }} // Validation rules if any
                render={({ field }) => (
                  <Select
                    {...field}
                    labelId="categoryId-label"
                    label="Category Type"
                    id="categoryId"
                    size="small"
                    onChange={(e) => {
                      field.onChange(e);
                      handleCategoryChange(e);
                    }}
                  >
                    {categoriesData
                      ?.filter(
                        (data) =>
                          data.name !== "SuperAdmin" &&
                          data.name !== "Employee" &&
                          data.isActive
                      )
                      .map((data) => ({
                        id: data.id,
                        label: data.name,
                      }))
                      .map((cat) => (
                        <MenuItem key={cat.id} value={cat.id}>
                          {cat.label}
                        </MenuItem>
                      ))}
                  </Select>
                )}
              />
              {errors.categoryId && (
                <FormHelperText>{errors.categoryId.message}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.85)} !important`,
        }}
      >
        <Button variant="outlined" onClick={handleClose}>
          Close
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(handleSave)}
        >
          {loading ? <CircularProgress color="inherit" size={24} /> : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SnapshotUploadDialog;
