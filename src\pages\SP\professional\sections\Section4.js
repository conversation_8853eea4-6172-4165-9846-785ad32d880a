// ** React Imports
import { forwardRef } from 'react'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import { Box } from '@mui/system'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'
import { useAuth } from 'src/hooks/useAuth'

// ** Icon Imports
import SelectMultipleBasic from 'src/@core/components/custom-components/SelectMultipleBasic'

const defaultValues = {
  dob: null,
  email: '',
  radio: '',
  select: '',
  lastName: '',
  password: '',
  textarea: '',
  firstName: '',
  checkbox: false,
  Textarea: ''
}

const structure = [
    {
      value:'JV', 
      name:'JV'
    },
    {
      value:'DM MODEL',
      name:'DM Model'
    },
    {
      value:'DEVELOPER',
      name:'Developer'
    },
    {
        value:'SELF REDEVELOPMENT',
        name:'Self Redevelopment'
      }
  ];

  const type = [
    {
      value:'RESIDENTIAL', 
      name:'Residential'
    },
    {
      value:'IT',
      name:'IT'
    },
    {
      value:'COMMERCIAL',
      name:'Commercial'
    },
    {
        value:'MIX',
        name:'Mix'
      },
    {
        value:'RETAIL',
        name:'Retail'
      }
  ];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: '100%' }} />
})

const Section4 = ({ onCancel,formData }) => {

  //Hooks
  const auth = useAuth();

  const { register, handleSubmit, setError, control, formState: { errors } } = useForm();


  async function submit(data) {

    const response = await auth.updateEntity(data,() => {
      console.error("Professional Requirement  Details failed");
    });
    onCancel();
  }

  return (
    <Box sx={{ pt: 3 }}>
      
        <Grid container spacing={5}>

        <Grid item xs={6}>
              <SelectMultipleBasic
                register={register}
                id={'structure'}
                label={'Structure'}
                nameArray={structure}
              />
            
            </Grid>

            <Grid item xs={6}>
              <SelectMultipleBasic
                register={register}
                id={'type'}
                label={'Type'}
                nameArray={type}
              />
            
            </Grid>

       
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name='other'
                control={control}
                rules={{ required: true }}
                defaultValue={formData?.address}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label='Other (please specify)'
                    error={Boolean(errors.address)}
                    aria-describedby='broker-validation-basic-address'
                  />
                )}
              />
              {errors.other && (
                <FormHelperText sx={{ color: 'error.main' }} id='broker-validation-basic-other'>
                  This field is required
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
         

          
          

          

          <Grid item xs={12}>
          <center>
            <Button size='medium' sx={{ mr:3 }} variant='outlined' color='primary' onClick={() => onCancel()} >
              Cancel
            </Button>
            <Button size='medium' type='button' variant='contained' onClick={handleSubmit(submit)}>
              Save
            </Button>
          </center>
        </Grid>
        </Grid>
     
    </Box>
  )
}

export default Section4
