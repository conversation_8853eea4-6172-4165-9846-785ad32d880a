// ** Next Import
import Link from "next/link";
import { useRouter } from 'next/router'

// ** <PERSON>UI Components
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";
import authConfig from "src/configs/auth";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Configs
import themeConfig from "src/configs/themeConfig";

// ** Demo Components Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import RegisterTwoSteps from "src/views/pages/auth/register-two-step";
import { useEffect } from "react";

// ** Styled Components
const LinkStyled = styled(Link)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  textDecoration: "none",
  color: "#444",
  marginRight: theme.spacing(5),
}));

const RegisterMultiStepsIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 500,
  marginTop: theme.spacing(12),
}));

const LeftWrapper = styled(Box)(({ theme }) => ({
  width: "100%",
  display: "flex",
  position: "relative",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(8),
  "& .img-mask": {
    left: 0,
  },
  [theme.breakpoints.up("lg")]: {
    maxWidth: 850,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  flex: 1,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(6),
  [theme.breakpoints.up("sm")]: {
    padding: theme.spacing(12),
  },
}));

const RegisterMultiSteps = () => {
  // ** Hooks
  const theme = useTheme();
  const hidden = useMediaQuery(theme.breakpoints.down("lg"));

  const router = useRouter()

  useEffect(() => {

    Promise.resolve().then(() => {
      if (window.localStorage?.getItem('userData') && window.localStorage?.getItem('accessToken')) {
        window.location.href = '/dashboard';
      }
    });
  }, [router.route]);

  if (!(window.localStorage?.getItem('userData') && window.localStorage?.getItem('accessToken'))) {
  return (
    <Box
      className="content-right"
      sx={{ backgroundColor: "customColors.bodyBg" }}
    >
      <RightWrapper>
        <LinkStyled href={authConfig.guestURL+ 'home'}>
          <Box
            sx={{
              top: { xs: 18, lg: 30 },
              left: { xs: 34, lg: 30 },
              display: "flex",
              position: "absolute",
              alignItems: "center",
            }}
          >
            <img
              width={42}
              height={44}
              alt=""
              src="/images/logo.webp"
              className=""
            ></img>
            <Typography
              sx={{
                ml: 2,
                fontWeight: 600,
                lineHeight: "24px",
                fontSize: "1.375rem",
              }}
            >             
              {themeConfig.templateName}
              <br />
              <Typography
                variant="body1"
                sx={{
                  fontSize: "1rem",
                }}
              >
                {themeConfig.businessName}
              </Typography>
            </Typography>
          </Box>
        </LinkStyled>
        <Box sx={{ maxWidth: 550 }}>
          <RegisterTwoSteps />
        </Box>
      </RightWrapper>
      {!hidden ? (
        <LeftWrapper>
          <RegisterMultiStepsIllustration
            alt="register-multi-steps-illustration"
            src="/images/pages/register.webp"
          />
        </LeftWrapper>
      ) : null}
    </Box>
  
  );
}
};

RegisterMultiSteps.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
RegisterMultiSteps.guestGuard = true;

export default RegisterMultiSteps;
