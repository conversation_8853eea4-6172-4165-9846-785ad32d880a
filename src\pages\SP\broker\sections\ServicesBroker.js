// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import Section3 from "src/pages/SP/broker/sections/Section3";
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  boolean,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "../../MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServicesBroker = ({ data, expanded }) => {

  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };
  const [services, setServices] = useState({
    top10MicroMarketsWorking: {
      isSupported: false,
      price: "",
    },
    residential: {
      isSupported: false,
      price: "",
    },
    commercial: {
      isSupported: false,
      price: "",
    },
    retail: {
      isSupported: false,
      price: "",
    },
    investor: {
      isSupported: false,
      price: "",
    },
  });

  return (
    <>
    {/* {can('broker_services_READ') &&  */}
      <AccordionBasic
      id={"panel-header-2"}
      ariaControls={"panel-content-2"}
      heading={"Services"}
      body={
        <>
          {state3 === "view" && (
            <TableContainer
              sx={{ padding: "4px 6px" }}
              className="tableBody"
              onClick={viewClick3}
            >
              <Table>
                <TableBody
                  sx={{
                    "& .MuiTableCell-root": {
                      p: `${theme.spacing(1.35, 1.125)} !important`,
                    },
                  }}
                >
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Service Name:
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        (Yes/No):
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>Top 10 Micro-markets working #:</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.services?.top10MicroMarketsWorking?.isSupported
                          ? "Yes"
                          : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>Residential:</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.services?.residential?.isSupported
                          ? "Yes"
                          : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography> Commercial</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.services?.commercial?.isSupported
                          ? "Yes"
                          : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>Retail</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.services?.retail?.isSupported ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  <TableRow>
                    <MUITableCell>
                      <Typography>Investor</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field">
                        {data?.services?.investor?.isSupported ? "Yes" : "No"}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {state3 === "edit" &&
            (data?.services != null ? (
              <Section3
                setter={setServices}
                defaultData={data.services}
                onCancel={editClick3}
              />
            ) : (
              <Section3
                setter={setServices}
                defaultData={services}
                onCancel={editClick3}
              />
            ))}
        </>
      }
      expanded={expanded}
    />
    {/* } */}
      
    </>
  );
};
export default ServicesBroker;
