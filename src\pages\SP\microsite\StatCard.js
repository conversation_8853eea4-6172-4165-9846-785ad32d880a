import React from 'react';
import { Box, Typography } from "@mui/material";
import { hexToRGBA } from 'src/@core/utils/hex-to-rgba'
import { useTheme } from '@mui/material/styles'

const StatCard = ({ icon, title, value }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 2.15,
        position: "relative",
        backgroundColor: hexToRGBA(theme.palette.primary.greenGrey, 0.22),
        borderRadius: 2,
        height: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Box
        sx={{
          marginTop: "-40px",
          backgroundColor: "rgb(230, 237, 229)",
          borderRadius: "50%",
          width: 110,
          height: 110,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <img height={55} src={icon} alt={title} />
      </Box>
      <Typography variant="body1" align="center" fontWeight={400} sx={{ mt: 0, mb: 1 }}>
        {title}
      </Typography>
      <Typography variant="body2" align="center" fontWeight={600}>
        {value}
      </Typography>
    </Box>
  );
}

export default StatCard;
