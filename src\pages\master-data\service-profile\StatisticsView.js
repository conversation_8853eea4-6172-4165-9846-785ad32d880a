// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "@mui/material/TableCell";
import StatisticsEdit from "./StatisticsEdit";
import { AuthContext } from "src/context/AuthContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const StatisticsView = ({ data,names, expanded, fetchUsers,onCancel,fetchAll }) => {
  const theme = useTheme();

  const [state, setState] = useState("view");


  const { listNames } =
  useContext(AuthContext);

  const [viewData, setViewData] = useState(data);

  function updateFormData(formData, listNames) {
    // Clone formData to avoid direct mutation
    let updatedFormData = { ...formData };
  
     // Update listNames with the names array
     updatedFormData.listNames = listNames;

     return updatedFormData;
   
  }

  useEffect(()=>{
    
    const updatedFormData = updateFormData(data, names);
    setViewData(updatedFormData)
  },[data,names])
  

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const activeQuestions = viewData?.listNames?.filter(
    (question) => question.isActive === true
  );


  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={`${viewData?.name} Questions`}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px", cursor: "pointer" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Service name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {viewData?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Questions:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                      {viewData?.listNames && (
                            <Typography className="data-field">
                                {activeQuestions?.map((item) => item.name).join(", ")}
                            </Typography>
                        )}
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <StatisticsEdit
                onCancel={editClick}
                formData={viewData}
                names={names}
                fetchUsers={fetchUsers}
                setData={setViewData}
                fetchAll={fetchAll}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default StatisticsView;
