import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";


import PageHeader from "src/@core/components/page-header";
import NavTabFSI from "src/@core/components/custom-components/NavTabFSI";
import MasterData from "./MasterData";
import FSIRules from "./FSIRules";
import Locations from "./Locations";
import Zones from "./Zones";

const Calculator = () => {


  return (
    <Card>
      
      <NavTabFSI
        tabContent1={
          <>
            <Locations/>
          </>
        }
        tabContent2={
          <>
            <Zones/>
          </>
        }
        tabContent3={
          <>
            <MasterData masterDataType={"TYPE_FSI"}/>
          </>
        }
        tabContent4={
          <>
            <MasterData masterDataType={"CITY_FSI"}/>
          </>
        }
        tabContent5={
            <>
            <MasterData masterDataType={"WARD_FSI"}/>
            </>
        }
        tabContent6={
            <>
            <FSIRules/>
            </>
        }
      />
    </Card>
  );
};

export default Calculator;
