import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import {
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import SearchIcon from "@mui/icons-material/Search";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Controller, useForm } from "react-hook-form";

import { Box } from "@mui/system";



const RequisitionDetails = () => {
  // const { user, listValues } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const [openViewDialog, setOpenViewDialog] = useState(false);
  // Constants
  const rowsPerPageOptions = [5,10, 15, 20, 25, 50, 100];
  const [employeesData, setEmployeesData] = useState([]);

  // Define columns
  const columns = [
    {
      field: "societyName",
      minWidth: 135,
      headerName: "Society Name",
      flex: 0.13,
    //   valueGetter: (params) => params.row.additionalDetails.title,
    },
    {
        field: "requestDate",
        minWidth: 135,
        headerName: "Request Date",
        flex: 0.16
    },
    {
        field: "requisitionStatus",
        minWidth: 185,
        headerName: "Requisition Status",
        flex: 0.15
    },
    {
      field: "description",
      minWidth: 135,
      headerName: "Description",
      flex: 0.15,
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenViewDialog(true);
        //   setTaskData({
        //     ...taskData,
        //     id: row.id,
        //   });
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                // onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");
  

  const [openDialog, setOpenDialog] = useState(false);


  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

    // const url = getUrl(authConfig.getAllTasks) 
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.tasks || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const handleCloseEditDialog = () => {
    reset();
    setOpenViewDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Card>
        {/* <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={12}>
              <Grid
                container
                spacing={2}
                alignItems="center"
              >
                 <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6">Requisitions</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm={8} md={6} lg={5}>
                      <FormControl fullWidth>
                        <Controller
                          name="mainSearch"
                          control={control}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search by title and description"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setSearchKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                  fetchUsers(page, pageSize, searchKeyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                        fetchUsers(
                                          page,
                                          pageSize,
                                          searchKeyword
                                        );
                                      }}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                      <Grid item xs={12} sm="auto" md="auto" lg="auto">
                        <Button variant="contained"  sx={{ textTransform: 'none'}} onClick={handleOpenDialog}>
                        Create New Task
                        </Button>
                      </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box> */}
        <Divider />
        
      
        <CardContent>
          <Box style={{ height: "100%", width: "100%" }}>
            <DataGrid
              rows={userList || []}
              columns={columns}
              autoHeight
              checkboxSelection
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={userList.length > 0 ? rowsPerPageOptions : []}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={38}
              headerHeight={38} 
            />
          </Box>
        </CardContent>
      </Card>
    </>
  );
};

export default RequisitionDetails;
