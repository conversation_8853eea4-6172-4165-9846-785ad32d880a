import React from 'react';
import {
  Grid,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell ,
  Card,
  Box,
} from '@mui/material';
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import MUITableCell from '../SP/MUITableCell';

const DraggableTable = ({
  dataList,
  setDataList,
  handleDragEnd,
  handleDelete,
  metaPostsData,
}) => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TableContainer component={Card} sx={{ pt: { xs: 2, md: 3 }, px: { xs: 2, md: 3 } }}>
          <DragDropContext onDragEnd={handleDragEnd(dataList, setDataList)}>
            <Table sx={{ minWidth: 480 }} size="small">
              <TableHead sx={{ whiteSpace: 'nowrap' }}>
                <TableRow>
                  <MUITableCell sx={{ fontWeight: 'bold', noWrap: 'nowrap' }}>Title</MUITableCell>
                  <MUITableCell sx={{ fontWeight: 'bold' }}>wordPressId</MUITableCell>
                  <MUITableCell sx={{ fontWeight: 'bold' }}>Type</MUITableCell>
                  <MUITableCell sx={{ fontWeight: 'bold' }}>Sequence</MUITableCell>
                  <MUITableCell sx={{ fontWeight: 'bold' }}>Actions</MUITableCell>
                </TableRow>
              </TableHead>
              <Droppable droppableId="table">
                {(provided) => (
                  <Box component={TableBody} {...provided.droppableProps} ref={provided.innerRef}>
                    {dataList?.map((item, index) => (
                      <Draggable key={index} draggableId={`draggable-${index}`} index={index}>
                        {(provided) => (
                          <TableRow
                            sx={{
                              '&:last-of-type  td': { border: 0 },
                            }}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            ref={provided.innerRef}
                          >
                            {metaPostsData?.find((record) => record.wordPressId == item.wordPressId) ? (
                              <>
                                <MUITableCell>{item.title}</MUITableCell>
                                <MUITableCell>{item.wordPressId}</MUITableCell>
                                <MUITableCell>{item?.format === 'video' ? 'Video' : 'Blog'}</MUITableCell>
                                <MUITableCell>{index + 1}</MUITableCell>
                                <MUITableCell>
                                  <IconButton
                                    sx={{ p: 0, width: 26, height: 26 }}
                                    size="small"
                                    color="error"
                                    onClick={() => handleDelete(index, dataList, setDataList)}
                                  >
                                    <Icon icon="tabler:trash" />
                                  </IconButton>
                                </MUITableCell>
                              </>
                            ) : null}
                          </TableRow>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            </Table>
          </DragDropContext>
        </TableContainer>
      </Grid>
    </Grid>
  );
};

export default DraggableTable;
