import axios from 'axios';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

/**
 * Create axios instance with default configuration
 */
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request interceptor to add authentication token
 */
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * Response interceptor to handle common errors
 */
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Clear stored authentication data
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      localStorage.removeItem('rememberMe');
      
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    
    // Handle network errors
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }
    
    return Promise.reject(error);
  }
);

/**
 * Authentication API endpoints
 */
export const authAPI = {
  /**
   * Login user
   * @param {Object} credentials - User credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @param {boolean} credentials.rememberMe - Remember me option
   * @returns {Promise} API response
   */
  login: async (credentials) => {
    // Mock API response for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (credentials.email === '<EMAIL>' && credentials.password === 'Demo123!') {
          resolve({
            data: {
              user: {
                id: 1,
                fullName: 'Demo User',
                email: credentials.email,
                avatar: null,
              },
              token: 'mock-jwt-token-' + Date.now(),
            },
          });
        } else {
          reject({
            response: {
              status: 401,
              data: {
                message: 'Invalid email or password',
              },
            },
          });
        }
      }, 1000);
    });
    
    // Uncomment below for real API integration
    // return apiClient.post('/auth/login', credentials);
  },

  /**
   * Register new user
   * @param {Object} userData - User registration data
   * @param {string} userData.fullName - User full name
   * @param {string} userData.email - User email
   * @param {string} userData.password - User password
   * @returns {Promise} API response
   */
  register: async (userData) => {
    // Mock API response for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (userData.email && userData.password && userData.fullName) {
          resolve({
            data: {
              user: {
                id: Date.now(),
                fullName: userData.fullName,
                email: userData.email,
                avatar: null,
              },
              token: 'mock-jwt-token-' + Date.now(),
            },
          });
        } else {
          reject({
            response: {
              status: 400,
              data: {
                message: 'All fields are required',
              },
            },
          });
        }
      }, 1000);
    });
    
    // Uncomment below for real API integration
    // return apiClient.post('/auth/register', userData);
  },

  /**
   * Send forgot password email
   * @param {Object} data - Email data
   * @param {string} data.email - User email
   * @returns {Promise} API response
   */
  forgotPassword: async (data) => {
    // Mock API response for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (data.email && data.email.includes('@')) {
          resolve({
            data: {
              message: 'Password reset link sent to your email',
            },
          });
        } else {
          reject({
            response: {
              status: 400,
              data: {
                message: 'Please provide a valid email address',
              },
            },
          });
        }
      }, 1000);
    });
    
    // Uncomment below for real API integration
    // return apiClient.post('/auth/forgot-password', data);
  },

  /**
   * Social login
   * @param {Object} socialData - Social login data
   * @param {string} socialData.provider - Social provider (google, facebook)
   * @param {string} socialData.token - Social access token
   * @returns {Promise} API response
   */
  socialLogin: async (socialData) => {
    // Mock API response for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (socialData.provider && socialData.token) {
          resolve({
            data: {
              user: {
                id: Date.now(),
                fullName: `${socialData.provider} User`,
                email: `user@${socialData.provider}.com`,
                avatar: null,
                provider: socialData.provider,
              },
              token: 'mock-jwt-token-' + Date.now(),
            },
          });
        } else {
          reject({
            response: {
              status: 400,
              data: {
                message: 'Social login failed',
              },
            },
          });
        }
      }, 1000);
    });
    
    // Uncomment below for real API integration
    // return apiClient.post('/auth/social-login', socialData);
  },

  /**
   * Refresh authentication token
   * @returns {Promise} API response
   */
  refreshToken: async () => {
    return apiClient.post('/auth/refresh-token');
  },

  /**
   * Logout user
   * @returns {Promise} API response
   */
  logout: async () => {
    return apiClient.post('/auth/logout');
  },
};

/**
 * User API endpoints
 */
export const userAPI = {
  /**
   * Get current user profile
   * @returns {Promise} API response
   */
  getProfile: async () => {
    return apiClient.get('/user/profile');
  },

  /**
   * Update user profile
   * @param {Object} userData - Updated user data
   * @returns {Promise} API response
   */
  updateProfile: async (userData) => {
    return apiClient.put('/user/profile', userData);
  },

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @returns {Promise} API response
   */
  changePassword: async (passwordData) => {
    return apiClient.put('/user/change-password', passwordData);
  },
};

/**
 * Generic API helper functions
 */
export const apiHelpers = {
  /**
   * Get authentication headers
   * @returns {Object} Headers object with authorization
   */
  getAuthHeaders: () => {
    const token = localStorage.getItem('authToken');
    return token ? { Authorization: `Bearer ${token}` } : {};
  },

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated: () => {
    const token = localStorage.getItem('authToken');
    return !!token;
  },

  /**
   * Handle API errors
   * @param {Error} error - API error
   * @returns {string} User-friendly error message
   */
  handleApiError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data?.message || 'An error occurred. Please try again.';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error. Please check your connection.';
    } else {
      // Something else happened
      return 'An unexpected error occurred. Please try again.';
    }
  },
};

export default apiClient;
