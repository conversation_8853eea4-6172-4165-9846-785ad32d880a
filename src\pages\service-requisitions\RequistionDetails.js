import { useState, useEffect, useContext, useRef, Fragment } from "react";
import {
  Box,
  Grid,
  Button,
  Typography,
  Divider,
  IconButton,
  Card,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  FormControl,
  InputLabel,
  TextField,
  Slider,
  FormHelperText,
  DialogContentText,
  TableCell,
  FormControlLabel,
  RadioGroup,
  Radio,
  Switch,
  Autocomplete,
  TableHead,
  Chip,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import Tooltip from "@mui/material/Tooltip";

import CustomAvatar from "src/@core/components/mui/avatar";


import Icon from "src/@core/components/icon";

import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import "react-datepicker/dist/react-datepicker.css";
import authConfig from "src/configs/auth";
import axios from "axios";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import MUITableCell from "../SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CommentsDialog from "./CommentsDialog";
import SiteVisitTimings from "./SiteVisitTimings";
import { DataGrid } from "@mui/x-data-grid";
import SelectProject from "src/@core/components/custom-components/SelectProject";

import QuoteRequestDialog from "./QuoteRequestDialog";
import { MenuItem, Menu } from "@mui/material";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const marks = Array.from({ length: 20 }, (_, i) => ({
  value: (i + 1) * 5,
  label: `${(i + 1) * 5}`,
}));

function valuetext(value) {
  return `${value}`;
}

const userList = [];

const rupees = [
  {
    value: "HUNDRED",
    key: "hundred",
  },
  {
    value: "THOUSAND",
    key: "thousand",
  },
  {
    value: "LAKHS",
    key: "lakhs",
  },
  {
    value: "CRORES",
    key: "crores",
  },
];

const dummyData = [
  {
    id: 1,
    name: "Service Provider 1",
    spCompanyName: "Company A",
    file: "quote1.pdf",
    remarks: "Remark 1",
    uploadedDate: "2024-08-21",
    quotationStatus: "Pending",
  },
  {
    id: 2,
    name: "Service Provider 2",
    spCompanyName: "Company B",
    file: "quote2.pdf",
    remarks: "Remark 2",
    uploadedDate: "2024-08-20",
    quotationStatus: "Approved",
  },
  {
    id: 3,
    name: "Service Provider 3",
    spCompanyName: "Company C",
    file: "quote3.pdf",
    remarks: "Remark 3",
    uploadedDate: "2024-08-19",
    quotationStatus: "Rejected",
  },
];

const sampleData = [
  {
    id: 1,
    name: "CHS member 1",
    mobileNumber: "**********",
    siteVisitDate: "2024-08-24",
    startTime: "9:00",
    endTime: "11:00",
  },
  {
    id: 2,
    name: "CHS member 2",
    mobileNumber: "**********",
    siteVisitDate: "2024-08-24",
    startTime: "11:00",
    endTime: "13:00",
  },
  {
    id: 3,
    name: "CHS member 3",
    mobileNumber: "**********",
    siteVisitDate: "2024-08-24",
    startTime: "13:00",
    endTime: "15:00",
  },
];

const RequisitionDetails = ({ open, role, onClose, fetchRequisitions }) => {
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");
  const theme = useTheme();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [siteVisitOpen,setSiteVisitOpen] = useState(false)
  const [selectedQuotes, setSelectedQuotes] = useState([]);

  const handleDialogOpen = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);

  const handleSiteVisitDialogOpen = () => setSiteVisitOpen(true);
  const handleSiteVisitDialogClose = () => setSiteVisitOpen(false);

  const handleQuotesSubmit = (quotes) => {
    setSelectedQuotes(quotes);
    handleDialogClose();
  };

  const auth = useAuth();
  const { user, getAllListValuesByListNameId, listValues, userData } =
    useContext(AuthContext);
  const {
    register,
    handleSubmit,
    control,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [addButton, setAddButton] = useState(false);
  const [budgetValue, setBudgetValue] = useState([20, 30]);

  const handleChange = (event, newValue) => {
    setBudgetValue(newValue);
  };

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [budget, setBudget] = useState("");

  const [data, setData] = useState([]);

  const [selectedOptions, setSelectedOptions] = useState({});

  const [convList, setConvList] = useState([]);

  const [conversation, setConversation] = useState({});
  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleInfoDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const [allServicesList, setAllServicesList] = useState([]);
  const [subServicesList, setSubServicesList] = useState([]);
  const [requisitionDialog, setRequisitionDialog] = useState(false);
  const [societySelected, setSocietySelected] = useState(false);
  const [selectedSociety, setSelectedSociety] = useState({});
  const [listOfSocieties, setListOfSocieties] = useState([]);

  const societyOptions = listOfSocieties
    .filter((society) => society?.name)
    .map((society) => ({
      value: society,
      key: society?.name,
    }));

  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);

          setListOfSocieties(metadataArray);
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
  }, []);

  const [dataView, setDataView] = useState({});

  const [societyId, setSocietyId] = useState("");

  const [designation, setDesignation] = useState("");

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdown) +
        "?selectionType=SOCIETY_SUB_CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        if (
          res.data.data.length > 0 &&
          res.data.data[0].metaData &&
          res.data.data[0].metaData.subRoleTypes
        ) {
          setSubCategories(res.data.data[0].metaData.subRoleTypes);
        }
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (!!subCategories) {
      let data = [];
      subCategories.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListOfSubCategories(data);
    }
  }, [subCategories]);

  const handleSocietyChange = (newValue) => {
    setSelectedSociety(newValue);
    setSocietySelected(true);
    setSocietyId(newValue?.userId);
    setDataView(newValue);
    const loc = newValue?.designation
      ? listOfSubCategories.find((item) => item.value === newValue?.designation)
          ?.key
      : null;

    setDesignation(loc);
  };

  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=ALL_EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleClick = () => {
    setRequisitionDialog(true);
  };

  const handleClickClose = () => {
    setRequisitionDialog(false);
  };

  const [specificationList, setSpecificationList] = useState(false);
  const [dataTransformed, setDataTransformed] = useState([]);

  const handleAdd = () => {
    setSpecificationList(true);
    const transformedData = transformData(selectedOptions);
    setDataTransformed(transformedData);
    setRequisitionDialog(false);
  };

  const handleCancel = () => {
    setDataView({});
    setData([]);
    setSelectedOptions({});
    setDesignation("");
    setSocietySelected(false);
    setSelectedSociety("");
    setValue("serviceType", "");
    setValue("subServices", []);
    setValue("anyOtherService", "");
    setValue("requirementDeadLine", "");
    setValue("priority", "");
    setBudget("");
    setValue("specifications", "");
    setValue("societyRemarks", "");
    setValue("referenceType", "");
    setValue("referralName", "");
    setValue("assignedTo", "");
    setValue("status", "");
    setValue("houzerSocietyTeamMember", "");
    setValue("dataSentDate", "");
    setRequisitionDialog(false);
    setAddButton(false);
    setSpecificationList(false);
    onClose();
  };
  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleSubServicesSuccess = (data) => {
    setSubServicesList(data?.listValues);
  };

  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };

  const [priorityData, setPriorityData] = useState(null);

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const [subServiceId, setSubServiceId] = useState("");

  useEffect(() => {
    if (subServiceId) {
      getAllListValuesByListNameId(
        subServiceId,
        handleSubServicesSuccess,
        handleError
      );
    }
  }, [subServiceId]);

  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [showSubService, setShowSubService] = useState(false);
  const [serviceId, setServiceId] = useState("");

  const [specifications, setSpecifications] = useState([]);

  useEffect(() => {
    if (serviceId) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setSpecifications(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      fetchAll(serviceId);
    }
  }, [serviceId]);

  function reverseTransformData(transformedData) {
    const originalData = {};

    transformedData?.forEach((item) => {
      // Check if the listValues array has more than one item.
      // If so, map through the array and extract the listValueId, forming a new array.
      // If not, just extract the single listValueId.
      if (item.listValues.length > 0) {
        originalData[item.listNameId] = item.listValues.map(
          (valueItem) => valueItem.listValueId
        );
      } else {
        originalData[item.listNameId] = item.otherValue;
      }
    });

    return originalData;
  }

  const [finalUser, setFinalUser] = useState({});

  useEffect(() => {
    if (userData && userData.length > 0) {
      // Find the user whose serviceNameId matches the serviceId
      const userWithMatchingService = userData.find(
        (user) => user.serviceNameId === serviceId
      );
      if (userWithMatchingService) {
        // If a user is found, set the user data in the state variable
        setFinalUser(userWithMatchingService);
        const reverse = reverseTransformData(
          userWithMatchingService.metadata.listNames
        );
        setSelectedOptions(reverse);
      }
    }
  }, [userData, serviceId]);

  const [viewData, setViewData] = useState([]);
  useEffect(() => {
    // Create a mapping of listValueId to listValue
    const listValueMap = specifications.reduce((map, item) => {
      item.values.forEach((value) => {
        map[value.id] = { id: value.id, name: value.name };
      });
      return map;
    }, {});

    // Get all list names from specifications
    const listNames = specifications.map((item) => {
      const metadataItem = dataTransformed?.find(
        (list) => list.listNameId === item.id
      );
      const otherValue = metadataItem ? metadataItem.otherValue : null;

      return {
        id: item.id,
        name: item.name,
        otherValue: otherValue,
        values:
          metadataItem && metadataItem.listValues.length
            ? metadataItem.listValues.map(
                (value) => listValueMap[value.listValueId]
              )
            : [],
      };
    });
    setViewData(listNames);
  }, [dataTransformed, specifications]);

  const handleSelectChange = (event) => {
    setRequisitionDialog(false);
    setSpecificationList(false);
    setSelectedOptions({});
    setAddButton(true);
    const value = event?.id;
    setServiceId(value);
    const name = value
      ? listValues?.find((item) => item?.id === value)?.name
      : null;
    if (name === "Any other") {
      setIsOtherSelected(true);
    } else {
      setShowSubService(true);
    }
  };

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Requisition added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Requisition. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const isApiCalling = useRef(false);

  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    if (budget === "HUNDRED") {
      budgetValue[0] = `${budgetValue[0]}00`;
      budgetValue[1] = `${budgetValue[1]}00`;
    } else if (budget === "THOUSAND") {
      budgetValue[0] = `${budgetValue[0]}000`;
      budgetValue[1] = `${budgetValue[1]}000`;
    } else if (budget === "LAKHS") {
      budgetValue[0] = `${budgetValue[0]}00000`;
      budgetValue[1] = `${budgetValue[1]}00000`;
    } else if (budget === "CRORES") {
      budgetValue[0] = `${budgetValue[0]}0000000`;
      budgetValue[1] = `${budgetValue[1]}0000000`;
    }

    isApiCalling.current = true;
    const transformedData = transformData(selectedOptions);
    let fields;
    if (role) {
      fields = {
        userId: user?.id,
        requisitionData: {
          serviceType: data?.serviceType,
          subServices: data?.subServices,
          anyOtherServices: data?.anyOtherServices,
          priority: data?.priority,
          specifications: {
            listNames: transformedData,
          },
          units: budget,
          budget: `${budgetValue[0]} - ${budgetValue[1]}`,
          requirementDeadLine: data?.requirementDeadLine,
          societyRemarks: data?.societyRemarks,
        },
        conversationData: convList,
      };
    } else {
      fields = {
        userId: societyId,
        requisitionData: {
          serviceType: data?.serviceType,
          subServices: data?.subServices,
          anyOtherServices: data?.anyOtherServices,
          priority: data?.priority,
          specifications: {
            listNames: transformedData,
          },
          units: budget,
          budget: `${budgetValue[0]} - ${budgetValue[1]}`,
          requirementDeadLine: data?.requirementDeadLine,
          societyRemarks: data?.societyRemarks,
        },
        conversationData: convList,
        teamMember: data?.houzerSocietyTeamMember,
        referralName: data?.referralName,
        referenceType: data?.referenceType,
        assignedTo: data?.assignedTo,
        status: data?.status,
        dataSentDate: data?.dataSentDate,
      };
    }

    try {
      const response = await auth.postRequisition(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
    }

    fetchRequisitions();
    handleCancel();
    isApiCalling.current = false;
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  function transformData(originalData) {
    const transformedData = [];
    for (const listNameId in originalData) {
      let listValues = [];
      let otherValue = null;

      if (Array.isArray(originalData[listNameId])) {
        listValues = originalData[listNameId].map((listValueId) => ({
          listValueId,
        }));
      } else {
        otherValue = originalData[listNameId];
      }

      transformedData.push({
        listNameId,
        otherValue,
        listValues,
      });
    }

    return transformedData;
  }

  const handleSpecificationChange = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: event.target.value,
    });
  };

  const handleChangeSelect = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: [event.target.value],
    });
  };

  const columns = [
    { field: "id", headerName: "ID", width: 70, flex: 3 },
    { field: "name", headerName: "Name", width: 150, flex: 3 },
    { field: "spCompanyName", headerName: "Company Name", width: 150, flex: 3 },
    { field: "file", headerName: "File", width: 150, flex: 3 },
    { field: "remarks", headerName: "Remarks", width: 150, flex: 3 },
    { field: "uploadedDate", headerName: "Uploaded Date", width: 150, flex: 3 },
    {
      field: "quotationStatus",
      headerName: "Quotation Status",
      width: 150,
      flex: 3,
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      flex: 3,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenViewDialog(true);
          //   setTaskData({
          //     ...taskData,
          //     id: row.id,
          //   });
        };

        const [anchorEl, setAnchorEl] = useState(null);
        const handleMenuClick = (event) => {
          setAnchorEl(event.currentTarget);
        };

        const handleMenuClose = () => {
          setAnchorEl(null);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              // onClick={onClick}
            >
              <IconButton onClick={handleMenuClick}>
                <Icon icon="bi:three-dots-vertical" />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem>Edit</MenuItem>
                <MenuItem>Remind</MenuItem>
                <MenuItem>Cancel</MenuItem>
              </Menu>
            </CustomAvatar>
          </div>
        );
      },
    },
  ];

  const cols = [
    { field: "id", headerName: "ID", width: 70, flex: 1 },
    { field: "name", headerName: "Name", width: 100, flex: 3 },
    {
      field: "mobileNumber",
      headerName: "Contact Number",
      width: 100,
      flex: 3,
    },
    {
      field: "siteVisitDate",
      headerName: "Site Visit Date",
      width: 100,
      flex: 3,
    },
    { field: "startTime", headerName: "Start Time", width: 100, flex: 3 },
    { field: "endTime", headerName: "End Time", width: 100, flex: 3 },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          const row = params.row;
          setCurrentRow(row);
          setOpenViewDialog(true);
          //   setTaskData({
          //     ...taskData,
          //     id: row.id,
          //   });
        };

        const [anchorEl, setAnchorEl] = useState(null);
        const handleMenuClick = (event) => {
          setAnchorEl(event.currentTarget);
        };

        const handleMenuClose = () => {
          setAnchorEl(null);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              // onClick={onClick}
            >
              <IconButton onClick={handleMenuClick}>
                <Icon icon="bi:three-dots-vertical" />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem>Edit</MenuItem>
                <MenuItem>Delete</MenuItem>
              </Menu>
            </CustomAvatar>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Create Service Requisition
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          {!role && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Society Information
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell colSpan={2}>
                        <SelectAutoComplete
                          id={"society"}
                          label={"Select society name"}
                          nameArray={societyOptions}
                          register={register}
                          value={selectedSociety}
                          defaultValue={selectedSociety}
                          onChange={(e) => handleSocietyChange(e.target.value)}
                          error={Boolean(errors.listNamesId)}
                        />
                      </MUITableCell>
                    </TableRow>

                    {societySelected && (
                      <>
                        <TableRow>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>Location:</Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.location}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>Zone:</Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.zone}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>Address:</Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.societyAddress}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>
                              Society contact name:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.chairmanName}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>Designation:</Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {designation}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>
                              Contact number:
                            </Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.mobileNumber}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell sx={{ width: "150px" }}>
                            <Typography style={field}>Email Id:</Typography>
                          </MUITableCell>
                          <MUITableCell sx={{ width: "200px" }}>
                            <Typography className="data-field">
                              {dataView?.loginEmail}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Requisition Details
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={6.67}>
                <FormControl fullWidth error={Boolean(errors.serviceType)}>
                  <Controller
                    name="serviceType"
                    control={control}
                    rules={{ required: "serviceType is required" }}
                    render={({ field }) => (
                      <Autocomplete
                        {...field}
                        onChange={(event, newValue) => {
                          field.onChange(newValue ? newValue.id : null);
                          handleSelectChange(newValue);
                        }}
                        value={
                          allServicesList?.find(
                            (service) => service.id === field.value
                          ) || null
                        }
                        options={allServicesList || []}
                        getOptionLabel={(option) => option.listValue || ""}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Service Type"
                            error={Boolean(errors.serviceType)}
                            helperText={
                              errors.serviceType
                                ? errors.serviceType.message
                                : ""
                            }
                          />
                        )}
                        size="small"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  onClick={handleClick}
                  disabled={!addButton}
                >
                  Add Specifications
                </Button>
              </Grid>
              {specificationList && (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell>Selected Options</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {viewData
                          ?.slice()
                          ?.sort((a, b) => a.listSequence - b.listSequence)
                          ?.map((category) =>
                            category.values.length > 0 ||
                            category.otherValue ? (
                              <TableRow key={category.name}>
                                <TableCell>{category.name}</TableCell>
                                <TableCell>
                                  {category.values.length > 0
                                    ? category.values.map((value) => (
                                        <Chip
                                          key={value.id}
                                          label={value.name}
                                          style={{
                                            marginRight: "4px",
                                            marginTop: "10px",
                                          }}
                                        />
                                      ))
                                    : category.otherValue && (
                                        <Chip
                                          label={category.otherValue}
                                          style={{
                                            marginRight: "4px",
                                          }}
                                        />
                                      )}
                                </TableCell>
                              </TableRow>
                            ) : null
                          )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
              {isOtherSelected && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="anyOtherService"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Other Service"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Other Service"
                          error={Boolean(errors.anyOtherService)}
                          helperText={errors.anyOtherService?.message}
                          aria-describedby="validation-basic-anyOtherService"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLine"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requirement DeadLine"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLine"
                        value={field.value}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={2}>
                <FormControl fullWidth>
                  <InputLabel id="priority"> Priority</InputLabel>
                  <Controller
                    name="priority"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="priority-label"
                        label="Priority"
                        id="priority"
                        size="small"
                      >
                        {priorityData?.map((status) => (
                          <MenuItem key={status.id} value={status.id}>
                            {status.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box display="flex" alignItems="center">
                  <Typography id="range-slider" gutterBottom>
                    Budget in
                  </Typography>
                  <Grid item xs={4} sx={{ marginLeft: "6px" }}>
                    <SelectProject
                      register={register}
                      id={"budget"}
                      label={"units"}
                      nameArray={rupees}
                      value={budget}
                      onChange={(e) => setBudget(e.target.value)}
                      error={Boolean(errors.budget)}
                      aria-describedby="validation-budget"
                    />
                  </Grid>
                </Box>
                <Slider
                  value={budgetValue}
                  onChange={handleChange}
                  valueLabelDisplay="auto"
                  aria-labelledby="range-slider"
                  getAriaValueText={valuetext}
                  step={5}
                  marks={marks}
                  min={5}
                  max={100}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="societyRemarks"
                    control={control}
                    rules={{ required: "societyRemarks is required" }}
                    //   defaultValue={formData?.awards}
                    render={({ field }) => (
                      <TextField
                        rows={3}
                        multiline
                        {...field}
                        label="Society Remarks"
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 1000 }}
                        error={Boolean(errors.societyRemarks)}
                        aria-describedby="societyRemarks"
                      />
                    )}
                  />
                  {errors.societyRemarks && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="societyRemarks"
                    >
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Card>

          {!role && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Status and Assignment Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <Grid container spacing={5} style={{ padding: "16px" }}>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="referenceType"
                      control={control}
                      rules={{ required: "Reference is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Reference Type"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter your Reference Type"
                          error={Boolean(errors.referenceType)}
                          helperText={errors.referenceType?.message}
                          aria-describedby="validation-referenceType"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.houzerSocietyTeamMember)}
                  >
                    <Controller
                      name="houzerSocietyTeamMember"
                      control={control}
                      rules={{
                        required: "Houzer Society Team Member is required",
                      }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Houzer Society Team Member"
                              error={Boolean(errors.houzerSocietyTeamMember)}
                              helperText={
                                errors.houzerSocietyTeamMember
                                  ? errors.houzerSocietyTeamMember.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="referralName"
                      control={control}
                      rules={{ required: "Team reference is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Team Reference "
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Team Reference"
                          error={Boolean(errors.referralName)}
                          helperText={errors.referralName?.message}
                          aria-describedby="validation-referralName"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                    <Controller
                      name="assignedTo"
                      control={control}
                      rules={{ required: "Assigned To is required" }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Assigned To"
                              error={Boolean(errors.assignedTo)}
                              helperText={
                                errors.assignedTo
                                  ? errors.assignedTo.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth error={Boolean(errors.status)}>
                    <Controller
                      name="status"
                      control={control}
                      rules={{ required: "Status is required" }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            statusData?.find(
                              (status) => status.id === field.value
                            ) || null
                          }
                          options={statusData || []}
                          getOptionLabel={(option) => option.listValue || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Status"
                              error={Boolean(errors.status)}
                              helperText={
                                errors.status ? errors.status.message : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="dataSentDate"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Data Sent Date"
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="dataSentDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Card>
          )}
          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Quotes
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid item xs={12} sm={4}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <div>
                  <Button
                    sx={{ margin: "20px" }}
                    variant="contained"
                    onClick={handleDialogOpen}
                  >
                    Request Preliminary Quote
                  </Button>
                </div>
              </Box>

              <QuoteRequestDialog
                open={isDialogOpen}
                onClose={handleDialogClose}
                onSubmit={handleQuotesSubmit}
                //data={dummyData}
              />
            </Grid>
            <Box style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={dummyData}
                columns={columns}
                autoHeight
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            </Box>
          </Card>

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Site Visit Timings
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid item xs={12} sm={4}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <div>
                  <Button
                    sx={{ margin: "20px" }}
                    variant="contained"
                    onClick={handleSiteVisitDialogOpen}
                  >
                    Add site visit slot
                  </Button>
                </div>
              </Box>

              <SiteVisitTimings
                open={siteVisitOpen}
                onClose={handleSiteVisitDialogClose}
              />
            </Grid>
            <Box style={{ height: "100%", width: "100%" }}>
              <DataGrid
                rows={sampleData}
                columns={cols}
                autoHeight
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            </Box>
          </Card>

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Comments Section
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid container sx={{ padding: "10px" }} justifyContent="flex-end">
              {role ? (
                <CommentsDialog
                  setConvList={setConvList}
                  list={[]}
                  role={role}
                />
              ) : (
                <CommentsDialog setConvList={setConvList} list={[]} />
              )}
              {convList?.length > 0 && (
                <TableContainer>
                  <Table sx={{ ml: 4 }}>
                    <TableHead>
                      {role ? (
                        <TableRow>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      ) : (
                        <TableRow>
                          <MUITableCell>Follow-up Date</MUITableCell>
                          <MUITableCell>Comments</MUITableCell>
                          <MUITableCell>Follow Up Action</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {role
                        ? convList?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))
                        : convList?.map((row, index) => (
                            <TableRow key={index}>
                              <MUITableCell>{row.followUpDate}</MUITableCell>
                              <MUITableCell>{row.comments}</MUITableCell>
                              <MUITableCell>{row.followUpAction}</MUITableCell>
                              <MUITableCell>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Card>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={requisitionDialog}
        onClose={handleClickClose}
        maxWidth={"md"}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start", md: "center" },
            fontSize: { xs: 20, md: 26 },
          }}
          textAlign={"center"}
          fontWeight={"bold"}
        >
          Add Specifications
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClickClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <TableContainer>
              <Table>
                <TableBody>
                  {Object.values(
                    // Group categories by sectionId
                    specifications.reduce((sections, category) => {
                      if (!sections[category.sectionId]) {
                        sections[category.sectionId] = [];
                      }
                      sections[category.sectionId].push(category);
                      return sections;
                    }, {})
                  ).map((sectionCategories) => (
                    <Fragment key={sectionCategories[0].sectionId}>
                      <TableRow>
                        <TableCell
                          colSpan={2}
                          style={{ fontSize: "18px", fontWeight: "bold" }}
                        >
                          {/* section header */}
                          {sectionCategories[0].sectionId
                            ? listValues.find(
                                (item) =>
                                  item.id === sectionCategories[0].sectionId
                              )?.name
                            : null}
                        </TableCell>
                      </TableRow>

                      {sectionCategories.map((specification) => (
                        <TableRow key={specification.id}>
                          <TableCell>{specification.name}</TableCell>
                          <TableCell>
                            {(() => {
                              switch (specification.component) {
                                case "Radio buttons":
                                  return (
                                    <FormControl component="fieldset">
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <RadioGroup
                                            {...field}
                                            aria-label="option"
                                            size="small"
                                            value={field.value}
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            row
                                          >
                                            {specification.values.map(
                                              (value) => (
                                                <FormControlLabel
                                                  key={value.id}
                                                  value={value.id}
                                                  control={<Radio />}
                                                  label={value.name}
                                                />
                                              )
                                            )}
                                          </RadioGroup>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Multi Select Dropdown":
                                  return (
                                    <FormControl style={{ width: "500px" }}>
                                      <InputLabel
                                        id={specification.name}
                                        style={{ zIndex: 0 }}
                                      >
                                        Select From {specification.name}
                                      </InputLabel>
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <Select
                                            multiple
                                            labelId={specification.name}
                                            size="small"
                                            label={`Select from ${specification.name}`}
                                            value={field.value || []}
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(
                                                event.target.value
                                              );
                                            }}
                                            renderValue={(selected) => (
                                              <span>
                                                {selected
                                                  .map(
                                                    (selectedValue) =>
                                                      specification.values.find(
                                                        (value) =>
                                                          value.id ===
                                                          selectedValue
                                                      )?.name
                                                  )
                                                  .join(", ")}
                                              </span>
                                            )}
                                          >
                                            {specification.values.map(
                                              (value) => (
                                                <MenuItem
                                                  key={value.id}
                                                  value={value.id}
                                                >
                                                  {value.name}
                                                </MenuItem>
                                              )
                                            )}
                                          </Select>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Single Select Dropdown":
                                  return (
                                    <FormControl style={{ width: "500px" }}>
                                      <InputLabel
                                        id={specification.name}
                                        style={{ zIndex: 0 }}
                                      >
                                        Select From {specification.name}
                                      </InputLabel>
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <Select
                                            labelId={specification.name}
                                            label={`Select from ${specification.name}`}
                                            size="small"
                                            value={field.value || ""}
                                            onChange={(event) => {
                                              handleChangeSelect(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                          >
                                            {specification.values.map(
                                              (value) => (
                                                <MenuItem
                                                  key={value.id}
                                                  value={value.id}
                                                >
                                                  {value.name}
                                                </MenuItem>
                                              )
                                            )}
                                          </Select>
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Switch":
                                  return (
                                    <FormControl
                                      key={specification.name}
                                      component="fieldset"
                                    >
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <FormControlLabel
                                            control={
                                              <Switch
                                                {...field}
                                                checked={field.value}
                                                size="small"
                                                onChange={(event) => {
                                                  handleSpecificationChange(
                                                    specification.id,
                                                    event
                                                  );
                                                  field.onChange(event);
                                                }}
                                                name={specification.name}
                                                inputProps={{
                                                  "aria-label":
                                                    specification.name,
                                                }}
                                              />
                                            }
                                            label="Yes"
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Number Text Field":
                                  return (
                                    <FormControl
                                      key={specification.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={specification.name}
                                            size="small"
                                            type="number"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": specification.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                case "Text Area":
                                  return (
                                    <FormControl
                                      key={specification.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={specification.name}
                                            size="small"
                                            rows={3}
                                            multiline
                                            type="number"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": specification.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                                default:
                                  return (
                                    <FormControl
                                      key={specification.name}
                                      component="fieldset"
                                      style={{ width: "500px" }}
                                    >
                                      <Controller
                                        name={`selectedOptions.${specification.name}`}
                                        control={control}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label={specification.name}
                                            size="small"
                                            variant="outlined"
                                            onChange={(event) => {
                                              handleSpecificationChange(
                                                specification.id,
                                                event
                                              );
                                              field.onChange(event);
                                            }}
                                            fullWidth
                                            inputProps={{
                                              "aria-label": specification.name,
                                            }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  );
                              }
                            })()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "center",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleClickClose}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleAdd}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openMoreInfoDialog}
        onClose={handleInfoDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Comments Details
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleInfoDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Follow Up Date
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpDate}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}

                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>Comments</Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.comments}</Typography>
                  </MUITableCell>
                </TableRow>
                {!role && (
                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        FollowUp Action
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography>{conversation.followUpAction}</Typography>
                    </MUITableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleInfoDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default RequisitionDetails;
